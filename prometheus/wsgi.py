#!/usr/bin/env python
# coding=utf-8
"""
Manage.py commands
"""
import sys

from object_registry import finalize_app_initialization

# noinspection PyUnresolvedReferences
from pos.models import *
from prometheus.app import create_app

# noinspection PyUnresolvedReferences
from prometheus.infrastructure.database import db_engine
from prometheus.models import *

db_engine.setup_tenant_sessions()

app = create_app()
if 'db' not in sys.argv:
    finalize_app_initialization()
