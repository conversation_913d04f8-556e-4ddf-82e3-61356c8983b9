import logging

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.decorators import session_manager
from prometheus.application.hotel_settings.catalog_application_service import (
    CatalogApplicationService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.repositories.credit_note_series_repository import (
    CreditNoteSequenceRepository,
)
from prometheus.domain.billing.repositories.invoice_series_repository import (
    InvoiceSequenceRepository,
)
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.catalog.repositories.reseller_gst_repository import (
    ResellerGstRepository,
)
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from ths_common.constants.billing_constants import IssuedByType
from ths_common.exceptions import ValidationException

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        HotelRepository,
        HotelConfigRepository,
        ResellerGstRepository,
        CreditNoteSequenceRepository,
        InvoiceSequenceRepository,
        CatalogServiceClient,
    ]
)
class HotelOwnershipUpdateService(object):
    def __init__(
        self,
        hotel_repository,
        hotel_config_repository,
        reseller_gst_repository,
        credit_note_series_repository,
        invoice_series_repository,
        catalog_service_client,
    ):
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.reseller_gst_repository = reseller_gst_repository
        self.credit_note_series_repository = credit_note_series_repository
        self.invoice_series_repository = invoice_series_repository
        self.catalog_service_client = catalog_service_client

    @session_manager(commit=True)
    def reset_hotel_ownership(self, hotel_id, gstin_num, series_details, user_data):
        hotel_aggregate = self.hotel_repository.load(hotel_id=hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
        )

        if not series_details:
            (
                invoice_sequence_prefix,
                invoice_sequence_strategy,
                invoice_sequence_number,
            ) = self.invoice_series_repository.get_current_invoice_series_details(
                IssuedByType.HOTEL,
                hotel_aggregate.hotel.hotel_id,
                hotel_aggregate.hotel.gstin_num,
            )
            (
                credit_note_sequence_prefix,
                credit_note_sequence_strategy,
                credit_note_sequence_number,
            ) = self.credit_note_series_repository.get_current_credit_note_series_details(
                IssuedByType.HOTEL,
                hotel_aggregate.hotel.hotel_id,
                hotel_aggregate.hotel.gstin_num,
            )
        else:
            (
                invoice_sequence_prefix,
                invoice_sequence_strategy,
                invoice_sequence_number,
            ) = (
                series_details['invoice']['prefix'],
                series_details['invoice']['strategy'],
                series_details['invoice']['sequence_number'],
            )
            (
                credit_note_sequence_prefix,
                credit_note_sequence_strategy,
                credit_note_sequence_number,
            ) = (
                series_details['credit_note']['prefix'],
                series_details['credit_note']['strategy'],
                series_details['credit_note']['sequence_number'],
            )

        if gstin_num:
            (
                hotel_dto,
                hotel_room_type_config_dtos,
            ) = CatalogApplicationService.load_hotel_from_catalog(
                hotel_id, self.catalog_service_client
            )

            if hotel_dto.gstin_num != gstin_num:
                raise ValidationException(
                    error=ApplicationErrors.INVALID_GSTIN_RESET_REQUEST,
                    format_dict=dict(gstin=hotel_dto.gstin_num),
                )

            hotel_aggregate.update_gst_details(hotel_dto)
            self.hotel_repository.update(hotel_aggregate)

        self._reset_invoice_sequence(
            IssuedByType.HOTEL,
            hotel_aggregate,
            invoice_sequence_prefix,
            invoice_sequence_number,
            invoice_sequence_strategy,
            user_data,
        )

        self._reset_credit_note_sequence(
            IssuedByType.HOTEL,
            hotel_aggregate,
            credit_note_sequence_prefix,
            credit_note_sequence_number,
            credit_note_sequence_strategy,
            user_data,
        )

    def _reset_invoice_sequence(
        self,
        issued_by_type,
        hotel_aggregate,
        prefix,
        sequence_number,
        sequence_generation_strategy,
        user_data,
    ):
        if issued_by_type == IssuedByType.RESELLER:
            reseller_gst_aggregate = self.reseller_gst_repository.load(
                hotel_aggregate.hotel.legal_state_id
            )
            gstin = reseller_gst_aggregate.gst_details.gstin_num
        else:
            gstin = hotel_aggregate.hotel.gstin_num

        self.invoice_series_repository.reset_invoice_sequence(
            user_data.user_type,
            issued_by_type,
            hotel_aggregate.hotel.hotel_id,
            prefix,
            sequence_number,
            sequence_generation_strategy,
            gstin=gstin,
        )

    def _reset_credit_note_sequence(
        self,
        issued_by_type,
        hotel_aggregate,
        prefix,
        sequence_number,
        sequence_generation_strategy,
        user_data,
    ):
        if issued_by_type == IssuedByType.RESELLER:
            reseller_gst_aggregate = self.reseller_gst_repository.load(
                hotel_aggregate.hotel.legal_state_id
            )
            gstin = reseller_gst_aggregate.gst_details.gstin_num
        else:
            gstin = hotel_aggregate.hotel.gstin_num

        self.credit_note_series_repository.reset_credit_note_sequence(
            user_data.user_type,
            issued_by_type,
            hotel_aggregate.hotel.hotel_id,
            prefix,
            sequence_number,
            sequence_generation_strategy,
            gstin=gstin,
        )
