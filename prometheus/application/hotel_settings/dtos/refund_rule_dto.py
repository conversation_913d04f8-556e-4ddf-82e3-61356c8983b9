class RefundRuleDto:
    def __init__(
        self,
        refund_mode_priority_list=None,
        refund_modes_eligible_for_partial_refunds=None,
        is_payout_link_enabled=False,
        non_refundable_payment_modes=None,
    ):
        self.refund_mode_priority_list = (
            refund_mode_priority_list if refund_mode_priority_list else []
        )
        self.refund_modes_eligible_for_partial_refunds = (
            refund_modes_eligible_for_partial_refunds
            if refund_modes_eligible_for_partial_refunds
            else []
        )
        self.non_refundable_payment_modes = (
            non_refundable_payment_modes if non_refundable_payment_modes else []
        )
        self.is_payout_link_enabled = is_payout_link_enabled
