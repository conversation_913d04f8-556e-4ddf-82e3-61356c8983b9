from prometheus import crs_context
from prometheus.domain.billing.dto import PaymentData
from ths_common.constants.billing_constants import PaymentChannels
from ths_common.constants.tenant_settings_constants import TenantSettingName


def add_payments_to_bill(
    self,
    bill_aggregate,
    default_billed_entity_category,
    payment_request,
    hotel_aggregate,
):
    payments_dto = [
        PaymentData.from_dict(payment_dict) for payment_dict in payment_request
    ]
    self.payment_data_sanitizer.sanitize(
        payments_dto, bill_aggregate, default_billed_entity_category
    )
    payments_dto = bill_aggregate.add_payments(payments_dto, raise_event=False)
    refund_rules = self.tenant_settings.get_refund_rule(
        hotel_id=hotel_aggregate.hotel_id,
    )
    is_auto_refund_enabled = self.tenant_settings.get_setting_value(
        TenantSettingName.IS_AUTO_REFUND_ENABLED.value,
        hotel_id=hotel_aggregate.hotel_id,
    )
    for payment_dto in payments_dto:
        if (
            crs_context.is_treebo_tenant()
            and payment_dto.payment_channel == PaymentChannels.ONLINE
        ) or (
            is_auto_refund_enabled
            and refund_rules
            and payment_dto.payment_mode in refund_rules.refund_mode_priority_list
        ):
            bill_aggregate.post_payment_and_update_posting_date(
                payment_dto.payment_id, payment_dto.date_of_payment
            )
