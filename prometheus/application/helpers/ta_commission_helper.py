from typing import Iterable, List, Set, Tuple

from treebo_commons.utils.dateutils import ymd_str_to_date

from object_registry import register_instance
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.dtos import Room<PERSON>ight<PERSON><PERSON>ommissionDto
from prometheus.domain.booking.entities import RoomStay
from prometheus.domain.booking.entities.room_night_commission import (
    RoomNightTACommission,
)
from prometheus.domain.booking.services.booking_commission_calculator import (
    BookingCommissionCalculator,
)
from prometheus.domain.domain_events.domain_event_registry import (
    publish_ta_commission_change_event,
)
from prometheus.infrastructure.external_clients.company_profile_service_client import (
    CompanyProfileServiceClient,
)
from ths_common.constants.booking_constants import TACommissionStatus
from ths_common.value_objects import TADetails


@register_instance(
    dependencies=[CompanyProfileServiceClient, BookingCommissionCalculator]
)
class TACommissionHelper(object):
    def __init__(
        self,
        company_profile_service_client: CompanyProfileServiceClient,
        booking_commission_calculator: BookingCommissionCalculator,
    ):
        self.company_profile_service_client = company_profile_service_client
        self.booking_commission_calculator = booking_commission_calculator

    def populate_commission_details_in_travel_agent(
        self, travel_agent_details: TADetails, hotel_id
    ):
        travel_agent_details.ta_commission_details = (
            self.company_profile_service_client.fetch_ta_commission_details(
                travel_agent_details.legal_details.external_reference_id,
                hotel_id,
            )
        )

    @staticmethod
    def _get_date_wise_room_night_pre_tax_price(
        room_stay: RoomStay, bill_aggregate: BillAggregate
    ):
        active_stay_dates = room_stay.charge_id_map.keys()
        stay_date_room_night_price_map = dict()
        for date in active_stay_dates:
            date = ymd_str_to_date(date)
            room_rent_charge_id = room_stay.get_charge_for_date(date)
            stay_date_room_night_price_map[
                date
            ] = bill_aggregate.get_pre_tax_room_stay_amount(room_rent_charge_id)
        return stay_date_room_night_price_map

    @staticmethod
    def _segregate_existing_commissions(
        room_stay: RoomStay,
    ) -> Tuple[List[RoomNightTACommission], List[RoomNightTACommission], Set]:
        existing_commission = room_stay.get_all_active_room_night_ta_commissions()
        current_stay_date = [
            ymd_str_to_date(date) for date in room_stay.charge_id_map.keys()
        ]
        ta_commissions_to_update, ta_commissions_to_cancel = [], []
        date_wise_commission = {
            rn_ta_cm.applicable_on: rn_ta_cm for rn_ta_cm in existing_commission
        }
        for date, rn_ta_cm in date_wise_commission.items():
            if date in current_stay_date:
                ta_commissions_to_update.append(rn_ta_cm)
            else:
                ta_commissions_to_cancel.append(rn_ta_cm)
        new_dates_to_add_commission = set(current_stay_date) - set(
            date_wise_commission.keys()
        )
        return (
            ta_commissions_to_update,
            ta_commissions_to_cancel,
            new_dates_to_add_commission,
        )

    def add_commission_in_new_booking(
        self, booking_aggregate: BookingAggregate, bill_aggregate: BillAggregate
    ):
        travel_agent_details: TADetails = booking_aggregate.get_travel_agent_details()
        if not travel_agent_details:
            return

        if not (
            travel_agent_details.has_super_hero_profile_tagged()
            or travel_agent_details.has_commission_rule()
        ):
            return

        room_stays = booking_aggregate.get_active_room_stays()
        for room_stay in room_stays:
            current_room_night_prices = self._get_date_wise_room_night_pre_tax_price(
                room_stay, bill_aggregate
            )
            stay_dates = [
                ymd_str_to_date(date) for date in room_stay.charge_id_map.keys()
            ]
            new_commission_dtos = self._add_ta_commission_for_new_stay_date(
                stay_dates, current_room_night_prices
            )
            self.booking_commission_calculator.calculate_commission(
                booking_aggregate.hotel_id,
                booking_aggregate.booking_id,
                new_commission_dtos,
                travel_agent_details,
                booking_aggregate.booking.created_at.date(),
            )
            valid_commission_dtos = [
                rn_cm
                for rn_cm in new_commission_dtos
                if rn_cm.posttax_amount is not None
            ]
            if not valid_commission_dtos:
                return
            booking_aggregate.add_ta_commission_to_room_stay(
                room_stay.room_stay_id, valid_commission_dtos
            )
        booking_aggregate.booking_event_raiser.register_ta_commission_added_event()

    def handle_ta_commission_recalculation_on_booking(
        self, booking_aggregate: BookingAggregate, bill_aggregate, old_commission_rule
    ):
        if not booking_aggregate.should_recalculate_commission():
            return

        travel_agent_details: TADetails = booking_aggregate.get_travel_agent_details()
        if (
            travel_agent_details
            and travel_agent_details.has_super_hero_profile_tagged()
            and not travel_agent_details.has_commission_rule()
        ):
            self.populate_commission_details_in_travel_agent(
                travel_agent_details,
                hotel_id=booking_aggregate.hotel_id,
            )
        current_commission_rule = booking_aggregate.get_ta_commission_rule()
        rule_changed = old_commission_rule != current_commission_rule
        if current_commission_rule:
            self.recalculate_commission_on_booking(
                booking_aggregate, bill_aggregate, for_rule_change=rule_changed
            )
        else:
            booking_aggregate.cancel_all_ta_commissions()

    @publish_ta_commission_change_event
    def _cancel_all_commission(self, booking_aggregate):
        booking_aggregate.cancel_all_ta_commissions()

    @publish_ta_commission_change_event
    def recalculate_commission_on_booking(
        self,
        booking_aggregate: BookingAggregate,
        bill_aggregate: BillAggregate,
        for_rule_change=False,
    ):
        room_stays = booking_aggregate.get_active_room_stays()
        self._recalculate_commission_for_room_stays(
            booking_aggregate, room_stays, bill_aggregate, for_rule_change
        )

    @staticmethod
    def _remove_duplicate_stays_scoped_under_booking(room_stays: Iterable[RoomStay]):
        # can't override hash method of entity
        filtered, sack = [], set()
        for rs in room_stays:
            if rs.room_stay_id not in sack:
                filtered.append(rs)
                sack.add(rs.room_stay_id)
        return filtered

    # this function will handle change in room night price(guest add/remove, rate plan change, update room type)
    # change in stay duration (remove commission for non stay duration and add commission for stay duration)
    # in ta profile commission rule
    @publish_ta_commission_change_event
    def recalculate_commission_for_room_stays(
        self,
        booking_aggregate: BookingAggregate,
        room_stays: Iterable[RoomStay],
        bill_aggregate: BillAggregate,
        for_rule_change=False,
    ):
        if not booking_aggregate.should_recalculate_commission():
            return

        self._recalculate_commission_for_room_stays(
            booking_aggregate, room_stays, bill_aggregate, for_rule_change
        )

    def _recalculate_commission_for_room_stays(
        self,
        booking_aggregate: BookingAggregate,
        room_stays: Iterable[RoomStay],
        bill_aggregate: BillAggregate,
        for_rule_change=False,
    ):
        travel_agent_details: TADetails = booking_aggregate.get_travel_agent_details()
        if not travel_agent_details:
            return

        if not (
            travel_agent_details.has_super_hero_profile_tagged()
            or travel_agent_details.has_commission_rule()
        ):
            return

        room_stays = self._remove_duplicate_stays_scoped_under_booking(room_stays)
        for room_stay in room_stays:
            (
                ta_commissions_to_update,
                ta_commissions_to_cancel,
                new_dates_to_add_commission,
            ) = self._segregate_existing_commissions(room_stay)
            current_room_night_prices = self._get_date_wise_room_night_pre_tax_price(
                room_stay, bill_aggregate
            )
            commission_dto_for_calculation = self._handle_ta_commission_update(
                booking_aggregate,
                current_room_night_prices,
                room_stay,
                ta_commissions_to_update,
                for_rule_change,
            )
            commission_dto_for_calculation.extend(
                self._add_ta_commission_for_new_stay_date(
                    new_dates_to_add_commission, current_room_night_prices
                )
            )
            self.booking_commission_calculator.calculate_commission(
                booking_aggregate.hotel_id,
                booking_aggregate.booking_id,
                commission_dto_for_calculation,
                booking_aggregate.get_travel_agent_details(),
                booking_aggregate.booking.created_at.date(),
            )
            ta_commissions_to_cancel.extend(
                [
                    rn_cm
                    for rn_cm in commission_dto_for_calculation
                    if rn_cm.posttax_amount is None
                    and rn_cm.ta_commission_id is not None
                ]
            )
            booking_aggregate.cancel_ta_commission(
                room_stay.room_stay_id,
                [ta_cm.ta_commission_id for ta_cm in ta_commissions_to_cancel],
            )
            valid_commission_dtos = [
                rn_cm
                for rn_cm in commission_dto_for_calculation
                if rn_cm.posttax_amount is not None
            ]
            if valid_commission_dtos:
                self._add_or_update_ta_commission_to_room_stay(
                    booking_aggregate, valid_commission_dtos, room_stay
                )

    @staticmethod
    def _add_or_update_ta_commission_to_room_stay(
        booking_aggregate, commission_dto_for_calculation, room_stay
    ):
        for ta_commission_dto in commission_dto_for_calculation:
            if ta_commission_dto.ta_commission_id is None:
                booking_aggregate.add_ta_commission_to_room_stay(
                    room_stay.room_stay_id, [ta_commission_dto]
                )
            else:
                booking_aggregate.update_ta_commission_in_room_stay(
                    room_stay.room_stay_id,
                    ta_commission_dto.ta_commission_id,
                    ta_commission_dto,
                )

    @staticmethod
    def _add_ta_commission_for_new_stay_date(
        new_dates_to_add_commission, current_room_night_prices
    ):
        commission_dto_for_calculation = []
        for date in new_dates_to_add_commission:
            room_night_price = current_room_night_prices.get(date)
            new_commission_dto = (
                RoomNightTACommissionDto.initialise_with_room_night_price(
                    applicable_on=date,
                    room_night_price=room_night_price,
                )
            )
            commission_dto_for_calculation.append(new_commission_dto)
        return commission_dto_for_calculation

    def _handle_ta_commission_update(
        self,
        booking_aggregate,
        current_room_night_prices,
        room_stay,
        ta_commissions_to_update,
        for_rule_change=False,
    ):
        commission_dto_for_calculation = []
        for room_night_ta_commission in ta_commissions_to_update:
            current_room_night_price = current_room_night_prices.get(
                room_night_ta_commission.applicable_on
            )
            if (
                current_room_night_price.amount
                != room_night_ta_commission.room_night_price.amount
                or for_rule_change
            ):
                if room_night_ta_commission.status == TACommissionStatus.LOCKED:
                    ta_commission_allowance = (
                        booking_aggregate.nullify_locked_ta_commission_via_reissue(
                            room_stay.room_stay_id,
                            room_night_ta_commission.ta_commission_id,
                        )
                    )
                    new_commission_dto = (
                        RoomNightTACommissionDto.initialise_with_room_night_price(
                            applicable_on=room_night_ta_commission.applicable_on,
                            room_night_price=current_room_night_price,
                            reissued_on=ta_commission_allowance.reissued_on,
                            locked_on=ta_commission_allowance.locked_on,
                            status=TACommissionStatus.LOCKED,
                        )
                    )
                    commission_dto_for_calculation.append(new_commission_dto)
                else:
                    commission_dto_for_calculation.append(
                        self._build_commission_dto_for_update(
                            room_night_ta_commission, current_room_night_price
                        )
                    )
        return commission_dto_for_calculation

    @staticmethod
    def _build_commission_dto_for_update(
        ta_commission: RoomNightTACommission, new_room_night_price
    ):
        return RoomNightTACommissionDto.initialise_with_room_night_price(
            applicable_on=ta_commission.applicable_on,
            room_night_price=new_room_night_price,
            ta_commission_id=ta_commission.ta_commission_id,
        )

    def recalculate_commission_for_room_stay_from_charge_ids(
        self,
        bill_aggregate,
        booking_aggregate,
        charge_ids=None,
        charge_id_to_room_stay_map=None,
    ):
        if not booking_aggregate.should_recalculate_commission():
            return

        if charge_id_to_room_stay_map is None:
            charge_id_to_room_stay_map = (
                booking_aggregate.get_all_active_rate_plan_charges_to_room_stay_map()
            )

        if charge_ids:
            room_stay_commission_to_be_recalculated = [
                charge_id_to_room_stay_map.get(charge_id)
                for charge_id in charge_ids
                if charge_id_to_room_stay_map.get(charge_id)
            ]
        else:
            room_stay_commission_to_be_recalculated = (
                charge_id_to_room_stay_map.values()
            )
        self.recalculate_commission_for_room_stays(
            booking_aggregate, room_stay_commission_to_be_recalculated, bill_aggregate
        )
