import logging

from prometheus.domain.integration_event.integration_event_factory import (
    IntegrationEventFactory,
)

logger = logging.getLogger(__name__)


class IntegrationEventWriter:
    def __init__(self, integration_event_repository):
        self.integration_event_repository = integration_event_repository
        self.event_dtos = []

    def write(self, event_dto):
        """
        Stores the event in buffer
        :param event_dto:
        :return:
        """
        self.event_dtos.append(event_dto)

    def flush(self):
        """
        Flushes all the events written, into the DB
        :return:
        """
        event_aggregates = []
        for event_dto in self.event_dtos:
            event_aggregates.append(self._flush(event_dto))
        return event_aggregates

    def _flush(self, event_dto):
        event_aggregate = IntegrationEventFactory.create_integration_event(event_dto)
        self.integration_event_repository.save(event_aggregate)
        logger.info(
            'Integration event created with event id: %s',
            event_aggregate.integration_event.event_id,
        )
        return event_aggregate
