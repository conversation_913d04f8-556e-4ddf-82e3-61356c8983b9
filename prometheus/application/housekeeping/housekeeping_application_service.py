from typing import List

from treebo_commons.utils import dateutils as dt

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit_housekeeping
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.common.decorators import authorize_write_op
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.factories.hotel_staff_factory import HotelStaffFactory
from prometheus.domain.catalog.repositories import RoomTypeRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.catalog.repositories.hotel_staff_repository import (
    HotelStaffRepository,
)
from prometheus.domain.catalog.repositories.room_repository import RoomRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.dtos.checked_in_guest_data import CheckedInGuestData
from prometheus.domain.inventory.factories.hk_occupancy_audit_factory import (
    HKOccupancyAuditFactory,
)
from prometheus.domain.inventory.repositories.hk_occupancy_audit_repository import (
    HKOccupancyAuditRepository,
)
from prometheus.domain.inventory.repositories.room_allotment_repository import (
    RoomAllotmentRepository,
)
from prometheus.domain.inventory.value_objects.hk_occupancy import HKOccupancy
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.housekeeping_facts import HouseKeepingFacts
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from prometheus.infrastructure.external_clients.template_service_client import (
    TemplateFormat,
    TemplateNameSpace,
    TemplateService,
)
from schema_instances import get_schema_obj
from ths_common.constants.booking_constants import AgeGroup
from ths_common.constants.catalog_constants import RoomType
from ths_common.constants.inventory_constants import AllottedFor, RoomCurrentStatus
from ths_common.exceptions import InvalidOperationError, ValidationException
from ths_common.utils import dateutils
from ths_common.value_objects import Occupancy


@register_instance(
    dependencies=[
        RoomRepository,
        HotelRepository,
        RoomAllotmentRepository,
        HotelStaffRepository,
        HotelConfigRepository,
        AwsServiceClient,
        RoomTypeRepository,
        BookingRepository,
        HKOccupancyAuditRepository,
    ]
)
class HouseKeepingApplicationService(object):
    DefaultSignedUrlExpirationHours = 2
    DefaultSignedUrlExpirationSeconds = DefaultSignedUrlExpirationHours * 3600

    def __init__(
        self,
        room_repository,
        hotel_repository,
        room_allotment_repository,
        hotel_staff_repository,
        hotel_config_repository,
        aws_service_client,
        room_type_repository,
        booking_repository,
        hk_occupancy_audit_repository,
    ):
        self.room_repository = room_repository
        self.hotel_repository = hotel_repository
        self.room_allotment_repository = room_allotment_repository
        self.hotel_staff_repository = hotel_staff_repository
        self.hotel_config_repository = hotel_config_repository
        self.aws_service_client = aws_service_client
        self.room_type_repository = room_type_repository
        self.booking_repository = booking_repository
        self.hk_occupancy_audit_repository = hk_occupancy_audit_repository

    def is_housekeeping_enabled(self, hotel_id):
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        return hotel_aggregate.hotel.housekeeping_enabled

    @session_manager(commit=True)
    @authorize_write_op
    def create_new_housekeeper(self, hotel_id, name):
        _ = self.hotel_repository.load(hotel_id)
        hotel_staff_aggregate = self.hotel_staff_repository.load_for_update(hotel_id)
        if not hotel_staff_aggregate:
            hotel_staff_aggregate = HotelStaffFactory.create_new_housekeeper(
                hotel_id, name
            )
            housekeeper = hotel_staff_aggregate.housekeepers[0]
            self.hotel_staff_repository.save(hotel_staff_aggregate)
        else:
            housekeeper = hotel_staff_aggregate.add_new_housekeeper(name)
            self.hotel_staff_repository.update(hotel_staff_aggregate)
        return housekeeper

    @session_manager(commit=True)
    @authorize_write_op
    def update_housekeeper(self, hotel_id, housekeeper_id, name):
        hotel_staff_aggregate = self.hotel_staff_repository.load_for_update(hotel_id)
        housekeeper = hotel_staff_aggregate.update_housekeeper(housekeeper_id, name)
        self.hotel_staff_repository.update(hotel_staff_aggregate)
        return housekeeper

    @session_manager(commit=True)
    @authorize_write_op
    def remove_housekeeper(self, hotel_id, housekeeper_id):
        housekeeping_records = self.get_all_housekeeping_records(hotel_id)
        housekeeper_assigned = [
            housekeeping_record
            for housekeeping_record in housekeeping_records
            if housekeeping_record.housekeeper_id == housekeeper_id
        ]
        if housekeeper_assigned:
            raise InvalidOperationError(
                error=ApplicationErrors.ASSIGNED_HOUSEKEEPER_DELETION
            )
        self.hotel_staff_repository.delete_housekeeper(hotel_id, housekeeper_id)

    @session_manager(commit=True)
    @authorize_write_op
    def create_or_update_multiple_housekeepers(self, hotel_id, request_data: list):
        """

        :param hotel_id:
        :param request_data: List of data from CreateOrUpdateHouseKeepersSchema schema
        :return:
        """
        _ = self.hotel_repository.load(hotel_id)
        hotel_staff_aggregate = self.hotel_staff_repository.load_for_update(hotel_id)

        housekeeping_records = self.get_all_housekeeping_records(hotel_id)
        housekeepers = []
        for data in request_data:
            if 'delete' in data and data.get('delete'):
                housekeeper_id = data.get('housekeeper_id')
                housekeeper_assigned = [
                    housekeeping_record
                    for housekeeping_record in housekeeping_records
                    if housekeeping_record.housekeeper_id == housekeeper_id
                ]
                if housekeeper_assigned:
                    raise InvalidOperationError(
                        error=ApplicationErrors.ASSIGNED_HOUSEKEEPER_DELETION
                    )

                hotel_staff_aggregate.delete_housekeeper(housekeeper_id)
                self.hotel_staff_repository.delete_housekeeper(hotel_id, housekeeper_id)
            elif data.get('housekeeper_id'):
                housekeepers.append(
                    hotel_staff_aggregate.update_housekeeper(
                        data.get('housekeeper_id'), data.get('name')
                    )
                )
            else:
                housekeepers.append(
                    hotel_staff_aggregate.add_new_housekeeper(data.get('name'))
                )

        self.hotel_staff_repository.update(hotel_staff_aggregate)
        return housekeepers

    def get_all_housekeepers(self, hotel_id):
        _ = self.hotel_repository.load(hotel_id)
        hotel_staff_aggregate = self.hotel_staff_repository.load(hotel_id)
        return hotel_staff_aggregate.housekeepers

    @session_manager(commit=True)
    @audit_housekeeping(action_performed="update_housekeeping_record")
    @authorize_write_op
    def update_housekeeping_occupancy_record(
        self, hotel_id, room_id, occupancy_data, user_data
    ):
        RuleEngine.action_allowed(
            action="housekeeping_occupancy_record",
            facts=HouseKeepingFacts(
                user_type=user_data.user_type,
            ),
            fail_on_error=True,
        )
        _ = self.room_repository.load(hotel_id=hotel_id, room_id=room_id)
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        hotel_context = crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate
        )
        room_allotment_aggregate = self.room_allotment_repository.load_for_update(
            hotel_id, room_id
        )
        room_allotment_aggregate.update_housekeeping_occupancy(
            HKOccupancy(
                count=occupancy_data.get('count'),
                remarks=occupancy_data.get('remarks'),
                applicable_for=hotel_context.current_date(),
            )
        )
        room_wise_grouped_active_guests = self._get_room_wise_guest_stays(
            hotel_id, [room_allotment_aggregate]
        )
        housekeeping_record = room_allotment_aggregate.housekeeping_record
        housekeeping_record.fd_occupancy = None
        if room_wise_grouped_active_guests.get(housekeeping_record.room_id):
            housekeeping_record.fd_occupancy = (
                self._get_occupancy_from_active_guest_stays(
                    room_wise_grouped_active_guests[housekeeping_record.room_id]
                )
            )
        hk_occupancy_audit = (
            HKOccupancyAuditFactory.create_new_audit_from_room_allotment_aggregate(
                room_allotment_aggregate
            )
        )
        self.room_allotment_repository.update(room_allotment_aggregate)
        self.hk_occupancy_audit_repository.save(hk_occupancy_audit)
        housekeeping_record.room_current_status = (
            room_allotment_aggregate.room_inventory.current_status
        )

        IntegrationEventApplicationService.create_housekeeping_status_event(
            housekeeping_record=housekeeping_record,
            user_action="update_housekeeping_record",
        )
        return housekeeping_record

    @session_manager(commit=True)
    @audit_housekeeping(action_performed="update_housekeeping_record")
    @authorize_write_op
    def update_housekeeping_record(
        self, hotel_id, room_id, housekeeping_status, housekeeper_id, remarks, user_data
    ):
        _ = self.room_repository.load(hotel_id=hotel_id, room_id=room_id)
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        crs_context.set_hotel_context(hotel_aggregate, hotel_config_aggregate)
        room_allotment_aggregate = self.room_allotment_repository.load_for_update(
            hotel_id, room_id
        )
        if housekeeping_status:
            if room_allotment_aggregate.is_marked_out_of_order():
                raise ValidationException(
                    error=ApplicationErrors.HOUSEKEEPING_STATUS_UPDATED_DISALLOWED_FROM_DNR
                )
            room_allotment_aggregate.update_housekeeping_record(
                housekeeping_status=housekeeping_status, remarks=remarks
            )
        if housekeeper_id:
            hotel_staff_aggregate = self.hotel_staff_repository.load(hotel_id)
            housekeeper = hotel_staff_aggregate.get_housekeeper(housekeeper_id)
            room_allotment_aggregate.assign_housekeeper(housekeeper)

        self.room_allotment_repository.update(room_allotment_aggregate)
        room_wise_grouped_active_guests = self._get_room_wise_guest_stays(
            hotel_id, [room_allotment_aggregate]
        )
        housekeeping_record = room_allotment_aggregate.housekeeping_record
        if room_wise_grouped_active_guests.get(housekeeping_record.room_id):
            housekeeping_record.fd_occupancy = (
                self._get_occupancy_from_active_guest_stays(
                    room_wise_grouped_active_guests[housekeeping_record.room_id]
                )
            )
        housekeeping_record.room_current_status = (
            room_allotment_aggregate.room_inventory.current_status
        )

        IntegrationEventApplicationService.create_housekeeping_status_event(
            housekeeping_record=housekeeping_record,
            user_action="update_housekeeping_record",
        )
        return housekeeping_record

    def get_all_housekeeping_records(self, hotel_id):
        _ = self.hotel_repository.load(hotel_id)
        room_allotment_aggregates = (
            self.room_allotment_repository.load_all_housekeeping_records(hotel_id)
        )
        housekeeping_records = []
        room_wise_grouped_active_guests = self._get_room_wise_guest_stays(
            hotel_id, room_allotment_aggregates
        )

        for room_allotment_aggregate in room_allotment_aggregates:
            housekeeping_record = room_allotment_aggregate.housekeeping_record
            housekeeping_record.room_current_status = (
                room_allotment_aggregate.room_inventory.current_status
            )
            if room_wise_grouped_active_guests.get(housekeeping_record.room_id):
                housekeeping_record.fd_occupancy = (
                    self._get_occupancy_from_active_guest_stays(
                        room_wise_grouped_active_guests[housekeeping_record.room_id]
                    )
                )
            housekeeping_records.append(housekeeping_record)
        return housekeeping_records

    def _get_room_wise_guest_stays(self, hotel_id, room_allotment_aggregates):
        room_ids = []
        for room_allotment_aggregate in room_allotment_aggregates:
            current_room_status = room_allotment_aggregate.room_inventory.current_status
            if (
                current_room_status == RoomCurrentStatus.OCCUPIED
                and room_allotment_aggregate.room_allotments
            ):
                room_ids.append(room_allotment_aggregate.room_inventory.room_id)
        room_wise_checked_in_allotments = (
            self.room_allotment_repository.load_checked_in_room_allotments(
                hotel_id, room_ids
            )
        )
        latest_checked_in_allotments = []
        for room_id, allotments in room_wise_checked_in_allotments.items():
            allotments = [
                ra for ra in allotments if ra.allotted_for == AllottedFor.STAY.value
            ]
            sorted_room_allotments = sorted(
                allotments, key=lambda ra: ra.start_time, reverse=True
            )
            if len(sorted_room_allotments) > 0:
                latest_checked_in_allotments.append(
                    sorted_room_allotments[0].allotment_id
                )
        room_wise_grouped_active_guests = (
            self.booking_repository.load_checked_in_guests_for_allotment_ids(
                latest_checked_in_allotments
            )
        )
        return room_wise_grouped_active_guests

    @staticmethod
    def _get_occupancy_from_active_guest_stays(guest_stays: List[CheckedInGuestData]):
        adult = 0
        child = 0
        for gs in guest_stays:
            if gs.age_group == AgeGroup.ADULT.value:
                adult += 1
            if gs.age_group == AgeGroup.CHILD.value:
                child += 1
        return Occupancy(adult=adult, child=child)

    @set_hotel_context()
    def generate_housekeeping_record_template_url(self, hotel_id, hotel_aggregate=None):
        housekeeping_records = self.get_all_housekeeping_records(hotel_id)
        housekeeping_records_list = list()
        room_aggregates_room_id_map = dict()
        room_aggregates = self.room_repository.load_multiple(hotel_id=hotel_id)
        for room_aggregate in room_aggregates:
            room_aggregates_room_id_map[
                room_aggregate.room.room_id
            ] = room_aggregate.room

        room_id_type_map = self.room_type_repository.load_type_map()

        for housekeeping_record in housekeeping_records:
            room_details = room_aggregates_room_id_map.get(housekeeping_record.room_id)
            room_type_id = room_details.room_type_id
            room_type = room_id_type_map[room_type_id].room_type.type
            # Housekeeping Print View doesn’t have House Accounts Room Type
            if room_type != RoomType.HOUSE_ACCOUNTS.value and room_details.is_active():
                housekeeping_data = {
                    "roomNumber": room_details.room_number,
                    "roomStatus": housekeeping_record.room_current_status.value,
                    "houseKeepingStatus": housekeeping_record.housekeeping_status.value,
                    "houseKeepingRemark": housekeeping_record.remarks,
                    "roomType": room_details.room_type_id,
                }
                housekeeping_records_list.append(housekeeping_data)
        housekeeping_records_list.sort(
            key=lambda hr: (hr['roomType'], hr['roomNumber'])
        )
        context = dict(
            date=dateutils.format_datetime_human_readable(dt.current_datetime()),
            houseKeepingRecords=housekeeping_records_list,
        )
        from prometheus.common.serializers import HouseKeepingRecordTemplateSchema

        template_json = (
            get_schema_obj(HouseKeepingRecordTemplateSchema).dump(context).data
        )
        housekeeping_record_url = TemplateService().generate(
            TemplateNameSpace.HOUSEKEEPING_STATUS.value,
            template_json,
            TemplateFormat.PDF,
        )

        if not housekeeping_record_url:
            return None

        housekeeping_record_signed_url = (
            self.aws_service_client.get_presigned_url_from_s3_url(
                housekeeping_record_url,
                link_expires_in=self.DefaultSignedUrlExpirationSeconds,
            )
        )
        return housekeeping_record_signed_url
