from typing import List

from prometheus.domain.billing.dto.charge_data import ChargeData


class NewBillDto(object):
    def __init__(
        self,
        bill_date,
        app_id,
        parent_reference_number,
        parent_info,
        vendor_id,
        vendor_details,
        charges: List[ChargeData],
    ):
        self.bill_date = bill_date
        self.app_id = app_id
        self.parent_reference_number = parent_reference_number
        self.parent_info = parent_info
        self.vendor_id = vendor_id
        self.vendor_details = vendor_details
        self.charges = charges

    def set_parent_info(self, parent_info):
        self.parent_info = parent_info
