from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.decorators import session_manager
from prometheus.domain.billing.repositories import BillRepository
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from prometheus.infrastructure.external_clients.template_service_client import (
    TemplateFormat,
    TemplateNameSpace,
    TemplateService,
)
from schema_instances import get_schema_obj
from ths_common.constants.billing_constants import (
    BillAppId,
    PaymentStatus,
    PaymentTypes,
)


@register_instance(dependencies=[BillRepository, AwsServiceClient])
class PrintBillCommandHandler:
    DefaultSignedUrlExpirationHours = 2
    DefaultSignedUrlExpirationSeconds = DefaultSignedUrlExpirationHours * 3600

    def __init__(
        self, bill_repository: BillRepository, aws_service_client: AwsServiceClient
    ):
        self.bill_repository = bill_repository
        self.aws_service_client = aws_service_client

    @staticmethod
    def _get_charge_details_for_bill_template(bill_aggregate):
        return [
            dict(
                applicable_date=dateutils.to_date(charge.applicable_date),
                item_name=charge.item.name,
                item_details=charge.item.details,
                pretax_amount=charge.pretax_amount,
                posttax_amount=charge.posttax_amount,
                # TODO: Remove tax_type from key
                tax={
                    tax.tax_type: dict(percentage=tax.percentage, amount=tax.amount)
                    for tax in charge.tax_details
                },
                tax_breakup=charge.tax_details,
            )
            for charge in bill_aggregate.active_charges
        ]

    @staticmethod
    def _get_payment_details_for_bill_template(bill_aggregate):
        return [
            dict(
                date_of_payment=dateutils.to_date(payment.date_of_payment),
                id="{}-{}".format(bill_aggregate.bill_id, payment.payment_id),
                payment_type='Refund'
                if payment.payment_type == PaymentTypes.REFUND
                else 'Payment',
                payment_mode=payment.payment_mode,
                amount=payment.amount,
            )
            for payment in bill_aggregate.payments
            if payment.status != PaymentStatus.CANCELLED
        ]

    @session_manager()
    def handle(self, bill_id):
        bill_aggregate = self.bill_repository.load(bill_id)
        crs_context.set_current_bill(bill_aggregate)

        if bill_aggregate.bill.app_id != BillAppId.POS_APP.value:
            raise NotImplementedError()

        bill_template_context = dict(
            bill=bill_aggregate,
            parent_info=bill_aggregate.bill.parent_info,
            charge_items=self._get_charge_details_for_bill_template(bill_aggregate),
            payments=self._get_payment_details_for_bill_template(bill_aggregate),
        )
        from prometheus.common.serializers import PosBillTemplateSchema

        template_json = (
            get_schema_obj(PosBillTemplateSchema).dump(bill_template_context).data
        )
        bill_template_url = TemplateService().generate(
            TemplateNameSpace.THS_POS_BILL.value, template_json, TemplateFormat.PDF
        )

        if not bill_template_url:
            return None

        presigned_url = self.aws_service_client.get_presigned_url_from_s3_url(
            s3_url=bill_template_url,
            link_expires_in=self.DefaultSignedUrlExpirationSeconds,
        )

        return presigned_url
