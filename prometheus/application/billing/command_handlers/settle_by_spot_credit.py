from typing import List

from treebo_commons.money import Money

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.billing.dtos.spot_credit_settlement_dto import (
    SpotCreditSettlementData,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.exceptions import SpotCreditIssueError
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.services.spot_credit_service import SpotCreditService
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    SpotCreditSettlementType,
)
from ths_common.constants.tenant_settings_constants import TenantSettingName


@register_instance(
    dependencies=[
        BillRepository,
        BookingRepository,
        SpotCreditService,
        TenantSettings,
    ]
)
class SettleBySpotCreditCommandHandler:
    def __init__(
        self,
        bill_repository: BillRepository,
        booking_repository: BookingRepository,
        spot_credit_service: SpotCreditService,
        tenant_settings: TenantSettings,
    ):
        self.bill_repository = bill_repository
        self.booking_repository = booking_repository
        self.spot_credit_service = spot_credit_service
        self.tenant_settings = tenant_settings

    @session_manager(commit=True)
    @set_hotel_context()
    @audit(audit_type=AuditType.SPOT_CREDIT_PROVIDED)
    def handle(
        self,
        bill_id,
        settlement_data: List[SpotCreditSettlementData],
        user_data,
        hotel_aggregate=None,
    ):
        bill_aggregate = self.bill_repository.load_for_update(bill_id)
        booking_aggregate = self.booking_repository.load_booking_by_bill_id(bill_id)
        RuleEngine.action_allowed(
            action='settle_by_spot_credit',
            facts=Facts(user_type=user_data.user_type),
            fail_on_error=True,
        )
        if not hotel_aggregate:
            crs_context_middleware.set_hotel_context(booking_aggregate.booking.hotel_id)
        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_bill(bill_aggregate)
        spot_credit_eligible_account_categories = (
            self._get_spot_credit_enabled_account_categories(booking_aggregate)
        )
        settled_folios = []
        for settlement_item in settlement_data:
            billed_entity_id = settlement_item.billed_entity_id
            settlement_type = settlement_item.spot_credit_settlement_type
            billed_entity = bill_aggregate.get_billed_entity(billed_entity_id)
            self._fail_if_not_eligible(
                billed_entity, spot_credit_eligible_account_categories
            )
            accounts_to_settle = []
            if settlement_type == SpotCreditSettlementType.BILLED_ENTITY_LEVEL:
                accounts_to_settle = self._get_eligible_accounts_for_spot_credit(
                    [billed_entity],
                    bill_aggregate,
                )
            elif settlement_type == SpotCreditSettlementType.BOOKING_LEVEL:
                accounts_to_settle = self._get_eligible_accounts_for_spot_credit(
                    bill_aggregate.billed_entities,
                    bill_aggregate,
                )
            if len(accounts_to_settle) > 0:
                settled_folios.append(
                    self.spot_credit_service.settle_by_spot_credit(
                        bill_aggregate,
                        billed_entity,
                        accounts_to_settle,
                        settlement_type,
                    )
                )
        if len(settled_folios) == 0:
            raise SpotCreditIssueError(
                description="Unable to find eligible folios to issue spot credit"
            )
        self.bill_repository.update(bill_aggregate)
        IntegrationEventApplicationService.create_bill_updated_event(
            bill_aggregate=bill_aggregate, user_action="spot_credit_issued"
        )
        return settled_folios

    def _get_spot_credit_enabled_account_categories(self, booking_aggregate):
        spot_credit_eligible_account_categories = (
            self.tenant_settings.get_setting_value(
                TenantSettingName.SPOT_CREDIT_ELIGIBLE_ACCOUNT_CATEGORY.value,
                booking_aggregate.hotel_id,
            )
        )
        if spot_credit_eligible_account_categories:
            return list(
                map(
                    lambda x: BilledEntityCategory(x),
                    spot_credit_eligible_account_categories,
                )
            )
        return [
            BilledEntityCategory.BOOKER,
            BilledEntityCategory.PRIMARY_GUEST,
            BilledEntityCategory.CONSUMING_GUESTS,
        ]

    @staticmethod
    def _fail_if_not_eligible(billed_entity, spot_credit_eligible_account_category):
        if not billed_entity.is_active:
            raise SpotCreditIssueError(
                description="Spot credit can only be issued to active billed entities"
            )
        if billed_entity.category not in spot_credit_eligible_account_category:
            raise SpotCreditIssueError(
                description=f"Spot credit cannot be issued to {billed_entity.category}"
            )

    @staticmethod
    def _get_eligible_accounts_for_spot_credit(
        billed_entities,
        bill_aggregate,
    ):
        un_invoiced_non_credit_accounts = {
            BilledEntityAccountVO(be.billed_entity_id, bea.account_number)
            for be in billed_entities
            for bea in be.accounts
            if not bea.is_invoiced() and bea.is_non_credit_account()
        }
        accounts = []
        for account in un_invoiced_non_credit_accounts:
            summary = bill_aggregate.get_account_summary(account)
            currency = summary.balance_to_clear_before_checkout.currency
            if summary.balance_to_clear_before_checkout != Money(currency=currency):
                accounts.append(account)
        return accounts
