from object_registry import register_instance
from prometheus.application.decorators import session_manager
from prometheus.domain.billing.repositories import BillRepository


@register_instance(dependencies=[BillRepository])
class VoidBillCommandHandler:
    def __init__(self, bill_repository: BillRepository):
        self.bill_repository = bill_repository

    @session_manager(commit=True)
    def handle(self, bill_id):
        bill_aggregate = self.bill_repository.load_for_update(bill_id)
        bill_aggregate.void()
        self.bill_repository.update(bill_aggregate)
        return bill_aggregate
