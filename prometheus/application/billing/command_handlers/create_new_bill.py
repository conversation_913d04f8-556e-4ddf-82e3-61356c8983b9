from object_registry import register_instance
from prometheus.application.billing.command_handlers.pos.create_new_bill import (
    PosCreateNewBillCommandHandler,
)
from prometheus.application.billing.dtos.new_bill_dto import NewBillDto
from prometheus.application.decorators import session_manager, set_hotel_context
from ths_common.constants.billing_constants import BillAppId


@register_instance(dependencies=[PosCreateNewBillCommandHandler])
class CreateNewBillCommandHandler:
    def __init__(
        self, pos_create_new_bill_command_handler: PosCreateNewBillCommandHandler
    ):
        self.pos_create_new_bill_command_handler = pos_create_new_bill_command_handler

    @session_manager(commit=True)
    @set_hotel_context()
    def handle(self, new_bill_dto: NewBillDto, hotel_aggregate=None):
        if new_bill_dto.app_id == BillAppId.POS_APP.value:
            return self.pos_create_new_bill_command_handler.handle(new_bill_dto)
        else:
            raise NotImplementedError()
