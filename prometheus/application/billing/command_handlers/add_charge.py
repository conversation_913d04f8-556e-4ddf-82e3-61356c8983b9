from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.decorators import session_manager
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.services import TaxService
from prometheus.domain.catalog.repositories import SellerRepository
from ths_common.constants.billing_constants import BillAppId
from ths_common.exceptions import ValidationException


@register_instance(dependencies=[BillRepository, SellerRepository, TaxService])
class AddChargeCommandHandler:
    def __init__(
        self,
        bill_repository: BillRepository,
        seller_repository: SellerRepository,
        tax_service: TaxService,
    ):
        self.bill_repository = bill_repository
        self.seller_repository = seller_repository
        self.tax_service = tax_service

    @session_manager(commit=True)
    def handle(self, bill_id, version, charge_dto):
        if version is None:
            raise ValidationException(ApplicationErrors.BILL_VERSION_MANDATORY)

        bill_aggregate = self.bill_repository.load_for_update(bill_id, version)

        if bill_aggregate.bill.app_id != BillAppId.POS_APP.value:
            raise ValidationException(
                ApplicationErrors.ADD_CHARGE_NOT_ALLOWED_FOR_CRS_VIA_API
            )

        seller_aggregate = self.seller_repository.load(bill_aggregate.vendor_id)
        crs_context.set_seller_context(seller_aggregate)
        tax_updated_charge_dtos = self.tax_service.update_taxes(
            charge_dtos=[charge_dto],
            buyer_gst_details=None,
            seller_has_lut=None,
            seller_id=seller_aggregate.seller.seller_id,
        )

        charges = bill_aggregate.add_charges(tax_updated_charge_dtos)
        self.bill_repository.update(bill_aggregate)

        # IntegrationEventApplicationService.create_bill_updated_event(bill_aggregate=bill_aggregate,
        # user_action="add_charge")
        return charges[0], bill_aggregate.bill.version
