from object_registry import register_instance
from prometheus.application.billing.helpers.signed_url_generator import (
    SignedUrlGenerator,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.domain.billing.repositories import InvoiceRepository
from ths_common.constants.billing_constants import InvoiceStatus


@register_instance(dependencies=[InvoiceRepository, SignedUrlGenerator])
class GetInvoicesForBillQueryHandler:
    def __init__(
        self,
        invoice_repository: InvoiceRepository,
        signed_url_generator: SignedUrlGenerator,
    ):
        self.invoice_repository = invoice_repository
        self.signed_url_generator = signed_url_generator

    @session_manager()
    @set_hotel_context()
    def handle(self, bill_id, only_confirmed=True, hotel_aggregate=None):
        """
        get the invoices for a given bill_id
        Args:
            bill_id:
            only_confirmed: return only confirmed invoices

        Returns:

        """
        invoice_aggregates = self.invoice_repository.load_for_bill_id(bill_id)

        if only_confirmed:
            filtered_invoice_aggregates = [
                invoice_aggregate
                for invoice_aggregate in invoice_aggregates
                if invoice_aggregate.invoice.status
                not in {InvoiceStatus.PREVIEW, InvoiceStatus.CANCELLED}
            ]
        else:
            filtered_invoice_aggregates = invoice_aggregates
        for invoice_aggregate in filtered_invoice_aggregates:
            signed_url, expiration = self.signed_url_generator.generate_signed_url(
                invoice_aggregate.invoice.invoice_url
            )
            invoice_aggregate.set_signed_url(signed_url, expiration)
        return filtered_invoice_aggregates
