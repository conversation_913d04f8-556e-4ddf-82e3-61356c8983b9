from object_registry import register_instance
from prometheus.application.billing.helpers.payment_receipt_service import (
    PaymentReceiptService,
)
from prometheus.application.billing.query_handlers.get_payments import (
    GetPaymentsQueryHandler,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.repositories.payment_receipt_repository import (
    PaymentReceiptRepository,
)


@register_instance(
    dependencies=[BillRepository, PaymentReceiptRepository, PaymentReceiptService]
)
class GetPaymentQueryHandler(GetPaymentsQueryHandler):
    @session_manager()
    @set_hotel_context()
    def handle(self, bill_id, payment_id, hotel_aggregate=None):
        bill_aggregate = self.bill_repository.load(bill_id)
        payment = bill_aggregate.get_payment(payment_id)
        payment_receipt = self.payment_receipt_service.get_payment_receipt(
            bill_id, payment_id
        )
        if payment_receipt:
            payment.payment_receipt_url = payment_receipt.signed_url
        return payment, bill_aggregate.bill.version
