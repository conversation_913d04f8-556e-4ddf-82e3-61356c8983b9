from object_registry import register_instance
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.domain.billing.repositories import BillRepository


@register_instance(dependencies=[BillRepository])
class GetChargesQueryHandler:
    def __init__(self, bill_repository: BillRepository):
        self.bill_repository = bill_repository

    @session_manager()
    @set_hotel_context()
    def handle(
        self,
        bill_id,
        charge_id_gt=None,
        limit=None,
        charge_ids=None,
        hotel_aggregate=None,
    ):
        charges = self.bill_repository.load_charges(
            bill_id, charge_id_gt, limit, charge_ids
        )
        max_charge_id = None
        if charge_id_gt == 0 and limit is not None:
            max_charge_id = self.bill_repository.get_max_charge_id(bill_id)
        bill_version = self.bill_repository.get_current_bill_version(bill_id)
        return charges, bill_version, max_charge_id
