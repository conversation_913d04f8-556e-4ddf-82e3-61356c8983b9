from object_registry import register_instance
from prometheus.application.billing.helpers.signed_url_generator import (
    SignedUrlGenerator,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.domain.billing.repositories import InvoiceRepository


@register_instance(dependencies=[InvoiceRepository, SignedUrlGenerator])
class GetInvoiceByIdsQueryHandler:
    def __init__(
        self,
        invoice_repository: InvoiceRepository,
        signed_url_generator: SignedUrlGenerator,
    ):
        self.invoice_repository = invoice_repository
        self.signed_url_generator = signed_url_generator

    @session_manager()
    def handle(self, invoice_ids, generate_signed_url=False):
        invoice_aggregates = self.invoice_repository.load_all(invoice_ids)
        if generate_signed_url:
            for invoice_aggregate in invoice_aggregates:
                signed_url, expiration = self.signed_url_generator.generate_signed_url(
                    invoice_aggregate.invoice.invoice_url
                )
                invoice_aggregate.set_signed_url(signed_url, expiration)
        return invoice_aggregates
