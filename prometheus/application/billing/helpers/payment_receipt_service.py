from object_registry import register_instance
from prometheus.application.billing.helpers.signed_url_generator import (
    SignedUrlGenerator,
)
from prometheus.domain.billing.repositories.payment_receipt_repository import (
    PaymentReceiptRepository,
)


@register_instance(dependencies=[PaymentReceiptRepository, SignedUrlGenerator])
class PaymentReceiptService:
    def __init__(
        self,
        payment_receipt_repository: PaymentReceiptRepository,
        signed_url_generator: SignedUrlGenerator,
    ):
        self.payment_receipt_repository = payment_receipt_repository
        self.signed_url_generator = signed_url_generator

    def refresh_payment_receipt_signed_url(self, payment_receipt_aggregate):
        signed_url, expiration = self.signed_url_generator.generate_signed_url(
            payment_receipt_aggregate.payment_receipt.payment_receipt_url
        )
        payment_receipt_aggregate.update_payment_signed_url(signed_url, expiration)

    def get_payment_receipt(self, bill_id, payment_id, hotel_aggregate=None):
        payment_receipt_aggregate = self.payment_receipt_repository.load(
            payment_id, bill_id
        )
        if payment_receipt_aggregate:
            self.refresh_payment_receipt_signed_url(payment_receipt_aggregate)
            return payment_receipt_aggregate.payment_receipt
        return None

    def get_payment_receipts(self, bill_id):
        payment_receipt_aggregates = self.payment_receipt_repository.load_for_bill_id(
            bill_id
        )
        payment_receipts = dict()
        for payment_receipt_aggregate in payment_receipt_aggregates:
            self.refresh_payment_receipt_signed_url(payment_receipt_aggregate)
            payment_receipts[
                payment_receipt_aggregate.payment_receipt.payment_id
            ] = payment_receipt_aggregate.payment_receipt
        return payment_receipts
