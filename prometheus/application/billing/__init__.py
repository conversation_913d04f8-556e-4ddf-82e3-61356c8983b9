from object_registry import locate_instance
from prometheus.application.billing.async_job_handlers.payment_receipt_template_generation import (
    GeneratePaymentReceiptTemplateJobHandler,
)
from prometheus.application.billing.async_job_handlers.refresh_invoices_for_billed_entity import (
    RefreshInvoicesOnBilledEntityChangeJobHandler,
)
from prometheus.application.billing.async_job_handlers.send_confirmed_booking_voucher_communication import (
    SendConfirmedBookingVoucherJobHandler,
)
from prometheus.application.billing.async_job_handlers.send_payment_receipt_communication import (
    SendPaymentReceiptJobHandler,
)
from prometheus.application.billing.async_job_handlers.send_refund_communication import (
    ScheduleRefundCommunicationJobHandler,
)
from prometheus.async_job.job_registry import JobRegistry
from ths_common.constants.scheduled_job_constants import JobName

job_registry = locate_instance(JobRegistry)

job_registry.register(
    JobName.PAYMENT_RECEIPT_UPLOAD_ASYNC_JOB_NAME.value,
    locate_instance(
        GeneratePaymentReceiptTemplateJobHandler
    ).generate_and_upload_payment_receipt_template,
)
job_registry.register(
    JobName.REFRESH_INVOICES.value,
    locate_instance(
        RefreshInvoicesOnBilledEntityChangeJobHandler
    ).refresh_invoices_for_billed_entity,
)
job_registry.register(
    JobName.SEND_BOOKING_CONFIRMATION_VOUCHER.value,
    locate_instance(
        SendConfirmedBookingVoucherJobHandler
    ).send_confirmed_booking_voucher,
)
job_registry.register(
    JobName.SEND_PAYMENT_RECEIPT.value,
    locate_instance(SendPaymentReceiptJobHandler).send_payment_receipt,
)
job_registry.register(
    JobName.SCHEDULE_PAYOUT_LINK_COMMUNICATION.value,
    locate_instance(
        ScheduleRefundCommunicationJobHandler
    ).send_payout_link_communication,
)
job_registry.register(
    JobName.SCHEDULE_DIRECT_REFUND_COMMUNICATION.value,
    locate_instance(
        ScheduleRefundCommunicationJobHandler
    ).send_direct_refund_communication,
)
job_registry.register(
    JobName.SCHEDULE_PAYMENT_FAILURE_COMMUNICATION.value,
    locate_instance(
        ScheduleRefundCommunicationJobHandler
    ).send_payment_failure_communication,
)
job_registry.register(
    JobName.SCHEDULE_PAYOUT_LINK_NOT_APPROVED_COMMUNICATION.value,
    locate_instance(
        ScheduleRefundCommunicationJobHandler
    ).send_payout_link_not_approved_communication,
)
job_registry.register(
    JobName.AUTO_REFUND_BLACKLISTED_USER_COMMUNICATION.value,
    locate_instance(
        ScheduleRefundCommunicationJobHandler
    ).send_blacklisted_user_communication,
)
job_registry.register(
    JobName.SCHEDULE_PARTIAL_REFUND_COMMUNICATION.value,
    locate_instance(
        ScheduleRefundCommunicationJobHandler
    ).send_partial_refund_communication,
)
