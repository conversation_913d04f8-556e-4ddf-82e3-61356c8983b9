from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.billing.repositories import CashierSessionRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.booking.repositories.web_checkin_repository import (
    WebCheckinRepository,
)
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.catalog.repositories.seller_repository import SellerRepository
from prometheus.domain.critical_task.dtos.critical_task_search_query import (
    CriticalTaskSearchQuery,
)
from prometheus.domain.critical_task.entities.critical_task import CriticalTask
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from ths_common.constants.critical_task_constants import CriticalTaskType
from ths_common.constants.tenant_settings_constants import TenantSettingName


@register_instance(
    dependencies=[
        BookingRepository,
        HotelRepository,
        HotelConfigRepository,
        WebCheckinRepository,
        CashierSessionRepository,
        TenantSettings,
        SellerRepository,
    ]
)
class CriticalTaskService(object):
    def __init__(
        self,
        booking_repository: BookingRepository,
        hotel_repository,
        hotel_config_repository,
        web_checkin_repository,
        cashier_session_repository,
        tenant_settings,
        seller_repository,
    ):
        self.booking_repository = booking_repository
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.web_checkin_repository = web_checkin_repository
        self.cashier_session_repository = cashier_session_repository
        self.tenant_settings = tenant_settings
        self.seller_repository = seller_repository

    def search_critical_checkins(self, hotel_id):
        return self.booking_repository.load_booking_ids_with_critical_checkins(hotel_id)

    def search_critical_checkouts(self, hotel_id):
        return self.booking_repository.load_booking_ids_with_critical_checkouts(
            hotel_id
        )

    def _get_pending_checkin_end_time(self, booking_ids):
        if not booking_ids:
            return None
        oldest_booking_pending_checkin = (
            self.booking_repository.load_oldest_booking_pending_checkin(booking_ids)
        )
        if (
            oldest_booking_pending_checkin is None
            or not oldest_booking_pending_checkin.room_stays
        ):
            return None
        oldest_room_stay = sorted(
            oldest_booking_pending_checkin.room_stays, key=lambda rs: rs.checkin_date
        )[0]
        oldest_checkin_date = oldest_room_stay.checkin_date
        hotel_context = crs_context.get_hotel_context()
        critical_task_time = hotel_context.get_next_switch_over_date_time_after_time(
            oldest_checkin_date
        )
        grace_period = hotel_context.checkin_grace_time
        return critical_task_time + grace_period

    def _search_open_cashier_sessions(self, hotel_id):
        seller_ids = self.seller_repository.load_seller_ids_for_hotel(hotel_id=hotel_id)
        seller_ids.append(hotel_id)
        return self.cashier_session_repository.load_critical_cashier_session_ids(
            vendor_ids=seller_ids
        )

    def search_critical_tasks(
        self, search_query: CriticalTaskSearchQuery, user_date=None
    ):
        hotel_aggregate = self.hotel_repository.load(search_query.hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(
            search_query.hotel_id
        )
        crs_context.set_hotel_context(hotel_aggregate, hotel_config_aggregate)

        critical_tasks = []
        if search_query.critical_checkins:
            entity_ids = self.search_critical_checkins(search_query.hotel_id)
            critical_tasks.append(
                CriticalTask(
                    CriticalTaskType.CRITICAL_CHECKIN,
                    "booking",
                    entity_ids,
                )
            )
        if search_query.critical_checkouts:
            entity_ids = self.search_critical_checkouts(search_query.hotel_id)
            critical_tasks.append(
                CriticalTask(
                    CriticalTaskType.CRITICAL_CHECKOUT,
                    "booking",
                    entity_ids,
                )
            )
        if search_query.pending_web_checkins:
            entity_ids = (
                self.web_checkin_repository.load_pending_web_checkin_booking_ids(
                    hotel_id=search_query.hotel_id
                )
            )
            critical_tasks.append(
                CriticalTask(
                    CriticalTaskType.PENDING_WEB_CHECKINS,
                    "booking",
                    entity_ids,
                    self._get_pending_checkin_end_time(booking_ids=entity_ids),
                )
            )
        if (
            self.tenant_settings.get_setting_value(
                TenantSettingName.CASHIERING_ENABLED.value,
                hotel_id=search_query.hotel_id,
            )
            and search_query.open_cashier_sessions
        ):
            entity_ids = self._search_open_cashier_sessions(
                hotel_id=search_query.hotel_id
            )
            critical_tasks.append(
                CriticalTask(
                    CriticalTaskType.CRITICAL_CASHIER_SESSIONS,
                    "cashier_sessions",
                    entity_ids,
                )
            )

        return critical_tasks
