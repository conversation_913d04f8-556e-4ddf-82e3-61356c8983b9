from object_registry import register_instance
from prometheus.application.decorators import set_hotel_context
from prometheus.domain.booking.repositories import BookingRepository


@register_instance(
    dependencies=[
        BookingRepository,
    ]
)
class GetExpenseByIdQueryHandler:
    def __init__(
        self,
        booking_repo,
    ):
        self.booking_repository = booking_repo

    @set_hotel_context()
    def handle(self, booking_id, expense_id, hotel_aggregate=None):
        expense = self.booking_repository.load_expense(booking_id, expense_id)
        current_booking_version = self.booking_repository.get_current_booking_version(
            booking_id
        )
        return expense, current_booking_version
