from object_registry import register_instance
from prometheus.application.booking.query_handlers.get_booking_v2 import (
    GetBookingV2QueryHandler,
)


@register_instance(dependencies=[GetBookingV2QueryHandler])
class GetRoomStayV2QueryHandler:
    def __init__(self, get_booking_v2_query_handler: GetBookingV2QueryHandler):
        self.get_booking_v2_query_handler = get_booking_v2_query_handler

    def handle(self, booking_id, room_stay_id, user_data):
        booking_context = self.get_booking_v2_query_handler.handle(
            booking_id, user_data
        )
        booking_aggregate = booking_context.booking_aggregate
        room_stay = booking_aggregate.get_room_stay(room_stay_id)
        room_stay.refresh_allowed_actions(booking_aggregate._is_soft_booking())
        return room_stay, booking_aggregate.current_version()
