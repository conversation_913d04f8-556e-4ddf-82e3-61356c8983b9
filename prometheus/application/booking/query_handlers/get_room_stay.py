from object_registry import register_instance
from prometheus.application import crs_context_middleware
from prometheus.application.decorators import set_hotel_context
from prometheus.domain.booking.repositories import BookingRepository


@register_instance(dependencies=[BookingRepository])
class GetRoomStayQueryHandler:
    def __init__(self, booking_repository: BookingRepository):
        self.booking_repository = booking_repository

    @set_hotel_context()
    def handle(self, booking_id, room_stay_id, user_data, hotel_aggregate=None):
        """

        :param booking_id:
        :param room_stay_id:
        :param user_data:
        :return:
        """
        booking_aggregate = self.booking_repository.load(
            booking_id, user_data=user_data
        )
        if not hotel_aggregate:
            crs_context_middleware.set_hotel_context(booking_aggregate.booking.hotel_id)

        room_stay = booking_aggregate.get_room_stay(room_stay_id)
        room_stay.refresh_allowed_actions(booking_aggregate._is_soft_booking())
        return room_stay, booking_aggregate.current_version()
