import urllib

from object_registry import register_instance
from prometheus.application.billing.helpers.signed_url_generator import (
    SignedUrlGenerator,
)
from prometheus.application.decorators import set_hotel_context
from prometheus.domain.booking.repositories.attachment_repository import (
    AttachmentRepository,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts


@register_instance(
    dependencies=[
        SignedUrlGenerator,
        BookingRepository,
        AttachmentRepository,
    ]
)
class GetAttachmentQueryHandler:
    def __init__(
        self,
        signed_url_generator: SignedUrlGenerator,
        booking_repository: BookingRepository,
        attachment_repository: AttachmentRepository,
    ):
        self.signed_url_generator = signed_url_generator
        self.booking_repository = booking_repository
        self.attachment_repository = attachment_repository

    @set_hotel_context()
    def handle(self, booking_id, user_data, hotel_aggregate=None):
        self.booking_repository.validate_booking_hotel_id(booking_id=booking_id)

        attachment_aggregates = self.attachment_repository.load_all(
            booking_id, user_data
        )
        for attachment_aggregate in attachment_aggregates:
            if RuleEngine.action_allowed(
                action="access_attachment",
                facts=Facts(
                    user_type=attachment_aggregate.user_data.user_type,
                    attachment_aggregate=attachment_aggregate,
                ),
                fail_on_error=False,
            ):
                signed_url, _ = self.signed_url_generator.generate_signed_url(
                    urllib.parse.unquote(attachment_aggregate.attachment.original_url)
                )
                attachment_aggregate.attachment.signed_url = signed_url
        return attachment_aggregates
