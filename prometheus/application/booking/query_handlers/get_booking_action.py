from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.booking.command_handlers.state_transitions.cancel_booking_action import (
    CancelBookingActionCommandHandler,
)
from prometheus.application.booking.command_handlers.state_transitions.no_show_booking_action import (
    NoShowBookingActionCommandHandler,
)
from prometheus.application.booking.command_handlers.state_transitions.reverse_checkin_guest import (
    ReverseCheckinCommandHandler,
)
from prometheus.application.booking.command_handlers.state_transitions.reverse_checkout_guest import (
    ReverseCheckoutCommandHandler,
)
from prometheus.application.decorators import set_hotel_context
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.repositories import InvoiceRepository
from prometheus.domain.booking.repositories import (
    BookingActionRepository,
    BookingRepository,
)
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from ths_common.constants.booking_constants import BookingActions
from ths_common.exceptions import ValidationException


@register_instance(
    dependencies=[
        BookingActionRepository,
        BookingRepository,
        HotelRepository,
        HotelConfigRepository,
        InvoiceRepository,
        ReverseCheckinCommandHandler,
        ReverseCheckoutCommandHandler,
        CancelBookingActionCommandHandler,
        NoShowBookingActionCommandHandler,
    ]
)
class GetBookingActionQueryHandler:
    def __init__(
        self,
        booking_action_repository: BookingActionRepository,
        booking_repository: BookingRepository,
        hotel_repository: HotelRepository,
        hotel_config_repository: HotelConfigRepository,
        invoice_repository: InvoiceRepository,
        reverse_checkin_command_handler: ReverseCheckinCommandHandler,
        reverse_checkout_command_handler: ReverseCheckoutCommandHandler,
        cancel_booking_command_handler: CancelBookingActionCommandHandler,
        no_show_booking_command_handler: NoShowBookingActionCommandHandler,
    ):
        self.booking_action_repository = booking_action_repository
        self.booking_repository = booking_repository
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.invoice_repository = invoice_repository
        self.reverse_checkin_command_handler = reverse_checkin_command_handler
        self.reverse_checkout_command_handler = reverse_checkout_command_handler
        self.cancel_booking_command_handler = cancel_booking_command_handler
        self.no_show_booking_command_handler = no_show_booking_command_handler

    @set_hotel_context()
    def handle(self, booking_id, action_id, user_data, hotel_aggregate=None):
        booking_action_aggregate = self.booking_action_repository.load(action_id)
        if booking_action_aggregate.booking_action.booking_id != booking_id:
            raise ValidationException(
                ApplicationErrors.ACTION_DOES_NOT_BELONG_TO_BOOKING
            )

        if self.booking_action_repository.is_latest_reversible_action(
            booking_id, action_id
        ):
            booking_aggregate = self.booking_repository.load(
                booking_id, skip_customers=True
            )
            self.update_reversal_allowed(
                booking_aggregate, booking_action_aggregate, user_data
            )
            current_booking_version = booking_aggregate.current_version()
        else:
            current_booking_version = (
                self.booking_repository.get_current_booking_version(booking_id)
            )

        return booking_action_aggregate, current_booking_version

    def update_reversal_allowed(
        self, booking_aggregate, latest_reversible_action_aggregate, user_data
    ):
        # NOTE: This method doesn't use customer and expense from booking_aggregate. So we can skip loading them,
        # as volume this API calls on this method is large
        user_is_allowed_to_reverse_action = self._is_user_allowed_to_reverse_action(
            booking_aggregate, latest_reversible_action_aggregate, user_data
        )
        if user_is_allowed_to_reverse_action:
            latest_reversible_action_aggregate.booking_action.mark_reversal_allowed()

    def _is_user_allowed_to_reverse_action(
        self, booking_aggregate, action_aggregate, user_data
    ):
        if not action_aggregate.booking_action.is_reversible:
            return False

        hotel_aggregate = self.hotel_repository.load(booking_aggregate.hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(
            booking_aggregate.hotel_id
        )
        hotel_context = crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
        )

        action_datetime = hotel_context.hotel_checkin_date(
            action_aggregate.booking_action.created_at
        )
        if hotel_context.current_date() > action_datetime:
            return False

        if action_aggregate.booking_action.action_type == BookingActions.CHECKIN:
            # NOTE: Expense Not used
            # Customer not used
            return self.reverse_checkin_command_handler.validate_undo_checkin_policy(
                booking_aggregate, action_aggregate, user_data, fail_on_error=False
            )
        if action_aggregate.booking_action.action_type == BookingActions.CHECKOUT:
            # NOTE: Expense Not used
            # Customer not used
            return self.reverse_checkout_command_handler.validate_undo_checkout_policy(
                booking_aggregate, action_aggregate, user_data, fail_on_error=False
            )
        if action_aggregate.booking_action.action_type == BookingActions.CANCEL:
            # NOTE: Expense Not used
            # Customer not used
            return self.cancel_booking_command_handler.validate_reverse_cancellation_policy(
                booking_aggregate, user_data, fail_on_error=False
            )
        if action_aggregate.booking_action.action_type == BookingActions.NOSHOW:
            # NOTE: Expense Not used
            # Customer not used
            invoice_aggregates = self.invoice_repository.load_for_bill_id(
                booking_aggregate.bill_id
            )
            return self.no_show_booking_command_handler.validate_undo_no_show_policy(
                booking_aggregate,
                action_aggregate,
                invoice_aggregates,
                user_data,
                fail_on_error=False,
            )
        return False
