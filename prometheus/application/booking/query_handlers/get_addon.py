from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.decorators import set_hotel_context
from prometheus.domain.booking.repositories import AddonRepository, BookingRepository
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository


@register_instance(
    dependencies=[
        BookingRepository,
        HotelRepository,
        HotelConfigRepository,
        AddonRepository,
    ]
)
class GetAddonQueryHandler:
    def __init__(
        self, booking_repo, hotel_repository, hotel_config_repository, addon_repository
    ):
        self.booking_repository = booking_repo
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.addon_repository = addon_repository

    @set_hotel_context()
    def handle(self, booking_id, linked_addons_required, hotel_aggregate=None):
        if not hotel_aggregate:
            booking_aggregate = self.booking_repository.load(booking_id)
            crs_context.set_hotel_context(
                self.hotel_repository.load(booking_aggregate.booking.hotel_id),
                hotel_config_aggregate=self.hotel_config_repository.load(
                    booking_aggregate.booking.hotel_id
                ),
            )

        addons = self.addon_repository.get_addons_for_booking(
            booking_id, include_linked=linked_addons_required
        )
        return [addon for addon in addons if not addon.deleted]
