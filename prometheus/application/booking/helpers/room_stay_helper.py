from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.booking.dtos.booking_sub_resource_change_event import (
    BookingSubResourcesChangeEvent,
)
from prometheus.application.booking.helpers.billed_entity_service import (
    BilledEntityService,
)
from prometheus.application.booking.helpers.e_reg_card_template_service import (
    ERegCardTemplateService,
)
from prometheus.application.booking.helpers.inclusion_charge_service import (
    InclusionChargeService,
)
from prometheus.application.booking.helpers.room_stay_dto_creator import (
    RoomStayDtoCreator,
)
from prometheus.application.booking.helpers.web_checkin_service import WebCheckinService
from prometheus.application.helpers.billed_entity_helper import (
    attach_default_billed_entity_account_to_room_stay_charges,
)
from prometheus.application.helpers.expense_item_helper import ExpenseItemHelper
from prometheus.application.helpers.gst_details_helper import (
    get_gst_details_based_on_billed_entity_category,
)
from prometheus.common.helpers import occupancy_change_handler
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.repositories import (
    BookingRepository,
    ExpenseItemRepository,
)
from prometheus.domain.catalog.repositories import (
    HotelRepository,
    SkuCategoryRepository,
)
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.repositories import RoomTypeInventoryRepository
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts
from ths_common.constants.booking_constants import BookingSubResources


@register_instance(
    dependencies=[
        HotelRepository,
        HotelConfigRepository,
        BookingRepository,
        BillRepository,
        SkuCategoryRepository,
        RoomTypeInventoryRepository,
        WebCheckinService,
        ERegCardTemplateService,
        RoomStayDtoCreator,
        InclusionChargeService,
        BilledEntityService,
        ExpenseItemHelper,
        ExpenseItemRepository,
    ]
)
class RoomStayHelperService:
    def __init__(
        self,
        hotel_repository: HotelRepository,
        hotel_config_repository: HotelConfigRepository,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        sku_category_repository: SkuCategoryRepository,
        room_type_inventory_repository: RoomTypeInventoryRepository,
        web_checkin_application_service: WebCheckinService,
        eregcard_template_service: ERegCardTemplateService,
        room_stay_dto_creator: RoomStayDtoCreator,
        inclusion_charge_service: InclusionChargeService,
        billed_entity_service: BilledEntityService,
        expense_item_helper: ExpenseItemHelper,
        expense_item_repository: ExpenseItemRepository,
    ):
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.sku_category_repository = sku_category_repository
        self.room_type_inventory_repository = room_type_inventory_repository
        self.web_checkin_application_service = web_checkin_application_service
        self.eregcard_template_service = eregcard_template_service
        self.room_stay_dto_creator = room_stay_dto_creator
        self.inclusion_charge_service = inclusion_charge_service
        self.billed_entity_service = billed_entity_service
        self.expense_item_helper = expense_item_helper
        self.expense_item_repository = expense_item_repository

    def add_room_stay(
        self,
        booking_aggregate: BookingAggregate,
        bill_aggregate,
        room_stay,
        user_data,
        grouped_sku_categories,
    ):
        return self._add_or_update_room_stay(
            booking_aggregate,
            bill_aggregate,
            room_stay,
            user_data,
            grouped_sku_categories,
        )

    def update_room_stay(
        self,
        booking_aggregate: BookingAggregate,
        bill_aggregate,
        room_stay,
        room_stay_id,
        user_data,
        grouped_sku_categories,
    ):
        """
        This function will
        - Replace complete room stay and guest stay details
        - Add Charges
        - Add booking customers
        - Add Rate Plan
        - Expenses
        - Billed Entities
        Note: If this function is used to replace the rooms stay first clear existing
         - customers
         - charges
         - expenses
         - billed entities
        """

        return self._add_or_update_room_stay(
            booking_aggregate,
            bill_aggregate,
            room_stay,
            user_data,
            grouped_sku_categories,
            room_stay_id=room_stay_id,
        )

    def _add_or_update_room_stay(
        self,
        booking_aggregate: BookingAggregate,
        bill_aggregate,
        room_stay_data,
        user_data,
        grouped_sku_categories,
        room_stay_id=None,
    ):
        # if room_stay_id is None, this will add 'room_stay' data as new room stay
        # else this will replace an existing room stay having specified room_stay_id with given 'room_stay_data'

        hotel_context = crs_context.get_hotel_context()
        RuleEngine.action_allowed(
            action="add_room_stay",
            facts=Facts(
                user_type=user_data.user_type,
                action_payload=room_stay_data,
                booking_aggregate=booking_aggregate,
                hotel_context=hotel_context,
            ),
            fail_on_error=True,
        )

        expense_items = (
            self.expense_item_helper.add_missing_skus_if_sku_not_found(
                expense_items=self.expense_item_repository.load_all(),
                rate_plan_inclusions=room_stay_data.get('rate_plan_inclusions', []),
            )
            if room_stay_data.get('rate_plan_inclusions')
            else []
        )

        (
            room_stay_dtos,
            rate_plan_dtos,
        ) = self.room_stay_dto_creator.create_room_stay_and_rate_plan_dtos(
            [room_stay_data],
            grouped_sku_categories,
            expense_items,
        )
        room_stay_dto = room_stay_dtos[0]
        billed_entity_category_for_room_stay_charges = (
            booking_aggregate.get_default_billed_entity_category()
        )
        gst_details = get_gst_details_based_on_billed_entity_category(
            booking_aggregate, billed_entity_category_for_room_stay_charges
        )
        self.room_stay_dto_creator.calculate_tax_on_room_stay_charges(
            room_stay_dtos,
            booking_aggregate.booking.seller_model,
            gst_details,
        )
        charges = bill_aggregate.add_charges(room_stay_dto.charges)
        room_stay_dto.add_charges(charges)
        room_rate_plans = booking_aggregate.add_rate_plans(
            room_stay_dto, rate_plan_dtos
        )
        room_stay_dto.room_rate_plans = room_rate_plans

        if room_stay_id:
            room_stay_dto.room_stay_id = room_stay_id
            room_stay = booking_aggregate.update_existing_room_stay(
                room_stay_dto,
                override_checkin_time=hotel_context.checkin_time,
                override_checkout_time=hotel_context.checkout_time,
            )
            booking_aggregate.refresh_booking_checkin_checkout_dates()
        else:
            room_stay = booking_aggregate.add_room_stay(
                room_stay_dto,
                override_checkin_time=hotel_context.checkin_time,
                override_checkout_time=hotel_context.checkout_time,
            )

        inclusion_charges = []
        if any(
            dto.stay_date_wise_inclusion_expense_and_charge_dtos
            for dto in room_stay_dtos
        ):
            inclusion_charges = (
                self.inclusion_charge_service.add_room_stay_wise_inclusion_charges(
                    bill_aggregate, booking_aggregate, [room_stay], room_stay_dtos
                )
            )

        self.billed_entity_service.create_or_update_billed_entity_data_for_allocated_guest_stays(
            booking_aggregate, bill_aggregate, room_stay.guest_stays
        )
        attach_default_billed_entity_account_to_room_stay_charges(
            room_stay,
            room_stay_dto.charges + inclusion_charges,
            booking_aggregate,
            bill_aggregate,
        )
        bill_aggregate.bill.parent_info.update(
            dict(
                checkin_date=booking_aggregate.booking.checkin_date,
                checkout_date=booking_aggregate.booking.checkout_date,
            )
        )
        occupancy_change_handler.update_occupancy_details_in_booked_charges(
            booking_aggregate, bill_aggregate, room_stay=room_stay
        )
        room_stay.refresh_allowed_actions(booking_aggregate._is_soft_booking())
        return room_stay

    @staticmethod
    def generate_room_stay_change_event(booking_aggregate, room_stays_ids, user_action):
        return BookingSubResourcesChangeEvent(
            booking_id=booking_aggregate.booking_id,
            hotel_id=booking_aggregate.booking.hotel_id,
            affected_resource=BookingSubResources.ROOM_STAY,
            affected_resource_ids=room_stays_ids,
            user_action=user_action,
        )
