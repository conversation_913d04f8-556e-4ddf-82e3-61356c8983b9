from typing import List

from object_registry import register_instance
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.billing.entities.charge import Charge
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.billing.services.charge_edit_service import ChargeEditService
from prometheus.domain.booking.repositories.addon_repository import AddonRepository
from prometheus.domain.booking.repositories.expense_item_repository import (
    ExpenseItemRepository,
)
from ths_common.constants.billing_constants import ChargeStatus


@register_instance(
    dependencies=[
        ChargeEditService,
        InvoiceRepository,
        AddonRepository,
        ExpenseItemRepository,
        TenantSettings,
    ]
)
class RoomStayPriceChangeHandler(object):
    def __init__(
        self,
        charge_edit_service: ChargeEditService,
        invoice_repository: InvoiceRepository,
        addon_repository: AddonRepository,
        expense_item_repository: ExpenseItemRepository,
        tenant_settings: TenantSettings,
    ):
        self.charge_edit_service = charge_edit_service
        self.invoice_repository = invoice_repository
        self.addon_repository = addon_repository
        self.expense_item_repository = expense_item_repository
        self.tenant_settings = tenant_settings

    def on_room_stay_charge_edit(
        self,
        booking_aggregate,
        bill_aggregate,
        room_stay,
        edited_charges: List[Charge],
        delete_rate_plan_addons=False,
    ):
        if not delete_rate_plan_addons:
            return

        all_addons = self.addon_repository.get_addons_for_room_stay(
            booking_aggregate.booking.booking_id, room_stay.room_stay_id
        )
        rate_plan_addons = [
            addon for addon in all_addons if addon.addon.is_rate_plan_addon
        ]
        # This method will delete addons only.
        self._delete_rate_plan_addons(rate_plan_addons)
        # Rate plan inc charges need to be cancelled via room stay expense
        self._cancel_all_rate_plan_inclusion_charges(
            room_stay, booking_aggregate, bill_aggregate
        )

    def _delete_rate_plan_addons(self, rate_plan_addons):
        for addon in rate_plan_addons:
            if not addon.addon.is_rate_plan_addon:
                continue
            addon.mark_deleted()
        self.addon_repository.update_all(rate_plan_addons)

    @staticmethod
    def _cancel_all_rate_plan_inclusion_charges(
        room_stay, booking_aggregate, bill_aggregate
    ):
        expenses = booking_aggregate.get_expenses_of_room_stay(room_stay.room_stay_id)
        charge_ids_to_cancel = [e.charge_id for e in expenses if e.via_rate_plan]
        cancelled_charges = bill_aggregate.cancel_charges(charge_ids_to_cancel)
        cancelled_charge_ids = [
            c.charge_id for c in cancelled_charges if c.status == ChargeStatus.CANCELLED
        ]
        booking_aggregate.cancel_expense_for_charges(cancelled_charge_ids)
