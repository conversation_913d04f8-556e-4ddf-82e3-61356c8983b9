from object_registry import register_instance
from prometheus.domain.booking.repositories.attachment_repository import (
    AttachmentRepository,
)


@register_instance(dependencies=[AttachmentRepository])
class WebCheckInVerifier:
    def __init__(self, attachment_repository: AttachmentRepository):
        self.attachment_repository = attachment_repository

    def is_web_checkin_complete(self, booking_aggregate, guest_ids):
        if (
            booking_aggregate.check_if_all_rooms_assigned()
            and self.check_if_all_attachments_are_verified(
                booking_aggregate=booking_aggregate, guest_ids=guest_ids
            )
        ):
            return True
        return False

    def check_if_all_attachments_are_verified(self, booking_aggregate, guest_ids):
        attachment_aggregates = self.attachment_repository.load_all(
            booking_id=booking_aggregate.booking.booking_id
        )
        attachment_id_aggregate_dict = {
            attachment_aggregate.attachment.attachment_id: attachment_aggregate
            for attachment_aggregate in attachment_aggregates
        }
        for guest_id in guest_ids:
            if not booking_aggregate.is_guest_stay_active(guest_id):
                continue
            attachment_id = booking_aggregate.get_attachment_id(guest_id)
            if not (
                attachment_id in attachment_id_aggregate_dict
                and attachment_id_aggregate_dict[attachment_id].is_attachment_verified()
            ):
                return False
        return True
