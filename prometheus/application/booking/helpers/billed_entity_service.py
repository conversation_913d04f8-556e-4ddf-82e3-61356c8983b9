import logging

from object_registry import register_instance
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.domain.billing.dto.billed_entity_data import BilledEntityData
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.entities import Customer
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    BilledEntityStatus,
    ChargeBillToTypes,
)
from ths_common.value_objects import Name

logger = logging.getLogger(__name__)


@register_instance(dependencies=[JobSchedulerService])
class BilledEntityService(object):
    def __init__(self, job_scheduler_service: JobSchedulerService):
        self.job_scheduler_service = job_scheduler_service

    def _add_billed_entity_for_ta_and_company_of_booking_owner(
        self, booking_aggregate, bill_aggregate
    ):
        if (
            not booking_aggregate.is_ta_bulk_booking()
            and booking_aggregate.get_travel_agent_details()
        ):
            self._add_billed_entity_from_booking_travel_agent_details(
                booking_aggregate, bill_aggregate
            )
        if booking_aggregate.get_company_details():
            self._add_billed_entity_from_booking_company_details(
                booking_aggregate, bill_aggregate
            )

    @staticmethod
    def is_booking_owner_eligible_for_billing(booking_aggregate):
        if not booking_aggregate.is_b2b_ta_booking():
            return True
        if booking_aggregate.is_b2b_ta_bulk_booking():
            return True
        if (
            booking_aggregate.get_default_billed_entity_category()
            == BilledEntityCategory.BOOKER
        ):
            return True
        return False

    def _add_billed_entity_for_booking_owner(
        self, booking_owner, booking_aggregate, bill_aggregate
    ):
        # booker name would come default hyphen
        # skipping creating billed entity
        if booking_owner.name and booking_owner.name.full_name == "-":
            return
        if self.is_booking_owner_eligible_for_billing(booking_aggregate):
            is_guest = booking_aggregate.is_guest(booking_owner.customer_id)
            booker_billed_entity = bill_aggregate.add_billed_entity(
                BilledEntityData(
                    name=booking_owner.name,
                    category=BilledEntityCategory.BOOKER,
                    secondary_category=(
                        BilledEntityCategory.PRIMARY_GUEST
                        if booking_owner.is_primary
                        else BilledEntityCategory.CONSUMING_GUESTS
                    )
                    if is_guest
                    else None,
                )
            )
            booking_owner.update_billed_entity_id(booker_billed_entity.billed_entity_id)

    @staticmethod
    def add_billed_entity_for_guest(
        customer: Customer, bill_aggregate, status=BilledEntityStatus.ACTIVE
    ):
        category = (
            BilledEntityCategory.PRIMARY_GUEST
            if customer.is_primary
            else BilledEntityCategory.CONSUMING_GUESTS
        )
        guest_billed_entity = bill_aggregate.add_billed_entity(
            BilledEntityData(name=customer.name, category=category, status=status)
        )
        customer.update_billed_entity_id(guest_billed_entity.billed_entity_id)

    def add_billed_entities_for_booking(
        self, booking_aggregate, bill_aggregate, override=False
    ):
        if override:
            bill_aggregate.safe_inactivate_billed_entities(
                [be.billed_entity_id for be in bill_aggregate.billed_entities],
                BilledEntityStatus.INACTIVE,
            )
            bill_aggregate.delete_billed_entities()
        booking_owner = booking_aggregate.get_booking_owner()
        self._add_billed_entity_for_ta_and_company_of_booking_owner(
            booking_aggregate, bill_aggregate
        )
        self._add_billed_entity_for_booking_owner(
            booking_owner, booking_aggregate, bill_aggregate
        )
        for room_stay in booking_aggregate.get_active_room_stays():
            for guest_stay in room_stay.active_guest_stays():
                if not guest_stay.guest_id or booking_aggregate.is_booking_owner(
                    guest_stay.guest_id
                ):
                    continue
                customer = booking_aggregate.get_customer(guest_stay.guest_id)
                self.add_billed_entity_for_guest(customer, bill_aggregate)
                if not customer.has_company_detail():
                    continue
                self._add_billed_entity_for_guest_company(customer, bill_aggregate)

    @staticmethod
    def deactivate_and_delete_billed_entity(billed_entity_ids, bill_aggregate):
        bill_aggregate.safe_inactivate_billed_entities(
            billed_entity_ids,
            BilledEntityStatus.INACTIVE,
        )
        bill_aggregate.delete_billed_entities(
            [bill_aggregate.get_billed_entity(be_id) for be_id in billed_entity_ids]
        )
        bill_aggregate.delete_folio_of_billed_entities(billed_entity_ids)

    def add_billed_entities_for_customer_if_eligible(
        self, booking_aggregate, bill_aggregate, customer
    ):
        if booking_aggregate.is_booking_owner(customer.customer_id):
            self._add_billed_entity_for_booking_owner(
                customer, booking_aggregate, bill_aggregate
            )
        else:
            self.add_billed_entity_for_guest(customer, bill_aggregate)

    def create_or_update_billed_entity_data_for_allocated_guest_stays(
        self, booking_aggregate, bill_aggregate, guest_stays
    ):
        for guest_stay in guest_stays:
            if not guest_stay.guest_id:
                continue
            customer = booking_aggregate.get_customer(guest_stay.guest_id)
            if not customer.billed_entity_id:
                self.add_billed_entities_for_customer_if_eligible(
                    booking_aggregate, bill_aggregate, customer
                )
            elif (
                not customer.company_billed_entity_id and customer.has_company_detail()
            ):
                self._add_billed_entities_for_company(
                    booking_aggregate, bill_aggregate, customer
                )
            self.update_billed_entity_for_customer(
                booking_aggregate, bill_aggregate, customer
            )

    def _update_billed_entity_name_for_guest(self, bill_aggregate, customer):
        billed_entity = bill_aggregate.get_billed_entity(customer.billed_entity_id)
        if not billed_entity.name.has_case_insensitive_match(customer.name):
            billed_entity.update_name(customer.name)
            self.job_scheduler_service.schedule_refresh_invoice(
                bill_aggregate.bill_id, customer.billed_entity_id
            )

    @staticmethod
    def _update_billed_entity_category_for_guest(
        bill_aggregate, customer, billed_entity_category=None
    ):
        billed_entity = bill_aggregate.get_billed_entity(customer.billed_entity_id)
        if customer.is_primary and billed_entity.category not in (
            BilledEntityCategory.PRIMARY_GUEST,
            BilledEntityCategory.BOOKER,
        ):
            billed_entity.update_category(BilledEntityCategory.PRIMARY_GUEST)
        if (
            customer.is_primary
            and billed_entity.category == BilledEntityCategory.BOOKER
        ):
            bill_aggregate.update_billed_entity_secondary_category(
                customer.billed_entity_id, BilledEntityCategory.PRIMARY_GUEST
            )
        if billed_entity_category and not customer.is_primary:
            if billed_entity.secondary_category == BilledEntityCategory.PRIMARY_GUEST:
                bill_aggregate.update_billed_entity_secondary_category(
                    customer.billed_entity_id, None
                )
            else:
                billed_entity.update_category(billed_entity_category)

    def update_billed_entity_for_customer(
        self, booking_aggregate, bill_aggregate, customer
    ):
        if customer.customer_id and customer.billed_entity_id:
            self._update_billed_entity_name_for_guest(bill_aggregate, customer)
            if not booking_aggregate.is_booking_owner(customer.customer_id):
                self._update_billed_entity_category_for_guest(bill_aggregate, customer)
        elif customer.customer_id and not customer.billed_entity_id:
            self.add_billed_entities_for_customer_if_eligible(
                booking_aggregate, bill_aggregate, customer
            )

        if customer.company_billed_entity_id and customer.has_company_detail():
            self._update_billed_entity_name_for_company(bill_aggregate, customer)
        elif customer.has_company_detail():
            self._add_billed_entities_for_company(
                booking_aggregate, bill_aggregate, customer
            )
        return True

    @staticmethod
    def _update_billed_entity_name_for_company(bill_aggregate, customer):
        company_billed_entity = bill_aggregate.get_billed_entity(
            customer.company_billed_entity_id
        )
        if company_billed_entity.name.first_name != customer.company_legal_name():
            company_billed_entity.update_name(Name(customer.company_legal_name()))

    def _add_billed_entities_for_guest(
        self, booking_aggregate, bill_aggregate, customer
    ):
        if booking_aggregate.is_booking_owner(customer.customer_id):
            self._add_billed_entity_for_booking_owner(
                customer, booking_aggregate, bill_aggregate
            )
        else:
            self.add_billed_entity_for_guest(customer, bill_aggregate)

    def _add_billed_entities_for_company(
        self, booking_aggregate, bill_aggregate, customer
    ):
        if booking_aggregate.is_booking_owner(customer.customer_id):
            self._add_billed_entity_for_ta_and_company_of_booking_owner(
                booking_aggregate, bill_aggregate
            )
        else:
            self._add_billed_entity_for_guest_company(customer, bill_aggregate)

    @staticmethod
    def derive_bill_to_type(bill_aggregate, billed_entity_account):
        billed_entity = bill_aggregate.get_billed_entity(
            billed_entity_account.billed_entity_id
        )
        if billed_entity.category in {
            BilledEntityCategory.BOOKER_COMPANY,
            BilledEntityCategory.TRAVEL_AGENT,
        }:
            return ChargeBillToTypes.COMPANY
        else:
            return ChargeBillToTypes.GUEST

    @staticmethod
    def _add_billed_entity_for_guest_company(customer: Customer, bill_aggregate):
        guest_company_billed_entity = bill_aggregate.add_billed_entity(
            BilledEntityData(
                name=Name(customer.company_legal_name()),
                category=BilledEntityCategory.GUEST_COMPANY,
            )
        )
        customer.update_company_billed_entity_id(
            guest_company_billed_entity.billed_entity_id
        )

    @staticmethod
    def _add_billed_entity_from_booking_company_details(
        booking_aggregate, bill_aggregate
    ):
        company_billed_entity = bill_aggregate.add_billed_entity(
            BilledEntityData(
                name=Name(booking_aggregate.get_company_legal_name()),
                category=BilledEntityCategory.BOOKER_COMPANY,
            )
        )
        booking_aggregate.update_billed_entity_id_in_company_detail(
            company_billed_entity.billed_entity_id
        )
        # setting up booking owner company billed id based on company details
        booking_owner = booking_aggregate.get_booking_owner()
        if not booking_owner.company_billed_entity_id:
            booking_owner.update_company_billed_entity_id(
                company_billed_entity.billed_entity_id
            )
        return company_billed_entity

    @staticmethod
    def _add_billed_entity_from_booking_travel_agent_details(
        booking_aggregate: BookingAggregate, bill_aggregate
    ):
        travel_agent_billed_entity = bill_aggregate.add_billed_entity(
            BilledEntityData(
                name=Name(booking_aggregate.get_travel_agent_legal_name()),
                category=BilledEntityCategory.TRAVEL_AGENT,
            )
        )
        booking_aggregate.update_billed_entity_id_in_travel_agent_detail(
            travel_agent_billed_entity.billed_entity_id
        )
        # settting up booking owner company billed id based on travel details
        booking_owner = booking_aggregate.get_booking_owner()
        if not booking_owner.company_billed_entity_id:
            booking_owner.update_company_billed_entity_id(
                travel_agent_billed_entity.billed_entity_id
            )
        return travel_agent_billed_entity

    def create_or_update_billed_entity_from_booking_company_details(
        self, booking_aggregate, bill_aggregate
    ):
        company_details = booking_aggregate.get_company_details()
        if not company_details.billed_entity_id:
            return self._add_billed_entity_from_booking_company_details(
                booking_aggregate, bill_aggregate
            )
        company_billed_entity = bill_aggregate.get_billed_entity(
            company_details.billed_entity_id
        )
        if (
            company_billed_entity.name.full_name
            != booking_aggregate.get_company_legal_name()
        ):
            company_billed_entity.update_name(
                Name(booking_aggregate.get_company_legal_name())
            )
        return company_billed_entity

    def create_or_update_billed_entity_from_booking_travel_agent_details(
        self, booking_aggregate, bill_aggregate
    ):
        travel_agent_details = booking_aggregate.get_travel_agent_details()
        if not travel_agent_details.billed_entity_id:
            return self._add_billed_entity_from_booking_travel_agent_details(
                booking_aggregate, bill_aggregate
            )
        travel_agent_billed_entity = bill_aggregate.get_billed_entity(
            travel_agent_details.billed_entity_id
        )
        if (
            travel_agent_billed_entity.name.full_name
            != booking_aggregate.get_travel_agent_legal_name()
        ):
            travel_agent_billed_entity.update_name(
                Name(booking_aggregate.get_travel_agent_legal_name())
            )
        return travel_agent_billed_entity

    def switch_billed_entity_primary_guest(
        self, bill_aggregate, old_customer, new_primary_customer
    ):
        self._update_billed_entity_category_for_guest(
            bill_aggregate,
            old_customer,
            billed_entity_category=BilledEntityCategory.CONSUMING_GUESTS,
        )
        self._update_billed_entity_category_for_guest(
            bill_aggregate, new_primary_customer
        )

    @staticmethod
    def add_billed_entity_for_auto_funding(customer, bill_aggregate):
        billed_entity = bill_aggregate.add_billed_entity(
            BilledEntityData(
                name=Name(customer.company_legal_name()),
                category=BilledEntityCategory.FRANCHISER,
            )
        )
        customer.update_company_billed_entity_id(billed_entity.billed_entity_id)
