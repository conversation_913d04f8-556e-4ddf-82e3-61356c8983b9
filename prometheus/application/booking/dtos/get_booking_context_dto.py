from typing import Optional

from prometheus.domain.billing.dto.bill_summary_dto import BillSummaryDto
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.aggregates.web_checkin_aggregate import (
    WebCheckinAggregate,
)


class GetBookingContextDTO:
    def __init__(
        self,
        booking_aggregate: BookingAggregate,
        bill_summary: Optional[BillSummaryDto],
        web_checkin_aggregate: Optional[WebCheckinAggregate],
    ):
        self.booking_aggregate = booking_aggregate
        self.bill_summary = bill_summary
        self.web_checkin_aggregate = web_checkin_aggregate
