import logging

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.booking.command_handlers.invoice.helpers import (
    preview_invoice_helpers,
)
from prometheus.application.booking.helpers.create_preview_invoice import (
    CreatePreviewInvoiceCommand,
)
from prometheus.application.booking.template_generation.crs_booking_invoice_template_service import (
    CrsBookingInvoiceTemplateService,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.helpers.invoice_issuer_service import InvoiceIssuerService
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.catalog.repositories.reseller_gst_repository import (
    ResellerGstRepository,
)
from prometheus.domain.catalog.repositories.sku_category_repository import (
    SkuCategoryRepository,
)
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from ths_common.constants.billing_constants import ChargeStatus, IssuedToType

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        InvoiceRepository,
        BookingRepository,
        BillRepository,
        SkuCategoryRepository,
        ResellerGstRepository,
        CatalogServiceClient,
        CrsBookingInvoiceTemplateService,
        HotelRepository,
        HotelConfigRepository,
        TenantSettings,
    ]
)
class GenerateProformaInvoiceCommandHandler:
    def __init__(
        self,
        invoice_repository,
        booking_repository,
        bill_repository,
        sku_category_repository,
        reseller_gst_repository,
        catalog_service_client: CatalogServiceClient,
        crs_booking_invoice_template_service: CrsBookingInvoiceTemplateService,
        hotel_repository,
        hotel_config_repository,
        tenant_settings,
    ):
        self.invoice_repository = invoice_repository
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.sku_category_repository = sku_category_repository
        self.reseller_gst_detail_repository = reseller_gst_repository
        self.catalog_service_client = catalog_service_client
        self.crs_booking_invoice_template_service = crs_booking_invoice_template_service
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.tenant_settings = tenant_settings

    def _set_hotel_context(self, hotel_id):
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        hotel_context = crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate
        )
        return hotel_aggregate, hotel_config_aggregate, hotel_context

    @staticmethod
    def _consume_charges_and_allowances_for_proforma_invoice(
        booking_aggregate, bill_aggregate
    ):
        # All the charge splits and customers created here are for proforma invoice, they shouldn't be saved in the db
        for room_stay in booking_aggregate.get_active_room_stays():
            all_charges_on_room_stay = (
                booking_aggregate.get_all_applicable_charges_on_room_stay(
                    room_stay.room_stay_id
                )
            )
            for charge_id in all_charges_on_room_stay:
                charge = bill_aggregate.get_charge(charge_id)
                # Creating dummy room number if there's no room assigned to room stay
                if charge.status != ChargeStatus.CREATED:
                    continue
                if room_stay.room_allocation and room_stay.room_allocation.room_no:
                    charge.item.details['room_no'] = room_stay.room_allocation.room_no
                else:
                    charge.item.details['room_no'] = 'R{0}'.format(
                        room_stay.room_stay_id
                    )
                # Consuming all charges that are in created state
                bill_aggregate.consume_charge(charge, charge.applicable_date)
                for charge_split in charge.charge_splits:
                    charge_split.consume_allowances(charge.applicable_date)

    @session_manager(commit=False)
    @set_hotel_context()
    def handle(self, booking_id, billed_entity_account=None, hotel_aggregate=None):
        # All operations for proforma invoice templates are not stored in the db
        booking_aggregate = self.booking_repository.load(booking_id)
        bill_aggregate = self.bill_repository.load(booking_aggregate.booking.bill_id)
        if not hotel_aggregate:
            (
                hotel_aggregate,
                _,
                hotel_context,
            ) = self._set_hotel_context(booking_aggregate.booking.hotel_id)

        hotel_context = crs_context.get_hotel_context()

        crs_context.set_current_booking(booking_aggregate)
        self._consume_charges_and_allowances_for_proforma_invoice(
            booking_aggregate, bill_aggregate
        )
        issued_by_type, _ = InvoiceIssuerService.get_issuer_type_for_new_invoice(
            bill_aggregate.bill.bill_id,
            hotel_aggregate.hotel.hotel_id,
            self.invoice_repository,
            self.catalog_service_client,
            booking_aggregate,
            self.tenant_settings,
        )

        sku_category_aggregates = self.sku_category_repository.load_all()
        grouped_sku_categories = {
            aggregate.sku_category.sku_category_id: aggregate.sku_category
            for aggregate in sku_category_aggregates
        }

        invoice_previews = CreatePreviewInvoiceCommand(
            bill_aggregate,
            booking_aggregate,
            hotel_context.current_date(),
            None,
            None,
            InvoiceIssuerService.get_issuer(
                issued_by_type,
                crs_context.get_hotel_context().build_vendor_details(),
                self.reseller_gst_detail_repository.load(
                    crs_context.get_hotel_context().legal_state_id
                ),
                catalog_service_client=self.catalog_service_client,
            ),
            issued_by_type,
            IssuedToType.CUSTOMER,
            preview_invoice_helpers.get_charge_splits_to_be_invoiced(
                None,
                booking_aggregate,
                bill_aggregate,
                include_cancellation_no_show_charges=True,
                billed_entity_account=billed_entity_account,
            ),
            grouped_sku_categories,
            proforma_invoice=True,
        ).execute()
        (
            templates,
            invoice_url_map,
        ) = self.crs_booking_invoice_template_service.generate_and_upload_proforma_invoice_template(
            booking_aggregate,
            invoice_aggregates=invoice_previews,
            hotel_aggregate=hotel_aggregate,
            bill_aggregate=bill_aggregate,
        )
        return templates, invoice_url_map
