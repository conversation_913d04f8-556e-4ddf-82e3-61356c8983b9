import copy
import logging

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.helpers.ta_commission_helper import TACommissionHelper
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.facts import Facts
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.value_objects import TACommissionDetails, TADetails

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        HotelConfigRepository,
        TACommissionHelper,
    ]
)
class UpdateTACommissionCommandHandler(object):
    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        hotel_repository: HotelRepository,
        hotel_config_repository: HotelConfigRepository,
        ta_commission_helper: TACommissionHelper,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.ta_commission_helper = ta_commission_helper

    @session_manager(commit=True)
    @audit(audit_type=AuditType.TA_COMMISSION_MODIFIED)
    @set_hotel_context()
    def handle(
        self,
        booking_id,
        booking_version,
        commission_data,
        user_data,
        hotel_aggregate=None,
    ):
        """
        Validations:
            - Booking Version check -- done
        :param booking_id:
        :param booking_version:
        :param commission_data:
        :param user_data:
        :param hotel_aggregate:
        :return:
        """
        booking_aggregate = self.booking_repository.load_for_update(
            booking_id, user_data=user_data
        )
        booking_aggregate.fail_if_outdated_version(booking_version)
        if not hotel_aggregate:
            crs_context.set_hotel_context(
                self.hotel_repository.load(booking_aggregate.booking.hotel_id),
                hotel_config_aggregate=self.hotel_config_repository.load(
                    booking_aggregate.booking.hotel_id
                ),
            )

        crs_context.set_current_booking(booking_aggregate)
        bill_aggregate = self.bill_repository.load_for_update(
            booking_aggregate.booking.bill_id
        )
        RuleEngine.action_allowed(
            action="update_ta_commission_rule",
            facts=Facts(
                user_type=user_data.user_type,
                action_payload=None,
                hotel_context=crs_context.get_hotel_context(),
                booking_aggregate=booking_aggregate,
            ),
            fail_on_error=True,
        )
        travel_agent_details = booking_aggregate.get_travel_agent_details()
        travel_agent_details_value_object: TADetails = copy.deepcopy(
            travel_agent_details
        )
        commission_data['post_commission_amount'] = True
        travel_agent_details_value_object.ta_commission_details = (
            TACommissionDetails.from_json(commission_data)
        )
        booking_aggregate.booking.travel_agent_details.ta_commission_details.recalculate_commission_on_booking_modification = (
            False
        )
        booking_aggregate.update_travel_agent_details(travel_agent_details_value_object)
        self.ta_commission_helper.recalculate_commission_on_booking(
            booking_aggregate, bill_aggregate, for_rule_change=True
        )
        self.booking_repository.update(booking_aggregate)
        return booking_aggregate
