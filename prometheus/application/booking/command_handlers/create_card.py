import logging

from object_registry import register_instance
from prometheus.application.decorators import session_manager
from prometheus.domain.billing.factories.card_factory import CardFactory
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.billing.repositories.card_repository import CardRepository

logger = logging.getLogger(__name__)


@register_instance(dependencies=[CardRepository, BillRepository])
class CreateCardCommandHandler:
    def __init__(
        self, card_repository: CardRepository, bill_repository: BillRepository
    ):
        self.card_repository = card_repository
        self.bill_repository = bill_repository

    @session_manager(commit=True)
    def handle(self, bill_id, card_data):
        """
        :param bill_id:
        :param card_data: a dict created from AddCardSchema
        :return:
        """
        self.bill_repository.validate_bill_hotel_id(bill_id=bill_id)
        card_aggregate = CardFactory.create_card(bill_id=bill_id, card_data=card_data)
        self.card_repository.save(card_aggregate)

        return card_aggregate
