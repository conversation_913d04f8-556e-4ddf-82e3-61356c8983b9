import logging
from collections import defaultdict

from treebo_commons.utils import dateutils

from object_registry import locate_instance, register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit, audit_housekeeping
from prometheus.application.billing.command_handlers.modify_locked_invoices import (
    ModifyLockedInvoicesCommandHandler,
)
from prometheus.application.billing.dtos.invoice_reissue_dto import NewInvoiceAmountDto
from prometheus.application.booking.helpers.invoice_cancellation_service import (
    InvoiceCancellationService,
)
from prometheus.application.booking.helpers.invoice_confirmation_service import (
    InvoiceConfirmationService,
)
from prometheus.application.helpers.ta_commission_helper import TACommissionHelper
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.housekeeping.house_status_service import HouseStatusService
from prometheus.application.inventory.query_handlers.get_available_room_slots import (
    GetAvailableRoomSlotsQueryHandlers,
)
from prometheus.application.inventory.service.inventory_application_service import (
    InventoryApplicationService,
)
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.application.services.room_stay_overflow_service import (
    RoomStayOverflowService,
)
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.dto.allowance_data import AllowanceData
from prometheus.domain.billing.dto.charge_data import ChargeData
from prometheus.domain.billing.dto.chargesplit_data import ChargeSplitData
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.billing.repositories.credit_note_repository import (
    CreditNoteRepository,
)
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.billing.services.add_allowance_service import AddAllowanceService
from prometheus.domain.billing.services.tax_service import TaxService
from prometheus.domain.booking.dtos.expense_dto import ExpenseDto
from prometheus.domain.booking.repositories.booking_action_repository import (
    BookingActionRepository,
)
from prometheus.domain.booking.repositories.booking_invoice_group_repository import (
    BookingInvoiceGroupRepository,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.booking.repositories.expense_item_repository import (
    ExpenseItemRepository,
)
from prometheus.domain.booking.repositories.room_stay_overflow_repository import (
    RoomStayOverflowRepository,
)
from prometheus.domain.booking.services.booking_checkout_request_generator import (
    BookingCheckoutRequestGenerator,
)
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.catalog.repositories.room_repository import RoomRepository
from prometheus.domain.catalog.repositories.room_type_repository import (
    RoomTypeRepository,
)
from prometheus.domain.catalog.repositories.sku_category_repository import (
    SkuCategoryRepository,
)
from prometheus.domain.domain_events.domain_event_registry import (
    register_room_status_change_domain_event,
)
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.domain_events.room_status_changed_event import (
    RoomStatusChangedEvent,
)
from prometheus.domain.inventory.exceptions import InventoryException
from prometheus.domain.inventory.inventory_requirement import InventoryRequirement
from prometheus.domain.inventory.repositories.room_allotment_repository import (
    RoomAllotmentRepository,
)
from prometheus.domain.inventory.repositories.room_type_inventory_repository import (
    RoomTypeInventoryRepository,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.room_stay_facts import RoomStayFacts
from prometheus.infrastructure.alerting.newrelic_service_client import (
    NewrelicServiceClient,
)
from ths_common.constants.action_reversal_alert_messages import (
    ActionReversalAlertMessage,
)
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import ChargeSplitType
from ths_common.constants.booking_constants import (
    ExpenseAddedBy,
    ExpenseStatus,
    InvoiceGroupStatus,
)
from ths_common.constants.funding_constants import FundingExpenseItem
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.constants.inventory_constants import (
    RoomCurrentStatus,
    RoomReservationStatus,
)
from ths_common.constants.tenant_settings_constants import TenantSettingName
from ths_common.exceptions import ValidationException
from ths_common.utils.dateutils import get_settlement_date
from ths_common.value_objects import (
    ActionReversalAlert,
    ActionReversalSideEffects,
    ChargeItem,
    ExpenseChargeItemDetails,
)

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        RoomTypeInventoryRepository,
        RoomTypeRepository,
        BookingActionRepository,
        RoomRepository,
        HotelRepository,
        BookingInvoiceGroupRepository,
        RoomStayOverflowRepository,
        InvoiceRepository,
        JobSchedulerService,
        HotelConfigRepository,
        NewrelicServiceClient,
        InvoiceConfirmationService,
        InvoiceCancellationService,
        ExpenseItemRepository,
        CreditNoteRepository,
        SkuCategoryRepository,
        TaxService,
        ModifyLockedInvoicesCommandHandler,
        TenantSettings,
        InventoryApplicationService,
        RoomStayOverflowService,
        GetAvailableRoomSlotsQueryHandlers,
        TACommissionHelper,
        AddAllowanceService,
    ]
)
class ReverseCheckoutCommandHandler:
    def __init__(
        self,
        booking_repository,
        bill_repository,
        room_type_inventory_repository,
        room_type_repository,
        booking_action_repository,
        room_repository,
        hotel_repository,
        booking_invoice_group_repository,
        room_stay_overflow_repository,
        invoice_repository,
        job_scheduler_service,
        hotel_config_repository,
        alerting_service,
        invoice_confirmation_service,
        invoice_cancellation_service,
        expense_item_repository,
        credit_note_repository,
        sku_category_repository,
        tax_service,
        modify_locked_invoices_command_handler,
        tenant_settings,
        inventory_application_service: InventoryApplicationService,
        room_stay_overflow_service,
        get_available_room_slots_query_handler: GetAvailableRoomSlotsQueryHandlers,
        ta_commission_helper: TACommissionHelper,
        add_allowance_service: AddAllowanceService,
    ):
        self.booking_repository = booking_repository
        self.booking_action_repository = booking_action_repository
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.booking_invoice_group_repository = booking_invoice_group_repository
        self.bill_repository = bill_repository
        self.room_type_repository = room_type_repository
        self.room_repository = room_repository
        self.invoice_repository = invoice_repository
        self.job_scheduler_service = job_scheduler_service
        self.room_allotment_repository = RoomAllotmentRepository()
        self.invoice_confirmation_service = invoice_confirmation_service
        self.invoice_cancellation_service = invoice_cancellation_service
        self.room_type_inventory_repository = room_type_inventory_repository
        self.room_stay_overflow_repository = room_stay_overflow_repository
        self.alerting_service = alerting_service
        self.expense_item_repository = expense_item_repository
        self.credit_note_repository = credit_note_repository
        self.sku_category_repository = sku_category_repository
        self.tax_service = tax_service
        self.modify_locked_invoices_command_handler = (
            modify_locked_invoices_command_handler
        )
        self.tenant_settings = tenant_settings
        self.inventory_application_service = inventory_application_service
        self.room_stay_overflow_service = room_stay_overflow_service
        # TODO: Get rid of this query handler
        self.get_available_room_slots_query_handler = (
            get_available_room_slots_query_handler
        )
        self.ta_commission_helper = ta_commission_helper
        self.add_allowance_service = add_allowance_service

    @audit(audit_type=AuditType.CHECKOUT_REVERSED)
    @audit_housekeeping(action_performed="reverse_checkout")
    def handle(self, booking_aggregate, booking_action_aggregate, user_data):
        self.validate_undo_checkout_policy(
            booking_aggregate, booking_action_aggregate, user_data
        )
        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_action_id(booking_action_aggregate)

        # Load everything
        hotel_aggregate = self.hotel_repository.load(booking_aggregate.hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(
            booking_aggregate.hotel_id
        )
        hotel_context = crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
        )
        bill_aggregate = self.bill_repository.load_for_update(booking_aggregate.bill_id)
        crs_context.set_current_bill(bill_aggregate)

        checkout_action_payload = booking_action_aggregate.booking_action.payload
        reversal_side_effects = ActionReversalSideEffects()
        updated_aggregates = dict()

        booking_invoice_group_id = checkout_action_payload.get('invoice_group_id')
        booking_invoice_group_aggregate = (
            self.booking_invoice_group_repository.load_for_update(
                booking_invoice_group_id
            )
        )

        # undo charge cancellation
        invoice_aggregates = self.invoice_repository.load_all_for_update(
            booking_invoice_group_aggregate.invoice_ids
        )
        if invoice_aggregates:
            expense_item_aggregates = self.expense_item_repository.load_all(
                include_linked=True
            )
            grouped_expense_items = {
                expense_item_aggregate.expense_item_id: expense_item_aggregate
                for expense_item_aggregate in expense_item_aggregates
            }
            invoice_ids_for_credit_note = []
            invoice_ids_for_cancel = []
            for invoice_aggregate in invoice_aggregates:
                if invoice_aggregate.invoice.is_locked:
                    invoice_ids_for_credit_note.append(invoice_aggregate.invoice_id)
                else:
                    invoice_ids_for_cancel.append(invoice_aggregate.invoice_id)

            if len(invoice_ids_for_credit_note) > 0:
                self._cancel_invoices_via_credit_note(
                    booking_aggregate,
                    bill_aggregate,
                    [
                        aggregate
                        for aggregate in invoice_aggregates
                        if aggregate.invoice_id in invoice_ids_for_credit_note
                    ],
                    booking_action_aggregate,
                    grouped_expense_items,
                    user_data,
                    updated_aggregates,
                )

            if len(invoice_ids_for_cancel) > 0:
                self._cancel_invoices(
                    [
                        aggregate
                        for aggregate in invoice_aggregates
                        if aggregate.invoice_id in invoice_ids_for_cancel
                    ],
                    bill_aggregate,
                    booking_aggregate,
                    booking_action_aggregate,
                    hotel_context,
                )
        self.invoice_repository.update_all(invoice_aggregates)

        (
            checkout_reversal_alerts,
            room_ids_for_house_status_update,
        ) = self._perform_reverse_checkout(
            booking_aggregate, booking_invoice_group_aggregate, hotel_context
        )
        if (
            crs_context.is_treebo_tenant()
            and booking_aggregate.should_calculate_ta_commission()
        ):
            self.ta_commission_helper.recalculate_commission_on_booking(
                booking_aggregate, bill_aggregate
            )
        reversal_side_effects.add_alerts(checkout_reversal_alerts)

        self.booking_repository.update(booking_aggregate)
        self.bill_repository.update(bill_aggregate)
        updated_aggregates['booking_aggregate'] = booking_aggregate
        updated_aggregates['bill_aggregate'] = bill_aggregate

        if room_ids_for_house_status_update:
            locate_instance(HouseStatusService).set_house_status_for_rooms(
                hotel_id=booking_aggregate.hotel_id,
                room_ids=room_ids_for_house_status_update,
            )

        IntegrationEventApplicationService.create_booking_updated_event(
            **updated_aggregates, user_action="undo_checkout"
        )
        booking_action_aggregate.booking_action.reversal_side_effects = (
            reversal_side_effects
        )

        booking_invoice_group_aggregate.update_status(InvoiceGroupStatus.CANCELLED)
        self.booking_invoice_group_repository.update(booking_invoice_group_aggregate)

        return booking_action_aggregate

    def validate_undo_checkout_policy(
        self, booking_aggregate, booking_action_aggregate, user_data, fail_on_error=True
    ):
        checkout_action_payload = booking_action_aggregate.booking_action.payload
        booking_invoice_group_id = checkout_action_payload.get('invoice_group_id')

        booking_invoice_group_aggregate = self.booking_invoice_group_repository.load(
            booking_invoice_group_id
        )
        invoice_aggregates = (
            self.invoice_repository.load_all(
                booking_invoice_group_aggregate.invoice_ids
            )
            if checkout_action_payload.get('should_generate_invoice')
            else []
        )
        room_wise_preview_request = (
            booking_invoice_group_aggregate.room_wise_invoice_request
        )

        room_stay_ids = [request.room_stay_id for request in room_wise_preview_request]
        bill_aggregate = self.bill_repository.load(booking_aggregate.booking.bill_id)
        for room_stay_id in room_stay_ids:
            room_stay = booking_aggregate.get_room_stay(room_stay_id)
            if not RuleEngine.action_allowed(
                action="reverse_checkout",
                facts=RoomStayFacts(
                    room_stay,
                    user_type=user_data.user_type,
                    current_time=dateutils.current_datetime(),
                    booking_aggregate=booking_aggregate,
                    hotel_context=crs_context.get_hotel_context(),
                    invoice_aggregates=invoice_aggregates,
                ),
                fail_on_error=fail_on_error,
            ):
                return False
            if self._is_new_invoiced_expense_added_to_room_post_checkout(
                booking_aggregate,
                bill_aggregate,
                booking_action_aggregate,
                room_stay,
                fail_on_error,
            ):
                return False
        return True

    def _perform_reverse_checkout(
        self, booking_aggregate, booking_invoice_group_aggregate, hotel_context
    ):
        # Right now this method call should happen before doing undo_checkout_guests on booking_aggregate,
        # as there is dependency on current_room_allocation.checkout_date, which is cleared by undo_checkout_guests
        # method
        # TODO: Find a way to remove that dependency for order of method call
        (
            action_reversal_alerts,
            room_ids_for_house_status_update,
        ) = self._update_room_allotment_before_reverse_checkout(
            booking_aggregate, booking_invoice_group_aggregate, hotel_context
        )

        room_wise_preview_request = (
            booking_invoice_group_aggregate.room_wise_invoice_request
        )

        room_type_inventory_requirement = (
            self._build_room_type_inventory_requirement_for_undo_checkout(
                booking_aggregate.hotel_id,
                booking_aggregate,
                room_wise_preview_request,
                hotel_context,
            )
        )

        # Room type inventory update
        if len(room_type_inventory_requirement) > 0:
            self._block_inventory(
                room_type_inventory_requirement, user_action="undo_checkout"
            )

        # undo checkout guests
        checkout_request_per_rooms = (
            BookingCheckoutRequestGenerator.generate_checkout_request(
                room_wise_preview_request,
                booking_invoice_group_aggregate.request_datetime,
                booking_aggregate,
            )
        )

        booking_aggregate.undo_checkout_guests(checkout_request_per_rooms)

        return action_reversal_alerts, room_ids_for_house_status_update

    @staticmethod
    def _build_room_type_inventory_requirement_for_undo_checkout(
        hotel_id, booking_aggregate, room_wise_preview_request, hotel_context
    ):
        # build room type inventory requirement
        room_type_inventory_requirement = InventoryRequirement(hotel_id)
        for checkout_request in room_wise_preview_request:
            if not checkout_request.room_complete_checkout:
                continue

            room_stay = booking_aggregate.get_room_stay(checkout_request.room_stay_id)
            # calculate inventory update dates
            request_date = hotel_context.hotel_date(room_stay.actual_checkout_date)
            actual_checkout_date = dateutils.to_date(room_stay.checkout_date)

            start_date = min(request_date, actual_checkout_date)
            end_date = actual_checkout_date

            room_type_inventory_requirement.add(
                room_stay.room_type_id, start_date, end_date
            )

        return room_type_inventory_requirement

    def _update_room_allotment_before_reverse_checkout(
        self, booking_aggregate, booking_invoice_group_aggregate, hotel_context
    ):
        action_reversal_alerts = []
        room_ids_for_house_status_update = []
        room_wise_preview_request = (
            booking_invoice_group_aggregate.room_wise_invoice_request
        )

        # undo room inventory release
        for checkout_request in room_wise_preview_request:
            # handle complete room checkout flow
            if not checkout_request.room_complete_checkout:
                continue

            # create payload for physical inventory update
            room_stay = booking_aggregate.get_room_stay(checkout_request.room_stay_id)

            (
                action_reversal_alert,
                _room_ids_for_house_status_update,
            ) = self._extend_current_allocation_or_allot_new_room_for_reverse_checkout(
                booking_aggregate, room_stay, hotel_context
            )

            if action_reversal_alert:
                action_reversal_alerts.append(action_reversal_alert)

            if _room_ids_for_house_status_update:
                room_ids_for_house_status_update.extend(
                    _room_ids_for_house_status_update
                )

        return action_reversal_alerts, room_ids_for_house_status_update

    def _extend_current_allocation_or_allot_new_room_for_reverse_checkout(
        self, booking_aggregate, room_stay, hotel_context
    ):
        current_room_allocation = room_stay.room_allocation
        room_allotment_aggregate = self.room_allotment_repository.load_for_update(
            hotel_id=booking_aggregate.hotel_id, room_id=current_room_allocation.room_id
        )
        room_ids_for_house_status_update = []
        try:
            room_allotment_aggregate.reset_room_allotment_actual_end_time(
                current_room_allocation.room_allotment_id
            )
            old_room_status = room_allotment_aggregate.room_inventory.current_status
            room_allotment_aggregate.update_current_status(RoomCurrentStatus.OCCUPIED)
            register_room_status_change_domain_event(
                RoomStatusChangedEvent(
                    hotel_id=booking_aggregate.hotel_id,
                    room_id=current_room_allocation.room_id,
                    old_housekeeping_status=room_allotment_aggregate.housekeeping_record.housekeeping_status,
                    new_housekeeping_status=room_allotment_aggregate.housekeeping_record.housekeeping_status,
                    old_room_status=old_room_status,
                    new_room_status=room_allotment_aggregate.room_inventory.current_status,
                    remarks="Checkout Reversed",
                )
            )

            business_date = hotel_context.current_date()
            if dateutils.to_date(room_stay.checkout_date) == business_date:
                room_allotment_aggregate.update_reservation_status(
                    RoomReservationStatus.DUE_OUT, action='reverse_checkout'
                )
            elif (
                dateutils.to_date(current_room_allocation.checkin_date) == business_date
            ):
                room_allotment_aggregate.update_reservation_status(
                    RoomReservationStatus.ARRIVED, action='reverse_checkout'
                )
            else:
                room_allotment_aggregate.update_reservation_status(
                    RoomReservationStatus.STAY_OVER, action='reverse_checkout'
                )

            self.room_allotment_repository.update(room_allotment_aggregate)

            return None, room_ids_for_house_status_update

        except InventoryException:
            logger.warning(
                "Unable to reallocate room_id: %s for room_stay: %s for booking: %s",
                current_room_allocation.room_id,
                room_stay.room_stay_id,
                booking_aggregate.booking_id,
            )

            room_ids_for_house_status_update.append(current_room_allocation.room_id)

            # Allot new room starting current_room_allocation.checkout_date
            rooms_available = self.get_available_room_slots_query_handler.handle(
                booking_aggregate.hotel_id,
                current_room_allocation.checkout_date,
                room_stay.checkout_date,
                room_stay.room_type_id,
            )

            if not rooms_available:
                raise InventoryException(ApplicationErrors.INVENTORY_AUTO_UPDATE_FAILED)

            new_room_allocation, _ = booking_aggregate.allocate_room(
                room_stay.room_stay_id,
                rooms_available[0].room,
                rooms_available[0].room_id,
                dateutils.add(current_room_allocation.checkout_date, minutes=1),
            )

            allotment_id = self.inventory_application_service.allot_room_inventory(
                hotel_id=booking_aggregate.hotel_id,
                room_id=new_room_allocation.room_id,
                start_time=new_room_allocation.checkin_date,
                expected_end_time=room_stay.checkout_date,
                hotel_context=hotel_context,
                is_booking_checked_in=True,
            )

            new_room_allocation.set_allotment_id(allotment_id)

            room_ids_for_house_status_update.append(new_room_allocation.room_id)

            action_reversal_alert = ActionReversalAlert(
                message=ActionReversalAlertMessage.ROOM_CHANGED.message(
                    old_room=current_room_allocation.room_no,
                    new_room=new_room_allocation.room_no,
                ),
                payload=[
                    dict(
                        room_say_id=room_stay.room_stay_id,
                        old_room_no=current_room_allocation.room_no,
                        new_room_no=new_room_allocation.room_no,
                        allotment_id=allotment_id,
                    )
                ],
            )

            return action_reversal_alert, room_ids_for_house_status_update

    def _block_inventory(
        self, inventory_requirement, allow_overbooking=False, user_action=None
    ):
        overbooking_messages = self.inventory_application_service.update_inventory(
            block_inventory=inventory_requirement,
            allow_overbooking=allow_overbooking,
            user_action=user_action,
        )
        return overbooking_messages

    def issue_credit_notes_and_add_new_charges_on_undo_checkout(
        self,
        booking_aggregate,
        bill_aggregate,
        invoice_aggregates,
        booking_action_aggregate,
        grouped_expense_items,
        user_type,
    ):
        grouped_invoices = {
            invoice_aggregate.invoice.invoice_id: invoice_aggregate
            for invoice_aggregate in invoice_aggregates
        }

        new_invoice_amounts = defaultdict(list)
        hotel_uses_posttax = self.tenant_settings.get_setting_value(
            TenantSettingName.HOTEL_USES_POSTTAX_PRICE.value, booking_aggregate.hotel_id
        )
        for invoice_aggregate in invoice_aggregates:
            old_billed_entity_account = invoice_aggregate.invoice.billed_entity_account
            billed_entity = bill_aggregate.get_billed_entity(
                old_billed_entity_account.billed_entity_id
            )
            account = billed_entity.add_new_account()
            account_number = account.account_number
            bill_aggregate.add_folio_if_not_exists(
                billed_entity.billed_entity_id, account_number
            )
            for invoice_charge in invoice_aggregate.invoice_charges:
                pretax_amount = None
                posttax_amount = None
                if hotel_uses_posttax:
                    posttax_amount = str(invoice_charge.posttax_amount)
                else:
                    pretax_amount = str(invoice_charge.pretax_amount)
                new_invoice_amounts[invoice_aggregate.invoice.invoice_id].append(
                    NewInvoiceAmountDto(
                        invoice_charge.invoice_charge_id,
                        pre_tax_amount=pretax_amount,
                        posttax_amount=posttax_amount,
                        new_invoice_amount=None,
                        billed_entity_account=BilledEntityAccountVO(
                            billed_entity_id=billed_entity.billed_entity_id,
                            account_number=account_number,
                        ),
                    )
                )

        # Issue full credit notes, creates new account in each billed entity, where an allowance will get added
        # corresponding to each invoice
        (
            credit_notes,
            hotel_credit_notes,
            grouped_credit_shell_aggregate,
        ) = self.modify_locked_invoices_command_handler.issue_full_credit_notes_for_invoices(
            booking_aggregate,
            bill_aggregate,
            invoice_aggregates,
            user_type,
            generate_hotel_side=True,
            comment="Reversal of checkout action",
        )

        sku_category_aggregates = self.sku_category_repository.load_all()
        grouped_sku_categories = {
            aggregate.sku_category.sku_category_id: aggregate.sku_category
            for aggregate in sku_category_aggregates
        }

        bill_side_effect = booking_action_aggregate.booking_action.side_effects.bill
        if bill_side_effect.cancelled_charge_ids:
            bill_aggregate.reverse_bill_side_effect(bill_side_effect)
            booking_aggregate.mark_cancel_expense_as_created_for_charges(
                bill_side_effect.cancelled_charge_ids
            )

        if bill_side_effect.grouped_cancelled_charges:
            for (
                room_stay_id_wise_charges
            ) in bill_side_effect.grouped_cancelled_charges.room_stay_charges:
                for charge_id in room_stay_id_wise_charges.charge_ids:
                    room_stay = booking_aggregate.get_room_stay(
                        room_stay_id_wise_charges.room_stay_id
                    )
                    room_stay.charge_id_map[
                        dateutils.date_to_ymd_str(
                            bill_aggregate.get_charge(charge_id).applicable_date
                        )
                    ] = charge_id
                    room_stay.charge_ids.append(charge_id)

        charge_ids_to_skip = (
            booking_action_aggregate.booking_action.side_effects.bill.added_charge_ids
            or []
        )
        # Adding new charge for new invoice amount will again add new account for each billed entity
        self.modify_locked_invoices_command_handler.create_expenses_and_charges_for_new_reissue_invoices(
            bill_aggregate,
            booking_aggregate,
            grouped_invoices,
            new_invoice_amounts,
            grouped_expense_items,
            grouped_credit_shell_aggregate,
            transfer_payment_to_new_invoice_account=True,
            grouped_sku_categories=grouped_sku_categories,
            is_reverse_checkout=True,
            charge_ids_to_skip=charge_ids_to_skip,
        )

        return credit_notes, hotel_credit_notes

    @staticmethod
    def _is_new_invoiced_expense_added_to_room_post_checkout(
        booking_aggregate,
        bill_aggregate,
        booking_action_aggregate,
        room_stay,
        fail_on_error,
    ):
        # expenses with invoiced charges
        invoiced_expenses_for_room_stay = [
            expense
            for expense in booking_aggregate.get_active_expenses_for_room_stay(
                room_stay.room_stay_id
            )
            if bill_aggregate.get_charge(expense.charge_id).has_invoice_attached
        ]

        if any(
            invoiced_expense.created_at
            > booking_action_aggregate.booking_action.created_at
            for invoiced_expense in invoiced_expenses_for_room_stay
            if invoiced_expense.expense_item_id
            not in (
                FundingExpenseItem.TREEBO_MANUAL_FUNDING.value,
                FundingExpenseItem.TREEBO_AUTO_FUNDING.value,
            )
        ):
            if fail_on_error:
                raise ValidationException(
                    ApplicationErrors.REVERSE_CHECKOUT_NOT_ALLOWED_AFTER_NEW_INVOICED_CHARGE_ADDITION_TO_ROOM_POST_CHECKOUT
                )
            return True
        return False

    def reinstantiate_charge_as_new_expense(
        self,
        charge_id,
        booking_aggregate,
        bill_aggregate,
        grouped_sku_categories,
        grouped_expense_items,
        room_stay_id=None,
    ):
        # TODO: Move this to a common service as we are using similar kind of logic in many places
        old_charge = bill_aggregate.get_charge(charge_id)
        hotel_context = crs_context.get_hotel_context()
        (
            expense_dto,
            charge_item,
        ) = self._create_expense_dto_and_charge_item_from_old_charge(
            old_charge,
            bill_aggregate,
            booking_aggregate,
            grouped_expense_items,
            room_stay_id=room_stay_id,
        )
        sku_category = grouped_sku_categories.get(charge_item.sku_category_id)
        charge_dto = ChargeData(
            posttax_amount=old_charge.posttax_amount,
            charge_to=old_charge.charge_to,
            applicable_date=old_charge.applicable_date,
            comment=expense_dto.comments,
            item=charge_item,
        )
        new_expense = booking_aggregate.add_expense(
            expense_dto,
            get_settlement_date(dateutils.current_datetime(), next_month=True),
        )
        charge_splits = [
            ChargeSplitData(
                percentage=charge_split.percentage,
                billed_entity_account=bill_aggregate.get_default_billed_entity_account(
                    booking_aggregate.get_default_billed_entity_category(),
                    charge_type=charge_split.charge_type,
                ),
                charge_type=charge_split.charge_type,
                bill_to_type=charge_split.bill_to_type,
            )
            for charge_split in old_charge.charge_splits
        ]
        charge_dto.charge_splits = charge_splits
        charge_dto.charge_split_type = ChargeSplitType.PERCENTAGE_SPLIT
        tax_updated_charge_dtos = self.tax_service.update_taxes(
            [charge_dto],
            buyer_gst_details=booking_aggregate.booking_owner_gst_details(),
            seller_has_lut=self.tax_service.seller_has_lut(
                booking_aggregate.booking.seller_model, hotel_context
            ),
            hotel_id=hotel_context.hotel_id,
        )
        new_charge = bill_aggregate.add_charges(tax_updated_charge_dtos)[
            0
        ]  # 1 charge only
        if room_stay_id:
            room_stay = booking_aggregate.get_room_stay(room_stay_id)
            room_stay.charge_id_map[
                dateutils.date_to_ymd_str(new_charge.applicable_date)
            ] = new_charge.charge_id
            room_stay.charge_ids.append(new_charge.charge_id)
        new_expense.charge_id = new_charge.charge_id
        if (
            dateutils.to_date(new_charge.applicable_date)
            <= hotel_context.current_date()
        ):
            new_expense.status = ExpenseStatus.CONSUMED
            bill_aggregate.consume_charge(new_charge, hotel_context.current_date())

    @staticmethod
    def _create_expense_dto_and_charge_item_from_old_charge(
        old_charge,
        bill_aggregate,
        booking_aggregate,
        grouped_expense_items,
        room_stay_id=None,
    ):
        # TODO: Duplicate method from InvoiceReIssueService
        if old_charge.is_room_rent():
            expense_item = grouped_expense_items.get('booking_modification')
            room_stay = booking_aggregate.get_room_stay(room_stay_id)
            new_charge_item = ChargeItem(
                expense_item.name,
                expense_item.sku_category_id,
                ExpenseChargeItemDetails(
                    room_stay.room_stay_id,
                    old_charge.item.details['room_no'],
                    old_charge.item.details['room_type'],
                    old_charge.item.details['room_type_code'],
                    old_charge.item.details['occupancy'],
                ),
                item_id=expense_item.expense_item_id,
            )
            new_expense_dto = ExpenseDto(
                expense_item_id=expense_item.expense_item_id,
                room_stay_id=room_stay.room_stay_id,
                status=ExpenseStatus.CREATED,
                added_by=ExpenseAddedBy.TREEBO
                if crs_context.is_treebo_tenant()
                else ExpenseAddedBy.HOTEL,
                created_at=dateutils.current_datetime(),
                comments='',
                applicable_date=old_charge.applicable_date,
                guests=old_charge.charge_to,
            )
        else:
            new_charge_item = old_charge.item
            expense = booking_aggregate.get_expense_for_charge(old_charge.charge_id)
            new_expense_dto = ExpenseDto(
                sku_id=expense.sku_id,
                expense_item_id=expense.expense_item_id,
                room_stay_id=expense.room_stay_id,
                status=expense.status,
                comments=expense.comments,
                added_by=expense.added_by,
                created_at=dateutils.current_datetime(),
                applicable_date=expense.applicable_date,
                guests=old_charge.charge_to,
            )
        return new_expense_dto, new_charge_item

    def _cancel_invoices_via_credit_note(
        self,
        booking_aggregate,
        bill_aggregate,
        invoice_aggregates,
        booking_action_aggregate,
        grouped_expense_items,
        user_data,
        updated_aggregates,
    ):
        (
            credit_notes,
            hotel_credit_notes,
        ) = self.issue_credit_notes_and_add_new_charges_on_undo_checkout(
            booking_aggregate,
            bill_aggregate,
            invoice_aggregates,
            booking_action_aggregate,
            grouped_expense_items,
            user_data.user_type,
        )
        self.credit_note_repository.save_all(credit_notes)
        self.credit_note_repository.save_all(hotel_credit_notes)
        self.job_scheduler_service.schedule_credit_note_upload(
            bill_aggregate.bill_id, credit_notes
        )
        updated_aggregates['invoice_aggregates'] = invoice_aggregates
        if credit_notes:
            IntegrationEventApplicationService.create_credit_note_event(
                IntegrationEventType.CREDIT_NOTE_GENERATED,
                credit_note_aggregates=credit_notes,
                invoice_aggregates=invoice_aggregates,
                user_action='undo_checkout',
            )

    def _cancel_invoices(
        self,
        invoice_aggregates,
        bill_aggregate,
        booking_aggregate,
        booking_action_aggregate,
        hotel_context,
    ):
        cancelled_invoice_aggregates = []
        hotel_invoice_aggregates = []
        for invoice_aggregate in invoice_aggregates:
            # Store in Table
            hotel_invoice_number = None
            if invoice_aggregate.invoice.hotel_invoice_id:
                hotel_invoice_aggregate = self.invoice_repository.load_for_update(
                    invoice_aggregate.invoice.hotel_invoice_id
                )
                hotel_invoice_number = hotel_invoice_aggregate.invoice.invoice_number

            if invoice_aggregate.invoice.invoice_date == hotel_context.current_date():
                bill_aggregate.save_invoice_numbers_available_for_reuse(
                    invoice_aggregate.invoice.billed_entity_account,
                    invoice_number=invoice_aggregate.invoice.invoice_number,
                    invoice_date=invoice_aggregate.invoice.invoice_date,
                    hotel_invoice_number=hotel_invoice_number,
                )

                # Cancel the invoice
                (
                    bill_aggregate,
                    cancelled_invoice_aggregate,
                    hotel_invoice_aggregate,
                ) = self.invoice_cancellation_service.cancel_invoice(
                    bill_aggregate, invoice_aggregate
                )
                cancelled_invoice_aggregates.append(cancelled_invoice_aggregate)
                if hotel_invoice_aggregate:
                    hotel_invoice_aggregates.append(hotel_invoice_aggregate)

                # Mark the account as invoiced=False
                bill_aggregate.mark_billed_entity_account_as_uninvoiced(
                    invoice_aggregate.invoice.billed_entity_account
                )
        self.invoice_repository.update_all(hotel_invoice_aggregates)
        cancelled_invoice_aggregates.extend(hotel_invoice_aggregates)
        bill_side_effect = booking_action_aggregate.booking_action.side_effects.bill
        if bill_side_effect.cancelled_charge_ids:
            bill_aggregate.reverse_bill_side_effect(bill_side_effect)
            booking_aggregate.mark_cancel_expense_as_created_for_charges(
                bill_side_effect.cancelled_charge_ids
            )
        if bill_side_effect.grouped_cancelled_charges:
            for (
                room_stay_id_wise_charges
            ) in bill_side_effect.grouped_cancelled_charges.room_stay_charges:
                for charge_id in room_stay_id_wise_charges.charge_ids:
                    room_stay = booking_aggregate.get_room_stay(
                        room_stay_id_wise_charges.room_stay_id
                    )
                    room_stay.charge_id_map[
                        dateutils.date_to_ymd_str(
                            bill_aggregate.get_charge(charge_id).applicable_date
                        )
                    ] = charge_id
                    room_stay.charge_ids.append(charge_id)
        if bill_side_effect.added_charge_ids:
            for charge_id in bill_side_effect.added_charge_ids:
                charge = bill_aggregate.get_charge(charge_id)
                for cs in charge.charge_splits:
                    allowance_data = AllowanceData(
                        pretax_amount=cs.get_pretax_amount_post_allowance(),
                        remarks='Booking Reverse checkout',
                        consume_at_creation=True,
                    )
                    self.add_allowance_service.add_allowance_to_charge_split(
                        bill_aggregate,
                        charge,
                        cs.charge_split_id,
                        allowance_data,
                        booking_aggregate,
                    )
        if cancelled_invoice_aggregates:
            IntegrationEventApplicationService.create_invoice_event(
                event_type=IntegrationEventType.INVOICE_CANCELLED,
                invoice_aggregates=cancelled_invoice_aggregates,
                user_action="cancel_invoice_for_reverse_checkout",
            )
