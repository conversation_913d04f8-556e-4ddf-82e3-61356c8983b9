from collections import namedtuple
from typing import Optional

from object_registry import locate_instance, register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit, audit_housekeeping
from prometheus.application.booking.command_handlers.expenses.expense_service_handler import (
    StayServiceCleanupFacade,
)
from prometheus.application.booking.command_handlers.helpers.update_room_stay_helpers import (
    UpdateRoomStayHelpers,
)
from prometheus.application.booking.dtos.expense_cleanup_side_effect_dto import (
    ExpenseServiceCleanupSideEffects,
)
from prometheus.application.booking.dtos.room_stay_update_dto import RoomStayUpdateDto
from prometheus.application.booking.helpers.e_reg_card_template_service import (
    ERegCardTemplateService,
)
from prometheus.application.booking.helpers.room_stay_helper import (
    RoomStayHelperService,
)
from prometheus.application.booking.helpers.room_stay_price_change_handler import (
    RoomStayPriceChangeHandler,
)
from prometheus.application.booking.helpers.update_room_stay_room_type import (
    UpdateRoomStayRoomTypeCommand,
)
from prometheus.application.decorators import session_manager
from prometheus.application.helpers.ta_commission_helper import TACommissionHelper
from prometheus.application.housekeeping.house_status_service import HouseStatusService
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.common.helpers import occupancy_change_handler
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.services import TaxService
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.repositories import RoomRepository
from prometheus.domain.inventory.repositories.inventory_block_repository import (
    InventoryBlockRepository,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.exceptions import ValidationException
from ths_common.value_objects import RoomStayConfig


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        RoomRepository,
        RoomStayPriceChangeHandler,
        TaxService,
        ERegCardTemplateService,
        UpdateRoomStayHelpers,
        TACommissionHelper,
        InventoryBlockRepository,
    ]
)
class AllocateRoomCommandHandler:
    RoomAllocationEffects = namedtuple(
        "RoomAllocationEffects",
        [
            "new_room_allocation",
            "room_ids_for_house_status_update",
            "service_cleanup_effects",
        ],
    )

    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        room_repository: RoomRepository,
        room_stay_price_change_handler: RoomStayPriceChangeHandler,
        tax_service: TaxService,
        eregcard_template_service: ERegCardTemplateService,
        update_room_stay_helper: UpdateRoomStayHelpers,
        ta_commission_helper: TACommissionHelper,
        inventory_block_repository: InventoryBlockRepository,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.room_repository = room_repository
        self.room_stay_price_change_handler = room_stay_price_change_handler
        self.tax_service = tax_service
        self.eregcard_template_service = eregcard_template_service
        self.update_room_stay_helper = update_room_stay_helper
        self.ta_commission_helper = ta_commission_helper
        self.inventory_block_repository = inventory_block_repository

    @session_manager(commit=True)
    @audit_housekeeping(action_performed="room_change")
    def handle(
        self,
        booking_id,
        room_stay_id,
        booking_version,
        room_stay_update_data,
        user_data,
    ):
        (
            bill_aggregate,
            booking_aggregate,
            hotel_aggregate,
            hotel_context,
            hotel_id,
            room_stay,
        ) = self._load_aggregates_for_update(
            booking_id, booking_version, room_stay_id, user_data
        )

        room_stay_update_dto = RoomStayUpdateDto(
            room_stay,
            room_stay_update_data,
            self.update_room_stay_helper.derive_checkin_datetime_for_new_room_allocation(
                room_stay
            ),
        )

        new_room_stay_config = room_stay_update_dto.new_room_stay_config
        current_room_stay_config = RoomStayConfig(
            room_type_id=room_stay.room_type_id,
            checkin_date=room_stay.checkin_date,
            checkout_date=room_stay.checkout_date,
        )
        room_type_updation_side_effects = self.update_room_stay_room_type(
            booking_aggregate,
            bill_aggregate,
            room_stay,
            new_room_stay_config,
            room_stay_update_dto,
            hotel_aggregate,
            hotel_context,
            user_data,
        )

        self.eregcard_template_service.schedule_eregcard_url_generation(
            booking_id,
            hotel_id,
            customer_ids=booking_aggregate.get_customer_ids_for_room_stays(
                [room_stay_id]
            ),
        )

        occupancy_change_handler.update_occupancy_details_in_booked_charges(
            booking_aggregate, bill_aggregate, room_stay=room_stay
        )
        self.bill_repository.update(bill_aggregate)
        self.booking_repository.update(booking_aggregate)

        if room_type_updation_side_effects.room_ids_for_house_status_update:
            locate_instance(HouseStatusService).set_house_status_for_rooms(
                hotel_id=hotel_id,
                room_ids=room_type_updation_side_effects.room_ids_for_house_status_update,
            )

        room_stay_change_event = RoomStayHelperService.generate_room_stay_change_event(
            booking_aggregate,
            [room_stay.room_stay_id],
            user_action="update",
        )
        IntegrationEventApplicationService.create_booking_updated_event(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            booking_sub_resource_change_event=room_stay_change_event,
            user_action="update_room_stay_room_type",
        )

        room_stay.refresh_allowed_actions(booking_aggregate._is_soft_booking())

        addon_inventories_to_release = None
        service_cleanup_effects = (
            room_type_updation_side_effects.service_cleanup_effects
        )
        if service_cleanup_effects:
            addon_inventories_to_release = (
                service_cleanup_effects.inventory_blocks_released
            )
            self.inventory_block_repository.update_all(
                service_cleanup_effects.inventory_blocks_released
            )

        if new_room_stay_config != current_room_stay_config:
            self.update_room_stay_helper.update_inventory_on_room_update(
                hotel_id,
                booking_aggregate,
                current_room_stay_config,
                new_room_stay_config,
                room_type_updation_side_effects.new_room_allocation,
                addon_inventories_to_release=addon_inventories_to_release,
                user_action="allocate_room",
            )

        return room_stay, booking_aggregate.current_version()

    def _load_aggregates_for_update(
        self, booking_id, booking_version, room_stay_id, user_data
    ):
        booking_aggregate = self.booking_repository.load_for_update(
            booking_id, user_data=user_data
        )
        booking_aggregate.fail_if_outdated_version(booking_version)
        if booking_aggregate.is_cancelled() or booking_aggregate.is_noshow():
            raise ValidationException(ApplicationErrors.INVALID_BOOKING_FOR_EDIT)

        hotel_id = booking_aggregate.booking.hotel_id
        bill_aggregate = self.bill_repository.load_for_update(
            booking_aggregate.booking.bill_id
        )

        room_stay = booking_aggregate.get_room_stay(room_stay_id)
        if room_stay.is_cancelled() or room_stay.is_noshow():
            raise ValidationException(ApplicationErrors.INVALID_ROOM_FOR_EDIT)

        hotel_aggregate = crs_context_middleware.set_hotel_context(hotel_id)

        hotel_context = crs_context.get_hotel_context()
        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_bill(bill_aggregate)
        return (
            bill_aggregate,
            booking_aggregate,
            hotel_aggregate,
            hotel_context,
            hotel_id,
            room_stay,
        )

    @audit(audit_type=AuditType.ROOM_STAY_ROOM_TYPE_CHANGED)
    def update_room_stay_room_type(
        self,
        booking_aggregate,
        bill_aggregate,
        room_stay,
        new_room_stay_config,
        room_stay_update_dto: RoomStayUpdateDto,
        hotel_aggregate,
        hotel_context,
        user_data,
    ):
        RuleEngine.action_allowed(
            action="update_room_type",
            facts=Facts(
                user_type=user_data.user_type,
                action_payload=room_stay_update_dto.room_stay_update_data,
                booking_aggregate=booking_aggregate,
                hotel_context=hotel_context,
            ),
            fail_on_error=True,
        )

        room_allocation_data = None
        if room_stay_update_dto.new_room_allocation_requested():
            room_aggregate = self.room_repository.load(
                room_id=room_stay_update_dto.get_room_id_to_be_allocated(),
                hotel_id=booking_aggregate.booking.hotel_id,
            )
            room_allocation_data = room_stay_update_dto.create_room_allocation_data(
                room_aggregate
            )

        service_cleanup_effects: Optional[ExpenseServiceCleanupSideEffects] = None
        if room_stay.room_type_id != new_room_stay_config.room_type_id:
            service_cleanup_effects: ExpenseServiceCleanupSideEffects = (
                StayServiceCleanupFacade.clear_room_services(
                    booking_aggregate,
                    bill_aggregate,
                    room_stay.room_stay_id,
                    user_action='update_room_type',
                )
            )

        new_room_allocation, previous_room_allocation = UpdateRoomStayRoomTypeCommand(
            booking_aggregate,
            room_stay,
            bill_aggregate,
            room_stay_update_dto.room_stay_update_data,
            room_allocation_data,
            new_room_stay_config,
            hotel_aggregate,
            self.tax_service,
            self.room_stay_price_change_handler,
            self.ta_commission_helper,
        ).execute()

        room_ids_for_house_status_update = []
        if new_room_allocation or previous_room_allocation:
            room_ids_for_house_status_update = (
                self.update_room_stay_helper.create_and_update_room_allotment(
                    room_stay,
                    new_room_allocation,
                    previous_room_allocation,
                    hotel_context,
                )
            )
        return self.RoomAllocationEffects(
            new_room_allocation,
            room_ids_for_house_status_update,
            service_cleanup_effects,
        )
