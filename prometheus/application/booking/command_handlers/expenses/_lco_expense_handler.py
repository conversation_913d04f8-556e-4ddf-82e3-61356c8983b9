import logging
from typing import Optional

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.application.booking.command_handlers.expenses._base_handler import (
    ExpenseAddonHandlerBase,
    ExpenseCleanupHandlerBase,
)
from prometheus.application.booking.dtos.expense_cleanup_side_effect_dto import (
    ExpenseServiceCleanupSideEffects,
)
from prometheus.application.inventory.command_handlers.update_inventory_block import (
    UpdateInventoryBlockCommandHandler,
)
from prometheus.application.inventory.service.inventory_application_service import (
    InventoryApplicationService,
)
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.domain_events.expense_addon import (
    LCIAdditionEvent,
    LCIRemovalEvent,
)
from prometheus.domain.booking.entities import RoomStay
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.domain_events.domain_event_registry import register_event
from prometheus.domain.inventory.dtos.inventory_block_dto import Inventory<PERSON>lockDTO
from prometheus.domain.inventory.entities.inventory_block import InventoryBlock
from ths_common.constants.booking_constants import ServiceTypes
from ths_common.constants.inventory_constants import InventoryBlockType
from ths_common.exceptions import ValidationException
from ths_common.value_objects import ExpenseServiceContext

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        UpdateInventoryBlockCommandHandler,
        InventoryApplicationService,
    ]
)
class LOCExpenseHandler(ExpenseAddonHandlerBase, ExpenseCleanupHandlerBase):
    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        inventory_block_command_handler: UpdateInventoryBlockCommandHandler,
        inventory_application_service: InventoryApplicationService,
    ):
        self.booking_repository: BookingRepository = booking_repository
        self.bill_repository: BillRepository = bill_repository
        self.inventory_block_command_handler: UpdateInventoryBlockCommandHandler = (
            inventory_block_command_handler
        )
        self.inventory_application_service: InventoryApplicationService = (
            inventory_application_service
        )

    @staticmethod
    def get_service_type():
        return ServiceTypes.LATE_CHECKOUT

    def add_service(
        self,
        booking_aggregate: BookingAggregate,
        bill_aggregate: BillAggregate,
        expense_data: dict,
        hotel_context,
    ):
        room_stay_id = expense_data.get("room_stay_id")
        hotel_id = booking_aggregate.hotel_id
        booking_id = booking_aggregate.booking_id

        service_context: ExpenseServiceContext = expense_data.get("service_context")

        room_stay: RoomStay = booking_aggregate.get_room_stay(room_stay_id)

        self._validate_expense_data(booking_aggregate, expense_data, room_stay)

        inventory_block_id = service_context.service_details.inventory_block_id

        inventory_block_data = InventoryBlockDTO(
            room_type_id=room_stay.room_type_id,
            start_date=dateutils.to_date(room_stay.checkout_date),
            end_date=dateutils.to_date(dateutils.add(room_stay.checkout_date, days=1)),
            room_stay_id=room_stay.room_stay_id,
            block_type=InventoryBlockType.LATE_CHECKOUT_BLOCK,
        )

        inventory_block: InventoryBlock = (
            self.inventory_application_service.resolve_inventory_block(
                booking_id,
                inventory_block_data,
                inventory_block_id,
                hotel_id,
                user_action='late_checkout_added',
            )
        )

        service_context.service_details.inventory_block_id = inventory_block.block_id

        register_event(LCIAdditionEvent(booking_id, room_stay_id))

        self.booking_repository.update(booking_aggregate)
        self.bill_repository.update(bill_aggregate)

        return inventory_block

    def _validate_expense_data(
        self,
        booking_aggregate: BookingAggregate,
        expense_data: dict,
        room_stay: RoomStay,
    ):
        if room_stay.is_checked_out() or not room_stay.is_active():
            raise ValidationException(
                description="Invalid room stay state for LCO expense"
            )

        expenses = booking_aggregate.get_active_expenses_for_service_type(
            room_stay.room_stay_id,
            service_type=self.get_service_type(),
        )

        if expenses:
            raise ValidationException(
                description="LCO Expense already exists for this room stay"
            )

        skus = expense_data.get("skus", [])

        if len(skus) > 1:
            raise ValidationException(
                description="Multiple skus are not allowed for LCO expense"
            )

        sku = skus[0]
        date_wise_prices = sku.get("date_wise_prices", [])

        if len(date_wise_prices) > 1:
            raise ValidationException(
                description="Multiple date wise prices are not allowed for LCO expense"
            )

        price_details = date_wise_prices[0]

        quantity = price_details.get("quantity")

        if quantity > 1:
            raise ValidationException(
                description="Quantity greater than 1 is not allowed for LCO expense"
            )

        applicable_date = price_details.get("applicable_date")

        if dateutils.to_date(applicable_date) != dateutils.to_date(
            room_stay.checkout_date
        ):
            raise ValidationException(
                description="For LCO applicable date should be same as checkout date"
            )

    def cleanup_for_room_stay(
        self,
        booking_aggregate: BookingAggregate,
        bill_aggregate,
        room_stay_id,
        user_action=None,
    ) -> Optional[ExpenseServiceCleanupSideEffects]:
        expenses = booking_aggregate.get_active_expenses_for_service_type(
            room_stay_id,
            service_type=self.get_service_type(),
        )
        if not expenses:
            return

        expense = expenses[0]
        return self._perform_cleanup(
            booking_aggregate,
            bill_aggregate,
            expense,
            user_action,
        )

    def cleanup_for_expense(
        self,
        booking_aggregate,
        bill_aggregate,
        expense,
        user_action=None,
    ) -> Optional[ExpenseServiceCleanupSideEffects]:
        if expense.service_context.service_type == self.get_service_type():
            return self._perform_cleanup(
                booking_aggregate,
                bill_aggregate,
                expense,
                user_action,
            )

    def _perform_cleanup(
        self,
        booking_aggregate,
        bill_aggregate,
        expense,
        user_action,
    ) -> Optional[ExpenseServiceCleanupSideEffects]:
        bill_aggregate.cancel_charges([expense.charge_id])
        booking_aggregate.cancel_expense(expense.expense_id)

        released_blocks = self.inventory_block_command_handler.release_inventory_blocks(
            [expense.service_context.service_details.inventory_block_id],
            booking_aggregate.hotel_id,
            user_action=user_action,
            apply_inventory_updates=False,
        )

        register_event(
            LCIRemovalEvent(
                booking_aggregate.booking_id,
                expense.room_stay_id,
            )
        )

        return ExpenseServiceCleanupSideEffects(
            inventory_blocks_released=released_blocks,
        )
