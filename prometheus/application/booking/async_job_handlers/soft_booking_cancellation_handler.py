import logging
import os

from flask import current_app as app
from treebo_commons.money import Money
from treebo_commons.request_tracing.context import get_current_tenant_id
from treebo_commons.utils import dateutils

from object_registry import locate_instance, register_instance
from prometheus import crs_context
from prometheus.application.booking.command_handlers.state_transitions.cancel_booking_action import (
    CancelBookingActionCommandHandler,
)
from prometheus.application.decorators import session_manager
from prometheus.application.housekeeping.house_status_service import HouseStatusService
from prometheus.application.inventory.service.inventory_application_service import (
    InventoryApplicationService,
)
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.application.services.room_stay_overflow_service import (
    RoomStayOverflowService,
)
from prometheus.async_job.job_result_dto import JobResultDto
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.dtos.b2b_booking_confirmation import B2BBookingConfirmationDto
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.booking.repositories.room_stay_overflow_repository import (
    RoomStayOverflowRepository,
)
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.inventory_requirement_service import (
    InventoryRequirementService,
)
from prometheus.domain.inventory.repositories.room_type_inventory_repository import (
    RoomTypeInventoryRepository,
)
from prometheus.infrastructure.alerting.newrelic_service_client import (
    NewrelicServiceClient,
)
from prometheus.infrastructure.external_clients.athena_service_client import (
    AthenaServiceClient,
)
from prometheus.infrastructure.external_clients.notification_service_client import (
    NotificationEmailIds,
    NotificationServiceClient,
)

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        RoomTypeInventoryRepository,
        NewrelicServiceClient,
        HotelRepository,
        HotelConfigRepository,
        CancelBookingActionCommandHandler,
        RoomStayOverflowRepository,
        JobSchedulerService,
        RoomStayOverflowService,
        InventoryApplicationService,
        AthenaServiceClient,
    ]
)
class SoftBookingCancellationHandler(object):
    def __init__(
        self,
        booking_repository,
        bill_repository,
        room_type_inventory_repository,
        alerting_service,
        hotel_repository,
        hotel_config_repository,
        cancel_booking_command_handler: CancelBookingActionCommandHandler,
        room_stay_overflow_repository,
        job_scheduler_service,
        room_stay_overflow_service,
        inventory_service: InventoryApplicationService,
        athena_client: AthenaServiceClient,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.inventory_requirement_service = InventoryRequirementService()
        self.room_type_inventory_repository = room_type_inventory_repository
        self.alerting_service = alerting_service
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.cancel_booking_command_handler = cancel_booking_command_handler
        self.room_stay_overflow_repository = room_stay_overflow_repository
        self.job_scheduler_service = job_scheduler_service
        self.room_stay_overflow_service = room_stay_overflow_service
        self.inventory_service = inventory_service
        self.athena_client = athena_client

    @session_manager(commit=True)
    def cancel_soft_blocked_booking_if_not_confirmed(
        self, booking_id=None, hold_till=None
    ) -> JobResultDto:
        booking_aggregate = self.booking_repository.load_for_update(booking_id)
        hotel_aggregate = self.hotel_repository.load(booking_aggregate.booking.hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(
            booking_aggregate.booking.hotel_id
        )
        crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
        )
        crs_context.set_current_booking(booking_aggregate)

        old_hold_till = dateutils.isoformat_str_to_datetime(hold_till)
        if not booking_aggregate.booking.hold_till == old_hold_till:
            logger.debug(
                "Ignoring the job: cancel_soft_blocked_booking_if_not_confirmed. Hold till value has changed. "
                "Older value: %s, current value: %s",
                old_hold_till,
                booking_aggregate.booking.hold_till,
            )
            return JobResultDto.success()

        if booking_aggregate.is_confirmed() or booking_aggregate.is_cancelled():
            return JobResultDto.success()

        bill_aggregate = self.bill_repository.load(booking_aggregate.bill_id)
        if booking_aggregate.is_temporary() and bill_aggregate.net_paid_amount > Money(
            '0', crs_context.hotel_context.base_currency
        ):
            # Booking is not confirmed yet, but some payment has been added. Mark this booking as confirmed
            booking_aggregate.confirm_booking()
            self.booking_repository.update(booking_aggregate)
            IntegrationEventApplicationService.create_booking_updated_event(
                booking_aggregate=booking_aggregate,
                user_action="cancel_soft_blocked_booking",
            )
            self.job_scheduler_service.schedule_web_checkin_notification(
                booking_aggregate.booking.checkin_date.date(),
                booking_aggregate.booking_id,
                hotel_id=booking_aggregate.hotel_id,
            )
            return JobResultDto.success()
        if booking_aggregate.is_primus_booking():
            self.job_scheduler_service.schedule_booking_confirmation_in_b2b(
                booking_aggregate
            )
            return JobResultDto.success()
        logger.debug(
            "Booking not confirmed yet. Current Time: %s, Hold Till: %s. Cancelling the booking",
            dateutils.current_datetime(),
            booking_aggregate.booking.hold_till,
        )

        bill_aggregate = self.bill_repository.load_for_update(
            booking_aggregate.booking.bill_id
        )
        (
            room_stay_configs,
            _,
            _,
            _,
            _,
            _,
            room_ids_for_house_status_update,
            _,
            _,
        ) = self.cancel_booking_command_handler.cancel_booking(
            booking_aggregate,
            bill_aggregate,
            cancel_action_payload=dict(cancellation_reason="Soft Booking expired"),
        )

        self.booking_repository.update(booking_aggregate)
        self.bill_repository.update(bill_aggregate)

        if room_ids_for_house_status_update:
            locate_instance(HouseStatusService).set_house_status_for_rooms(
                hotel_id=booking_aggregate.hotel_id,
                room_ids=room_ids_for_house_status_update,
            )

        IntegrationEventApplicationService.create_booking_updated_event(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            user_action="cancel_soft_blocked_booking",
        )

        if room_stay_configs:
            hotel_id = booking_aggregate.booking.hotel_id
            old_inventory_requirement = (
                self.inventory_requirement_service.build_inventory_requirement(
                    hotel_id, room_stay_configs
                )
            )

            self.inventory_service.update_inventory(
                release_inventory=old_inventory_requirement,
                user_action="cancel_soft_blocked_booking",
            )

            self.room_stay_overflow_service.recompute_and_unmark_overflows(
                hotel_aggregate.hotel.hotel_id,
                old_inventory_requirement.min_date,
                old_inventory_requirement.max_date,
                old_inventory_requirement.room_type_ids,
                user_action="cancel_soft_blocked_booking",
            )

        if booking_aggregate.is_primus_booking():
            event_payload = dict(
                booking_reference_id=booking_aggregate.booking.reference_number,
                hotel_id=booking_aggregate.booking.hotel_id,
            )
            self.email_soft_booking_expiry_event(event_payload)

        return JobResultDto.success()

    def confirm_b2b_booking(self, booking_reference_number):
        confirmation_payload = B2BBookingConfirmationDto(
            booking_id=booking_reference_number
        )
        try:
            self.athena_client.confirm_booking(confirmation_payload)
        except Exception as e:
            logger.exception("Unable to confirm b2b booking")

    @staticmethod
    def email_soft_booking_expiry_event(event_payload):
        try:
            send_to = app.config['B2B_SOFT_BOOKING_EXPIRY_EMAIL_RECEIVER_LIST']
            subject = f"[IMPORTANT] Primus Soft booking expiry: {event_payload['booking_reference_id']}"
            html = (
                f"Hi,<br/><br/>[IMPORTANT] Primus soft booking expired (Tenant: {get_current_tenant_id()})"
                f" CRS ({os.environ.get('APP_ENV', 'local')}) Booking: ({event_payload['booking_reference_id']})"
                f" Hotel ({event_payload['hotel_id']})<br><br>"
            )
            NotificationServiceClient().email(
                body_html=html,
                subject=subject,
                sender=NotificationEmailIds.NOREPLY.value,
                recievers=[send_to],
            )
        except Exception as e:
            logger.critical("Unable to send soft booking expiry email alerts")
