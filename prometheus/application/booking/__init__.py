from object_registry import locate_instance
from prometheus.application.booking.async_job_handlers.email_invoice_on_checkout import (
    EmailInvoicesAndCreditNotesHandler,
)
from prometheus.application.booking.async_job_handlers.eregcard_url_generation import (
    ERegCardUrlGenerationJobHandler,
)
from prometheus.application.booking.async_job_handlers.incomplete_booking_reminder import (
    IncompleteBookingHandler,
)
from prometheus.application.booking.async_job_handlers.soft_booking_cancellation_handler import (
    SoftBookingCancellationHandler,
)
from prometheus.application.booking.command_handlers.invoice.generate_invoice_template import (
    GenerateInvoiceTemplateCommandHandler,
)
from prometheus.async_job.job_registry import JobRegistry
from ths_common.constants.scheduled_job_constants import JobName

job_registry = locate_instance(JobRegistry)

job_registry.register(
    JobName.INVOICE_TEMPLATE_GENERATION.value,
    locate_instance(GenerateInvoiceTemplateCommandHandler).handle,
)
job_registry.register(
    JobName.GENERATE_EREGCARD_URL.value,
    locate_instance(ERegCardUrlGenerationJobHandler).execute_eregcard_url_generation,
)
job_registry.register(
    JobName.SOFT_BOOKING_CANCELLATION_JOB_NAME.value,
    locate_instance(
        SoftBookingCancellationHandler
    ).cancel_soft_blocked_booking_if_not_confirmed,
)
job_registry.register(
    JobName.SCHEDULE_INVOICE_EMAIL_ON_CHECKOUT.value,
    locate_instance(
        EmailInvoicesAndCreditNotesHandler
    ).email_invoices_and_credit_notes_to_booker,
)

job_registry.register(
    JobName.SCHEDULE_BOOKING_CONFIRMATION_IN_B2B.value,
    locate_instance(SoftBookingCancellationHandler).confirm_b2b_booking,
)
job_registry.register(
    JobName.SCHEDULE_INCOMPLETE_BOOKING_COMMUNICATION.value,
    locate_instance(
        IncompleteBookingHandler
    ).send_and_schedule_incomplete_booking_communication,
)
job_registry.register(
    JobName.INCOMPLETE_BOOKING_REMINDER.value,
    locate_instance(IncompleteBookingHandler).send_incomplete_booking_reminder,
)
