from object_registry import register_instance
from prometheus.application.cashiering.helpers.auth_helper import (
    fail_if_user_not_authorized_to_access_cashier_module,
)
from prometheus.application.decorators import set_hotel_context
from prometheus.domain.billing.repositories import CashierSessionRepository
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)


@register_instance(dependencies=[CashierSessionRepository, AwsServiceClient])
class GetCashierSessionPaymentsQueryHandler:
    DefaultSignedUrlExpirationSeconds = 7200

    def __init__(
        self, cashier_session_repository: CashierSessionRepository, aws_service_client
    ):
        self.cashier_session_repository = cashier_session_repository
        self.aws_service_client = aws_service_client

    @set_hotel_context()
    def handle(self, cashier_session_id, user_data=None, hotel_aggregate=None):
        fail_if_user_not_authorized_to_access_cashier_module(user_data)
        cashier_session_aggregate = self.cashier_session_repository.load(
            cashier_session_id
        )
        payments = self._attach_payments_signed_url(cashier_session_aggregate.payments)
        return payments

    def _attach_payments_signed_url(self, cashier_payments):
        for cashier_payment in cashier_payments:
            if cashier_payment.voucher_url:
                cashier_payment.set_voucher_signed_url(
                    self.aws_service_client.get_presigned_url_from_s3_url(
                        cashier_payment.voucher_url,
                        link_expires_in=self.DefaultSignedUrlExpirationSeconds,
                    )
                )
        return cashier_payments
