from object_registry import register_instance
from prometheus.application.cashiering.helpers.auth_helper import (
    fail_if_user_not_authorized_to_access_cashier_module,
)
from prometheus.application.decorators import set_hotel_context
from prometheus.domain.billing.repositories import CashierSessionRepository
from prometheus.domain.billing.repositories.cash_register_repository import (
    CashRegisterRepository,
)
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)


@register_instance(
    dependencies=[CashierSessionRepository, AwsServiceClient, CashRegisterRepository]
)
class GetOpenCashierSessionsQueryHandler:
    DefaultSignedUrlExpirationSeconds = 7200

    def __init__(
        self,
        cashier_session_repository: CashierSessionRepository,
        aws_service_client,
        cash_register_repository,
    ):
        self.cashier_session_repository = cashier_session_repository
        self.aws_service_client = aws_service_client
        self.cash_register_repository = cash_register_repository

    @set_hotel_context()
    def latest_cashier_session(self, cash_register_id, hotel_aggregate=None):
        cashier_session_aggregate = (
            self.cashier_session_repository.get_latest_cashier_session(cash_register_id)
        )
        if not cashier_session_aggregate:
            return 0, None
        return (
            int(cashier_session_aggregate.cashier_session.session_number),
            cashier_session_aggregate,
        )

    @set_hotel_context()
    def handle(self, vendor_id, user_data=None, hotel_aggregate=None):
        fail_if_user_not_authorized_to_access_cashier_module(user_data)
        cash_register_aggregate = (
            self.cash_register_repository.get_cash_register_for_vendor(vendor_id)
        )

        latest_cashier_session_aggregate = (
            self.cashier_session_repository.get_latest_cashier_session(
                cash_register_aggregate.cash_register.cash_register_id
            )
        )
        if (
            latest_cashier_session_aggregate
            and latest_cashier_session_aggregate.cashier_session.is_closed()
        ):
            return None
        return latest_cashier_session_aggregate
