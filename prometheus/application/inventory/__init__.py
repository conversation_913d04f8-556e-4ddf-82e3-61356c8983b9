from object_registry import locate_instance
from prometheus.application.inventory.async_job_handlers.inventory_block_handler import (
    InventoryBlockAsyncJobHandler,
)
from prometheus.async_job.job_registry import JobRegistry
from ths_common.constants.scheduled_job_constants import JobName

job_registry = locate_instance(JobRegistry)

job_registry.register(
    JobName.RELEASE_INVENTORY.value,
    locate_instance(InventoryBlockAsyncJobHandler).expire_inventory_soft_blocks,
)

job_registry.register(
    JobName.FULFILL_VIRTUAL_INVENTORY_BLOCKS.value,
    locate_instance(InventoryBlockAsyncJobHandler).fulfill_virtual_blocks,
)
