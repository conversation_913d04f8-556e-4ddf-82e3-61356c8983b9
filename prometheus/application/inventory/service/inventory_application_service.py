import datetime
import logging
from collections import Counter, defaultdict
from typing import List

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit_dnr
from prometheus.application.inventory.command_handlers.mark_dnrs import (
    MarkDNRs<PERSON>ommandHandler,
)
from prometheus.application.inventory.command_handlers.resolve_dnr import (
    ResolveDNRCommandHandler,
)
from prometheus.application.inventory.command_handlers.sync_inventories import (
    SyncInventoriesCommandHandler,
)
from prometheus.application.inventory.helpers import missing_inventory_creator
from prometheus.application.inventory.query_handlers.get_dnrs import GetDNRsQueryHandler
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.application.services.room_stay_overflow_service import (
    RoomStayOverflowService,
)
from prometheus.async_job.job_registry import JobRegistry
from prometheus.async_job.job_result_dto import JobResultDto
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.booking.repositories.room_stay_overflow_repository import (
    RoomStayOverflowRepository,
)
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.catalog.repositories.room_repository import RoomRepository
from prometheus.domain.catalog.repositories.room_type_repository import (
    RoomTypeRepository,
)
from prometheus.domain.domain_events.domain_event_registry import (
    register_room_status_change_domain_event,
)
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.domain_events.room_status_changed_event import (
    RoomStatusChangedEvent,
)
from prometheus.domain.inventory.dtos.inventory_block_dto import InventoryBlockDTO
from prometheus.domain.inventory.dtos.mark_dnr_data import MarkDnrData
from prometheus.domain.inventory.entities.inventory_block import InventoryBlock
from prometheus.domain.inventory.entities.room_allotment import RoomAllotment
from prometheus.domain.inventory.errors import InventoryError
from prometheus.domain.inventory.exceptions import InventoryException
from prometheus.domain.inventory.factories.room_allotment_factory import (
    RoomAllotmentFactory,
)
from prometheus.domain.inventory.inventory_requirement import InventoryRequirement
from prometheus.domain.inventory.repositories.dnr_audit_trail_repository import (
    DNRAuditTrailRepository,
)
from prometheus.domain.inventory.repositories.dnr_repository import DNRRepository
from prometheus.domain.inventory.repositories.inventory_block_repository import (
    InventoryBlockRepository,
)
from prometheus.domain.inventory.repositories.room_allotment_repository import (
    RoomAllotmentRepository,
)
from prometheus.domain.inventory.repositories.room_type_inventory_repository import (
    RoomTypeInventoryRepository,
)
from prometheus.domain.inventory.services.dnr_service import DNRService
from prometheus.domain.inventory.services.room_type_inventory_service import (
    RoomTypeInventoryService,
)
from prometheus.infrastructure.alerting.newrelic_service_client import (
    NewrelicServiceClient,
)
from ths_common.constants.audit_trail_constants import DNRAuditType
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.constants.inventory_constants import (
    AllottedFor,
    DNRSource,
    DNRSubType,
    DNRType,
    HouseKeepingStatus,
    InventoryBlockStatus,
    RoomCurrentStatus,
    RoomReservationStatus,
)
from ths_common.constants.scheduled_job_constants import JobName
from ths_common.exceptions import InvalidInventoryBlock
from ths_common.utils.common_utils import merge_dict
from ths_common.utils.id_generator_utils import random_id_generator
from ths_common.value_objects import DateRange

logger = logging.getLogger(__name__)


class RoomAllocationInfo(object):
    def __init__(self, room_allotment_id, room_number):
        self.room_allotment_id = room_allotment_id
        self.room_number = room_number


@register_instance(
    dependencies=[
        RoomTypeInventoryRepository,
        BookingRepository,
        RoomRepository,
        HotelRepository,
        RoomTypeRepository,
        DNRService,
        NewrelicServiceClient,
        DNRRepository,
        DNRAuditTrailRepository,
        JobRegistry,
        JobSchedulerService,
        RoomStayOverflowRepository,
        RoomStayOverflowService,
        InventoryBlockRepository,
        GetDNRsQueryHandler,
        MarkDNRsCommandHandler,
        ResolveDNRCommandHandler,
        SyncInventoriesCommandHandler,
    ]
)
class InventoryApplicationService(object):
    """
    A service to manage inventories
    """

    def __init__(
        self,
        room_type_inventory_repository,
        booking_repository,
        room_repository,
        hotel_repository,
        room_type_repository,
        dnr_service,
        alerting_service,
        dnr_repository,
        dnr_audit_trail_repository,
        job_registry,
        job_scheduler_service,
        room_stay_overflow_repository,
        room_stay_overflow_service,
        inventory_block_repository,
        get_dnrs_query_handler: GetDNRsQueryHandler,
        mark_dnrs_command_handler: MarkDNRsCommandHandler,
        resolve_dnr_command_handler: ResolveDNRCommandHandler,
        sync_inventories_command_handler: SyncInventoriesCommandHandler,
    ):
        self.room_type_inventory_repository = room_type_inventory_repository
        self.booking_repository = booking_repository
        self.room_repository = room_repository
        self.hotel_repository = hotel_repository
        self.room_type_repository = room_type_repository
        self.dnr_service = dnr_service
        self.alerting_service = alerting_service
        self.hotel_config_repository = HotelConfigRepository()
        self.room_allotment_repository = RoomAllotmentRepository()
        self.dnr_repository = dnr_repository
        self.dnr_audit_trail_repository = dnr_audit_trail_repository
        self.job_scheduler_service = job_scheduler_service
        self.room_stay_overflow_repository = room_stay_overflow_repository
        job_registry.register(
            JobName.BULK_SYNC_INVENTORY_ASYNC_JOB_NAME.value,
            self.sync_inventories_for_given_date_strings,
        )
        self.room_stay_overflow_service = room_stay_overflow_service
        # TODO: Get rid of these handlers dependencies
        self.inventory_block_repository = inventory_block_repository
        self.get_dnrs_query_handler = get_dnrs_query_handler
        self.mark_dnrs_command_handler = mark_dnrs_command_handler
        self.resolve_dnr_command_handler = resolve_dnr_command_handler
        self.sync_inventories_command_handler = sync_inventories_command_handler

    def sync_inventories_for_given_date_strings(
        self, hotel_id, start_date, end_date
    ) -> JobResultDto:
        from_date = dateutils.isoformat_str_to_datetime(start_date).date()
        to_date = dateutils.isoformat_str_to_datetime(end_date).date()
        self.sync_inventories_command_handler.handle(
            hotel_id=hotel_id, from_date=from_date, to_date=to_date
        )
        return JobResultDto.success()

    def update_room_type_inventory_availabilities(
        self, hotel_aggregate, new_room_aggregates
    ):
        hotel_id = hotel_aggregate.hotel_id
        room_types = [
            room_aggregate.room.room_type_id for room_aggregate in new_room_aggregates
        ]
        room_type_count_added = dict(Counter(room_types))

        start_date = dateutils.current_date()
        room_type_ids = []
        for room_type_id, count in room_type_count_added.items():
            if self.room_type_inventory_repository.availability_exists(
                hotel_id, room_type_id, start_date
            ):
                self.room_type_inventory_repository.increment_availability(
                    hotel_id, room_type_id, start_date, count
                )
            else:
                room_type_inventory_aggregates = (
                    missing_inventory_creator.create_missing_availabilities(
                        hotel_aggregate,
                        {room_type_id: count},
                        start_date,
                        dateutils.add(start_date, months=11),
                        [],
                    )
                )
                self.room_type_inventory_repository.update_all(
                    room_type_inventory_aggregates.values()
                )

            room_type_ids.append(room_type_id)

        room_type_inventory_availabilities = (
            self.room_type_inventory_repository.fetch_availability(
                hotel_id, room_type_ids, start_date
            )
        )
        grouped_availabilities = defaultdict(lambda: defaultdict(list))
        for availability in room_type_inventory_availabilities:
            grouped_availabilities[availability.room_type_id][
                availability.date
            ] = availability

        IntegrationEventApplicationService.create_room_type_inventories_events(
            hotel_id, grouped_availabilities
        )

    # TODO: Called from catalog application service and mis report
    def get_dnrs(self, hotel_id, get_dnr_data: dict):
        return self.get_dnrs_query_handler.handle(hotel_id, get_dnr_data)

    # TODO: Called from catalog application service
    def mark_dnr_for_inactive_room(
        self, hotel_aggregate, room_id, room_type_id, user_data, business_date
    ):
        room_aggregate = self.room_repository.load(
            room_id, hotel_aggregate.hotel.hotel_id
        )
        room_allotment_aggregate = self.room_allotment_repository.load_for_update(
            hotel_id=hotel_aggregate.hotel.hotel_id, room_id=room_id
        )
        if not room_allotment_aggregate:
            room_allotment_aggregate = RoomAllotmentFactory.create_empty_allotment(
                hotel_id=hotel_aggregate.hotel.hotel_id, room_id=room_id
            )

        date_ranges_to_mark_dnr = (
            self._get_date_ranges_to_mark_dnr_for_inactive_room_starting_today(
                room_allotment_aggregate,
                hotel_aggregate.hotel_id,
                room_type_id,
                business_date,
            )
        )

        room_type_inventory_aggregates = (
            self.room_type_inventory_repository.load_for_update(
                hotel_id=hotel_aggregate.hotel.hotel_id,
                room_type_ids=[room_aggregate.room.room_type_id],
                from_date=date_ranges_to_mark_dnr[0].start_date,
                to_date=date_ranges_to_mark_dnr[
                    len(date_ranges_to_mark_dnr) - 1
                ].end_date,
            )
        )
        room_type_inventory_aggregate = (
            room_type_inventory_aggregates[0]
            if room_type_inventory_aggregates
            else None
        )

        for date_range in date_ranges_to_mark_dnr:
            mark_dnr_data = MarkDnrData(
                room_id,
                date_range.start_date,
                date_range.end_date,
                DNRSource.CRS_WEB,
                DNRType.INACTIVE_ROOM,
                DNRSubType.INACTIVE_ROOM,
                'Backend System',
                'Room is not active',
            )

            (
                dnr_aggregate,
                date_wise_room_type_availability_change,
            ) = self.mark_dnrs_command_handler.create_dnr(
                mark_dnr_data.json(),
                hotel_aggregate,
                room_aggregate,
                room_allotment_aggregate,
                room_type_inventory_aggregate,
                user_data,
            )

            IntegrationEventApplicationService.create_dnr_events(
                hotel_aggregate.hotel_id,
                dnr_aggregate,
                {
                    room_aggregate.room.room_type_id: date_wise_room_type_availability_change
                },
                event_type=IntegrationEventType.DNR_SET,
                user_action="mark_dnr",
            )

        self.room_allotment_repository.update(room_allotment_aggregate)
        self.room_type_inventory_repository.update(room_type_inventory_aggregate)

    def _get_date_ranges_to_mark_dnr_for_inactive_room_starting_today(
        self, room_allotment_aggregate, hotel_id, room_type_id, business_date
    ):
        # Default list of date_ranges starts today
        from_date = business_date

        # If there is room stay allotment crossing today, then start_date is extended till the end of the last room
        # stay allotment. In which case, we'll only mark DNR for inactive room, after the end date of last room stay
        # allotment
        last_allotment = room_allotment_aggregate.get_last_room_allotment(
            allotted_for=AllottedFor.STAY
        )
        if last_allotment and dateutils.to_date(last_allotment.end_time) > from_date:
            from_date = dateutils.to_date(last_allotment.end_time)

        dnr_allotments_after_from_date = (
            room_allotment_aggregate.get_all_dnr_allotments_after_date(from_date)
        )

        sorted_room_allotments = sorted(
            dnr_allotments_after_from_date, key=lambda ra: ra.start_time
        )

        date_range_to_mark_dnr = []
        for room_allotment in sorted_room_allotments:
            to_date = dateutils.to_date(room_allotment.start_date)
            date_range_to_mark_dnr.append(DateRange(from_date, to_date))
            from_date = to_date

        last_room_type_inventory_date = self.room_type_inventory_repository.fetch_last_date_with_inventory_availability(
            hotel_id, room_type_id
        )

        if last_room_type_inventory_date and last_room_type_inventory_date > from_date:
            date_range_to_mark_dnr.append(
                DateRange(from_date, last_room_type_inventory_date)
            )

        return date_range_to_mark_dnr

    # TODO: Called from catalog application service
    @audit_dnr(audit_type=DNRAuditType.DNR_RESOLVED)
    def remove_dnr_for_inactive_room(self, hotel_aggregate, room_aggregate, user_data):
        crs_context.set_hotel_context(hotel_aggregate)
        dnr_aggregates = self.dnr_repository.load_inactive_room_dnrs_for_update(
            hotel_aggregate.hotel.hotel_id,
            [room_aggregate.room.room_id],
            from_date=dateutils.current_date(),
            source=DNRSource.CRS_WEB,
            types=[DNRType.INACTIVE_ROOM],
        )

        max_date_range = DateRange(
            dnr_aggregates[len(dnr_aggregates) - 1].dnr.start_date,
            dnr_aggregates[0].dnr.end_date,
        )

        room_allotment_aggregate = self.room_allotment_repository.load_for_update(
            hotel_id=hotel_aggregate.hotel.hotel_id, room_id=room_aggregate.room.room_id
        )

        room_type_inventory_aggregates = (
            self.room_type_inventory_repository.load_multiple(
                hotel_id=hotel_aggregate.hotel.hotel_id,
                room_type_ids=[room_aggregate.room.room_type_id],
                from_date=max_date_range.start_date,
                to_date=max_date_range.end_date,
            )
        )
        room_type_inventory_aggregate = (
            room_type_inventory_aggregates[0]
            if room_type_inventory_aggregates
            else None
        )

        date_wise_room_type_availability_changes = dict()
        for dnr_aggregate in dnr_aggregates:
            date_wise_room_type_availability_change = (
                self.resolve_dnr_command_handler.resolve_dnr(
                    dnr_aggregate,
                    room_allotment_aggregate,
                    room_type_inventory_aggregate,
                    user_data,
                )
            )
            date_wise_room_type_availability_changes.update(
                date_wise_room_type_availability_change
            )

        self.room_allotment_repository.update(room_allotment_aggregate)
        self.dnr_repository.update_all(dnr_aggregates)
        self.room_type_inventory_repository.update(room_type_inventory_aggregate)

        for dnr_aggregate in dnr_aggregates:
            IntegrationEventApplicationService.create_dnr_events(
                hotel_aggregate.hotel.hotel_id,
                dnr_aggregate,
                {
                    room_aggregate.room.room_type_id: date_wise_room_type_availability_changes
                },
                event_type=IntegrationEventType.DNR_RELEASED,
                user_action="resolve_dnr",
            )

    @staticmethod
    def _over_booking_alert_messages(room_type_inventory_aggregates):
        messages = []
        for room_type_inventory_aggregate in room_type_inventory_aggregates:
            for (
                room_type_inventory_availability
            ) in room_type_inventory_aggregate.room_type_inventory_availabilities:
                if room_type_inventory_availability.actual_count < 0:
                    messages.append(
                        {
                            "hotel_id": room_type_inventory_aggregate.room_type_inventory.hotel_id,
                            "room_type_id": room_type_inventory_aggregate.room_type_inventory.room_type_id,
                            "date": str(room_type_inventory_availability.date),
                        }
                    )
        return messages

    def update_inventory(
        self,
        block_inventory: InventoryRequirement = None,
        release_inventory: InventoryRequirement = None,
        allow_overbooking=False,
        user_action=None,
    ):
        if not block_inventory and not release_inventory:
            return

        hotel_id = (
            block_inventory.hotel_id if block_inventory else release_inventory.hotel_id
        )
        room_type_inventory_aggregates = (
            self.room_type_inventory_repository.load_broadest_inventory_for_update(
                hotel_id, block_inventory, release_inventory
            )
        )

        updated_room_type_inventory_availabilities = dict()
        if release_inventory and len(release_inventory) > 0:
            updated_room_type_inventory_availabilities = (
                RoomTypeInventoryService.release_inventory(
                    release_inventory, room_type_inventory_aggregates
                )
            )

        if block_inventory and len(block_inventory) > 0:
            decreased_room_type_inventory_availabilities = (
                RoomTypeInventoryService.block_inventory(
                    block_inventory,
                    room_type_inventory_aggregates,
                    allow_overbooking=allow_overbooking,
                )
            )

            # Merge decreased inventory onto increased inventory, to get final updated inventory
            updated_room_type_inventory_availabilities = merge_dict(
                updated_room_type_inventory_availabilities,
                decreased_room_type_inventory_availabilities,
            )

        over_booking_messages = []
        if updated_room_type_inventory_availabilities:
            over_booking_messages = self._over_booking_alert_messages(
                room_type_inventory_aggregates
            )
            if self.alerting_service is not None:
                for message in over_booking_messages:
                    self.alerting_service.record_event("overbooking", message)
            self.room_type_inventory_repository.update_all(
                room_type_inventory_aggregates
            )
            IntegrationEventApplicationService.create_room_type_inventories_events(
                hotel_id,
                updated_room_type_inventory_availabilities,
                user_action=user_action,
            )
        return over_booking_messages

    def allot_room_inventory(
        self,
        hotel_id,
        room_id,
        start_time: datetime.datetime,
        expected_end_time: datetime.datetime,
        hotel_context,
        is_booking_checked_in,
    ):
        room_allotment_aggregate = self.room_allotment_repository.load_for_update(
            hotel_id=hotel_id, room_id=room_id
        )
        allotted_for = (
            AllottedFor.STAY if is_booking_checked_in else AllottedFor.FUTURE_STAY
        )
        actual_start_time = None
        if is_booking_checked_in:
            # Storing actual start time in case of checkin only
            actual_start_time = self._get_actual_start_time(hotel_context)

        if allotted_for == AllottedFor.STAY:
            if (
                room_allotment_aggregate.housekeeping_record.housekeeping_status
                == HouseKeepingStatus.OUT_OF_ORDER
            ):
                raise InventoryException(
                    error=InventoryError.OUT_OF_ORDER_ROOM_UNAVAILABLE_FOR_ALLOCATION
                )
            if (
                room_allotment_aggregate.room_inventory.current_status
                == RoomCurrentStatus.OCCUPIED
            ):
                raise InventoryException(error=InventoryError.ROOM_ALREADY_OCCUPIED)
        if not room_allotment_aggregate:
            room_allotment_aggregate = RoomAllotmentFactory.create_new_allotment(
                hotel_id=hotel_id,
                room_id=room_id,
                start_time=start_time,
                actual_start_time=actual_start_time,
                expected_end_time=expected_end_time,
                allotted_for=allotted_for,
            )
            self.room_allotment_repository.save(room_allotment_aggregate)
            allotment = room_allotment_aggregate.room_allotments[0]
        else:
            allotment = room_allotment_aggregate.add_stay_allotment(
                start_time=start_time,
                actual_start_time=actual_start_time,
                expected_end_time=expected_end_time,
                hotel_context=hotel_context,
                allotted_for=allotted_for,
            )

        if is_booking_checked_in:
            old_room_status = room_allotment_aggregate.room_inventory.current_status
            room_allotment_aggregate.update_current_status(RoomCurrentStatus.OCCUPIED)
            room_allotment_aggregate.update_reservation_status(
                RoomReservationStatus.ARRIVED
            )

            if (
                old_room_status
                != room_allotment_aggregate.room_inventory.current_status
            ):
                register_room_status_change_domain_event(
                    RoomStatusChangedEvent(
                        hotel_id=hotel_id,
                        room_id=room_id,
                        old_housekeeping_status=room_allotment_aggregate.housekeeping_record.housekeeping_status,
                        new_housekeeping_status=room_allotment_aggregate.housekeeping_record.housekeeping_status,
                        old_room_status=old_room_status,
                        new_room_status=room_allotment_aggregate.room_inventory.current_status,
                        remarks="Room Checked In",
                    )
                )

        if (
            not is_booking_checked_in
            and dateutils.to_date(start_time) == hotel_context.current_date()
        ):
            room_allotment_aggregate.update_reservation_status(
                RoomReservationStatus.DUE_IN
            )

        self.room_allotment_repository.update(room_allotment_aggregate)

        return allotment.allotment_id

    def update_allotment_on_checkin(
        self,
        hotel_id,
        room_id,
        allotment_id,
        start_time: datetime.datetime,
        hotel_context,
    ):
        room_allotment_aggregate = self.room_allotment_repository.load_for_update(
            hotel_id=hotel_id, room_id=room_id
        )
        actual_start_time = self._get_actual_start_time(hotel_context)

        if (
            room_allotment_aggregate.housekeeping_record.housekeeping_status
            == HouseKeepingStatus.OUT_OF_ORDER
        ):
            raise InventoryException(
                error=InventoryError.OUT_OF_ORDER_ROOM_UNAVAILABLE_FOR_ALLOCATION
            )
        if (
            room_allotment_aggregate.room_inventory.current_status
            == RoomCurrentStatus.OCCUPIED
        ):
            raise InventoryException(error=InventoryError.ROOM_ALREADY_OCCUPIED)
        old_room_status = room_allotment_aggregate.room_inventory.current_status

        room_allotment_aggregate.update_room_allotment_start_time(
            start_time=start_time, allotment_id=allotment_id
        )
        room_allotment_aggregate.update_room_allotment_actual_start_time(
            actual_start_time=actual_start_time, allotment_id=allotment_id
        )
        room_allotment_aggregate.update_room_allotment_allotted_for(
            allotment_id=allotment_id, allotted_for=AllottedFor.STAY
        )
        room_allotment_aggregate.update_current_status(RoomCurrentStatus.OCCUPIED)
        room_allotment_aggregate.update_reservation_status(
            RoomReservationStatus.ARRIVED
        )
        self.room_allotment_repository.update(room_allotment_aggregate)

        if old_room_status != room_allotment_aggregate.room_inventory.current_status:
            register_room_status_change_domain_event(
                RoomStatusChangedEvent(
                    hotel_id=hotel_id,
                    room_id=room_id,
                    old_housekeeping_status=room_allotment_aggregate.housekeeping_record.housekeeping_status,
                    new_housekeeping_status=room_allotment_aggregate.housekeeping_record.housekeeping_status,
                    old_room_status=old_room_status,
                    new_room_status=room_allotment_aggregate.room_inventory.current_status,
                    remarks="Room Checked In",
                )
            )

    def delete_room_allotments(self, deleted_room_allocations, hotel_id, remarks=None):
        room_allotments_ids_to_be_deleted = defaultdict(list)
        if deleted_room_allocations:
            for room_allocation in deleted_room_allocations:
                room_allotments_ids_to_be_deleted[room_allocation.room_id].append(
                    RoomAllocationInfo(
                        room_allocation.room_allotment_id, room_allocation.room_no
                    )
                )
        else:
            return []

        deleted_rooms = []
        # clearing room inventory
        room_allotment_aggregates = self.room_allotment_repository.load_multiple(
            hotel_id=hotel_id,
            room_ids=room_allotments_ids_to_be_deleted.keys(),
            for_update=True,
        )
        for room_id, room_allotment_aggregate in room_allotment_aggregates.items():
            room_allocations_info = room_allotments_ids_to_be_deleted.get(room_id, [])
            for room_allocation_info in room_allocations_info:
                room_allotment_id = room_allocation_info.room_allotment_id
                room_number = room_allocation_info.room_number
                old_room_status = room_allotment_aggregate.room_inventory.current_status
                room_allotment: RoomAllotment = (
                    room_allotment_aggregate.delete_room_allotment(room_allotment_id)
                )

                deleted_rooms.append(room_number)

                if room_allotment and room_allotment.is_allotted_for_stay():
                    room_allotment_aggregate.update_current_status(
                        RoomCurrentStatus.VACANT
                    )
                    if (
                        old_room_status
                        != room_allotment_aggregate.room_inventory.current_status
                    ):
                        register_room_status_change_domain_event(
                            RoomStatusChangedEvent(
                                hotel_id=hotel_id,
                                room_id=room_allotment_aggregate.housekeeping_record.room_id,
                                old_housekeeping_status=room_allotment_aggregate.housekeeping_record.housekeeping_status,
                                new_housekeeping_status=room_allotment_aggregate.housekeeping_record.housekeeping_status,
                                old_room_status=old_room_status,
                                new_room_status=room_allotment_aggregate.room_inventory.current_status,
                                remarks=remarks,
                            )
                        )

            self.room_allotment_repository.update(room_allotment_aggregate)

        return deleted_rooms

    def set_room_allotment_actual_end_time(
        self,
        hotel_id,
        room_id,
        allotment_id,
        actual_end_time,
        overridden_room_allotment=None,
    ):
        room_allotment_aggregate = self.room_allotment_repository.load_for_update(
            hotel_id=hotel_id, room_id=room_id
        )
        room_allotment = room_allotment_aggregate.update_allotment_actual_end_time(
            allotment_id, actual_end_time
        )

        if (
            room_allotment.is_allotted_for_stay()
            and room_allotment_aggregate.room_inventory.current_status
            == RoomCurrentStatus.OCCUPIED
        ):
            # NOTE (rohit): When this room allotment is of DNR, or FUTURE_STAY, the room won't be occupied.
            # Is there any other case?
            hotel_context = crs_context.get_hotel_context()

            old_housekeeping_status = (
                room_allotment_aggregate.housekeeping_record.housekeeping_status
            )
            old_room_status = room_allotment_aggregate.room_inventory.current_status

            if hotel_context.housekeeping_enabled:
                room_allotment_aggregate.update_housekeeping_record(
                    HouseKeepingStatus.DIRTY, raise_event=False
                )
            room_allotment_aggregate.update_current_status(RoomCurrentStatus.VACANT)
            room_allotment_aggregate.update_reservation_status(
                RoomReservationStatus.DEPARTED
            )

            if (
                old_housekeeping_status
                != room_allotment_aggregate.housekeeping_record.housekeeping_status
                or old_room_status
                != room_allotment_aggregate.room_inventory.current_status
            ):
                register_room_status_change_domain_event(
                    RoomStatusChangedEvent(
                        hotel_id=hotel_id,
                        room_id=room_id,
                        old_housekeeping_status=old_housekeeping_status,
                        new_housekeeping_status=room_allotment_aggregate.housekeeping_record.housekeeping_status,
                        old_room_status=old_room_status,
                        new_room_status=room_allotment_aggregate.room_inventory.current_status,
                        remarks="Room Checked Out",
                    )
                )

            # Once this room is checked out, check if there is a DNR starting current business date. In which case,
            # HK Status should be marked as Out Of Order
            if self.dnr_repository.has_dnr_starting_from_given_date(
                hotel_id, room_id, hotel_context.current_date()
            ):
                room_allotment_aggregate.update_housekeeping_status_on_dnr()

        if overridden_room_allotment:
            room_allotment_aggregate.delete_room_allotment(overridden_room_allotment)

        self.room_allotment_repository.update(room_allotment_aggregate)
        return room_allotment_aggregate

    def update_room_allotment_expected_end_time(
        self,
        hotel_id,
        room_id,
        allotment_id,
        expected_end_time: datetime.datetime = None,
        hotel_context=None,
    ):
        room_allotment_aggregate = self.room_allotment_repository.load_for_update(
            hotel_id=hotel_id, room_id=room_id
        )
        room_allotment_aggregate.update_room_allotment_expected_end_time(
            allotment_id, expected_end_time, hotel_context
        )

        self.room_allotment_repository.update(room_allotment_aggregate)
        return room_allotment_aggregate

    def get_max_allowed_end_time_for_allotments(
        self, hotel_id, room_wise_room_allotments
    ):
        """

        :param hotel_id:
        :param room_wise_room_allotments: A dict of structure {room_id: [room_allotment_id]}
        :return:
        """
        grouped_room_allotments = self.room_allotment_repository.load_multiple(
            hotel_id=hotel_id, room_ids=list(room_wise_room_allotments.keys())
        )

        max_allowed_end_times = dict()
        for room_id, room_allotment_ids in room_wise_room_allotments.items():
            room_allotment_aggregate = grouped_room_allotments.get(room_id)
            for room_allotment_id in room_allotment_ids:
                max_allowed_end_time = (
                    room_allotment_aggregate.get_max_allowed_end_time_for_allotment(
                        room_allotment_id
                    )
                )
                max_allowed_end_times[room_allotment_id] = max_allowed_end_time

        return max_allowed_end_times

    def _get_actual_start_time(self, hotel_context):
        business_date = hotel_context.current_date()
        calendar_date = dateutils.current_date()
        if calendar_date == business_date:
            return dateutils.current_datetime()

        time_of_checkin = dateutils.current_time()
        if (
            business_date == dateutils.subtract(calendar_date, days=1)
            and time_of_checkin < hotel_context.switch_over_time
        ):
            actual_start_time = dateutils.datetime_at_given_time(
                dateutils.add(business_date, days=1), time_of_checkin
            )
        else:
            actual_start_time = dateutils.subtract(
                hotel_context.attach_switch_over_time_to_date(
                    dateutils.add(business_date, days=1)
                ),
                seconds=1,
            )
        return actual_start_time

    def create_inventory_blocks(
        self,
        hotel_id,
        inventory_block_data: List[InventoryBlockDTO],
        booking_id,
        auto_consume=False,
    ):
        inventory_blocks = []

        for data in inventory_block_data:
            block = InventoryBlock(
                block_id=random_id_generator('IBL'),
                hotel_id=hotel_id,
                room_type_id=data.room_type_id,
                start_date=data.start_date,
                end_date=data.end_date,
                status=data.status,
                booking_id=booking_id,
                room_stay_id=data.room_stay_id,
                block_type=data.block_type,
            )
            if auto_consume:
                block.consume()
            inventory_blocks.append(block)

        self.inventory_block_repository.save_all(inventory_blocks)
        return inventory_blocks

    def consume_inventory_block(
        self, inventory_block_request: InventoryBlockDTO, booking_id: str
    ):
        inventory_block: InventoryBlock = self.inventory_block_repository.load(
            inventory_block_request.block_id
        )
        self._validate_inventory_block_request(
            inventory_block,
            inventory_block_request,
            booking_id,
        )
        if not inventory_block.status == InventoryBlockStatus.BLOCKED:
            raise InvalidInventoryBlock(
                f"Skipping Inventory Block {inventory_block_request.block_id} as it is not in blocked state"
            )

        if inventory_block_request.block_type:
            inventory_block.block_type = inventory_block_request.block_type

        if inventory_block_request.room_stay_id:
            inventory_block.room_stay_id = inventory_block_request.room_stay_id

        inventory_block.consume()

        self.inventory_block_repository.update_all([inventory_block])
        return inventory_block

    @staticmethod
    def _validate_inventory_block_request(
        inventory_block,
        inventory_block_request,
        booking_id,
    ):
        error_message = ""
        if inventory_block.booking_id != booking_id:
            error_message = (
                f"Booking id mismatch for inventory block {inventory_block.block_id}"
            )
        if inventory_block.room_type_id != inventory_block_request.room_type_id:
            error_message = f"Inventory block room type mismatch, block_id: {inventory_block.block_id}, "
            f'requested_room_type:{inventory_block_request.room_type_id}, '
            f'inventory_block_room_type: {inventory_block.room_type_id}'
        if (
            inventory_block.start_date != inventory_block_request.start_date
            or inventory_block.end_date != inventory_block_request.end_date
        ):
            error_message = (
                f"Stay date mismatch for inventory block {inventory_block.block_id}"
            )
        if error_message:
            raise InvalidInventoryBlock(message=error_message)

    def resolve_inventory_block(
        self,
        booking_id: str,
        inventory_block_data: InventoryBlockDTO,
        inventory_block_id: str,
        hotel_id: str,
        user_action=None,
    ) -> InventoryBlock:
        if inventory_block_id:
            inventory_block_data.block_id = inventory_block_id
            inventory_block = self.consume_inventory_block(
                inventory_block_data,
                booking_id,
            )
        else:
            inventory_requirement = InventoryRequirement(hotel_id)
            inventory_requirement.add(
                inventory_block_data.room_type_id,
                inventory_block_data.start_date,
                inventory_block_data.end_date,
            )
            self.update_inventory(
                block_inventory=inventory_requirement,
                user_action=user_action,
            )
            inventory_block: InventoryBlock = self.create_inventory_blocks(
                hotel_id,
                [inventory_block_data],
                booking_id,
                auto_consume=True,
            )[0]
        return inventory_block
