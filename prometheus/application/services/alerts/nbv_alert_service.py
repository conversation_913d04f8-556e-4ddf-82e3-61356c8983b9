import logging
import os
import urllib.parse

import sentry_sdk
from flask import current_app as app

from object_registry import register_instance
from prometheus.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from prometheus.infrastructure.external_clients.company_profile_service_client import (
    CompanyProfileServiceClient,
)

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        SlackAlertServiceClient,
        CompanyProfileServiceClient,
    ],
    arguments=[os.environ.get('APP_ENV', 'local')],
)
class NBVAnnouncementService(object):
    """
    A service to send  NBV announcements On Slack
    """

    def __init__(
        self,
        slack_alert_client,
        company_profiles_client,
        environment,
    ):
        self.environment = environment
        self.slack_alert_client = slack_alert_client
        self.company_profiles_client = company_profiles_client

    @staticmethod
    def _get_treebo_domain_email(poc):
        TREEBO_EMAIL_DOMAIN = 'treebo.com'
        if not poc:
            return None
        if not poc.email_ids:
            return None
        for email_id in poc.email_ids:
            if TREEBO_EMAIL_DOMAIN in email_id:
                return email_id
        return None

    @staticmethod
    def _create_announce_nbv_slack_message_payload(message, escalation_hyperlink):
        msg = [
            {"type": "section", "text": {"type": "mrkdwn", "text": message}},
            {
                "type": "section",
                "text": {"type": "mrkdwn", "text": " "},
                "accessory": {
                    "type": "button",
                    "style": "danger",
                    "text": {"type": "plain_text", "text": "Escalate"},
                    "url": escalation_hyperlink,
                },
            },
        ]

        return msg

    def announce_nbv(self, booking_aggregate, bill_aggregate, hotel_context):
        if self.environment not in ['prod', 'production']:
            return

        if booking_aggregate.booking.hotel_id in app.config['DUMMY_HOTEL_IDS']:
            return

        travel_agent = booking_aggregate.get_travel_agent_details()
        booker_company = travel_agent or booking_aggregate.get_company_details()

        if not (booker_company and booker_company.legal_details.external_reference_id):
            return

        if self.is_dummy_profile(booker_company):
            return

        try:
            (
                inside_sales_poc,
                treebo_poc,
            ) = self.company_profiles_client.get_treebo_poc_and_inside_sales_poc(
                booker_company.legal_details.external_reference_id
            )

            inside_sales_poc_slack_id = (
                self.slack_alert_client.retrieve_poc_slack_user_id(
                    self._get_treebo_domain_email(inside_sales_poc)
                )
            )
            treebo_poc_slack_id = self.slack_alert_client.retrieve_poc_slack_user_id(
                self._get_treebo_domain_email(treebo_poc)
            )

            if not (inside_sales_poc_slack_id or treebo_poc_slack_id):
                self.send_failed_alert_notification(
                    poc_found=False, booking_aggregate=booking_aggregate
                )
                return

            alerted_inside_sales_poc = (
                self.slack_alert_client.send_alert_through_slackbot(
                    self._get_nbv_announcement_data(
                        inside_sales_poc_slack_id,
                        booking_aggregate.booking,
                        booker_company.legal_details.legal_name,
                        bill_aggregate.total_pretax_amount(),
                        hotel_context,
                        bill_aggregate.average_pretax_room_stay_amount(),
                    )
                )
            )
            alerted_treebo_poc = self.slack_alert_client.send_alert_through_slackbot(
                self._get_nbv_announcement_data(
                    treebo_poc_slack_id,
                    booking_aggregate.booking,
                    booker_company.legal_details.legal_name,
                    bill_aggregate.total_pretax_amount(),
                    hotel_context,
                    bill_aggregate.average_pretax_room_stay_amount(),
                )
            )
            if alerted_treebo_poc or alerted_inside_sales_poc:
                return

            self.send_failed_alert_notification(
                poc_found=True,
                booking_aggregate=booking_aggregate,
                inside_sales_poc_slack_id=inside_sales_poc_slack_id,
                treebo_poc_slack_id=treebo_poc_slack_id,
            )
        except Exception as e:
            logger.info(f"Exception Occurred during announcing NBV. Exception: {e}")
            sentry_sdk.capture_exception(e)

    def _get_nbv_announcement_data(
        self, user_id, booking, company_legal_name, nbv, hotel_context, room_night_price
    ):
        booking_link = app.config.get('METABASE_B2B_TA_BOOKING_DASHBOARD') + str(
            booking.reference_number
        )

        escalation_message = (
            f" NBV allocation escalation by <@{user_id}> for booking `{booking.reference_number}` "
            f"<{booking_link}|(Link)> \\n _Please look into this and resolve_ "
        )

        escalation_hyperlink = (
            app.config['ESCALATION_URL']
            + f"?message={urllib.parse.quote(escalation_message)}"
        )

        message = (
            f" Hey <@{user_id}>, \n NBV allocated to you: {nbv} :moneybag: "
            f"(price per room night: {room_night_price})\n "
            f"For the booking ID `{booking.reference_number}` from *{company_legal_name}* which has been "
            f"created in *{hotel_context.hotel_name}* via {booking.source.application_code}. \n\n"
            f"*Keep up the good work!* \n\n"
            f"_If you are not the right POC, please click the button below to escalate this issue._"
        )

        payload = {
            'blocks': self._create_announce_nbv_slack_message_payload(
                message, escalation_hyperlink
            ),
            'channel': user_id,
        }
        return payload

    def send_failed_alert_notification(
        self,
        poc_found,
        booking_aggregate,
        inside_sales_poc_slack_id=None,
        treebo_poc_slack_id=None,
    ):
        message = (
            f"POC not found for booking `{booking_aggregate.booking.reference_number}` \n"
            f"_Please look into this and resolve_"
        )
        if poc_found:
            message = (
                f"Technical Error. Couldn't alert either of the POC's ( <@{inside_sales_poc_slack_id}> , "
                f"<@{treebo_poc_slack_id}> ) for booking `{booking_aggregate.booking.reference_number}` "
            )

        logger.exception(message)
        payload = {'channel': app.config['B2B_ALERTS_CHANNEL_ID'], 'text': message}
        self.slack_alert_client.send_alert_through_slackbot(payload)

    @staticmethod
    def is_dummy_profile(booker_company):
        return booker_company.legal_details.external_reference_id in app.config.get(
            'DUMMY_SUPERHERO_COMPANY_CODES', []
        )
