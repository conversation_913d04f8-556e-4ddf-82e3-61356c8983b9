import logging
import unicodedata

from treebo_commons.money import Money
from treebo_commons.request_tracing.context import request_context
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.billing.dtos.auto_refund_dtos import (
    AutoRefundContactDto,
    AutoRefundViaPaymentGatewayResponseDto,
    AutoRefundViaPayoutLinkResponseDto,
    AutoRefundViaTreeboCorporateRewardsResponseDTO,
    PayoutLinkDto,
    RefundViaRazorPayOrAirPayDto,
    RefundViaTreeboCorporateRewardsDto,
    RefundViaTreeboRewardsDto,
    TCPRefundViaRewardResponseDTO,
)
from prometheus.application.billing.helpers.auto_refund_service_helpers import (
    AutoRefundServiceHelpers,
)
from prometheus.application.billing.query_handlers.get_refund_mode import (
    GetRefundModeQueryHandler,
)
from prometheus.application.funding.funding_dtos import TCPDiscountDetails
from prometheus.application.helpers.redistribute_payments_helper import (
    RedistributePaymentHelper,
)
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.common.serializers.request.auto_refund import (
    AutoRefundPayoutLinkRequestSchema,
    AutoRefundViaRazorPayOrAirPayRequestSchema,
    AutoRefundViaTreeboCorporateRewardsRequestSchema,
    AutoRefundViaTreeboWalletRequestSchema,
)
from prometheus.common.serializers.response.auto_refund import (
    AutoRefundViaPayoutLinkResponseSchema,
    AutoRefundViaRazorPayOrAirPayResponseSchema,
    AutoRefundViaTreeboCorporateRewardsResponseSchema,
    AutoRefundViaTreeboWalletResponseSchema,
    TCPRefundViaRewardResponseSchema,
)
from prometheus.domain.billing.dto import PaymentData
from prometheus.domain.billing.dto.payment_split_data import PaymentSplitData
from prometheus.domain.billing.dto.refund_summary_dto import (
    RefundSplitsFailureDto,
    RefundSummaryDto,
)
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.infrastructure.external_clients.athena_service_client import (
    AthenaServiceClient,
)
from prometheus.infrastructure.external_clients.authn_service_client import (
    AuthNServiceClient,
)
from prometheus.infrastructure.external_clients.payment_service_client import (
    PaymentServiceClient,
)
from prometheus.infrastructure.external_clients.reward_service_client import (
    RewardServiceClient,
)
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    BillingErrorMessages,
    PaymentChannels,
    PaymentGateways,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import (
    PayoutLinkGenerationFailed,
    RefundFailed,
    ValidationException,
)
from ths_common.utils.id_generator_utils import generate_short_random_id
from ths_common.value_objects import PayoutContactDetails

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        TenantSettings,
        GetRefundModeQueryHandler,
        PaymentServiceClient,
        JobSchedulerService,
        AthenaServiceClient,
        AutoRefundServiceHelpers,
        BillRepository,
        BookingRepository,
        AuthNServiceClient,
        RewardServiceClient,
    ]
)
class AutoRefundService:
    def __init__(
        self,
        tenant_settings: TenantSettings,
        refund_mode_query_handler: GetRefundModeQueryHandler,
        payment_service_client: PaymentServiceClient,
        job_scheduler_service: JobSchedulerService,
        athena_service_client: AthenaServiceClient,
        auto_refund_service_helpers: AutoRefundServiceHelpers,
        bill_repository: BillRepository,
        booking_repository: BookingRepository,
        authn_service_client: AuthNServiceClient,
        reward_service_client: RewardServiceClient,
    ):
        self.tenant_settings = tenant_settings
        self.refund_mode_query_handler = refund_mode_query_handler
        self.payment_service_client = payment_service_client
        self.job_scheduler_service = job_scheduler_service
        self.athena_service_client = athena_service_client
        self.auto_refund_service_helpers = auto_refund_service_helpers
        self.bill_repository = bill_repository
        self.booking_repository = booking_repository
        self.authn_service_client = authn_service_client
        self.reward_service_client = reward_service_client

    def handle_refund_for_cancellation(
        self,
        amount,
        bill_aggregate,
        booking_aggregate,
        business_date,
        payout_contact_details,
        user_data,
    ):
        # checking if version is already updated (Bug)
        self.booking_repository.load_for_update(
            booking_aggregate.booking_id, version=booking_aggregate.booking.version
        )
        self.bill_repository.load_for_update(
            booking_aggregate.bill_id, version=bill_aggregate.bill.version
        )

        billed_entity = self.post_and_redistribute_payments(
            bill_aggregate, business_date
        )
        (
            refundable_payment,
            non_refundable_payment,
        ) = self.determine_refundable_and_non_refundable_payments(
            amount, bill_aggregate
        )
        if refundable_payment.amount > 0:
            payment_dto = self.create_payment_dto_for_refund_via_auto_cancellation(
                refundable_payment,
                bill_aggregate,
                billed_entity,
                business_date,
                payout_contact_details,
                user_data,
            )
            self.auto_refund_for_direct_refunds(
                payment_dto,
                bill_aggregate,
                booking_aggregate,
                refundable_payment,
                user_data,
                is_booking_cancelled=True,
            )

        if non_refundable_payment.amount > 0:
            self.refund_non_refundable_payment(
                non_refundable_payment, bill_aggregate, billed_entity, business_date
            )

    def refund_non_refundable_payment(
        self, non_refundable_payment, bill_aggregate, billed_entity, business_date
    ):
        refund_rule = self.tenant_settings.get_refund_rule(bill_aggregate.vendor_id)
        non_refundable_payment_modes = refund_rule.non_refundable_payment_modes
        credit_shell_to_billed_entity_map = (
            self.auto_refund_service_helpers.get_credit_shell_to_billed_entity_map(
                bill_aggregate.bill_id
            )
        )
        remaining_payment_by_payment_mode = self.auto_refund_service_helpers.calculate_net_balance_grouped_by_payment_modes(
            bill_aggregate,
            refund_rule,
            credit_shell_to_billed_entity_map=credit_shell_to_billed_entity_map,
        )
        for payment_mode in non_refundable_payment_modes:
            remaining_amount = remaining_payment_by_payment_mode.get(
                payment_mode, Money(0, bill_aggregate.bill.base_currency)
            )

            if remaining_amount.amount > 0:
                refund_amount = min(
                    remaining_payment_by_payment_mode[payment_mode],
                    non_refundable_payment,
                )
                self.pass_non_refundable_payments_refund(
                    refund_amount,
                    bill_aggregate,
                    billed_entity,
                    business_date,
                    payment_mode=payment_mode,
                )
                non_refundable_payment -= refund_amount
            if non_refundable_payment.amount == 0:
                break

    def auto_refund_for_direct_refunds(
        self,
        payment_dto,
        bill_aggregate,
        booking_aggregate,
        refundable_amount,
        user_data,
        is_booking_cancelled=False,
    ):
        refunds, refund_failures = [], []
        refund_summary_dto_kwargs = {
            "item_id": 1,
            "amount": refundable_amount,
        }
        if not is_booking_cancelled:
            refund_summary_dto_kwargs["requested_refund_mode"] = (
                PaymentModes.RAZORPAY_API
                if payment_dto.payment_mode == PaymentModes.PAYOUT_LINK
                else payment_dto.payment_mode
            )
        refund_summary_dto = RefundSummaryDto(**refund_summary_dto_kwargs)
        refund_mode = self.refund_mode_query_handler.handle(
            bill_aggregate.bill_id, [refund_summary_dto]
        )[0]
        # Todo: add validation refund mode.
        privileges = crs_context.privileges_as_dict
        is_refund_allowed = self.validate_payout_link_refund_eligibility(
            bill_aggregate, refund_mode, privileges, is_booking_cancelled
        )
        if not is_refund_allowed:
            return
        if refund_mode.should_process_auto_refund:
            for refund_split in refund_mode.refund_splits:
                refund, error_message = self.add_refund_for_payment_modes(
                    bill_aggregate, booking_aggregate, payment_dto, refund_split
                )
                if refund:
                    refunds.append(refund)
                else:
                    payment_failure_dto = RefundSplitsFailureDto(
                        amount=refund_split.amount,
                        error_message=error_message,
                        refund_mode=refund_split.refund_mode,
                    )
                    refund_failures.append(payment_failure_dto)
        if refund_failures:
            self.send_refund_failure_communication(
                booking_aggregate, refund_failures, user_data
            )
        return refunds, refund_failures

    def add_refund_for_payment_modes(
        self, bill_aggregate, booking_aggregate, payment_dto, refund_split
    ):
        if refund_split.refund_mode == PaymentModes.PAYOUT_LINK:
            return self.add_refund_via_payout_link(
                bill_aggregate, booking_aggregate, payment_dto, refund_split
            )
        elif refund_split.refund_mode in (
            PaymentModes.RAZORPAY_API,
            PaymentModes.AIR_PAY,
        ):
            return self.add_refund_via_razorpay_or_airpay(
                bill_aggregate, booking_aggregate, payment_dto, refund_split
            )
        elif refund_split.refund_mode == PaymentModes.TREEBO_POINTS:
            return self.add_refund_via_treebo_rewards(
                bill_aggregate, payment_dto, refund_split
            )
        elif refund_split.refund_mode == PaymentModes.TREEBO_CORPORATE_REWARDS:
            return self.add_refund_via_treebo_corporate_rewards(
                bill_aggregate, booking_aggregate, payment_dto, refund_split
            )

    def determine_refundable_and_non_refundable_payments(self, amount, bill_aggregate):
        refund_rule = self.tenant_settings.get_refund_rule(bill_aggregate.vendor_id)
        credit_shell_to_billed_entity_map = (
            self.auto_refund_service_helpers.get_credit_shell_to_billed_entity_map(
                bill_aggregate.bill_id
            )
        )
        (
            refundable_amount,
            non_refundable_amount,
        ) = self.auto_refund_service_helpers.get_total_refundable_and_non_refundable_amounts(
            bill_aggregate,
            refund_rule,
            credit_shell_to_billed_entity_map=credit_shell_to_billed_entity_map,
        )
        if amount <= refundable_amount:
            return amount, Money(0, bill_aggregate.bill.base_currency)
        else:
            refundable_used = min(amount, refundable_amount)
            remaining_amount = amount - refundable_used
            return refundable_used, min(non_refundable_amount, remaining_amount)

    @staticmethod
    @audit(audit_type=AuditType.REFUND_ADDED)
    def add_refund(
        bill_aggregate, payment_dto, refund_split, refund_via_refund_link=False
    ):
        amount = refund_split.amount
        refund_mode = refund_split.refund_mode

        # Update payment details for the refund
        payment_dto.amount = amount
        payment_dto.amount_in_payment_currency = amount
        payment_dto.payment_splits[0].amount = amount

        # in case of payout_link, use payment_mode as RAZORPAY
        if refund_via_refund_link:
            payment_dto.payment_mode = PaymentModes.RAZORPAY_API
            payment_dto.payment_mode_sub_type = refund_mode
        else:
            payment_dto.payment_mode = refund_mode

        refund = bill_aggregate.add_payments([payment_dto])[0]

        # Attach Source id to refunds
        bill_aggregate.link_refund_to_source_payment_id(
            refund_id=refund.payment_id, source_payment_id=refund_split.payment_id
        )
        payment_date = dateutils.datetime_at_given_time(
            crs_context.hotel_context.current_date(),
            crs_context.hotel_context.checkin_time,
        )
        bill_aggregate.post_payment_and_update_posting_date(
            refund.payment_id, payment_date
        )
        return refund

    def add_refund_via_payout_link(
        self, bill_aggregate, booking_aggregate, payment_dto, refund_split
    ):
        contact_details = payment_dto.payout_contact_details
        if not (contact_details and (contact_details.email or contact_details.phone)):
            return None, "Contact details required for generating payout link"
        name = (
            booking_aggregate.get_booking_owner().name.full_name_with_first_and_last_name
        )
        refund_dto = PayoutLinkDto(
            amount=str(refund_split.amount.amount),
            currency=refund_split.amount.currency.value,
            service_id=booking_aggregate.booking.reference_number,
            contact_info=AutoRefundContactDto(
                name=self.sanitize_name(name),
                email=contact_details.email,
                contact=self.generate_phone_number(contact_details.phone),
            ),
        )
        request_data = AutoRefundPayoutLinkRequestSchema().dump(refund_dto).data
        (
            success_response,
            failure_message,
        ) = self.payment_service_client.issue_refund_via_payout_link(request_data)
        if success_response:
            response_data = (
                AutoRefundViaPayoutLinkResponseSchema().load(success_response).data
            )
            response_dto = AutoRefundViaPayoutLinkResponseDto.create_from_data_dict(
                response_data
            )
            # Update corresponding fields
            payment_dto.payout_details = response_dto.payout_link
            payment_dto.payment_ref_id = response_dto.payout_link.pg_payout_id

            refund = self.add_refund(
                bill_aggregate, payment_dto, refund_split, refund_via_refund_link=True
            )
            auto_approved_payout_link_amount = (
                self.tenant_settings.get_auto_approved_payout_link_amount(
                    hotel_id=booking_aggregate.hotel_id
                )
            )
            auto_approved_pl_amount = Money(
                auto_approved_payout_link_amount, bill_aggregate.bill.base_currency
            )
            if auto_approved_pl_amount >= refund_split.amount:
                self.send_payout_link_communication(booking_aggregate, payment_dto)
            else:
                self.send_payout_link_not_approved_communication(
                    booking_aggregate, payment_dto
                )
            return refund, None
        else:
            logger.info("Auto refund failure via payout-link")
            self._check_failure_message(
                failure_message, booking_aggregate, payment_dto, contact_details
            )
            return None, failure_message

    def add_refund_via_razorpay_or_airpay(
        self, bill_aggregate, booking_aggregate, payment_dto, refund_split
    ):
        payment_id = bill_aggregate.get_payment(refund_split.payment_id).payment_ref_id
        gateway = (
            PaymentGateways.RAZORPAY
            if refund_split.refund_mode == PaymentModes.RAZORPAY_API
            else PaymentGateways.AIR_PAY
        )
        refund_amount = refund_split.amount.amount
        refund_dto = RefundViaRazorPayOrAirPayDto(
            amount=str(refund_amount),
            currency=payment_dto.amount.currency.value,
            service_id=booking_aggregate.booking.reference_number,
            payment_id=payment_id,
            gateway=gateway,
        )
        request_data = (
            AutoRefundViaRazorPayOrAirPayRequestSchema().dump(refund_dto).data
        )
        success_response, failure_message = self.payment_service_client.issue_refund(
            request_data
        )
        if success_response:
            response_data = (
                AutoRefundViaRazorPayOrAirPayResponseSchema()
                .load(success_response)
                .data
            )
            response_dto = AutoRefundViaPaymentGatewayResponseDto.create_from_data_dict(
                response_data
            )

            # Update corresponding fields
            payment_dto.payout_details = None
            payment_dto.payment_ref_id = response_dto.refund_order_id
            self.send_direct_refund_communication(booking_aggregate, refund_amount)
            return self.add_refund(bill_aggregate, payment_dto, refund_split), None
        else:
            return None, failure_message

    def add_refund_via_treebo_rewards(self, bill_aggregate, payment_dto, refund_split):
        payment_id = bill_aggregate.get_payment(refund_split.payment_id).payment_ref_id
        refund_dto = RefundViaTreeboRewardsDto(
            payment_id=payment_id,
            amount=str(refund_split.amount.amount),
            gateway=PaymentGateways.TREEBO_WALLET,
        )
        request_data = AutoRefundViaTreeboWalletRequestSchema().dump(refund_dto).data
        (
            success_response,
            failure_message,
        ) = self.payment_service_client.issue_refund_via_treebo_wallet(request_data)
        if success_response:
            response_data = (
                AutoRefundViaTreeboWalletResponseSchema().load(success_response).data
            )
            response_dto = AutoRefundViaPaymentGatewayResponseDto.create_from_data_dict(
                response_data
            )

            # Update corresponding fields
            payment_dto.payment_ref_id = response_dto.refund_order_id
            payment_dto.payout_details = None
            return self.add_refund(bill_aggregate, payment_dto, refund_split), None
        else:
            return None, failure_message

    def add_refund_via_treebo_corporate_rewards(
        self, bill_aggregate, booking_aggregate, payment_dto, refund_split
    ):
        refund_dto = RefundViaTreeboCorporateRewardsDto(
            amount=str(refund_split.amount.amount),
            booking_id=booking_aggregate.booking.reference_number,
        )
        request_data = (
            AutoRefundViaTreeboCorporateRewardsRequestSchema().dump(refund_dto).data
        )
        (
            success_response,
            failure_message,
        ) = self.athena_service_client.issue_refund_via_treebo_corporate_rewards(
            request_data
        )
        if success_response:
            response_data = (
                AutoRefundViaTreeboCorporateRewardsResponseSchema()
                .load(success_response)
                .data
            )
            response_dto = (
                AutoRefundViaTreeboCorporateRewardsResponseDTO.create_from_data_dict(
                    response_data
                )
            )

            # Update corresponding fields
            payment_dto.payment_ref_id = response_dto.order_id
            payment_dto.payout_details = None
            return self.add_refund(bill_aggregate, payment_dto, refund_split), None
        else:
            return None, failure_message

    @staticmethod
    def generate_phone_number(phone):
        return str(phone.country_code + phone.number)

    def add_refund_via_payout_link_for_credit_shell(
        self, booking_aggregate, credit_shell_dto
    ):
        contact_details = credit_shell_dto.payout_contact_details
        if not (contact_details and (contact_details.email or contact_details.phone)):
            raise ValidationException(
                ApplicationErrors.CONTACT_DETAILS_REQUIRED_FOR_GENERATING_PAYOUT_LINK
            )
        name = (
            booking_aggregate.get_booking_owner().name.full_name_with_first_and_last_name
        )
        refund_dto = PayoutLinkDto(
            amount=str(credit_shell_dto.amount.amount),
            currency=credit_shell_dto.amount.currency.value,
            service_id=booking_aggregate.booking.reference_number,
            contact_info=AutoRefundContactDto(
                name=self.sanitize_name(name),
                email=contact_details.email,
                contact=self.generate_phone_number(contact_details.phone),
            ),
        )
        request_data = AutoRefundPayoutLinkRequestSchema().dump(refund_dto).data
        (
            response,
            failure_message,
        ) = self.payment_service_client.issue_refund_via_payout_link(request_data)
        if response:
            response_data = AutoRefundViaPayoutLinkResponseSchema().load(response).data
            response_dto = AutoRefundViaPayoutLinkResponseDto.create_from_data_dict(
                response_data
            )
            return response_dto
        else:
            raise PayoutLinkGenerationFailed(failure_message)

    def create_payment_dto_for_refund_via_auto_cancellation(
        self,
        amount,
        bill_aggregate,
        billed_entity,
        business_date,
        payout_contact_details,
        user_data,
    ):
        billed_entity_details = bill_aggregate.get_billed_entity(
            billed_entity.billed_entity_id
        )
        paid_to = self.get_paid_to_for_payment(billed_entity_details)
        payment_channel = (
            PaymentChannels.FRONT_DESK
            if user_data and user_data.user_auth_id
            else PaymentChannels.ONLINE
        )
        return PaymentData(
            amount=amount,
            date_of_payment=business_date,
            payment_mode=PaymentModes.RAZORPAY_API,
            payment_mode_sub_type=None,
            payment_type=PaymentTypes.REFUND,
            payment_details=dict(),
            status=PaymentStatus.DONE,
            confirmed=True,
            paid_to=paid_to,
            payment_channel=payment_channel,
            payment_ref_id=None,
            paid_by='treebo',
            comment=None,
            amount_in_payment_currency=amount,
            refund_reason='Cancellation - Guest Initiated',
            payout_contact_details=(
                PayoutContactDetails.from_json(payout_contact_details)
                if payout_contact_details
                else None
            ),
            payor_billed_entity_id=billed_entity.billed_entity_id,
            payment_splits=[
                PaymentSplitData(
                    billed_entity_account=billed_entity,
                    amount=amount,
                )
            ],
        )

    def post_and_redistribute_payments(self, bill_aggregate, business_date):
        bill_aggregate.post_eligible_payments(business_date)
        redistribute_payment = RedistributePaymentHelper.redistribute_payments(
            bill_aggregate
        )
        billed_entity = self.get_billed_entity_for_remaining_amount(
            redistribute_payment
        )
        return billed_entity

    @staticmethod
    def get_billed_entity_for_remaining_amount(redistribute_payment):
        for p in redistribute_payment:
            for eas in p['entity_account_summary']:
                if eas.account_summary.balance < 0:
                    return eas.billed_entity_account

    def pass_non_refundable_payments_refund(
        self, amount, bill_aggregate, billed_entity, business_date, payment_mode
    ):
        paid_by, paid_to = self.get_paid_by_and_paid_to_for_payment(
            bill_aggregate, billed_entity, payment_mode
        )
        payment_ref_id = generate_short_random_id(prefix='R', length=9)
        _, payment_done = bill_aggregate.add_refund_for_non_refundable_amount(
            amount,
            billed_entity,
            business_date,
            reason='Cancellation - Guest Initiated',
            payment_mode=payment_mode,
            paid_by=paid_by,
            paid_to=paid_to,
            payment_ref_id=payment_ref_id,
        )
        bill_aggregate.post_payment_by_id(payment_done.payment_id)

    def get_paid_by_and_paid_to_for_payment(
        self, bill_aggregate, billed_entity, payment_mode
    ):
        refund_config = (
            self.tenant_settings.get_payment_configs_for_all_allowed_payment_methods(
                bill_aggregate.vendor_id
            )['refund']
        )
        paid_by = refund_config[payment_mode].paid_to
        billed_entity = bill_aggregate.get_billed_entity(billed_entity.billed_entity_id)
        paid_to = self.get_paid_to_for_payment(billed_entity)
        return paid_by, paid_to

    @staticmethod
    def get_paid_to_for_payment(billed_entity):
        if billed_entity.category == BilledEntityCategory.BOOKER_COMPANY:
            return PaymentReceiverTypes.CORPORATE
        elif billed_entity.category == BilledEntityCategory.TRAVEL_AGENT:
            return PaymentReceiverTypes.TA
        return PaymentReceiverTypes.GUEST

    def send_payout_link_communication(self, booking_aggregate, payment_dto):
        if payment_dto.payout_details:
            payout_details = payment_dto.payout_details
            refund_amount = payment_dto.amount.amount
            expire_by = payout_details.expire_by
            link = payout_details.short_url
            pg_payout_id = payout_details.pg_payout_id
            contact_details = payment_dto.payout_contact_details
            phone_number, email = (
                (
                    str(
                        contact_details.phone.country_code
                        + contact_details.phone.number
                    ),
                    contact_details.email,
                )
                if contact_details
                else (None, None)
            )
            self.job_scheduler_service.schedule_payout_link_communication(
                booking_aggregate,
                refund_amount,
                expire_by,
                link,
                pg_payout_id,
                phone_number=phone_number,
                email=email,
            )

    def send_direct_refund_communication(self, booking_aggregate, refund_amount):
        self.job_scheduler_service.schedule_direct_refund_communication(
            booking_aggregate, str(refund_amount)
        )

    def send_refund_failure_communication(
        self, booking_aggregate, refund_failures, user_data
    ):
        for refund_failure in refund_failures:
            payment_mode = refund_failure.refund_mode
            amount = refund_failure.amount
            error_message = refund_failure.error_message
            user_auth_id = (
                int(user_data.user_auth_id)
                if user_data and user_data.user_auth_id
                else None
            )
            if not (
                payment_mode in [PaymentModes.RAZORPAY_API, PaymentModes.PAYOUT_LINK]
                and amount.amount < 1
            ):
                self.job_scheduler_service.schedule_payment_failure_communication(
                    booking_aggregate, amount, payment_mode, error_message, user_auth_id
                )

    def send_blacklist_user_refund_failure_communication(
        self, booking_aggregate, contact_details, payment_mode
    ):
        self.job_scheduler_service.schedule_auto_refund_blacklisted_user_communication(
            booking_aggregate,
            contact_details,
            payment_mode,
        )

    def _check_failure_message(
        self, failure_message, booking_aggregate, payment_dto, contact_details
    ):
        user_contact_details = dict()
        if (
            failure_message
            == BillingErrorMessages.REFUND_ISSUER_USER_IS_BLACKLISTED.value
        ):
            logger.info("PayoutLink failure due to user issuing refund is Blacklisted")
            auth_id = (
                request_context.auth_id if hasattr(request_context, 'auth_id') else None
            )
            user_data = (
                self.authn_service_client.get_user_details(auth_id)
                if auth_id
                else dict()
            )
            if user_data.get('phone_number'):
                user_contact_details['phone'] = user_data.get("phone_number")
            if user_data.get('email'):
                user_contact_details['email'] = user_data.get('email')
        elif (
            failure_message
            == BillingErrorMessages.REFUND_RECEIVER_USER_IS_BLACKLISTED.value
        ):
            logger.info(
                "PayoutLink failure due to user receiving refund is Blacklisted"
            )
            user_contact_details['phone'] = self.generate_phone_number(
                contact_details.phone
            )
            user_contact_details['email'] = contact_details.email
        if user_contact_details:
            self.send_blacklist_user_refund_failure_communication(
                booking_aggregate, user_contact_details, payment_dto.payment_mode
            )

    def send_payout_link_not_approved_communication(
        self, booking_aggregate, payment_dto
    ):
        if payment_dto.payout_details:
            payout_details = payment_dto.payout_details
            refund_amount = payment_dto.amount.amount
            expire_by = payout_details.expire_by
            link = payout_details.short_url
            pg_payout_id = payout_details.pg_payout_id
            contact_details = payment_dto.payout_contact_details
            phone_number, email = (
                (
                    str(
                        contact_details.phone.country_code
                        + contact_details.phone.number
                    ),
                    contact_details.email,
                )
                if contact_details
                else (None, None)
            )
            self.job_scheduler_service.schedule_payout_link_not_approved_communication(
                booking_aggregate,
                refund_amount,
                expire_by,
                link,
                pg_payout_id,
                phone_number=phone_number,
                email=email,
            )

    @staticmethod
    def sanitize_name(name):
        normalized_text = unicodedata.normalize('NFD', name)
        sanitized_name = ''.join(
            c for c in normalized_text if not unicodedata.combining(c)
        )
        return sanitized_name

    @staticmethod
    def validate_payout_link_refund_eligibility(
        bill_aggregate, refund_mode, privileges, is_booking_cancelled
    ):
        """
        Check if the user with privilege CAN_REFUND_PAYOUT_LINK_UP_TO can refund or not.
        REQUIREMENT: For cancel booking proceed with cancellation, while for add refund flow, raise a error
        """
        if privileges and PrivilegeCode.CAN_REFUND_PAYOUT_LINK_UP_TO in privileges:
            allowed_refund_limit = Money(
                privileges[PrivilegeCode.CAN_REFUND_PAYOUT_LINK_UP_TO][0],
                bill_aggregate.bill.base_currency,
            )

            for rm in refund_mode.refund_splits:
                if (
                    rm.refund_mode == PaymentModes.PAYOUT_LINK
                    and rm.amount > allowed_refund_limit
                ):
                    if is_booking_cancelled:
                        return False
                    else:
                        raise RefundFailed()
        return True

    def refund_tcp_discounts(
        self, tcp_discount_details: TCPDiscountDetails, booking_aggregate
    ):
        (
            success_response,
            failure_message,
        ) = self.reward_service_client.refund_reward_points(
            tcp_discount_details.reward_transaction_id,
            tcp_discount_details.reward_source_name,
            tcp_discount_details.amount.amount,
        )
        if not success_response:
            payment_failure_dto = RefundSplitsFailureDto(
                amount=tcp_discount_details.amount,
                error_message=failure_message,
                refund_mode=PaymentGateways.TREEBO_WALLET,
            )
            self.send_refund_failure_communication(
                booking_aggregate, [payment_failure_dto], user_data=None
            )
            return None, failure_message
        response_data = TCPRefundViaRewardResponseSchema().load(success_response).data
        response_dto = TCPRefundViaRewardResponseDTO.create_from_data_dict(
            response_data
        )
        return response_dto, None
