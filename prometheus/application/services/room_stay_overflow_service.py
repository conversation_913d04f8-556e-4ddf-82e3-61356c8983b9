# coding=utf-8
"""
booking overflow application service
"""
import logging
from collections import defaultdict
from typing import List

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.booking.dtos import OverflowDto
from prometheus.domain.booking.entities import RoomStay
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.booking.repositories.room_stay_overflow_repository import (
    RoomStayOverflowRepository,
)
from prometheus.domain.booking.services.room_stay_overflow_domain_service import (
    RoomStayOverflowDomainService,
)
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.repositories.room_type_inventory_repository import (
    RoomTypeInventoryRepository,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.facts import Facts
from ths_common.constants.booking_constants import BookingChannels, BookingStatus
from ths_common.exceptions import InvalidOperationError
from ths_common.utils.common_utils import group_list

logger = logging.getLogger(__name__)


# ===============================================================================================================
# NOTE: Any change in this module should go through strict automation and manual testing.
# ===============================================================================================================
#
# This service is used during any operation which results in RoomTypeInventory change. Whenever any RoomTypeInventory
# is updated, this service re-computes the overbooking, and marks/unmarks a set of room_stays as "overflow" or
# "not_overflow".
#
# Overflow as a concept doesn't exist in CRS Backend, but it's only here to allow PMS Houseview UI to display RoomStay
# in appropriate overflow section. Overflow is not necessarily same as overbooking, because a RoomStay spanning 3 days
# can move to overflow region for all 3 days (since we can't split RoomStay day-wise in HouseView), even though
# overbooking is actually only on 1st day.
# ===============================================================================================================


@register_instance(
    dependencies=[
        HotelRepository,
        HotelConfigRepository,
        BookingRepository,
        RoomTypeInventoryRepository,
        RoomStayOverflowRepository,
    ]
)
class RoomStayOverflowService(object):
    def __init__(
        self,
        hotel_repository,
        hotel_config_repository,
        booking_repository: BookingRepository,
        room_type_inventory_repo,
        room_stay_overflow_repo,
    ):
        # NOTE (Rohit): This service is like a black box right now for us. Be careful while modifying anything here.
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.booking_repository = booking_repository
        self.room_type_inventory_repo = room_type_inventory_repo
        self.room_stay_overflow_repo = room_stay_overflow_repo

    def refresh_overflow_recompute_unmarks(
        self,
        start_date,
        end_date,
        room_type_ids,
        booking_aggregate_to_refresh,
        user_action=None,
    ):
        room_type_ids = set(room_type_ids)
        self.room_stay_overflow_repo.delete_overflow_tags(
            booking_aggregate_to_refresh.booking.booking_id,
            {
                room_stay.room_stay_id
                for room_stay in booking_aggregate_to_refresh.get_inactive_room_stays()
            },
        )

        self.recompute_and_unmark_overflows(
            booking_aggregate_to_refresh.booking.hotel_id,
            start_date,
            end_date,
            room_type_ids,
            user_action=user_action,
        )

    def _mark_overflow(
        self, booking_aggregate, room_stay, already_overflowing_room_stay_ids=None
    ):
        if already_overflowing_room_stay_ids is not None:
            if room_stay.room_stay_id in already_overflowing_room_stay_ids:
                raise InvalidOperationError(
                    error=ApplicationErrors.ROOM_STAY_OVERFLOW_MARK_FAILED
                )
        else:
            room_stay_overflow_aggregate = (
                self.room_stay_overflow_repo.load_room_stay_overflow(
                    booking_aggregate.booking.booking_id, room_stay.room_stay_id
                )
            )
            if room_stay_overflow_aggregate:
                raise InvalidOperationError(
                    error=ApplicationErrors.ROOM_STAY_OVERFLOW_MARK_FAILED
                )

        room_stay_overflow_aggregate = (
            RoomStayOverflowDomainService.mark_room_stay_as_overflow(
                booking_aggregate, room_stay
            )
        )
        return room_stay_overflow_aggregate

    @session_manager(commit=True)
    @set_hotel_context()
    def bulk_mark_and_unmark_overflow(
        self,
        hotel_id,
        mark_overflow_list: List[OverflowDto],
        unmark_overflow_list: List[OverflowDto],
        user_data,
        user_action=None,
        hotel_aggregate=None,
    ):
        if not hotel_aggregate:
            hotel_aggregate = self.hotel_repository.load(hotel_id)
            hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
            crs_context.set_hotel_context(
                hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
            )

        RuleEngine.action_allowed(
            action="swap_overflow",
            facts=Facts(
                user_type=user_data.user_type,
                current_time=dateutils.current_datetime(),
                hotel_context=crs_context.get_hotel_context(),
            ),
            fail_on_error=True,
        )

        if len(unmark_overflow_list) != 1:
            raise InvalidOperationError(
                error=ApplicationErrors.BULK_OVERFLOW_UNMARK_NOT_ALLOWED
            )

        all_overflows = mark_overflow_list + unmark_overflow_list
        booking_ids = {overflow_dto.booking_id for overflow_dto in all_overflows}
        booking_aggregates = self.booking_repository.load_all(booking_ids)
        grouped_booking_aggregates = {
            aggregate.booking_id: aggregate for aggregate in booking_aggregates
        }
        for overflow_dto in all_overflows:
            overflow_dto.booking_aggregate = grouped_booking_aggregates.get(
                overflow_dto.booking_id
            )

        unmark_overflow_record = unmark_overflow_list[0]
        unmark_booking_aggregate = unmark_overflow_record.booking_aggregate
        room_stay_overflow_to_be_swapped = unmark_booking_aggregate.get_room_stay(
            unmark_overflow_record.room_stay_id
        )

        self._fail_if_overflow_request_not_in_same_hotel(hotel_id, all_overflows)
        self._fail_if_overflow_request_not_in_same_room_type(all_overflows)
        self._fail_if_overflow_request_not_in_reserved_state(all_overflows)

        overflow_marked_aggregates = self._create_new_overflows(
            mark_overflow_list, room_stay_overflow_to_be_swapped
        )
        updated_overflow_aggregates = overflow_marked_aggregates

        # Delete overflow for room stay that is getting swapped out of overbooking
        room_stay_overflow_aggregate = (
            self.room_stay_overflow_repo.load_room_stay_overflow(
                unmark_booking_aggregate.booking_id,
                room_stay_overflow_to_be_swapped.room_stay_id,
            )
        )

        room_type_inventory_aggregates = self.room_type_inventory_repo.load_multiple(
            hotel_id,
            room_stay_overflow_aggregate.start_date,
            room_stay_overflow_aggregate.end_date,
            [room_stay_overflow_aggregate.room_type_id],
        )
        room_type_inventory_aggregate = room_type_inventory_aggregates[0]

        # try and accommodate the unmark_overflow_record room stay
        deleted_room_stay_overflow = self._remove_existing_overflow(
            hotel_id, room_stay_overflow_aggregate, room_type_inventory_aggregate
        )

        # deleted_room_stay_overflow is same as room_stay_overflow_aggregate
        if deleted_room_stay_overflow:
            self.room_stay_overflow_repo.delete_overflows([deleted_room_stay_overflow])
            updated_overflow_aggregates.append(deleted_room_stay_overflow)

            # anomaly_count of all the negative inventory dates should be zero
            dates_with_negative_inventory = {
                av.date: av
                for av in room_type_inventory_aggregate.room_type_inventory_availabilities
                if av.actual_count < 0
            }
            if dates_with_negative_inventory:
                min_date = min(dates_with_negative_inventory)
                max_date = max(dates_with_negative_inventory)
                room_type_inventory_aggregates = (
                    self.room_type_inventory_repo.load_multiple(
                        hotel_id,
                        min_date,
                        max_date,
                        [deleted_room_stay_overflow.room_type_id],
                    )
                )

                date_wise_overflow_count = (
                    self.room_stay_overflow_repo.get_overflow_count_for_dates(
                        hotel_id,
                        min_date,
                        max_date,
                        deleted_room_stay_overflow.room_type_id,
                    )
                )

                for date, availability in dates_with_negative_inventory.items():
                    availability = room_type_inventory_aggregates[
                        0
                    ].get_availability_for_date(date)
                    inventory_count = availability.actual_count
                    overflow_count = date_wise_overflow_count.get(date)
                    overbooking_count_not_covered_by_overflow_count = RoomStayOverflowDomainService.get_overbooking_not_covered_by_overflow_count(
                        inventory_count, overflow_count
                    )
                    if overbooking_count_not_covered_by_overflow_count > 0:
                        raise InvalidOperationError(
                            error=ApplicationErrors.OVERFLOW_UNMARKING_CREATED_OVERFLOW_ANOMALY
                        )

        IntegrationEventApplicationService.create_overflow_updated_event(
            hotel_id, updated_overflow_aggregates, user_action=user_action
        )

        return updated_overflow_aggregates

    def _remove_existing_overflow(
        self, hotel_id, room_stay_overflow_aggregate, room_type_inventory_aggregate
    ):
        # Existing overflow to be removed during swap_overflow operation
        date_wise_overflow_count = (
            self.room_stay_overflow_repo.get_overflow_count_for_dates(
                hotel_id,
                room_stay_overflow_aggregate.start_date,
                room_stay_overflow_aggregate.end_date,
                room_stay_overflow_aggregate.room_type_id,
            )
        )

        if RoomStayOverflowDomainService.can_accommodate_room_stay(
            room_type_inventory_aggregate, date_wise_overflow_count
        ):
            room_stay_overflow_aggregate.delete()
            return room_stay_overflow_aggregate

    def _create_new_overflows(
        self, mark_overflow_list, room_stay_getting_removed_from_overflow: RoomStay
    ):
        # New overflow created during swap_overflow operation
        overflow_marked_aggregates = list()
        for overflow_to_be_marked in mark_overflow_list:
            booking_aggregate = overflow_to_be_marked.booking_aggregate
            room_stay = booking_aggregate.get_room_stay(
                overflow_to_be_marked.room_stay_id
            )

            if not self._is_from_direct_or_ota_channel(booking_aggregate):
                raise InvalidOperationError(
                    error=ApplicationErrors.MARKING_OVERFLOW_NOT_ALLOWED_ON_CHANNEL
                )

            if not self._is_room_stay_overlapping(
                room_stay_getting_removed_from_overflow, room_stay
            ):
                # The room stay that is getting marked as overflow, should have overlapping stay duration with the
                # room stay which is getting removed from overflow list
                raise InvalidOperationError(
                    error=ApplicationErrors.ROOM_STAY_OVERLAPPING_IS_MANDATORY
                )

            rs_overflow_aggregate = self._mark_overflow(booking_aggregate, room_stay)
            overflow_marked_aggregates.append(rs_overflow_aggregate)

        self.room_stay_overflow_repo.save_all(overflow_marked_aggregates)
        return overflow_marked_aggregates

    def compute_and_mark_overflows_in_date_range(
        self, hotel_id, room_type_id, start_date, end_date, user_action=None
    ):
        room_type_inventory_aggregates = self.room_type_inventory_repo.load_multiple(
            hotel_id, start_date, end_date, [room_type_id]
        )
        room_type_inventory_aggregate = room_type_inventory_aggregates[0]
        dates_with_negative_inventory = {
            av.date: av
            for av in room_type_inventory_aggregate.room_type_inventory_availabilities
            if av.actual_count < 0
        }

        if not dates_with_negative_inventory:
            return []

        min_date = min(dates_with_negative_inventory)
        max_date = max(dates_with_negative_inventory)

        date_wise_overflow_count = (
            self.room_stay_overflow_repo.get_overflow_count_for_dates(
                hotel_id, min_date, max_date, room_type_id
            )
        )

        room_stay_id_to_mark_overflow = defaultdict(set)
        already_overflowing_room_stay_ids = defaultdict(set)
        for date, availability in dates_with_negative_inventory.items():
            # Basically inventory is -4, but overflow count is only 2. So we need to move 2 more rooms to overflow
            # In total 2 overbookings are not covered by current overflow count
            overbooking_count_not_covered_by_overflow_count = RoomStayOverflowDomainService.get_overbooking_not_covered_by_overflow_count(
                availability.actual_count, date_wise_overflow_count.get(date)
            )
            if overbooking_count_not_covered_by_overflow_count == 0:
                continue

            # Get more room stays which can be moved into overflow, to match the extra overbooking count. We need 2
            # overflowing room stays more.

            # First get all the bookings which are eligible for moving to overflow rows
            overflowable_bookings = (
                self.booking_repository.load_overflowable_bookings_for_date(
                    hotel_id, room_type_id, date
                )
            )

            # Get room stays of above bookings, which are already in overflow
            room_stay_overflow_aggregates = (
                self.room_stay_overflow_repo.get_overflowed_room_stays_for_bookings(
                    [aggregate.booking_id for aggregate in overflowable_bookings]
                )
            )
            grouped_room_stay_overflows = group_list(
                [
                    aggregate.room_stay_overflow
                    for aggregate in room_stay_overflow_aggregates
                ],
                'booking_id',
            )

            all_room_stays_eligible_for_overflow = dict()
            for booking_aggregate in overflowable_bookings:
                # First tag already overflowing room stay as overflowed
                overflowing_room_stay_ids = {
                    overflow.room_stay_id
                    for overflow in grouped_room_stay_overflows.get(
                        booking_aggregate.booking_id, []
                    )
                }
                already_overflowing_room_stay_ids[booking_aggregate.booking_id].update(
                    overflowing_room_stay_ids
                )
                booking_aggregate.tag_room_stay_overflows(overflowing_room_stay_ids)

                # Now get other non-overflowing room stays, whose stay duration covers the current date of loop
                room_stays_eligible_for_overflow = [
                    rs
                    for rs in booking_aggregate.room_stays
                    if rs.is_eligible_to_be_marked_overflow_on_date(date)
                ]

                if overbooking_count_not_covered_by_overflow_count >= len(
                    room_stays_eligible_for_overflow
                ):
                    all_room_stays_eligible_for_overflow[
                        booking_aggregate
                    ] = room_stays_eligible_for_overflow
                    overbooking_count_not_covered_by_overflow_count -= len(
                        room_stays_eligible_for_overflow
                    )
                else:
                    all_room_stays_eligible_for_overflow[
                        booking_aggregate
                    ] = room_stays_eligible_for_overflow[
                        :overbooking_count_not_covered_by_overflow_count
                    ]
                    overbooking_count_not_covered_by_overflow_count = 0

                if overbooking_count_not_covered_by_overflow_count == 0:
                    break

            if overbooking_count_not_covered_by_overflow_count > 0:
                # Total room stays which are eligible to be moved to overflows on 'date' is less than overbooking
                # All the overflows can't support/cover overbooking count
                raise InvalidOperationError(
                    error=ApplicationErrors.BOOKINGS_NOT_AVAILABLE_TO_MARK_OVERFLOW,
                    description="Bookings not available to mark overflow on: {0}".format(
                        date.isoformat()
                    ),
                )

            for (
                booking_aggregate,
                room_stays,
            ) in all_room_stays_eligible_for_overflow.items():
                for room_stay in room_stays:
                    room_stay_id_to_mark_overflow[booking_aggregate].add(
                        room_stay.room_stay_id
                    )

        if not room_stay_id_to_mark_overflow:
            return []

        marked_overflow_aggregates = list()
        for booking_aggregate, room_stay_ids in room_stay_id_to_mark_overflow.items():
            room_stays = booking_aggregate.get_room_stays(room_stay_ids)
            for room_stay in room_stays:
                room_stay_overflow_aggregate = self._mark_overflow(
                    booking_aggregate,
                    room_stay,
                    already_overflowing_room_stay_ids=already_overflowing_room_stay_ids.get(
                        booking_aggregate.booking_id, []
                    ),
                )

                marked_overflow_aggregates.append(room_stay_overflow_aggregate)

        if marked_overflow_aggregates:
            self.room_stay_overflow_repo.save_all(marked_overflow_aggregates)

        # NOTE: This below for loop is not buggy. It iterates over the list, of tuple. So previous loop,
        # anomaly_overflow is tuple, whereas in this loop we're exploding the tuple.
        unmarked_overflows = defaultdict(set)
        for booking_aggregate, room_stay_ids in room_stay_id_to_mark_overflow.items():
            room_stays = booking_aggregate.get_room_stays(room_stay_ids)
            start_date = dateutils.to_date(booking_aggregate.booking.checkin_date)
            end_date = dateutils.to_date(booking_aggregate.booking.checkout_date)
            unique_room_type_ids = {room_stay.room_type_id for room_stay in room_stays}

            for room_type_id in unique_room_type_ids:
                unmarked_overflow_map = self.recompute_and_unmark_overflows(
                    booking_aggregate.booking.hotel_id,
                    start_date,
                    end_date,
                    room_type_id,
                    send_event=False,
                    user_action=user_action,
                )

                for booking_id, room_stay_ids in unmarked_overflow_map.items():
                    unmarked_overflows[booking_id].update(room_stay_ids)

        final_marked_overflow_aggregates = []
        for overflow_aggregate in marked_overflow_aggregates:
            booking_id = overflow_aggregate.room_stay_overflow.booking_id
            room_stay_id = overflow_aggregate.room_stay_overflow.room_stay_id
            if (
                booking_id not in unmarked_overflows
                or unmarked_overflows[booking_id] != room_stay_id
            ):
                final_marked_overflow_aggregates.append(overflow_aggregate)

        return final_marked_overflow_aggregates

    def compute_and_mark_overflows(self, booking_aggregate, user_action=None):
        # delete all the previous overflow records of the booking
        self.room_stay_overflow_repo.delete_overflow_tag(
            booking_aggregate.booking.booking_id
        )

        room_stays_grouped_by_room_type = (
            booking_aggregate.get_active_room_stays_grouped_by_room_type()
        )
        # find the anomaly_count for all dates in booking
        start_date = dateutils.to_date(booking_aggregate.booking.checkin_date)
        end_date = dateutils.to_date(booking_aggregate.booking.checkout_date)

        room_type_inventory_aggregates = self.room_type_inventory_repo.load_multiple(
            booking_aggregate.booking.hotel_id,
            start_date,
            end_date,
            room_stays_grouped_by_room_type.keys(),
        )
        grouped_room_type_inventory_aggregates = {
            aggregate.room_type_id: aggregate
            for aggregate in room_type_inventory_aggregates
        }

        all_room_stays_to_mark_overflow = set()
        for room_type_id, room_stays in room_stays_grouped_by_room_type.items():
            room_type_inventory_aggregate = grouped_room_type_inventory_aggregates.get(
                room_type_id
            )
            dates_with_negative_inventory = {
                av.date: av
                for av in room_type_inventory_aggregate.room_type_inventory_availabilities
                if av.actual_count < 0
            }

            if not dates_with_negative_inventory:
                continue

            date_wise_overflow_count = (
                self.room_stay_overflow_repo.get_overflow_count_for_dates(
                    booking_aggregate.booking.hotel_id,
                    min(dates_with_negative_inventory),
                    max(dates_with_negative_inventory),
                    room_type_id,
                )
            )

            for date, availability in dates_with_negative_inventory.items():
                overbooking_count_not_covered_by_overflow_count = RoomStayOverflowDomainService.get_overbooking_not_covered_by_overflow_count(
                    availability.actual_count, date_wise_overflow_count.get(date)
                )
                if overbooking_count_not_covered_by_overflow_count == 0:
                    continue

                # find the overlapping room stays for days with overflow anomaly excluding already marked
                room_stays_eligible_for_overflow = [
                    rs.room_stay_id
                    for rs in room_stays
                    if rs.is_eligible_to_be_marked_overflow_on_date(date)
                ]
                if (
                    len(room_stays_eligible_for_overflow)
                    < overbooking_count_not_covered_by_overflow_count
                ):
                    raise InvalidOperationError(
                        error=ApplicationErrors.BOOKINGS_NOT_AVAILABLE_TO_MARK_OVERFLOW
                    )

                # Get total room stays == overbooking_count_not_covered_by_overflow_count
                all_room_stays_to_mark_overflow.update(
                    set(
                        room_stays_eligible_for_overflow[
                            :overbooking_count_not_covered_by_overflow_count
                        ]
                    )
                )

        # don't merge in the above for loop
        final_marked_overflow_aggregates = list()
        if not all_room_stays_to_mark_overflow:
            return final_marked_overflow_aggregates

        marked_overflow_aggregates = list()
        booking_wise_overflow_room_stay_ids = (
            self.room_stay_overflow_repo.get_overflowed_room_stay_ids_for_bookings(
                [booking_aggregate.booking_id]
            )
        )
        all_room_stays_to_mark_overflow = [
            booking_aggregate.get_room_stay(room_stay_id)
            for room_stay_id in all_room_stays_to_mark_overflow
        ]
        for room_stay in all_room_stays_to_mark_overflow:
            room_stay_overflow_aggregate = self._mark_overflow(
                booking_aggregate,
                room_stay,
                already_overflowing_room_stay_ids=booking_wise_overflow_room_stay_ids.get(
                    booking_aggregate.booking_id, []
                ),
            )
            marked_overflow_aggregates.append(room_stay_overflow_aggregate)

        if marked_overflow_aggregates:
            self.room_stay_overflow_repo.save_all(marked_overflow_aggregates)

        room_type_ids = list(
            {rs.room_type_id for rs in all_room_stays_to_mark_overflow}
        )
        unmarked_overflow_map = self.recompute_and_unmark_overflows(
            booking_aggregate.booking.hotel_id,
            start_date,
            end_date,
            room_type_ids,
            send_event=False,
            user_action=user_action,
        )

        for overflow_aggregate in marked_overflow_aggregates:
            booking_id = overflow_aggregate.room_stay_overflow.booking_id
            room_stay_id = overflow_aggregate.room_stay_overflow.room_stay_id
            if (
                booking_id not in unmarked_overflow_map
                or room_stay_id not in unmarked_overflow_map[booking_id]
            ):
                final_marked_overflow_aggregates.append(overflow_aggregate)

        if final_marked_overflow_aggregates:
            IntegrationEventApplicationService.create_overflow_updated_event(
                booking_aggregate.booking.hotel_id,
                final_marked_overflow_aggregates,
                user_action=user_action,
            )

        return final_marked_overflow_aggregates

    def recompute_and_unmark_overflows(
        self,
        hotel_id,
        start_date,
        end_date,
        room_type_ids,
        send_event=True,
        user_action=None,
    ):
        """
        For the given date range: [start_date, end_date], and room type ids, this method clears the existing room
        stay overflows, if those overflows are no more valid.

        This method would be used, whenever there is an inventory increasing operations, for e.g., cancel booking,
        remove dnr, resolve dnr, add room, etc
        """
        start_date, end_date = dateutils.to_date(start_date), dateutils.to_date(
            end_date
        )

        # Get all the overflow aggregates covering the given date range
        room_stay_overflow_aggregates = (
            self.room_stay_overflow_repo.get_overflowed_room_stays_for_dates(
                hotel_id, room_type_ids, start_date, end_date
            )
        )

        if not room_stay_overflow_aggregates:
            return

        grouped_room_stay_overflow_aggregates = defaultdict(list)
        for aggregate in room_stay_overflow_aggregates:
            grouped_room_stay_overflow_aggregates[aggregate.room_type_id].append(
                aggregate
            )

        deleted_room_stay_overflows = list()
        unmarked_overflows = defaultdict(set)
        room_type_ids = set(room_type_ids)

        for room_type_id in room_type_ids:
            room_type_room_stay_overflow_aggregates = (
                grouped_room_stay_overflow_aggregates[room_type_id]
            )
            if not room_type_room_stay_overflow_aggregates:
                continue

            # Now, for all the overflow aggregate covering original date range, get the min start date of those
            # aggregates and max end date of those aggregates
            min_overflow_date = min(
                aggregate.start_date
                for aggregate in room_type_room_stay_overflow_aggregates
            )
            max_overflow_date = max(
                aggregate.end_date
                for aggregate in room_type_room_stay_overflow_aggregates
            )
            room_type_inventory_aggregate = self.room_type_inventory_repo.load_multiple(
                hotel_id,
                min_overflow_date,
                max_overflow_date,
                room_type_ids=[room_type_id],
            )[0]
            rs_overflow_count_map = (
                self.room_stay_overflow_repo.get_overflow_count_for_dates(
                    hotel_id, min_overflow_date, max_overflow_date, room_type_id
                )
            )

            for room_stay_overflow_aggregate in room_type_room_stay_overflow_aggregates:
                # For each overflow, we'll decide whether it can be deleted or not, by comparing the availability
                # count of all dates between start and end date of that overflow, with the current overflow count
                # date wise.

                # This overflow can be deleted only if, for all the dates where this overflow exists, the current
                # total overflow count is greater than overbooking on that date
                # If there is at least 1 date in the overflow date range, where overflow count is less than or exact
                # match of overbooking count, then this overflow cannot be deleted

                # We'll begin with default assumption that overflow can be deleted. And then on first date
                # occurrence, where this is no more true, we'll set this to False and break the loop
                can_delete_this_overflow = True
                for date in dateutils.date_range(
                    room_stay_overflow_aggregate.start_date,
                    room_stay_overflow_aggregate.end_date,
                    end_inclusive=True,
                ):
                    overflow_count_for_date = rs_overflow_count_map.get(date)
                    availability = (
                        room_type_inventory_aggregate.get_availability_for_date(
                            date
                        ).actual_count
                    )
                    if availability >= 0:
                        # Availability is in positive. This condition means the overflow can be deleted on current
                        # date of the loop
                        continue

                    if overflow_count_for_date <= abs(availability):
                        # Overflow is 2, and availability is -2 or less than -2.
                        # In this condition, this overflow cannot be deleted. Break from loop, and continue to next
                        # overflow
                        can_delete_this_overflow = False
                        break

                if can_delete_this_overflow:
                    # Delete this overflow
                    room_stay_overflow_aggregate.delete()
                    deleted_room_stay_overflows.append(room_stay_overflow_aggregate)
                    unmarked_overflows[room_stay_overflow_aggregate.booking_id].add(
                        room_stay_overflow_aggregate.room_stay_id
                    )

                    # Reduce overflow count for all dates in this overflow aggregate by 1
                    # This is required, so that in the next loop iteration, next overflow is only looking into
                    # accommodating as per remaining overflow count
                    for date in dateutils.date_range(
                        room_stay_overflow_aggregate.start_date,
                        room_stay_overflow_aggregate.end_date,
                        end_inclusive=True,
                    ):
                        rs_overflow_count_map[date] -= 1

        if deleted_room_stay_overflows:
            self.room_stay_overflow_repo.delete_overflows(deleted_room_stay_overflows)

        if send_event and deleted_room_stay_overflows:
            IntegrationEventApplicationService.create_overflow_updated_event(
                hotel_id, deleted_room_stay_overflows, user_action=user_action
            )

        return unmarked_overflows

    @staticmethod
    def _is_room_stay_overlapping(rs1, rs2):
        return (
            rs1.checkin_date < rs2.checkout_date
            and rs1.checkout_date > rs2.checkin_date
        )

    def _fail_if_overflow_request_not_in_reserved_state(self, all_overflows):
        for overflow_req in all_overflows:
            room_stay = overflow_req.booking_aggregate.get_room_stay(
                overflow_req.room_stay_id
            )
            if room_stay.status != BookingStatus.RESERVED:
                raise InvalidOperationError(
                    error=ApplicationErrors.ROOM_STAY_MUST_BE_IN_RESERVED_STATE
                )

    def _fail_if_overflow_request_not_in_same_room_type(self, all_overflows):
        room_types = list()
        for overflow_req in all_overflows:
            room_stay = overflow_req.booking_aggregate.get_room_stay(
                overflow_req.room_stay_id
            )
            room_types.append(room_stay.room_type_id)

        if len(set(room_types)) > 1:
            raise InvalidOperationError(
                error=ApplicationErrors.ROOM_TYPE_ID_MISMATCH_IN_REQUEST
            )

    def _fail_if_overflow_request_not_in_same_hotel(self, hotel_id, all_overflows):
        for overflow_req in all_overflows:
            if overflow_req.booking_aggregate.booking.hotel_id != hotel_id:
                raise InvalidOperationError(
                    error=ApplicationErrors.HOTEL_ID_MISMATCH_IN_REQUEST
                )

    @classmethod
    def _is_from_direct_or_ota_channel(cls, booking_aggregate):
        return booking_aggregate.booking.source.channel_code in [
            BookingChannels.DIRECT.value,
            BookingChannels.OTA.value,
        ]

    def check_and_mark_room_stay_overflows(
        self, booking_aggregate, room_stay_id, user_action=None
    ):
        # delete all the previous overflow records of the room stay
        self.room_stay_overflow_repo.delete_overflow_tag(
            booking_id=booking_aggregate.booking.booking_id,
            room_stay_id=room_stay_id,
        )
        room_stay = booking_aggregate.get_room_stay(room_stay_id)

        room_stays_grouped_by_room_type = defaultdict(list)
        room_stays_grouped_by_room_type[room_stay.room_type_id].append(room_stay)

        # find the anomaly_count for all dates in booking
        start_date = dateutils.to_date(room_stay.checkin_date)
        end_date = dateutils.to_date(room_stay.checkout_date)

        room_type_inventory_aggregates = self.room_type_inventory_repo.load_multiple(
            booking_aggregate.booking.hotel_id,
            start_date,
            end_date,
            room_type_ids=[room_stay.room_type_id],
        )
        room_type_inventory_aggregate = (
            room_type_inventory_aggregates and room_type_inventory_aggregates[0]
        )
        if not room_type_inventory_aggregate:
            return

        room_stays_to_mark_overflow = set()
        dates_with_negative_inventory = {
            av.date: av
            for av in room_type_inventory_aggregate.room_type_inventory_availabilities
            if av.actual_count < 0
        }

        if not dates_with_negative_inventory:
            return

        date_wise_overflow_count = (
            self.room_stay_overflow_repo.get_overflow_count_for_dates(
                booking_aggregate.booking.hotel_id,
                min(dates_with_negative_inventory),
                max(dates_with_negative_inventory),
                room_stay.room_type_id,
            )
        )

        for date, availability in dates_with_negative_inventory.items():
            overbooking_count_not_covered_by_overflow_count = RoomStayOverflowDomainService.get_overbooking_not_covered_by_overflow_count(
                availability.actual_count, date_wise_overflow_count.get(date)
            )
            if overbooking_count_not_covered_by_overflow_count == 0:
                continue

            if overbooking_count_not_covered_by_overflow_count > 1:
                raise InvalidOperationError(
                    error=ApplicationErrors.BOOKINGS_NOT_AVAILABLE_TO_MARK_OVERFLOW
                )

            if room_stay.is_eligible_to_be_marked_overflow_on_date(date):
                room_stays_to_mark_overflow.add(room_stay.room_stay_id)

        if not room_stays_to_mark_overflow:
            return

        room_stay_overflow_aggregate = self._mark_overflow(
            booking_aggregate,
            room_stay,
        )

        self.room_stay_overflow_repo.save_all([room_stay_overflow_aggregate])

        unmarked_overflow_map = self.recompute_and_unmark_overflows(
            booking_aggregate.booking.hotel_id,
            start_date,
            end_date,
            [room_stay.room_type_id],
            send_event=False,
            user_action=user_action,
        )

        booking_id = room_stay_overflow_aggregate.room_stay_overflow.booking_id
        room_stay_id = room_stay_overflow_aggregate.room_stay_overflow.room_stay_id
        if (
            booking_id not in unmarked_overflow_map
            or room_stay_id not in unmarked_overflow_map[booking_id]
        ):
            IntegrationEventApplicationService.create_overflow_updated_event(
                booking_aggregate.booking.hotel_id,
                [room_stay_overflow_aggregate],
                user_action=user_action,
            )

        return [room_stay_overflow_aggregate]
