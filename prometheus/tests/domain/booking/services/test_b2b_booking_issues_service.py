from prometheus.domain.booking.services.b2b_booking_issues_service import (
    B2BBookingIssues,
    B2bBookingIssuesService,
)
from prometheus.domain.company_profiles.dto.company_profiles_dto import (
    POC,
    StatutoryDetail,
)
from prometheus.tests.factories.aggregate_factories import (
    BookingAggregateFactory,
    HotelFactory,
)
from prometheus.tests.factories.entity_factories import SubEntityFactory


def test_identify_issue_with_booking():
    booking_aggregate = BookingAggregateFactory()
    hotel_aggregate = HotelFactory()
    billing_entity = SubEntityFactory()
    b2b_booking_issues_service = B2bBookingIssuesService(
        billing_entity, sibling_sub_entities=None
    )
    b2b_booking_issues_service.identify_issue_with_booking(
        booking_aggregate, hotel_aggregate
    )
    assert b2b_booking_issues_service.issues == {}


def test_identify_issue_with_booking_with_gstin():
    booking_aggregate = BookingAggregateFactory()
    hotel_aggregate = HotelFactory()
    billing_entity = SubEntityFactory()
    billing_entity.statutory_details.append(
        StatutoryDetail(attachment_url="sample", field_name="gst", value="*********")
    )
    b2b_booking_issues_service = B2bBookingIssuesService(
        billing_entity, sibling_sub_entities=[]
    )
    b2b_booking_issues_service.identify_issue_with_booking(
        booking_aggregate, hotel_aggregate
    )
    assert len(b2b_booking_issues_service.issues) == 1
    assert b2b_booking_issues_service.issues.get(B2BBookingIssues.GSTIN_DIFFERENT_STATE)


def test_identify_issue_with_booking_with_gstin_with_same_state():
    booking_aggregate = BookingAggregateFactory()
    hotel_aggregate = HotelFactory()
    billing_entity = SubEntityFactory()
    billing_entity.statutory_details.append(
        StatutoryDetail(attachment_url="sample", field_name="gst", value="*********")
    )
    billing_entity.registered_address.state = hotel_aggregate.hotel.state.name
    b2b_booking_issues_service = B2bBookingIssuesService(
        billing_entity, sibling_sub_entities=[]
    )
    b2b_booking_issues_service.identify_issue_with_booking(
        booking_aggregate, hotel_aggregate
    )
    assert b2b_booking_issues_service.issues == {}


def test_identify_issue_with_dummy_guest_names():
    booking_aggregate = BookingAggregateFactory()
    hotel_aggregate = HotelFactory()
    booking_aggregate.customers[0].first_name = "Room1-Guest1"
    billing_entity = SubEntityFactory()
    b2b_booking_issues_service = B2bBookingIssuesService(
        billing_entity, sibling_sub_entities=None
    )
    b2b_booking_issues_service.identify_issue_with_booking(
        booking_aggregate, hotel_aggregate
    )
    assert len(b2b_booking_issues_service.issues) == 1
    assert B2BBookingIssues.GUEST_NAMES_DUMMY in b2b_booking_issues_service.issues


def test_identify_issue_with_dummy_guest_names_with_same_guest_names():
    booking_aggregate = BookingAggregateFactory(with_2_guest_stays=True)
    hotel_aggregate = HotelFactory()
    booking_aggregate.customers[0].first_name = "Room1-Guest1"
    billing_entity = SubEntityFactory()
    b2b_booking_issues_service = B2bBookingIssuesService(
        billing_entity, sibling_sub_entities=None
    )
    b2b_booking_issues_service.identify_issue_with_booking(
        booking_aggregate, hotel_aggregate
    )
    assert len(b2b_booking_issues_service.issues) == 2
    assert B2BBookingIssues.SAME_GUEST_NAMES in b2b_booking_issues_service.issues
    assert B2BBookingIssues.GUEST_NAMES_DUMMY in b2b_booking_issues_service.issues


def test_identify_issue_with_admin_name_as_guest_name():
    booking_aggregate = BookingAggregateFactory(with_2_guest_stays=True)
    hotel_aggregate = HotelFactory()
    booking_aggregate.customers[0].first_name = "Admin"
    booking_aggregate.customers[0].last_name = "Test"
    billing_entity = SubEntityFactory()
    billing_entity.point_of_contacts.append(
        POC(
            email_ids=["<EMAIL>"],
            name="Admin Test",
            phone_number={"number": "123", "country_code": "91"},
            department="booking",
            source="b2b",
            user_id="123",
            poc_type="primary",
            contact_types=["primary"],
            designation="Admin",
        )
    )

    b2b_booking_issues_service = B2bBookingIssuesService(
        billing_entity, sibling_sub_entities=None
    )
    b2b_booking_issues_service.identify_issue_with_booking(
        booking_aggregate, hotel_aggregate
    )
    assert B2BBookingIssues.ADMIN_NAME_GUEST in b2b_booking_issues_service.issues


def test_identify_issue_with_sibling_sub_entity_sharing_same_gstin():
    booking_aggregate = BookingAggregateFactory()
    hotel_aggregate = HotelFactory()
    billing_entity = SubEntityFactory()
    billing_entity.statutory_details.append(
        StatutoryDetail(attachment_url="sample", field_name="gst", value="*********")
    )
    sub_entity_2 = SubEntityFactory(superhero_company_code="dummy")
    sub_entity_2.statutory_details.append(
        StatutoryDetail(attachment_url="sample", field_name="gst", value="*********")
    )
    b2b_booking_issues_service = B2bBookingIssuesService(
        billing_entity, sibling_sub_entities=[billing_entity, sub_entity_2]
    )
    b2b_booking_issues_service.identify_issue_with_booking(
        booking_aggregate, hotel_aggregate
    )
    assert (
        B2BBookingIssues.MULTIPLE_GSTIN_ADDRESSES in b2b_booking_issues_service.issues
    )


def test_identify_issue_with_booking_gstin_having_different_state_same_alias():
    booking_aggregate = BookingAggregateFactory()
    hotel_aggregate = HotelFactory()
    billing_entity = SubEntityFactory()
    billing_entity.statutory_details.append(
        StatutoryDetail(attachment_url="sample", field_name="gst", value="*********")
    )
    billing_entity.registered_address.state = "Delhi"
    hotel_aggregate.hotel.state.name = "New Delhi"
    b2b_booking_issues_service = B2bBookingIssuesService(
        billing_entity, sibling_sub_entities=[]
    )
    b2b_booking_issues_service.identify_issue_with_booking(
        booking_aggregate, hotel_aggregate
    )
    assert b2b_booking_issues_service.issues == {}
