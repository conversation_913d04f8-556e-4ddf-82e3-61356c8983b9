import pytest
from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.domain.policy.facts.expense_facts import ExpenseFacts
from prometheus.domain.policy.rule.create_expense_rule import CreateExpenseRule
from prometheus.tests.factories.aggregate_factories import BookingAggregateFactory
from prometheus.tests.response.role_manager_privileges_response import (
    get_privilege_details_by_role,
)
from ths_common.constants.billing_constants import ChargeTypes
from ths_common.exceptions import PolicyAuthException
from ths_common.value_objects import PriceData


@pytest.fixture
def yesterday():
    return dateutils.subtract(dateutils.current_date(), days=1)


@pytest.fixture
def checked_out_booking(request, active_hotel_aggregate):
    from prometheus import crs_context

    active_hotel_aggregate.hotel.current_business_date = dateutils.current_date()
    crs_context.set_hotel_context(active_hotel_aggregate)

    booking = BookingAggregateFactory()
    guest_id = booking.customers[0].customer_id
    checkout_time = booking.booking.checkout_date
    checkin_success = (
        booking.room_stays[0]
        .guest_stays[0]
        .perform_checkin(guest_id=guest_id, checkin_date=booking.booking.checkin_date)
    )
    if checkin_success:
        booking.room_stays[0].guest_stays[0].perform_checkout(
            checkout_datetime=checkout_time
        )

    def clear_threadlocal_context():
        crs_context.clear()

    request.addfinalizer(clear_threadlocal_context)
    return booking


@pytest.fixture
def price():
    price_dict = {
        "pretax_amount": 100,
        "applicable_date": dateutils.current_datetime().isoformat(),
        "bill_to_type": "company",
        "type": ChargeTypes.CREDIT,
    }

    return PriceData(**price_dict)
