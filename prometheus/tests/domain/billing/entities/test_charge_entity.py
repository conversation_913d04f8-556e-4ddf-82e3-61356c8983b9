from datetime import datetime
from decimal import Decimal

import pytest
from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from prometheus.domain.billing.dto.chargesplit_data import ChargeSplitData
from prometheus.tests.mocks.tax_service_client_mock import mock_hotel_uses_posttax_price
from ths_common.constants.billing_constants import (
    ChargeBillToTypes,
    ChargeStatus,
    ChargeTypes,
    TaxTypes,
)
from ths_common.exceptions import ResourceNotFound, ValidationException
from ths_common.value_objects import ChargeItem, TaxDetail


class TestChargeEntity(object):
    def test_charge_is_active_status(self, consumed_charge_non_credit):
        assert consumed_charge_non_credit.is_active is True

        consumed_charge_non_credit._status = ChargeStatus.CREATED
        assert consumed_charge_non_credit.is_active is True

        consumed_charge_non_credit._status = ChargeStatus.CANCELLED
        assert consumed_charge_non_credit.is_active is False

    def test_get_split(self, consumed_charge_non_credit):
        charge_split_1 = consumed_charge_non_credit.get_split(1)
        assert charge_split_1.charge_split_id == 1

        charge_split_2 = consumed_charge_non_credit.get_split(2)
        assert charge_split_2.charge_split_id == 2

        with pytest.raises(ResourceNotFound):
            consumed_charge_non_credit.get_split(3)

    def test_charge_readonly_properties(self, consumed_charge_non_credit):
        # charge id should be readonly
        assert consumed_charge_non_credit.charge_id is not None
        with pytest.raises(AttributeError):
            consumed_charge_non_credit.charge_id = 2

        # pretax_amount is readonly
        assert consumed_charge_non_credit.pretax_amount is not None
        with pytest.raises(AttributeError):
            consumed_charge_non_credit.pretax_amount = Money('100', CurrencyType.INR)

        # tax_amount is readonly
        assert consumed_charge_non_credit.tax_amount is not None
        with pytest.raises(AttributeError):
            consumed_charge_non_credit.tax_amount = Money('100', CurrencyType.INR)

        # posttax_amount is readonly
        assert consumed_charge_non_credit.posttax_amount is not None
        with pytest.raises(AttributeError):
            consumed_charge_non_credit.posttax_amount = Money('100', CurrencyType.INR)

        # tax details in readonly
        assert consumed_charge_non_credit.tax_details is not None
        with pytest.raises(AttributeError):
            consumed_charge_non_credit.tax_details = [
                TaxDetail(
                    TaxTypes.SGST,
                    amount=Money('1000', CurrencyType.INR),
                    percentage=100,
                )
            ]

        # recorded_time is readonly
        assert consumed_charge_non_credit.recorded_time is not None
        with pytest.raises(AttributeError):
            consumed_charge_non_credit.recorded_time = datetime.now()

        # created_by is readonly
        assert consumed_charge_non_credit.created_by is not None
        with pytest.raises(AttributeError):
            consumed_charge_non_credit.created_by = "ram"

        # item is readonly
        assert consumed_charge_non_credit.item is not None
        with pytest.raises(AttributeError):
            consumed_charge_non_credit.item = ChargeItem("test", "stay", None)

    def test_charge_split_does_not_contain_deleted_entities(
        self, consumed_charge_non_credit
    ):
        charge_splits = consumed_charge_non_credit.charge_splits
        charge_split_len = len(charge_splits)

        charge_split_1 = consumed_charge_non_credit.get_split(1)
        charge_split_1.delete()
        charge_splits = consumed_charge_non_credit.charge_splits

        assert len(charge_splits) == charge_split_len - 1

        for charge_split in charge_splits:
            assert charge_split.charge_split_id != 1

    def test_applicable_date(
        self, consumed_charge_non_credit, created_charge_non_credit_guest
    ):
        assert isinstance(consumed_charge_non_credit.applicable_date, datetime)

        # test that applicable_date cannot be changed
        applicable_date = datetime.now()
        with pytest.raises(AttributeError):
            consumed_charge_non_credit.applicable_date = applicable_date

    def test_has_invoice_attached(self, consumed_charge_non_credit):
        assert consumed_charge_non_credit.has_invoice_attached is False

        consumed_charge_non_credit.get_split(1).update_invoice_id("INV-*********")

        assert consumed_charge_non_credit.has_invoice_attached is True

    def test_filter_splits(self, consumed_charge_non_credit):
        # mark on charge invoiced
        consumed_charge_non_credit.get_split(1).update_invoice_id("INV-*********")

        all_splits = consumed_charge_non_credit.filter_splits(exclude_invoiced=False)

        assert len(all_splits) == 2

        excluded_invoice_splits = consumed_charge_non_credit.filter_splits(
            exclude_invoiced=True
        )

        assert len(excluded_invoice_splits) == 1

    @mock_hotel_uses_posttax_price(value=True)
    def test_update_amount(self, created_charge_non_credit_guest):
        tax_details = [
            TaxDetail(
                TaxTypes.SGST, amount=Money('10', CurrencyType.INR), percentage=10
            ),
            TaxDetail(
                TaxTypes.CGST, amount=Money('10', CurrencyType.INR), percentage=10
            ),
            TaxDetail(TaxTypes.IGST, amount=Money('0', CurrencyType.INR), percentage=0),
        ]
        created_charge_non_credit_guest.update_amount(
            pretax_amount=Money('100', CurrencyType.INR),
            tax_amount=Money('20', CurrencyType.INR),
            posttax_amount=Money('120', CurrencyType.INR),
            tax_details=tax_details,
        )

        assert created_charge_non_credit_guest.pretax_amount == Money(
            '100', CurrencyType.INR
        )
        assert created_charge_non_credit_guest.posttax_amount == Money(
            '120', CurrencyType.INR
        )
        assert created_charge_non_credit_guest.tax_amount == Money(
            '20', CurrencyType.INR
        )
        assert len(created_charge_non_credit_guest.tax_details) == 3

        for charge_split in created_charge_non_credit_guest.charge_splits:
            assert charge_split.pre_tax == Money('50', CurrencyType.INR)
            assert charge_split.tax == Money('10', CurrencyType.INR)
            assert charge_split.post_tax == Money('60', CurrencyType.INR)
            assert len(charge_split.tax_details) == 3

        # Negative test
        # when pretax, posttax and tax does not sum up

        with pytest.raises(ValidationException):
            created_charge_non_credit_guest.update_amount(
                pretax_amount=Money('100', CurrencyType.INR),
                tax_amount=Money('20', CurrencyType.INR),
                posttax_amount=Money('130', CurrencyType.INR),
                tax_details=tax_details,
            )

        # when the tax details does not sum up
        with pytest.raises(ValidationException):
            created_charge_non_credit_guest.update_amount(
                pretax_amount=Money('100', CurrencyType.INR),
                tax_amount=Money('30', CurrencyType.INR),
                posttax_amount=Money('130', CurrencyType.INR),
                tax_details=tax_details,
            )
