from prometheus.integration_tests.config import common_config
from prometheus.integration_tests.config.request_uris import *
from prometheus.integration_tests.requests.base_request import BaseRequest
from prometheus.integration_tests.utilities.common_utils import return_date, convert_string_to_date
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.config.sheet_names import inventory_sheet_name

class InventoryRequests(BaseRequest):
    def get_available_room_ids(self, client, from_date, to_date, room_type=None, status_code=None, get_room_list=False,
                               hotel_id=None):
        uri = get_room_availability_slots_uri.format(hotel_id if hotel_id else common_config.HOTEL_ID[0],
                                                     return_date(int(from_date)),
                                                     return_date(int(to_date)), room_type)
        response = self.request_processor(client, 'GET', uri, status_code, None, None)
        if get_room_list:
            return [room_id['room_id'] for room_id in response.json['data']]
        else:
            return response.json

    def get_inventory_count(self, client_, from_date, to_date, room_type, status_code, hotel_id=None):
        uri = get_room_type_inventory.format(hotel_id = hotel_id if hotel_id else common_config.HOTEL_ID[0],
                                             from_date= convert_string_to_date(from_date),
                                             to_date=convert_string_to_date(to_date), room_type_id=room_type)
        response = self.request_processor(client_, 'GET', uri, status_code, None, None)
        return response.json

    def get_room_wise_inventory(self, client, status_code, booking_response):
        available_room_stay_with_count_map = {}
        for room_stay in booking_response['room_stays']:
            available_rooms = self.get_inventory_count(client, room_stay['stay_start'], room_stay['stay_end'],
                                                       room_stay['room_type_id'], status_code,
                                                       booking_response['hotel_id'])['data']
            available_room_stay_with_count_map[room_stay['room_stay_id']] = available_rooms
        return available_room_stay_with_count_map


    def get_all_inverntory_counts(self, client_, hotel_id, test_case_id, status_code):
        test_data = excel_utils.get_test_case_data(sheet_name=inventory_sheet_name, test_case_id=test_case_id)[0]
        uri = get_room_type_inventory.rsplit("&",1)[0]
        uri = uri.format(hotel_id=hotel_id,from_date = return_date(int(test_data['from_date'])).isoformat(),to_date=return_date(int(test_data['to_date'])).isoformat())
        response = self.request_processor(client_, 'GET', uri, status_code, None, None)
        return response.json
