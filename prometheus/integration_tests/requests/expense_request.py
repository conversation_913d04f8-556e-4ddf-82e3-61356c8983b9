import json

from prometheus.integration_tests.builders import expense_builder, create_expense_v3_builder
from prometheus.integration_tests.config.common_config import SUCCESS_CODES
from prometheus.integration_tests.config.request_uris import *
from prometheus.integration_tests.config.sheet_names import *
from prometheus.integration_tests.requests.base_request import BaseRequest
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.utilities.common_utils import del_none, get_booking_id, query_execute
from prometheus.tests.mockers import mock_tax_calculator_service, mock_catalog_client, mock_tax_call
from ths_common.constants.catalog_constants import SellerType


class ExpenseRequests(BaseRequest):
    def __init__(self):
        self.charge_id = None
        self.expense_id = None

    def create_expense_request(self, client, test_case_id, status_code, booking_id, user_type=None):
        if test_case_id == 'Create_Expense_44':
            booking_id = 'invalid_booking_id'
        elif test_case_id == 'Create_Expense_45':
            booking_id = None
        elif test_case_id == 'Create_Expense_46':
            booking_id = ''

        uri = create_expense_uri.format(booking_id)
        request_json = json.dumps(
            del_none(expense_builder.Expense(expense_sheet_name, test_case_id).__dict__))
        with mock_tax_calculator_service() and mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        if status_code in SUCCESS_CODES:
            self.charge_id = response.json['data'][0]['charge_id']
            self.expense_id = response.json['data'][0]['expense_id']
        return response.json

    def update_expense_request(self, client, test_case_id, status_code, booking_id, expense_id, user_type=None):
        uri = update_expense_uri.format(booking_id, expense_id)
        request_json = json.dumps(
            del_none(expense_builder.Expense.UpdateExpenseData(expense_sheet_name, test_case_id).__dict__))
        with mock_tax_calculator_service():
            response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        if status_code in SUCCESS_CODES:
            self.charge_id = response.json['data']['charge_id']
            self.expense_id = response.json['data']['expense_id']
        return response.json

    def get_expense_detail(self, client, status_code, booking_id, user_type=None):
        uri = get_expense_uri.format(booking_id)
        response = self.request_processor(client, 'GET', uri, status_code, user_type)
        return response.json

    def get_expense_by_expense_id(self, client, status_code, booking_id, expense_id, user_type=None):
        uri = get_expense_by_expense_id_uri.format(booking_id, expense_id)
        response = self.request_processor(client, 'GET', uri, status_code, user_type)
        return response.json

    def create_expense_request_v3(self, client, test_case_id, status_code, booking_id, user_type=None,
                                  new_tax_mocker=False):
        if test_case_id in ('Multiple_Expense_32', 'Multiple_Expense_33', 'Multiple_Expense_34'):
            booking_id = get_booking_id(expense_sheet_name, test_case_id)
        block_id = None
        if test_case_id in ('Create_Expense_96', 'Create_Expense_97', 'Create_Expense_98', 'Create_Expense_99'):
            block_id = query_execute(
                db_queries.GET_INVENTORY_BLOCK_ID.format(booking_id=booking_id, block_type='temp_block')).fetchall()
        uri = create_expense_v3_uri.format(booking_id)
        request_json = json.dumps(del_none(create_expense_v3_builder.CreateExpenseV3(expense_sheet_name,
                                                                                     test_case_id, block_id).__dict__))
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            if new_tax_mocker:
                with mock_tax_call(calculate_tax=True):
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
            else:
                with mock_tax_calculator_service():
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json
