import json

from prometheus.integration_tests.builders import web_checkin_builder
from prometheus.integration_tests.config.request_uris import web_checkin_uri, patch_web_checkin_uri
from prometheus.integration_tests.config.sheet_names import web_checkin_sheet_name
from prometheus.integration_tests.utilities.common_utils import del_none
from prometheus.integration_tests.requests.base_request import BaseRequest


class WebCheckInRequests(BaseRequest):

    def create_web_checkin_request(self, client, test_case_id, status_code, booking_id):
        request_json = json.dumps(
            del_none(web_checkin_builder.WebCheckin(web_checkin_sheet_name, test_case_id, booking_id).__dict__))
        uri = web_checkin_uri.format(booking_id=booking_id)
        response = self.request_processor(client, 'POST', uri, status_code, request_json)
        self.web_checkin_id = response.json['data']['web_checkin_id']
        return response.json

    def patch_web_checkin_request(self, client, test_case_id, status_code, booking_id):
        request_json = json.dumps(
            del_none(web_checkin_builder.PatchWebCheckin(web_checkin_sheet_name, test_case_id, booking_id).__dict__))
        uri = patch_web_checkin_uri.format(booking_id=booking_id, web_checkin_id=self.web_checkin_id)
        response = self.request_processor(client, 'PATCH', uri, status_code, request_json)
        return response.json
