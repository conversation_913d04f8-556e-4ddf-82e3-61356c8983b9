import json

from prometheus.integration_tests.config.request_uris import create_e_reg_card, delete_e_reg_card
from prometheus.integration_tests.builders import e_reg_card_builder
from prometheus.integration_tests.config.sheet_names import create_e_reg_card_sheet_name
from prometheus.integration_tests.utilities.common_utils import del_none
from prometheus.integration_tests.requests.base_request import BaseRequest


class ERegCardRequests(BaseRequest):

    def create_e_reg_card_request(self, client, test_case_id, status_code, booking_id):
        request_json = json.dumps(
            del_none(e_reg_card_builder.CreateERegCardRequest(create_e_reg_card_sheet_name, test_case_id
                                                              ).__dict__))
        uri = create_e_reg_card.format(booking_id)
        response = self.request_processor(client, 'POST', uri, status_code, request_json)
        self.e_reg_card_id = [response_data['e_reg_card_id'] for response_data in response.json['data']]
        return response.json

    def delete_e_reg_card_request(self, client, status_code, booking_id, is_primary):
        response_get_request = self.get_e_reg_card_request(client, 200, booking_id)
        for response_get_data in response_get_request['data']:
            if response_get_data['is_primary'] == is_primary:
                e_reg_card_id = response_get_data['e_reg_card_id']
                uri = delete_e_reg_card.format(booking_id, e_reg_card_id)
                response = self.request_processor(client, 'DELETE', uri, status_code)
                return response.json
        raise Exception("Not a single e_reg_card_id exist with condition is_primary = " + str(is_primary))

    def get_e_reg_card_request(self, client, status_code, booking_id):
        uri = create_e_reg_card.format(booking_id)
        response = self.request_processor(client, 'GET', uri, status_code)
        return response.json

    def edit_e_reg_card_request(self, client, test_case_id, status_code, booking_id, e_reg_card_id):
        request_json = json.dumps(
            del_none(e_reg_card_builder.EditERegCardRequest(create_e_reg_card_sheet_name, test_case_id,
                                                            e_reg_card_id).__dict__))
        uri = create_e_reg_card.format(booking_id)
        response = self.request_processor(client, 'PATCH', uri, status_code, request_json)
        return response.json
