import json

from treebo_commons.money.constants import CurrencyType

from object_registry import locate_instance
from prometheus.application.end_of_day.night_audit_service import NightAuditService
from prometheus.integration_tests.builders import (
    abort_checkout_builder,
    add_expense_builder,
    add_guest_stay_builder,
    add_multiple_guest_stay_builder,
    add_multiple_room_stay_builder,
    add_on_v2_builder,
    add_room_builder,
    addon_builder,
    allocate_room_builder,
    booking_action_builder,
    booking_action_builder_v2,
    booking_builder,
    booking_builder_v2,
    bulk_addon_v2_builder,
    bulk_edit_guest_stay_builder,
    calculate_cancellation_charges_builder,
    edit_guest_stay_builder,
    edit_rate_plan_charge_builder,
    get_refund_details_for_cancellation_policy_builder,
    mark_cancelled_room_guest_builder,
    mark_no_show_builder,
    preview_invoice_builder,
    redistribute_payments_between_accounts_builder,
    reissue_invoice_builder,
    update_default_billing_instructions_builder,
    update_rate_plan_builder,
    update_room_stay_builder,
    update_stay_duration_builder,
    update_ta_commission_builder,
)
from prometheus.integration_tests.builders.common_request_builder import (
    RoomStays,
    booking_repo,
    get_resource_version,
)
from prometheus.integration_tests.builders.external_clients.rate_manager_client import (
    RateManagerClient,
    get_rack_manger_data,
)
from prometheus.integration_tests.config.common_config import (
    PACKAGE_DETAILS,
    RATE_MANAGER_ENABLED,
    RESELLER_CONFIG,
    SUCCESS_CODES,
    USE_CANCELLATION_POLICY,
)
from prometheus.integration_tests.config.request_uris import *
from prometheus.integration_tests.config.sheet_names import *
from prometheus.integration_tests.requests.base_request import BaseRequest
from prometheus.integration_tests.requests.billing_requests import BillingRequests
from prometheus.integration_tests.resources.db_queries import UPDATE_PACKAGE_DETAILS
from prometheus.integration_tests.utilities.common_utils import *
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.tests.mockers import (
    mock_authn_service_client,
    mock_authz_service_client,
    mock_auto_approve_payout_link_amount,
    mock_aws_service_client,
    mock_catalog_client,
    mock_communication_service_client,
    mock_company_profile_service,
    mock_get_rate_plan_and_package_data,
    mock_get_setting_value_tenant,
    mock_rate_manager_client,
    mock_refund_response_by_payout_link,
    mock_refund_response_by_razorpay_api,
    mock_refund_response_by_treebo_corporate_rewards,
    mock_refund_reward_points_response,
    mock_refund_rules,
    mock_role_manager,
    mock_rule_engine,
    mock_tax_calculator_service,
    mock_tax_call,
    mock_template_service,
    mock_tenant_config,
    mock_tenant_config_for_payment_rules,
    mock_treebo_tenant_boolean,
    mock_trial_balance_reporting_service,
)
from ths_common.constants.catalog_constants import SellerType


class BookingRequests(BaseRequest):
    def __init__(self):
        BaseRequest.__init__(self)
        self.booking_response = None
        self.invoice_previews = []
        self.booking_invoices = None

    def add_room(self, client, test_case_id, status_code, booking_id, user_type=None):
        request_json = json.dumps(del_none(add_room_builder.AddRoom(add_room_stay_sheet_name, test_case_id,
                                                                    booking_id).__dict__))
        uri = add_room_uri.format(booking_id)

        response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)

        return response.json

    def new_booking_request(self, client, test_case_id, status_code, user_type=None, include_kerala_cess=False,
                            hotel_id='0016932', enable_rate_plan=False, is_booking_v2=False, is_inclusion_added=False):
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        request_json = json.dumps(
            del_none(booking_builder.CreateBookingRequest(new_booking_sheet_name, test_case_id, hotel_id,
                                                          enable_rate_plan, is_inclusion_added).__dict__))
        with mock_rate_manager_client():
            with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
                with mock_tax_calculator_service(include_kerala_cess=include_kerala_cess,
                                                 currency=CurrencyType(currency)):
                    with mock_role_manager():
                        uri = booking_uri_v2 if is_booking_v2 else booking_uri
                        response = self.request_processor(client, 'POST', uri, status_code, request_json,
                                                          user_type)
        if status_code in SUCCESS_CODES:
            self.booking_response = response.json['data']
            self.booking_id = response.json['data']['booking_id']
            self.bill_id = response.json['data']['bill_id']
            self.booking_ids.append(response.json['data']['booking_id'])
            self.reference_numbers.append(response.json['data']['reference_number'])
            self.bill_ids.append(response.json['data']['bill_id'])
            self.hotel_id = response.json['data']['hotel_id']
        return response.json

    def new_booking_request_v2(self, client, test_case_id, status_code, user_type=None, extras=None,
                               hotel_id='0016932', payment_matrix=False, seller_type=SellerType.MARKETPLACE.value,
                               new_tax_mocker=False):
        include_kerala_cess = extras.get('include_kerala_cess') if isinstance(extras, dict) else False
        if not new_tax_mocker:
            new_tax_mocker = extras.get('new_tax_mocker') if isinstance(extras, dict) else False
        if not payment_matrix:
            payment_matrix = extras.get('payment_matrix') if isinstance(extras, dict) else False
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        request_json = booking_builder_v2.BookingRequest(new_booking_v2_sheet_name, test_case_id,
                                                         hotel_id).booking_request()
        non_rate_plan_booking = extras.get('non_rate_plan_booking') if isinstance(extras, dict) else False
        rack_manager_mock_data = None
        if not non_rate_plan_booking:
            RateManagerClient(test_case_id, new_booking_v2_sheet_name, hotel_id).set_rate_plan_data()
            rack_manager_mock_data = get_rack_manger_data()
        has_slab_based_taxation = extras.get('has_slab_based_taxation') if isinstance(extras, dict) else False
        hotel_level_config = []
        clubbed_taxation = False
        if extras and isinstance(extras, dict) and extras.get('hotel_level_config'):
            hotel_level_config = extras.get('hotel_level_config')
            if not any(config.get("config_name") == "reseller_channels" for config in hotel_level_config):
                clubbed_taxation = True
        with mock_get_rate_plan_and_package_data(rack_manager_mock_data), \
                mock_catalog_client(mocked_seller_type=seller_type), mock_role_manager(), \
                mock_tenant_config(hotel_level_config=hotel_level_config), mock_company_profile_service():
            if payment_matrix and not new_tax_mocker:
                with mock_tenant_config_for_payment_rules(), \
                        mock_tax_calculator_service(include_kerala_cess=include_kerala_cess,
                                                    currency=CurrencyType(currency), clubbed_taxation=clubbed_taxation,
                                                    has_slab_based_taxation=has_slab_based_taxation):
                    response = self.request_processor(client, 'POST', booking_uri_v2, status_code, request_json,
                                                      user_type)
            elif new_tax_mocker:
                with mock_tenant_config_for_payment_rules(), mock_tax_call(calculate_tax=True):
                    response = self.request_processor(client, 'POST', booking_uri_v2, status_code, request_json,
                                                      user_type)
            else:
                with mock_tax_calculator_service(include_kerala_cess=include_kerala_cess,
                                                 currency=CurrencyType(currency), clubbed_taxation=clubbed_taxation,
                                                 has_slab_based_taxation=has_slab_based_taxation):
                    response = self.request_processor(client, 'POST', booking_uri_v2, status_code, request_json,
                                                      user_type)

        if status_code in SUCCESS_CODES:
            self.booking_response = response.json['data']
            self.booking_id = response.json['data']['booking_id']
            self.bill_id = response.json['data']['bill_id']
            self.hotel_id = response.json['data']['hotel_id']
            self.reference_number = response.json['data']['reference_number']
            self.booking_ids.append(response.json['data']['booking_id'])
            self.reference_numbers.append(response.json['data']['reference_number'])
            self.bill_ids.append(response.json['data']['bill_id'])
            self.room_ids = [room_id['room_stay_id'] for room_id in response.json['data']['room_stays']]
        return response.json

    def put_booking_request(self, client, test_case_id, status_code, user_type=None, extras=None,
                            hotel_id='0016932'):
        include_kerala_cess = extras.get('include_kerala_cess') if isinstance(extras, dict) else False
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        request_json = booking_builder_v2.BookingRequest(new_booking_v2_sheet_name, test_case_id,
                                                         hotel_id, self.booking_id, True).booking_request()
        non_rate_plan_booking = extras.get('non_rate_plan_booking') if isinstance(extras, dict) else False
        rack_manager_mock_data = None
        if not non_rate_plan_booking:
            RateManagerClient(test_case_id, new_booking_v2_sheet_name, hotel_id).set_rate_plan_data()
            rack_manager_mock_data = get_rack_manger_data()
        put_booking_uri = edit_booking_uri.format(self.booking_id)
        with mock_get_rate_plan_and_package_data(rack_manager_mock_data):
            with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
                with mock_tax_calculator_service(include_kerala_cess=include_kerala_cess,
                                                 currency=CurrencyType(currency)):
                    with mock_role_manager():
                        response = self.request_processor(client, 'PUT', put_booking_uri, status_code, request_json,
                                                          user_type)
        return response.json

    def put_booking_v2_request(self, client, test_case_id, status_code, user_type=None, extras=None, hotel_id='0016932',
                               has_slab_based_taxation=False, is_tax_clubbed=[]):
        include_kerala_cess = extras.get('include_kerala_cess') if isinstance(extras, dict) else False
        payment_matrix = extras.get('payment_matrix') if isinstance(extras, dict) else False
        clubbed_taxation = True if is_tax_clubbed else False
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        request_json = booking_builder_v2.BookingRequest(new_booking_v2_sheet_name, test_case_id,
                                                         hotel_id, self.booking_id,
                                                         is_put_booking_v2=True).booking_request()
        non_rate_plan_booking = extras.get('non_rate_plan_booking') if isinstance(extras, dict) else False
        rack_manager_mock_data = None
        if not non_rate_plan_booking:
            RateManagerClient(test_case_id, new_booking_v2_sheet_name, hotel_id, True).set_rate_plan_data()
            rack_manager_mock_data = get_rack_manger_data()
        put_booking_uri = edit_booking_v2_uri.format(self.booking_id)
        with mock_get_rate_plan_and_package_data(rack_manager_mock_data), \
                mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value), \
                mock_tax_calculator_service(include_kerala_cess=include_kerala_cess, currency=CurrencyType(currency),
                                            clubbed_taxation=clubbed_taxation,
                                            has_slab_based_taxation=has_slab_based_taxation), mock_role_manager(), \
                mock_tenant_config(hotel_level_config=is_tax_clubbed), mock_company_profile_service():
            if test_case_id in ('put_booking_79', 'put_booking_78'):
                with mock_treebo_tenant_boolean():
                    response = self.request_processor(client, 'PUT', put_booking_uri, status_code, request_json,
                                                      user_type)
            elif payment_matrix:
                with mock_tenant_config_for_payment_rules():
                    response = self.request_processor(client, 'PUT', put_booking_uri, status_code, request_json,
                                                      user_type)
            else:
                response = self.request_processor(client, 'PUT', put_booking_uri, status_code, request_json, user_type)
        return response.json

    def patch_booking_request(self, client, test_case_id, status_code, user_type=None, hotel_id='0016932'):
        request_json = booking_builder_v2.BookingRequest(new_booking_v2_sheet_name, test_case_id,
                                                         hotel_id, self.booking_id,
                                                         self.reference_number).patch_booking_request()
        patch_booking_uri = edit_booking_uri.format(self.booking_id)
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value), mock_role_manager(), \
                mock_tax_call(calculate_tax=True), mock_company_profile_service():
            response = self.request_processor(client, 'PATCH', patch_booking_uri, status_code, request_json,
                                              user_type)
        return response.json

    def get_booking_request(self, client, booking_id, status_code, user_type=None):
        uri = get_booking_uri.format(booking_id)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        self.booking_response = response.json['data']
        return response.json

    def get_booking_request_v2(self, client, booking_id, status_code, user_type=None):
        uri = get_booking_uri_v2.format(booking_id)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        self.booking_response = response.json['data']
        return response.json

    def edit_rate_plan_charge_request(self, client, test_case_id, currency, booking_id, status_code, user_type=None,
                                      has_slab_based_taxation=False, is_tax_clubbed=[], new_tax_mocker=False):
        clubbed_taxation = True if is_tax_clubbed else False
        request_json = edit_rate_plan_charge_builder.EditRatePlanCharge(charges_v2_sheet_name, test_case_id,
                                                                        booking_id).edit_rate_plan_charge_request()
        uri = edit_rate_plan_charge_uri.format(booking_id, get_test_case_data(charges_v2_sheet_name,
                                                                              test_case_id)[0]['room_stay_id'])
        with mock_role_manager(), mock_rule_engine(), mock_tenant_config(hotel_level_config=is_tax_clubbed):
            if new_tax_mocker:
                with mock_tax_call(calculate_tax=True):
                    response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
            else:
                with mock_tax_calculator_service(has_slab_based_taxation=has_slab_based_taxation,
                                                 clubbed_taxation=clubbed_taxation, currency=CurrencyType(currency)):
                    response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def edit_booking_details(self, client, test_case_id, status_code, booking_id, user_type=None,
                             include_kerala_cess=False, hotel_id='0016932'):
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        request_json = json.dumps(
            del_none(booking_builder.EditBookingRequest(new_booking_sheet_name, test_case_id, booking_id).__dict__))
        uri = edit_booking_details_uri.format(booking_id)
        with mock_tax_calculator_service(include_kerala_cess=include_kerala_cess, currency=CurrencyType(currency)):
            response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def edit_customer_details(self, client, test_case_id, status_code, booking_id, user_type=None,
                              include_kerala_cess=False, hotel_id='0016932', is_booker=None):
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        request_json = json.dumps(
            del_none(booking_builder.EditCustomerRequest(customer_data_sheet_name, test_case_id, booking_id,
                                                         is_booker).__dict__))
        uri = patch_customer_details_uri.format(booking_id,
                                                get_test_case_data(customer_data_sheet_name, test_case_id)[0][
                                                    'customer_id'])
        with mock_tax_calculator_service(include_kerala_cess=include_kerala_cess, currency=CurrencyType(currency)):
            with mock_role_manager():
                response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def update_default_billing_instructions(self, client, test_case_id, status_code, user_type=None):
        request_json = json.dumps(del_none(update_default_billing_instructions_builder.UpdateDefaultBillingInstructions(
            update_default_billing_instructions_sheet_name, test_case_id, self.booking_id).__dict__))
        uri = update_default_billing_instructions_uri.format(self.booking_id)
        with mock_tax_call(calculate_tax=True):
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def add_multiple_room_stay(self, client, test_case_id, status_code, booking_id, enable_rate_plan,
                               is_inclusion_added, user_type=None, has_slab_based_taxation=False,
                               is_tax_clubbed=[], new_tax_mocker=False):
        clubbed_taxation = True if is_tax_clubbed else False
        request_json = json.dumps(
            del_none(add_multiple_room_stay_builder.AddMultipleRoomStay(test_case_id, booking_id, enable_rate_plan,
                                                                        is_inclusion_added).__dict__))
        uri = add_multiple_room_stay_uri.format(booking_id)
        with mock_role_manager(), mock_tenant_config(hotel_level_config=is_tax_clubbed), mock_catalog_client():
            if new_tax_mocker:
                with mock_tax_call(calculate_tax=True):
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
            else:
                with mock_tax_calculator_service(has_slab_based_taxation=has_slab_based_taxation,
                                                 clubbed_taxation=clubbed_taxation):
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def add_guest_stay(self, client, test_case_id, status_code, booking_id, user_type=None, is_booker=False):
        request_json = json.dumps(
            del_none(add_guest_stay_builder.AddGuestStay(add_guest_stay_sheet_name_api, test_case_id,
                                                         booking_id, is_booker).__dict__))
        uri = add_guest_stay_uri.format(booking_id,
                                        get_test_case_data(add_guest_stay_sheet_name_api, test_case_id)[0][
                                            'room_stay_id'])
        with mock_role_manager():
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def edit_guest_stay(self, client, booking_id, test_case_id, status_code, user_type=None,
                        extras=None):
        request_json = edit_guest_stay_builder.EditGuestStay(guest_stays_sheet_name, test_case_id,
                                                             booking_id).get_edit_guest_stay_request()
        guest_data = get_test_case_data(guest_stays_sheet_name, test_case_id)[0]
        room_stay_id = guest_data['room_stay_id']
        guest_stay_id = guest_data['guest_stay_id']
        if test_case_id == 'patch_guest_stay_12':
            booking_id = '1234'
        elif test_case_id == 'patch_guest_stay_13':
            booking_id = None
        elif test_case_id == 'patch_guest_stay_14':
            room_stay_id = '12'
        elif test_case_id == 'patch_guest_stay_15':
            room_stay_id = None
        elif test_case_id == 'patch_guest_stay_16':
            guest_stay_id = '123'
        elif test_case_id == 'patch_guest_stay_17':
            guest_stay_id = None
        uri = edit_guest_stay_uri.format(booking_id, room_stay_id, guest_stay_id)
        with mock_role_manager():
            response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def add_multiple_guest_stay(self, client, test_case_id, status_code, booking_id, enable_rate_plan,
                                is_inclusion_added, user_type=None, has_slab_based_taxation=False,
                                hotel_id='0016932', is_tax_clubbed=[]):
        request_json = json.dumps(
            del_none(add_multiple_guest_stay_builder.AddMultipleGuestStay(add_guest_stay_sheet_name_api, test_case_id,
                                                                          booking_id, enable_rate_plan,
                                                                          is_inclusion_added).__dict__))
        room_stay_id = get_room_stay_id(add_guest_stay_sheet_name_api, test_case_id)
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        clubbed_taxation = True if is_tax_clubbed else False
        if test_case_id in ('AddMultipleGuest_83', 'AddMultipleGuest_84', 'AddMultipleGuest_85'):
            booking_id = get_booking_id(add_guest_stay_sheet_name_api, test_case_id)
        uri = add_multiple_guest_stay_uri.format(booking_id, room_stay_id)
        with mock_role_manager(), mock_tenant_config(hotel_level_config=is_tax_clubbed), mock_tax_call(
                calculate_tax=True):
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def bulk_edit_customer_details(self, client, test_case_id, status_code, booking_id, user_type=None,
                                   hotel_id='0016932'):
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        request_json = json.dumps(
            del_none(booking_builder.EditBulkCustomerRequest(customer_data_sheet_name, test_case_id,
                                                             booking_id).__dict__))
        uri = patch_bulk_customer_details_uri.format(booking_id)
        with mock_role_manager():
            response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def bulk_edit_guest_stay(self, client, test_case_id, status_code, booking_id, user_type=None):
        request_json = json.dumps(
            del_none(bulk_edit_guest_stay_builder.EditBulkGuestStay(bulk_edit_guest_stay_sheet_name, test_case_id,
                                                                    booking_id).__dict__))
        uri = patch_bulk_guest_stay_uri.format(booking_id)
        with mock_role_manager():
            response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    # $$$$$$$$$$$$$$$$$$$$$$$ ACTIONS $$$$$$$$$$$$$$$$$$$$
    def check_in_request(self, client, booking_id, test_case_id, status_code, user_type=None, hotel_id='0016932',
                         hotel_level_config=None, room_id=None):
        request_json = json.dumps(del_none(booking_action_builder.BookingActionBuilder
                                           (sheet_name=booking_action_sheet_name, test_case_id=test_case_id,
                                            client=client, booking_id=booking_id, hotel_id=hotel_id,
                                            room_id=room_id).__dict__))
        uri = booking_action_uri.format(booking_id)
        if hotel_level_config:
            with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
                with mock_tenant_config(hotel_level_config=hotel_level_config):
                    with mock_role_manager():
                        response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        else:
            with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
                with mock_role_manager():
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        if status_code in SUCCESS_CODES:
            self.action_id = response.json['data']['action_id']
        return response.json

    def check_in_request_v2(self, client, booking_id, test_case_id, status_code, user_type=None,
                            hotel_id='0016932', extras=None):
        room_id = extras.get('room_id') if extras else None
        hotel_level_config = extras.get('hotel_level_config', None) if extras else None

        checkin_builder = booking_action_builder_v2.CheckinBuilder(booking_actions_sheet_name, test_case_id, client,
                                                                   hotel_id, "Checkin")
        request_json = checkin_builder.get_checkin_booking_request(booking_id)
        uri = checkin_booking_uri.format(booking_id=booking_id)
        if hotel_level_config:
            with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
                with mock_tenant_config(hotel_level_config=hotel_level_config):
                    with mock_role_manager():
                        response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        else:
            with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
                with mock_role_manager():
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        if status_code in SUCCESS_CODES:
            self.action_id = response.json['data']['action_id']
            self.room_stay_ids_and_room_ids = checkin_builder.room_stay_ids_and_room_ids
        return response.json

    def reset_room_stays_and_room_ids(self):
        self.room_stay_ids_and_room_ids.clear()

    def preview_invoice_request(self, client, booking_id, test_case_id, status_code, user_type=None,
                                include_kerala_cess=False, hotel_id='0016932',
                                seller_type=SellerType.MARKETPLACE.value):
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        request_json = json.dumps(del_none(preview_invoice_builder.PreviewInvoiceBuilder
                                           (sheet_name=invoice_sheet_name, test_case_id=test_case_id,
                                            booking_id=booking_id).__dict__))
        uri = invoice_uri.format(booking_id)
        with mock_catalog_client(mocked_seller_type=seller_type), mock_tax_call(
                calculate_tax=True), mock_role_manager(), \
                mock_refund_rules(), mock_tenant_config(
            hotel_level_config=RATE_MANAGER_ENABLED + USE_CANCELLATION_POLICY + RESELLER_CONFIG):
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        if status_code in SUCCESS_CODES and response.json['data']['invoice_group_id']:
            self.invoice_group_id = response.json['data']['invoice_group_id']
            for invoice_preview in response.json['data']['invoice_previews']:
                self.invoice_previews.append(invoice_preview)
        else:
            self.invoice_group_id = None
        return response.json

    def patch_invoice_request(self, client, test_case_id, status_code, booking_id, invoice_id, user_type=None):
        request_json = json.dumps(del_none(preview_invoice_builder.PatchInvoiceBuilder
                                           (sheet_name=invoice_sheet_name, test_case_id=test_case_id,
                                            invoice_id=invoice_id).__dict__))
        uri = patch_invoice_uri.format(booking_id, invoice_id)
        response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def redistribute_payments_request(self, client, bill_id, test_case_id, status_code, user_type=None):
        request_json = json.dumps(del_none(redistribute_payments_between_accounts_builder.RedistributePaymentsBuilder
                                           (redistribute_payments_sheet_name, test_case_id,
                                            self.invoice_group_id).__dict__))
        uri = redistribute_payment_uri.format(bill_id)
        response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def get_refund_details_for_cancellation_policy_request(self, client, booking_id, test_case_id, status_code,
                                                           user_type=None, refund_rule_required=True):
        request_json = get_refund_details_for_cancellation_policy_builder.GetRefundDetailsForCancellationPolicy(
            get_refund_details_for_cancellation_policy_sheet_name, test_case_id,
            booking_id).get_refund_details_for_cancellation_policy_data()
        uri = get_refund_details_for_cancellation_policy_uri.format(booking_id=booking_id)
        if refund_rule_required:
            with mock_refund_rules():
                response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        else:
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def abort_checkout_request(self, client, booking_id, test_case_id, status_code, user_type=None):
        request_json = json.dumps(del_none(abort_checkout_builder.AbortCheckoutBuilder(abort_checkout_sheet_name,
                                                                                       test_case_id,
                                                                                       self.invoice_group_id).__dict__))
        uri = abort_checkout_uri.format(booking_id)
        response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def checkout_request(self, client, test_case_id, booking_id, invoice_group_id, status_code, user_type=None):
        request_json = json.dumps(del_none(booking_action_builder.BookingActionBuilder
                                           (sheet_name=booking_action_sheet_name, test_case_id=test_case_id,
                                            client=client, booking_id=booking_id,
                                            invoice_group_id=invoice_group_id).__dict__))
        uri = booking_action_uri.format(booking_id)
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value), mock_tenant_config(
                hotel_level_config=RESELLER_CONFIG):
            with mock_role_manager():
                response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        self.action_id = response.json['data']['action_id']
        return response.json

    def checkout_request_v2(self, client, test_case_id, booking_id, status_code, hotel_id='0016932',
                            user_type=None, seller_type=SellerType.MARKETPLACE.value):
        checkout_builder = booking_action_builder_v2.CheckoutBuilder(checkout_v2_sheet_name, test_case_id, client,
                                                                     hotel_id)
        request_json = checkout_builder.get_checkout_booking_request(booking_id, self.invoice_group_id)
        uri = checkout_uri.format(booking_id=booking_id)
        with mock_catalog_client(mocked_seller_type=seller_type), mock_role_manager(), mock_tenant_config(
                hotel_level_config=RESELLER_CONFIG):
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        if status_code is 200:
            self.action_id = response.json['data']['action_id']
        return response.json

    def cancel_request(self, client, booking_id, test_case_id, status_code, user_type=None, include_kerala_cess=False,
                       hotel_id='0016932'):
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        request_json = json.dumps(del_none(booking_action_builder.BookingActionBuilder
                                           (sheet_name=booking_action_sheet_name, test_case_id=test_case_id,
                                            client=client, booking_id=booking_id).__dict__))
        uri = booking_action_uri.format(booking_id)
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            with mock_tax_calculator_service(include_kerala_cess=include_kerala_cess, currency=CurrencyType(currency)):
                with mock_role_manager():
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        self.action_id = response.json['data']['action_id']
        return response.json

    def no_show_request(self, client, booking_id, test_case_id, status_code, user_type=None, include_kerala_cess=False):
        request_json = json.dumps(del_none(booking_action_builder.BookingActionBuilder
                                           (sheet_name=booking_action_sheet_name, test_case_id=test_case_id,
                                            client=client, booking_id=booking_id).__dict__))
        uri = booking_action_uri.format(booking_id)
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            with mock_tax_calculator_service(include_kerala_cess=include_kerala_cess):
                with mock_role_manager():
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        self.action_id = response.json['data']['action_id']
        return response.json

    def get_booking_actions(self, client, booking_id=None):
        if booking_id:
            uri = get_booking_actions_uri.format(booking_id=booking_id)
        else:
            uri = get_booking_actions_uri.format(booking_id=self.booking_id)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, 200)
        return response.json

    def delete_booking_action(self, client, status_code, include_kerala_cess=None, action_id=None, hotel_id='0016932',
                              clubbed_taxation=False):
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        request_json = json.dumps(
            del_none({'booking_id': self.booking_id, 'action_id': action_id if action_id else self.action_id}))
        uri = delete_booking_action_uri.format(booking_id=self.booking_id,
                                               action_id=action_id if action_id else self.action_id)
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            with mock_tax_calculator_service(include_kerala_cess=include_kerala_cess, currency=CurrencyType(currency),
                                             clubbed_taxation=clubbed_taxation):
                with mock_role_manager():
                    response = self.request_processor(client, 'DELETE', uri, status_code, request_json)
        return response.json

    def set_action_id(self, client, action_type):
        booking_actions = self.get_booking_actions(client)['data']
        for booking_action in booking_actions:
            if booking_action['action_type'] == action_type:
                self.action_id = booking_action['action_id']
                break

    # $$$$$$$$$$$$$$$$$$$$$$$ ADDONS $$$$$$$$$$$$$$$$$$$$
    def add_addon_v1_request(self, client, test_case_id, booking_id, status_code, user_type=None):
        request_json = json.dumps(del_none(addon_builder.AddOn
                                           (sheet_name=add_ons_sheet_name, test_case_id=test_case_id,
                                            booking_id=booking_id).__dict__))
        uri = add_addon_v1_uri.format(booking_id)
        with mock_tax_calculator_service():
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        if status_code == 200:
            self.addon_id_v1 = response.json['data']['addon_id']
        return response.json

    def remove_addon_v1(self, client, booking_id, addon_id, status_code, user_type=None):
        uri = delete_addon_v1_uri.format(booking_id, addon_id)
        response = self.request_processor(client, 'DELETE', uri, status_code, user_type=user_type)
        return response.json

    def create_add_on_v2(self, client, test_case_id, booking_id, status_code, user_type=None,

                         include_kerala_cess=False, hotel_id='0016932'):
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        request_json = json.dumps(del_none(add_on_v2_builder.AddOnV2
                                           (sheet_name=add_ons_sheet_name, test_case_id=test_case_id).__dict__))
        uri = addon_v2_uri.format(booking_id)
        with mock_tax_calculator_service(include_kerala_cess=include_kerala_cess, currency=CurrencyType(currency)):
            with mock_role_manager():
                response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        if status_code == 200:
            self.addon_id = response.json['data']['addon_id']
        return response.json

    def create_bulk_addon_v2(self, client, test_case_id, booking_id, status_code, user_type=None,
                             include_kerala_cess=False):
        request_json = json.dumps(
            del_none(bulk_addon_v2_builder.BulkAddOnV2(booking_id, sheet_name=bulk_addon_sheet_name,
                                                       test_case_id=test_case_id).__dict__))
        uri = bulk_addon_v2_uri.format(booking_id)
        with mock_tax_calculator_service(include_kerala_cess=include_kerala_cess):
            with mock_role_manager():
                response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def add_add_on_v2(self, client, test_case_id, booking_id, addon_id, status_code, user_type=None, currency='INR'):
        request_json = json.dumps(del_none(add_on_v2_builder.EditOnV2
                                           (sheet_name=add_ons_sheet_name, test_case_id=test_case_id,
                                            addon_id=addon_id, booking_id=booking_id).__dict__))
        uri = edit_addon_v2_uri.format(booking_id, addon_id)
        with mock_tax_calculator_service(include_kerala_cess=False, currency=CurrencyType(currency)):
            response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def edit_add_on_v2(self, client, test_case_id, booking_id, addon_id, status_code, user_type=None, currency='INR'):
        request_json = json.dumps(del_none(add_on_v2_builder.EditOnV2
                                           (sheet_name=add_ons_sheet_name, test_case_id=test_case_id,
                                            addon_id=addon_id, booking_id=booking_id).__dict__))
        uri = edit_addon_v2_uri.format(booking_id, addon_id)
        with mock_tax_calculator_service(include_kerala_cess=False, currency=CurrencyType(currency)):
            with mock_role_manager():
                response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def remove_add_on(self, client, booking_id, addon_id, status_code, user_type=None):
        uri = delete_addon_v2_uri.format(booking_id, addon_id)
        with mock_role_manager():
            response = self.request_processor(client, 'DELETE', uri, status_code, user_type=user_type)
        return response.json

    def get_all_add_on_v2(self, client, booking_id, include_linked, status_code, user_type=None):
        uri = get_all_addon_v2_uri.format(booking_id, include_linked)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code, user_type=user_type)
        return response.json

    # $$$$$$$$$$$$$$$$$$$$$$$ EXPENSE $$$$$$$$$$$$$$$$$$$$
    def get_expense(self, client, booking_id, expense_id, status_code, user_type=None):
        uri = update_expense_uri.format(booking_id, expense_id)
        with mock_role_manager():
            with mock_tax_calculator_service():
                response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        return response.json

    def add_expense(self, client, test_case_id, status_code, booking_id, user_type=None, is_mock_rule_req=False):
        request_json = json.dumps(
            del_none(add_expense_builder.AddExpense(add_expense_sheet_name, test_case_id, booking_id).__dict__))
        uri = add_expense_uri.format(booking_id)
        with mock_role_manager():
            with mock_tax_calculator_service():
                if is_mock_rule_req:
                    with mock_rule_engine():
                        response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
                else:
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def get_available_rooms(self, client, from_date, to_date, room_type_id, status_code, room_id_list_required,
                            hotel_id=None):
        room_id = []
        uri = get_available_room_slots.format(hotel_id=hotel_id if hotel_id else common_config.HOTEL_ID[0],
                                              from_date=from_date,
                                              to_date=to_date,
                                              room_type_id=room_type_id)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code)
        response_data = (response.json).get('data')
        for per_datewise_room in response_data:
            room_id.append(per_datewise_room['room_id'])
        if room_id_list_required:
            return room_id
        else:
            return response_data

    def update_room_stay(self, client, test_case_id, status_code, user_type=None, booking_id=None,
                         resource_version=None):
        request_json = json.dumps(
            del_none(update_room_stay_builder.UpdateRoomStay(update_room_stay_sheet_name, test_case_id,
                                                             booking_id, resource_version).__dict__))
        room_stay_id = get_room_stay_id(update_room_stay_sheet_name, test_case_id)
        uri = update_room_stay_uri.format(booking_id=self.booking_id, room_stay_id=room_stay_id)
        with mock_role_manager(), mock_tax_calculator_service():
            response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def allocate_room(self, client, test_case_id, status_code, hotel_id, enable_rate_plan, is_inclusion_added,
                      booking_id, user_type=None, has_slab_based_taxation=False, is_tax_clubbed=[]):
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        clubbed_taxation = True if is_tax_clubbed else False
        request_json = json.dumps(
            del_none(allocate_room_builder.AllocateRoom(allocate_room_sheet_name, test_case_id, booking_id, client,
                                                        hotel_id, enable_rate_plan, is_inclusion_added).__dict__))
        room_stay_id = get_room_stay_id(allocate_room_sheet_name, test_case_id)
        if test_case_id in ('AllocateRoom_53', 'AllocateRoom_54', 'AllocateRoom_55'):
            booking_id = get_booking_id(allocate_room_sheet_name, test_case_id)
        uri = allocate_room_uri.format(booking_id, room_stay_id)
        with mock_role_manager(), mock_tax_call(calculate_tax=True), mock_tenant_config(
                hotel_level_config=is_tax_clubbed):
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def bulk_update_room_stay(self, client, test_case_id, status_code, user_type=None):
        request_json = json.dumps(
            del_none(update_room_stay_builder.BulkUpdateRoomStay(update_room_stay_sheet_name, test_case_id,
                                                                 self.booking_id).__dict__))
        uri = bulk_update_room_stay_uri.format(booking_id=self.booking_id)
        with mock_role_manager(), mock_tax_calculator_service():
            response = self.request_processor(client, 'PATCH', uri, status_code, request_json, user_type)
        return response.json

    def update_stay_duration(self, client, test_case_id, status_code, booking_id, enable_rate_plan,
                             is_inclusion_needed, user_type=None, has_slab_based_taxation=False, is_tax_clubbed=[]):
        clubbed_taxation = True if is_tax_clubbed else False
        request_json = json.dumps(
            del_none(update_stay_duration_builder.UpdateStayDuration(update_stay_duration_sheet_name, test_case_id,
                                                                     booking_id, enable_rate_plan,
                                                                     is_inclusion_needed).__dict__))
        room_stay_id = get_room_stay_id(update_stay_duration_sheet_name, test_case_id)
        if test_case_id in ('UpdateStayDuration_60', 'UpdateStayDuration_61', 'UpdateStayDuration_62'):
            booking_id = get_booking_id(update_stay_duration_sheet_name, test_case_id)
        uri = update_stay_duration_uri.format(booking_id, room_stay_id)
        with mock_role_manager(), mock_tax_call(calculate_tax=True), \
                mock_tenant_config(hotel_level_config=is_tax_clubbed):
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def update_rate_plan(self, client, test_case_id, status_code, booking_id, enable_rate_plan,
                         is_inclusion_needed, user_type=None, has_slab_based_taxation=False, is_tax_clubbed=[]):
        clubbed_taxation = True if is_tax_clubbed else False
        request_json = json.dumps(
            del_none(update_rate_plan_builder.UpdateRatePlan(update_rate_plan_sheet_name, test_case_id, booking_id,
                                                             enable_rate_plan, is_inclusion_needed).__dict__))
        room_stay_id = get_room_stay_id(update_rate_plan_sheet_name, test_case_id)
        if test_case_id in ('UpdateRatePlan_76', 'UpdateRatePlan_77', 'UpdateRatePlan_78'):
            booking_id = get_booking_id(update_rate_plan_sheet_name, test_case_id)
        uri = update_rate_plan_uri.format(booking_id, room_stay_id)
        hotel_level_config = [{'config_name': 'rate_manager_enabled', 'config_value': 'true', 'value_type': 'json'}]
        if is_tax_clubbed:
            hotel_level_config = hotel_level_config + is_tax_clubbed
        with mock_role_manager(), mock_tenant_config(hotel_level_config=hotel_level_config), \
                mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value), mock_tax_call(calculate_tax=True):
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def mark_cancelled_room_guest(self, client, test_case_id, status_code, booking_id, enable_rate_plan,
                                  is_inclusion_needed, user_type=None, has_slab_based_taxation=False, is_tax_clubbed=[],
                                  hotel_id='0016932', new_tax_mocker=False):
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        clubbed_taxation = True if is_tax_clubbed else False
        request_json = json.dumps(
            del_none(mark_cancelled_room_guest_builder.MarkCancelledRoomGuest(mark_cancelled_room_guest_sheet_name,
                                                                              test_case_id, booking_id,
                                                                              enable_rate_plan,
                                                                              is_inclusion_needed).__dict__))
        if test_case_id in ('MarkCancelled_76', 'MarkCancelled_77', 'MarkCancelled_78'):
            booking_id = get_booking_id(mark_cancelled_room_guest_sheet_name, test_case_id)
        uri = mark_cancelled_room_guest_uri.format(booking_id)
        with mock_role_manager(), mock_tenant_config(hotel_level_config=is_tax_clubbed):
            if new_tax_mocker:
                with mock_tax_call(calculate_tax=True):
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
            else:
                with mock_tax_calculator_service(currency=CurrencyType(currency), clubbed_taxation=clubbed_taxation,
                                                 has_slab_based_taxation=has_slab_based_taxation):
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        if status_code in SUCCESS_CODES:
            self.action_id = response.json['data']['action_id']
        return response.json

    def mark_cancelled(self, client, booking_id, test_case_id, status_code, user_type=None, hotel_id='0016932',
                       extras=None, hotel_level_config=[], new_tax_mocker=False, reference_numbers=None):
        request_json = mark_cancelled_room_guest_builder.MarkCancelled(mark_cancelled_sheet_name, test_case_id,
                                                                       booking_id,
                                                                       reference_numbers).get_mark_cancelled_data()
        if test_case_id == 'cancel_booking_16':
            booking_id = "1234"
        elif test_case_id == 'cancel_booking_17':
            booking_id = ''
        uri = mark_cancelled_room_guest_uri.format(booking_id)
        with mock_role_manager(), mock_tenant_config(hotel_level_config=hotel_level_config), mock_refund_rules(), \
                mock_catalog_client(hotel_in_posttax=True), mock_refund_response_by_payout_link(), \
                mock_refund_response_by_treebo_corporate_rewards(), mock_refund_response_by_razorpay_api(), \
                mock_tenant_config_for_payment_rules(), mock_auto_approve_payout_link_amount(), mock_refund_reward_points_response():
            if new_tax_mocker:
                with mock_tax_call(calculate_tax=True):
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
            else:
                response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        if status_code in SUCCESS_CODES:
            self.action_id = response.json['data']['action_id']
        return response.json

    def get_invoice_id_bill_to_type_mapping(self, client, user_type, hotel_id):
        # TODO: Sends same invoice_id when 2 charge splits have same invoice id. Need to club
        invoice_id_bill_to_type_map = {}
        for invoice_preview in self.invoice_previews:
            bill_id = invoice_preview['bill_id']
            customer_id = invoice_preview['bill_to']['customer_id']
            bill_charges = BillingRequests.get_bill_charges(self, client, bill_id, 200, user_type)['data']
            for bill_charge in bill_charges:
                charge_id = bill_charge['charge_id']
                charge_request = \
                    BillingRequests.get_charge_request(self, client, bill_id, 200, charge_id, user_type)['data']
                allowed_charge_types = charge_request['type']
                bill_to_type = charge_request['bill_to_type']
                for charge_split in charge_request['charge_splits']:
                    if hotel_id != common_config.HOTEL_ID[0]:
                        posttax_amount = charge_split['post_tax'].split(' ')[1]
                    else:
                        posttax_amount = charge_split['post_tax']
                    customer_split_customer_id = charge_split['charge_to']
                    if bill_to_type == 'guest':
                        customer_id = customer_split_customer_id
                    invoice_id_bill_to_type_map[
                        customer_id + '_' + allowed_charge_types + '_' + bill_to_type + '_' + posttax_amount] = \
                        charge_split['invoice_id']
        return invoice_id_bill_to_type_map

    def reissue_invoice(self, client, test_case_id, status_code, user_type=None, invoice_id_map=None,
                        include_kerala_cess=False, hotel_id='0016932'):
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        request_json = json.dumps(
            del_none(reissue_invoice_builder.ReissueInvoiceBuilder(reissue_invoice_sheet_name, test_case_id,
                                                                   self.bill_id, invoice_id_map).__dict__))
        uri = reissue_invoice_uri.format(bill_id=self.bill_id)
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            with mock_tax_calculator_service(include_kerala_cess=include_kerala_cess, currency=CurrencyType(currency)):
                with mock_role_manager():
                    response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def set_invoice_preview(self):
        self.invoice_previews.clear()

    def get_booking_preview_invoices(self, client, booking_id, status_code, user_type=None, include_kerala_cess=None):
        uri = get_booking_preview_invoices_uri.format(booking_id=booking_id)
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            with mock_role_manager():
                # with mock_tax_calculator_service(include_kerala_cess=include_kerala_cess):
                response = self.request_processor(client, 'GET', uri, status_code, user_type=user_type)
        self.booking_invoices = response.json
        if status_code in SUCCESS_CODES:
            for booking_invoice_response in response.json['data']:
                self.invoice_id.append(booking_invoice_response['invoice_id'])
        return response.json

    def get_booking_proforma_invoices(self, client, extra_data, booking_id, status_code, user_type=None,
                                      include_kerala_cess=None):
        uri = get_booking_proforma_invoices_uri.format(booking_id=booking_id)
        if extra_data is not None:
            query = UPDATE_PACKAGE_DETAILS.format(package_details=PACKAGE_DETAILS)
            query_execute(query)
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            with mock_rate_manager_client(), mock_role_manager(), mock_rule_engine(), mock_template_service(), \
                    mock_aws_service_client("http://example.com/"):
                response = self.request_processor(client, 'GET', uri, status_code, user_type=user_type)

        self.booking_invoices = response.json
        return response.json

    def get_booking_invoices(self, client, booking_id, status_code, user_type=None, include_kerala_cess=None):
        uri = get_booking_preview_invoices_uri.format(booking_id=booking_id)
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            with mock_role_manager():
                # with mock_tax_calculator_service(include_kerala_cess=include_kerala_cess):
                response = self.request_processor(client, 'GET', uri, status_code, user_type=user_type)
        self.booking_invoices = response.json
        return response.json

    def get_room_stay_details(self, client, test_case_id, status_code, sheet_name, user_type=None):
        room_stay_id = get_room_stay_id(sheet_name, test_case_id)
        uri = get_room_stay_details_uri.format(booking_id=self.booking_id, room_stay_id=room_stay_id)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        return response.json

    def get_room_stay_details_v2(self, client, test_case_id, status_code, sheet_name, user_type=None,
                                 room_stay_id=None):
        if not room_stay_id:
            room_stay_id = get_room_stay_id(sheet_name, test_case_id)
        uri = get_room_stay_details_v2_uri.format(booking_id=self.booking_id, room_stay_id=room_stay_id)
        response = self.request_processor(client, 'GET', uri, status_code, user_type=user_type)
        return response.json

    def perform_night_audit_test(self, client, status_code, hotel_id, user_type):
        night_audit_service = locate_instance(NightAuditService)
        uri = get_night_audit_schedule_details.format(hotel_id)
        self.request_processor(client, 'GET', uri, status_code, None, user_type)
        with mock_catalog_client(), mock_trial_balance_reporting_service(), mock_communication_service_client(), \
                mock_authz_service_client(), mock_authn_service_client():
            night_audit_service.perform_night_audit(hotel_id)

    def get_all_invoice_by_booking_id_v2(self, client, booking_id, status_code, user_type=None):
        uri = get_all_invoice_for_invoice_id.format(booking_id)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        return response.json

    def mark_no_show(self, client, test_case_id, status_code, booking_id, user_type=None):
        if test_case_id == 'MarkNoShow_14':
            booking_id = ''
        uri = mark_no_show_uri.format(booking_id)
        resource_version = booking_repo().load(booking_id).booking.version
        request_json = json.dumps(
            del_none(mark_no_show_builder.MarkNoShow(mark_no_show_sheet_name, test_case_id, resource_version).__dict__))
        with mock_tax_call(calculate_tax=True):
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        if status_code in SUCCESS_CODES:
            self.action_id = response.json['data']['action_id']
        return response.json

    def get_allowed_booking_actions(self, client_, booking_id, status_code=200, user_type=None):
        uri = get_booking_allowed_action_uri.format(booking_id)
        response = self.request_processor(client_, 'GET', uri, status_code, user_type=user_type)
        return response.json

    def get_booking_audit_trail(self, client_, booking_id, status_code=200, user_type=None):
        uri = get_booking_audit_trail_uri.format(booking_id)
        response = self.request_processor(client_, 'GET', uri, status_code, user_type=user_type)
        return response.json

    def get_all_expense_by_booking_id(self, client, booking_id, status_code, user_type=None):
        uri = get_all_expense_uri.format(booking_id)
        with mock_role_manager():
            response = self.request_processor(client, 'GET', uri, status_code, None, user_type)
        return response.json

    def update_ta_commission(self, client, status_code, booking_id, commission_value, user_type=None,
                           commission_type='percent', commission_tax=None):
        request_json = update_ta_commission_builder.UpdateTACommission(
            booking_id, commission_value, commission_type, commission_tax
        ).update_ta_commission_request()
        uri = update_ta_commission_uri.format(booking_id=booking_id)
        with mock_role_manager():
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type)
        return response.json

    def calculate_cancellation_charges(self, client, status_code, test_case_id, booking_id, invoice_group_id,
                                       hotel_id=None, user_type=None):
        request_json = calculate_cancellation_charges_builder. \
            CalculateCancellationCharges(calculate_cancellation_charges, test_case_id, invoice_group_id). \
            calculate_cancellation_charges_request()
        booking_id = -1 if test_case_id == 'CancellationCharge_09' else booking_id
        uri = calculate_cancellation_charges_uri.format(booking_id=booking_id)
        with mock_refund_rules(), mock_get_setting_value_tenant(True):
            response = self.request_processor(client, 'POST', uri, status_code, request_json, user_type=user_type,
                                              hotel_id=hotel_id)
        return response.json

    # $$$$$$$$$$$$$$$$$$$$$$$ PUT Room Stay  $$$$$$$$$$$$$$$$$$$$
    def put_room_stay_v1(self, client, test_case_id, status_code, user_type=None, extras=None,
                         hotel_id='0016932'):
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        request_json = json.dumps({"data": RoomStays(room_stays_sheet_name, test_case_id).get_room_stay_data(currency),
                                   "resource_version": get_resource_version(self.booking_id)})
        room_stay_id = get_room_stay_id(room_stays_sheet_name, test_case_id)
        uri = update_room_stay_uri.format(booking_id=self.booking_id, room_stay_id=room_stay_id)
        with mock_role_manager(), mock_tax_calculator_service():
            response = self.request_processor(client, 'PUT', uri, status_code, request_json, user_type)
        return response.json
