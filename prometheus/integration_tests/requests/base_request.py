import logging

from flask import json

from prometheus.integration_tests.config import common_config
from prometheus.integration_tests.utilities.common_utils import assert_

logger = logging.getLogger(__name__)


class BaseRequest(object):
    def __init__(self):
        self.booking_id = None
        self.bill_id = None
        self.action_id = None
        self.invoice_group_id = None
        self.addon_id = None
        self.addon_id_v1 = None
        self.room_id = None
        self.hotel_id = None
        self.cash_register_id = None
        self.cash_register_session_id = None
        self.session_payment_id = None
        self.dnr_id = None
        self.allotment_id = None
        self.type = None
        self.subtype = None
        self.booking_ids = []
        self.bill_ids = []
        self.room_stay_ids_and_room_ids = None
        self.invoice_id = []
        self.credit_shell_id = None
        self.reference_number = None
        self.credit_note_id = None
        self.reference_numbers = []
        self.dnr_ids = []
        self.allotment_ids = []
        self.room_ids = []
        self.inactive_dnr_id = None
        self.inactive_dnr_ids = []
        self.credit_note_ids = []
        self.dnr_list = []
        self.inactive_dnr_list = []
        self.from_date = []
        self.to_date = []

    def request_processor(self, client_, request_type, url, status_code, request_json=None, user_type=None,
                          parameters=None, user=None, hotel_id=None):
        headers = {'Content-Type': 'application/json', common_config.USER_KEY: common_config.AUTOMATION,
                   'X-Application': 'automation_suite',
                   common_config.USER_TYPE_KEY: common_config.SUPER_ADMIN}
        client_type = {
            "POST": client_.post,
            "PATCH": client_.patch,
            "GET": client_.get,
            "DELETE": client_.delete,
            "PUT": client_.put
        }

        if user_type:  # if user type is present it will over-ride the existing value
            headers.update({common_config.USER_TYPE_KEY: user_type})
        if user:
            headers.update({common_config.USER_KEY: user})
        if hotel_id:
            headers['X-Hotel-Id'] = hotel_id
        print('\n\n' + '#' * 25 + 'REQUEST' + '#' * 25)
        print('REQUEST URL: ' + url + '\nREQUEST TYPE: ' + request_type + '\nHEADERS: ' + str(headers) +
              '\nREQUEST JSON: ' + str(request_json) + '\nREQUEST PARAMS: ' + str(parameters))
        response = client_type.get(request_type)(
            url,
            data=request_json,
            headers=headers
        )
        print('####################### RESPONSE ############################')
        print('RESPONSE CODE: ' + str(response.status_code) + '\nRESPONSE DATA: ' + json.dumps(response.json))
        assert_(response.status_code, status_code, 'Status code is not matching')
        return response
