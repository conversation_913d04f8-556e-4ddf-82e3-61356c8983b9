import pytest

from prometheus.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES, HOTEL_CURRENCY_MAP, HOTEL_ID
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.utilities.common_utils import query_execute
from prometheus.integration_tests.tests import before_test_actions
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.booking.validations.validation_allowance_v2 import ValidationAllowanceV2


class TestAddAllowance(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, previous_actions,extras", [
            ("AddAllowance_01", "Add allowance on booked roomstay charge", 400, "", "",
             "", "", "", "", before_test_actions.Checkin_Booking_Expense01, [1, 1]),
            ("AddAllowance_01", "Add allowance on booked non-linked charge", 400, "", "",
             "", "", "", "", before_test_actions.Checkin_Booking_Expense01, [2, 1]),
            ("AddAllowance_01", "Add allowance on booked linked charge", 400, "", "",
             "", "", "", "", before_test_actions.LINKED_CHARGE_FOR_CREATED_BOOKING, [2, 1]),
            ("AddAllowance_02", "Add allowance to posted credit charge, less than charge split (pretax)", 200, "", "",
             "", "", "", "", before_test_actions.POST_ROOMSTAY_CREDIT_CHARGE, [1, 1]),
            ("AddAllowance_03", "Add allowance to posted non-credit charge, less than charge split (pretax)", 200, "",
             "", "", "", "", "", before_test_actions.POST_ROOMSTAY_NON_CREDIT_CHARGE, [1, 1]),
            ("AddAllowance_04", "Add allowance to posted credit charge, more than charge split (pretax)", 400, "",
             "", "", "", "", "", before_test_actions.POST_ROOMSTAY_CREDIT_CHARGE, [1, 1]),
            ("AddAllowance_05", "Add allowance to posted non-credit charge, more than charge split (pretax)", 400, "",
             "", "", "", "", "", before_test_actions.POST_ROOMSTAY_NON_CREDIT_CHARGE, [1, 1]),
            ("AddAllowance_06", "Add allowance to posted credit charge, equal to charge split (pretax)", 200, "",
             "", "", "", "", "", before_test_actions.POST_ROOMSTAY_CREDIT_CHARGE, [1, 1]),
            ("AddAllowance_07", "Add allowance to posted non-credit charge, equal to charge split (pretax)", 200, "",
             "", "", "", "", "", before_test_actions.POST_ROOMSTAY_NON_CREDIT_CHARGE, [1, 1]),
            ("AddAllowance_08", "Add allowance to posted credit charge, less than charge split (posttax)", 200, "", "",
             "", "", "", "", before_test_actions.POST_ROOMSTAY_CREDIT_CHARGE, [1, 1]),
            ("AddAllowance_09", "Add allowance to posted non-credit charge, less than charge split (posttax)", 200, "",
             "", "", "", "", "", before_test_actions.POST_ROOMSTAY_NON_CREDIT_CHARGE, [1, 1]),
            ("AddAllowance_10", "Add allowance to posted credit charge, more than charge split (posttax)", 400, "",
             "", "", "", "", "", before_test_actions.POST_ROOMSTAY_CREDIT_CHARGE, [1, 1]),
            ("AddAllowance_11", "Add allowance to posted non-credit charge, more than charge split (posttax)", 400, "",
             "", "", "", "", "", before_test_actions.POST_ROOMSTAY_NON_CREDIT_CHARGE, [1, 1]),
            ("AddAllowance_12", "Add allowance to posted credit charge, equal to charge split (posttax)", 200, "",
             "", "", "", "", "", before_test_actions.POST_ROOMSTAY_CREDIT_CHARGE, [1, 1]),
            ("AddAllowance_13", "Add allowance to posted non-credit charge, equal to charge split (posttax)", 200, "",
             "", "", "", "", "", before_test_actions.POST_ROOMSTAY_NON_CREDIT_CHARGE, [1, 1]),
            ("AddAllowance_14", "Add allowance to posted credit non linked charge, equal to charge split (posttax)",
             200, "", "", "", "", "", "", before_test_actions.POSTED_NON_LINKED_CREDIT_CHARGE_FOR_CHECKEDIN_BOOKING,
             [6, 1]),
            ("AddAllowance_15", "Add allowance to posted non-credit non linked charge, equal to charge split (posttax)",
             200, "", "", "", "", "", "", before_test_actions.POSTED_NON_LINKED_CHARGE_FOR_CHECKEDIN_BOOKING, [6, 1]),
            ("AddAllowance_16", "Add allowance to posted credit linked charge, equal to charge split (pretax)", 200,
             "", "", "", "", "", "", before_test_actions.POSTED_LINKED_CREDIT_CHARGE_FOR_CHECKEDIN_BOOKING, [6, 1]),
            ("AddAllowance_17", "Add allowance to posted non-credit charge, equal to charge split (pretax)", 200, ""
             , "", "", "", "", "", before_test_actions.POSTED_LINKED_CHARGE_FOR_CHECKEDIN_BOOKING, [6, 1]),
            ("AddAllowance_18", "Add another allowance to posted non-credit charge", 200, ""
             , "", "", "", "", "", before_test_actions.POST_ROOMSTAY_NON_CREDIT_CHARGE + [
                 {'id': 'AddAllowance_02', 'type': 'add_allowance_v2', 'extras': [1, 1]}], [1, 1]),
            ("AddAllowance_19", "Add allowance to one of the charge splits", 200, ""
             , "", "", "", "", "Skip", before_test_actions.POSTED_NON_CREDIT_SPLIT_CHARGE, [1, 1]),
            ("AddAllowance_20", "Add allowance to both of the charge splits", 200, ""
             , "", "", "", "", "", before_test_actions.POSTED_NON_CREDIT_SPLIT_CHARGE + [
                 {'id': 'AddAllowance_02', 'type': 'add_allowance_v2', 'extras': [1, 2]}], [1, 1]),
            ("AddAllowance_23", "Add allowance on cancellation charge", 200, "", "", "", "", "", "",
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
              {'id': "CancelAction_15", 'type': 'cancel'}], [6, 1]),
            ("AddAllowance_24", "Add allowance to posted non-credit room stay charge of OTA booking(pretax)", 200, "",
             "", "", "", "", "", before_test_actions.POST_ROOMSTAY_NON_CREDIT_CHARGE_OTA_BOOKING, [1, 1]),
            ("AddAllowance_25", "Add allowance to posted non-credit room stay charge of OTA booking(posttax)", 200, "",
             "", "", "", "", "", before_test_actions.POST_ROOMSTAY_NON_CREDIT_CHARGE_OTA_BOOKING, [1, 1]),
            ("AddAllowance_26", "Add allowance to posted non-credit inclusion charge of OTA booking(pretax)", 200, "",
             "", "", "", "", "", before_test_actions.POST_ROOMSTAY_NON_CREDIT_CHARGE_OTA_BOOKING, [2, 1]),
            ("AddAllowance_27", "Add multiple allowance to posted non-credit room stay charge of OTA booking(pretax)",
             200, "", "", "", "", "", "", before_test_actions.POST_ROOMSTAY_NON_CREDIT_CHARGE_OTA_BOOKING +
             [{'id': 'AddAllowance_24', 'type': 'add_allowance_v2', 'extras': [1, 1]}], [1, 1]),
            ("AddAllowance_28", "Add allowance to posted non-credit inclusion charge of OTA booking(pretax) which "
                                "already has allowance on room stay charge", 200, "", "", "", "", "", "",
             before_test_actions.POST_ROOMSTAY_NON_CREDIT_CHARGE_OTA_BOOKING +
             [{'id': 'AddAllowance_24', 'type': 'add_allowance_v2', 'extras': [1, 1]}], [2, 1]),
            ("AddAllowance_29", "Add allowance to posted non-credit room stay charge of OTA booking(pretax) after "
                                "add guest", 200, "", "", "", "", "", "",
             [{'id': "booking_212", 'type': 'booking_v2'},
              {'id': 'AddGuest_01', 'type': 'add_multiple_guest_stay', 'enable_rate_plan': False,
               'is_inclusion_added': False}, {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'}], [1, 1]),
            ("AddAllowance_30", "Add allowance to posted non-credit room stay charge of OTA booking(pretax) after "
                                "remove guest", 200, "", "", "", "", "", "",
             [{'id': "ota_booking_multiple_guest", 'type': 'booking_v2'},
              {'id': 'MarkCancelled_02', 'type': 'mark_cancelled', 'enable_rate_manager': False,
               'is_inclusion_added': False}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'}], [1, 1]),
            ("AddAllowance_31", "Add allowance to posted non-credit room stay charge of OTA booking(pretax) after "
                                "updating stay duration", 200, "", "", "", "", "", "",
             [{'id': "booking_212", 'type': 'booking_v2'},
              {'id': 'UpdateStayDuration_129', 'type': 'update_stay_duration', 'enable_rate_manager': True,
               'is_inclusion_added': False}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'}], [1, 1]),
            ("AddAllowance_32", "Add allowance to posted non-credit room stay charge of OTA booking(pretax) after "
                                "updating rate plan", 200, "", "", "", "", "", "",
             [{'id': "booking_212", 'type': 'booking_v2'},
              {'id': 'UpdateRatePlan_01', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': True}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'}], [1, 1]),
            ("AddAllowance_33", "Add allowance to posted non-credit room stay charge of OTA booking(pretax) after "
                                "updating room stay price", 200, "", "", "", "", "", "",
             [{'id': "booking_212", 'type': 'booking_v2'},
              {'id': 'EditCharge_66', 'type': 'update_expense', 'charge_id': 1},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'}], [1, 1]),
            ("AddAllowance_34", "Add allowance to posted non-credit room stay charge of OTA booking(pretax) after put "
                                "booking", 200, "", "", "", "", "", "",
             [{'id': "booking_212", 'type': 'booking_v2'}, {'id': "put_booking_109", 'type': 'edit_booking_v2'},
              {'id': "checkin_01_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '6'}], [6, 1]),
            ("AddAllowance_35",
             "Edit commission then add allowance to posted non-credit room stay charge of OTA booking(pretax)", 200, "",
             "", "", "", "", "",
             [{'id': "booking_212", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'}], [1, 1]),
        ])
    @pytest.mark.regression
    def test_add_allowance(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                           error_message, dev_message, error_payload, skip_case, previous_actions,
                           extras):
        if skip_case:
            pytest.skip(skip_case)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        charge_id, charge_split_id = extras[0], extras[1]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.billing_request.add_allowance_v2(client_, test_case_id, charge_id, charge_split_id,
                                                         status_code, self.booking_request.bill_id, user_type)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(self.billing_request, test_case_id, client_, self.booking_request.bill_id, charge_id,
                            self.booking_request, self.booking_request.booking_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(billing_request, test_case_id, client_, bill_id, charge_id, booking_request, booking_id):
        request_validation = ValidationAllowanceV2(client_, test_case_id)
        request_validation.validate_response(client_, billing_request, 200, bill_id, charge_id)
        request_validation.validate_audit_trail(booking_request)
        request_validation.validate_integration_event(booking_id, 'add_allowance')
        request_validation.validate_commissions()


class TestUpdateAllowance(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, previous_actions, extras", [
            ("UpdateAllowance_01", "Marked status as consumed", 200, "", "",
             "", "", "", "", before_test_actions.ADD_ALLOWANCE_NON_CREDIT_CHARGE, [1, 1, 1, 'consumed']),
            ("UpdateAllowance_02", "Marked status as cancelled", 200, "", "",
             "", "", "", "", before_test_actions.ADD_ALLOWANCE_NON_CREDIT_CHARGE, [1, 1, 1, 'cancelled']),
            ("UpdateAllowance_03", "Mark status of allowance to consumed of one charge splits", 200, "", "",
             "", "", "", "", before_test_actions.ADD_ALLOWANCE_NON_CREDIT_SPLIT_CHARGE, [1, 1, 1, 'consumed']),
            ("UpdateAllowance_04", "Mark status of allowance to cancelled of one charge splits", 200, "", "",
             "", "", "", "", before_test_actions.ADD_ALLOWANCE_NON_CREDIT_SPLIT_CHARGE, [1, 1, 1, 'cancelled']),
            ("UpdateAllowance_05", "Marked status as cancelled", 200, "", "", "", "", "", "",
             before_test_actions.POST_ROOMSTAY_NON_CREDIT_CHARGE_OTA_BOOKING +
             [{'id': 'AddAllowance_02', 'type': 'add_allowance_v2', 'extras': [1, 1]}], [1, 1, 1, 'cancelled']),
            ("UpdateAllowance_06", "Mark status of one allowance to cancelled", 200, "", "", "", "", "", "",
             before_test_actions.POST_ROOMSTAY_NON_CREDIT_CHARGE_OTA_BOOKING +
             [{'id': 'AddBulkAllowance_44', 'type': 'add_bulk_allowance'}], [1, 1, 1, 'cancelled']),
            ("UpdateAllowance_07", "Edit commission then mark status as cancelled", 200, "", "", "", "", "", "",
             [{'id': "booking_212", 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'},
              {'id': 'AddAllowance_02', 'type': 'add_allowance_v2', 'extras': [1, 1]}], [1, 1, 1, 'cancelled']),
        ])
    @pytest.mark.regression
    def test_update_allowance(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                              error_message, dev_message, error_payload, skip_case, previous_actions,
                              extras):
        if skip_case:
            pytest.skip(skip_case)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        charge_id, charge_split_id, allowance_id, status = extras[0], extras[1], extras[2], extras[3]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.billing_request.update_allowance_v2(client_, charge_id, charge_split_id, allowance_id, status,
                                                            status_code, self.booking_request.bill_id, user_type)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(self.billing_request, test_case_id, client_, self.booking_request.bill_id, charge_id,
                            charge_split_id, self.booking_request.booking_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(billing_request, test_case_id, client_, bill_id, charge_id, charge_split_id, booking_id,):
        request_validation = ValidationAllowanceV2(client_, test_case_id)
        request_validation.validate_response(client_, billing_request, 200, bill_id, charge_id)
        request_validation.validate_post_allowance_tax_details(bill_id, charge_id, charge_split_id)
        request_validation.validate_integration_event(booking_id, 'update_allowance')
        request_validation.validate_commissions()


class TestAddAllowanceAfterChargeSplit(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, previous_actions, extras, has_slab_based_taxation", [
            ("AddAllowance_21", "Pass an allowance which have slab based taxation but not contain split", 200, "", "",
             "", "", "", "", before_test_actions.POST_ROOM_STAY_V2_01_SLAB_BASED, [1, 1], True),
            ("AddAllowance_22", "Pass an allowance after charge splits which has slab based taxation", 400, "", "", "",
             "", "", "True", before_test_actions.POST_ROOM_STAY_V2_01_SPLIT_SLAB_BASED, [1, 1], True),
        ])
    @pytest.mark.regression
    def test_add_allowance_after_charge_split(self, client_, test_case_id, tc_description, status_code, user_type,
                                              error_code, error_message, dev_message, error_payload, skip_case,
                                              previous_actions, extras, has_slab_based_taxation):
        if skip_case:
            pytest.skip(skip_case)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        charge_id, charge_split_id = extras[0], extras[1]

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=True))

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.billing_request.add_allowance_v2(client_, test_case_id, charge_id, charge_split_id,
                                                         status_code, self.booking_request.bill_id, user_type)

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=False))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(self.billing_request, test_case_id, client_, self.booking_request.bill_id, charge_id,
                            self.booking_request, self.booking_request.booking_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(billing_request, test_case_id, client_, bill_id, charge_id, booking_request, booking_id):
        request_validation = ValidationAllowanceV2(client_, test_case_id)
        request_validation.validate_response(client_, billing_request, 200, bill_id, charge_id)
        request_validation.validate_audit_trail(booking_request)
        request_validation.validate_integration_event(booking_id, 'add_allowance')
