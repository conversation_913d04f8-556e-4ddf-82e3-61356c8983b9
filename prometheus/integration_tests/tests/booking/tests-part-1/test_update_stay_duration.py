from datetime import date, timedelta
import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.booking.validations.validation_update_stay_duration import \
    ValidationUpdateStayDuration
from prometheus.integration_tests.utilities.common_utils import query_execute, set_inventory_count


class TestUpdateStayDuration(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, enable_rate_plan, is_inclusion_added, "
        "perform_night_audit, action_after_night_audit, is_booking_with_rate_plan", [
            ("UpdateStayDuration_01", 'Increase roomStay by reducing the current check in date',
             FUTURE_WALK_BOOKING_V2_01_2_days_WITHOUT_RATE_PLAN, 200, None, "", "", "", "", False, "", False, False,
             False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_01", 'Increase roomStay by reducing the current check in date',
             FUTURE_WALK_BOOKING_V2_01_2_days, 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_02", 'Increase roomStay by increasing the current checkout date',
             SINGLE_BOOKING_01_V2_WITHOUT_RATE_PLAN, 200, None, "", "", "", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_02", 'Increase roomStay by increasing the current checkout date',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_03", 'Reduce roomStay by decreasing the current checkout date',
             FUTURE_WALK_BOOKING_V2_01_2_days_WITHOUT_RATE_PLAN, 200,
             None, "", "", "", "", False, "", False, False, False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_03", 'Reduce roomStay by decreasing the current checkout date',
             FUTURE_WALK_BOOKING_V2_01_2_days, 200, None, "", "", "", "", False, "", False, False, False, False, ""),
            ("UpdateStayDuration_04", 'Reduce roomStay by increasing the current check in date',
             FUTURE_WALK_BOOKING_V2_01_2_days_WITHOUT_RATE_PLAN, 200, None, "", "", "", "", False, "", False, False,
             False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_04", 'Reduce roomStay by increasing the current check in date',
             FUTURE_WALK_BOOKING_V2_01_2_days, 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_05", 'Increase roomStay by decreasing check in date and increasing checkout date',
             FUTURE_WALK_BOOKING_V2_01_2_days_WITHOUT_RATE_PLAN, 200, None, "", "", "", "", False, "", False, False,
             False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_05", 'Increase roomStay by decreasing check in date and increasing checkout date',
             FUTURE_WALK_BOOKING_V2_01_2_days, 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_06",
             'Increase check in date and decrease checkout date such that check in date become greater than checkout '
             'date', FUTURE_WALK_BOOKING_V2_01_2_days_WITHOUT_RATE_PLAN, 400, None, "04010006",
             "[Checkout Date] -> Checkout date should be greater than checkin date", "", {"field": "checkout_date"},
             False, "", False, False, False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_06",
             'Increase check in date and decrease checkout date such that check in date become greater than checkout '
             'date', FUTURE_WALK_BOOKING_V2_01_2_days, 400, None, "04010006",
             "[Checkout Date] -> Checkout date should be greater than checkin date", "", {"field": "checkout_date"},
             False, "", True, True, False, False, ""),
            ("UpdateStayDuration_07",
             'Decrease the current checkout date so that check in date greater less than checkout date',
             FUTURE_WALK_BOOKING_V2_01_2_days_WITHOUT_RATE_PLAN, 400, None, "04010002",
             "Checkout date should be greater than checkin date", "", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_07",
             'Decrease the current checkout date so that check in date greater less than checkout date',
             FUTURE_WALK_BOOKING_V2_01_2_days, 400, None, "04010002",
             "Checkout date should be greater than checkin date", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_08",
             'Increase check in date such that check in date become greater than checkout date',
             FUTURE_WALK_BOOKING_V2_01_2_days_WITHOUT_RATE_PLAN, 400, None, "04010002",
             "Checkout date should be greater than checkin date", "", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_08",
             'Increase check in date such that check in date become greater than checkout date',
             FUTURE_WALK_BOOKING_V2_01_2_days, 400, None, "04010002",
             "Checkout date should be greater than checkin date", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_09",
             'Increase roomStay by increasing the current checkout date after booking checked in',
             [{'id': "booking_77", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}], 200, None, "",
             "", "", "", False, "", False, False, False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_09",
             'Increase roomStay by increasing the current checkout date after booking checked in',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}], 200, None, "",
             "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_10",
             'Decrease roomStay by decreasing the current checkout date after booking checked in',
             [{'id': "booking_82", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}], 200, None, "",
             "", "", "", False, "", False, False, False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_11", 'Increase check in date after booking checked in',
             [{'id': "booking_82", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}], 400, None,
             "04010126", "Checkin date cannot be changed for already checked-in room",
             "RoomStay has already been checked in. Cannot change room stay checkin date", "", False, "", False, False,
             False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_11", 'Increase check in date after booking checked in',
             [{'id': "booking_06", 'type': 'booking_v2'}, {'id': "checkinPost_02", 'type': 'check_in'}], 400, None,
             "04010126", "Checkin date cannot be changed for already checked-in room",
             "RoomStay has already been checked in. Cannot change room stay checkin date", "", False, "", False, False,
             False, False, ""),
            ("UpdateStayDuration_12", 'Reduce check in date below business date',
             SINGLE_BOOKING_01_V2_WITHOUT_RATE_PLAN, 403, None, "04010915",
             "Cannot change room stay to back-dated checkin", "", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_12", 'Reduce check in date below business date', SINGLE_WALK_BOOKING_V2_01, 403, None,
             "04010915", "Cannot change room stay to back-dated checkin", "", "", False, "", True, True, False,
             False, ""),
            ("UpdateStayDuration_13", 'Make check in date and checkout date same',
             FUTURE_WALK_BOOKING_V2_01_2_days_WITHOUT_RATE_PLAN, 400, None,
             "04010006", "[Checkout Date] -> Checkout date should be greater than checkin date", "",
             {"field": "checkout_date"}, False, "", False, False, False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_13", 'Make check in date and checkout date same',
             FUTURE_WALK_BOOKING_V2_01_2_days, 400, None, "04010006",
             "[Checkout Date] -> Checkout date should be greater than checkin date", "", {"field": "checkout_date"},
             False, "", True, True, False, False, ""),
            ("UpdateStayDuration_14", 'Change stay date for a cancelled room',
             [{'id': "booking_95", 'type': 'booking_v2'}, {'id': "CancelAction_39", 'type': 'cancel'}], 400, None,
             "04015007", "Cannot update room stay. RoomStay is either cancelled or marked noshow", "", "", False, "",
             False, False, False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_14", 'Change stay date for a cancelled room',
             [{'id': "booking_19", 'type': 'booking_v2'}, {'id': "CancelAction_39", 'type': 'cancel'}], 400, None,
             "04015007", "Cannot update room stay. RoomStay is either cancelled or marked noshow", "", "", False, "",
             False, False, False, False, ""),
            ("UpdateStayDuration_15", 'Update RoomStay to dates where there is NO_INVENTORY',
             SINGLE_BOOKING_01_V2_WITHOUT_RATE_PLAN, 400, None, "04010602",
             "Room type is not available for selected dates in this hotel", "", "", False, "", False, False, False,
             False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_16",
             'Update roomStay dates for one room outside booking duration for multi-room booking',
             [{'id': "booking_95", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", False, False, False,
             False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_16",
             'Update roomStay dates for one room outside booking duration for multi-room booking',
             [{'id': "booking_19", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True, False,
             False, ""),
            ("UpdateStayDuration_17", 'Update roomstay for past invalid checkout date after checking-in the room',
             [{'id': "booking_77", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}], 400, None,
             "04010002", "Checkout date should be greater than checkin date", "", "", False, "", False, False, False,
             False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_17", 'Update roomstay for past invalid checkout date after checking-in the room',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}], 400, None,
             "04010002", "Checkout date should be greater than checkin date", "", "", False, "", True, True, False,
             False, ""),
            ("UpdateStayDuration_18", 'Extend the checkout dates for the room where check in date is diff for guests',
             SINGLE_BOOKING_WITH_GUEST_DIFF_CHECKIN_DATES_V2_WITHOUT_RATE_PLAN, 200, None, "", "", "", "", False, "",
             False, False, False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_18", 'Extend the checkout dates for the room where check in date is diff for guests',
             SINGLE_BOOKING_WITH_GUEST_DIFF_CHECKIN_DATES_V2, 200, None, "", "", "", "", False, "", True, True, False,
             False, ""),
            ("UpdateStayDuration_19", 'Reduce the check in dates for the room where check in date is diff for guests',
             [{'id': 'booking_83_diff_guest_stay_dates_future', 'type': 'booking_v2'}], 400, None, "04010125",
             "Room stay dates and guest stay dates don't match. Please change guest stay dates one by one.",
             "Some guest stays have checkin date different from room stay checkin date. Cannot change room stay checkin"
             " date in such scenario.", "", False, "", False, False, False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_20", 'Reduce the check in dates for the room where checkout date is diff for guests',
             SINGLE_BOOKING_WITH_GUEST_DIFF_CHECKOUT_DATES_V2_WITHOUT_RATE_PLAN, 200, None, "", "", "", "", False, "",
             False, False, False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_20", 'Reduce the check in dates for the room where checkout date is diff for guests',
             SINGLE_BOOKING_WITH_GUEST_DIFF_CHECKOUT_DATES_V2, 200, None, "", "", "", "", False, "", True, True, False,
             False, ""),
            ("UpdateStayDuration_21",
             'Decrease the check out dates for the room where checkout date is diff for guests',
             SINGLE_BOOKING_WITH_GUEST_DIFF_CHECKOUT_DATES_V2_WITHOUT_RATE_PLAN, 400, None, "04010125",
             "Room stay dates and guest stay dates don't match. Please change guest stay dates one by one.",
             "Some guest stays have checkout date different from room stay checkout date. Cannot change room stay "
             "checkout date in such scenario.", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_21",
             'Decrease the check out dates for the room where checkout date is diff for guests',
             SINGLE_BOOKING_WITH_GUEST_DIFF_CHECKOUT_DATES_V2, 400, None, "04010125",
             "Room stay dates and guest stay dates don't match. Please change guest stay dates one by one.",
             "Some guest stays have checkout date different from room stay checkout date. Cannot change room stay "
             "checkout date in such scenario.", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_22", 'Update stay date on checkout date',
             [{'id': "booking_01_no_show", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}], 200,
             None, "", "", "", "", False, "", True, True, True, False, ""),
            ("UpdateStayDuration_23", 'Change stay date for partial early checkout room',
             [{'id': "booking_19", 'type': 'booking_v2'}, {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': 'invoicePreviewCheckoutV2_01', 'type': 'preview_invoice'},
              {'id': 'CheckoutV2_04', 'type': 'checkout_v2'}], 200, None, "", "", "", "", False, "", True, True, False,
             False, ""),
            ("UpdateStayDuration_24", 'Change stay date for partial checkout room',
             [{'id': "booking_add_multiple_room_48", 'type': 'booking_v2'},
              {'id': "checkin_04", 'type': 'checkin_v2'}], 200, None, "", "", "", "", False, "", True, True, True,
             PART_CHECKOUT_BOOKING_V2, ""),
            ("UpdateStayDuration_25", 'Change stay date for partial checked in room',
             [{'id': "booking_19_01_checkin_04", 'type': 'booking_v2'}, {'id': "checkin_11", 'type': 'checkin_v2'}],
             200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_26", 'Change stay date for checkout room', FULL_CHECKOUT_01_V2, 400, None, "04010127",
             "Checkout date cannot be changed for already checked-out room",
             "RoomStay has already been checked out. Cannot change room stay checkout date", "", False, "", True, True,
             False, False, ""),
            ("UpdateStayDuration_27", 'Change stay date by 2 days and provide different rate plan for different days',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04015949", "Invalid rate plan id", "", "", False, "", True, True,
             False, False, ""),
            ("UpdateStayDuration_28", 'Change stay date for mark no show room',
             [{'id': "booking_03_no_show", 'type': 'booking_v2'}, {'id': 'MarkNoShow_25', 'type': 'mark_no_show'}], 400,
             None, "04015007", "Cannot update room stay. RoomStay is either cancelled or marked noshow", "", "", False,
             "", True, True, True, False, ""),
            ("UpdateStayDuration_29", 'Change check in and checkout date to a past date', SINGLE_WALK_BOOKING_V2_01,
             403, None, "04010915", "Cannot change room stay to back-dated checkin", "", "", False, "", True, True,
             False, False, ""),
            ("UpdateStayDuration_30", 'change stay date for a room in which one guest is removed and one is remaining',
             [{'id': "booking_101", 'type': 'booking_v2'}, {'id': "MarkCancelled_21", 'type': 'mark_cancelled'}], 200,
             None, "", "", "", "", False, "", False, False, False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_30", 'change stay date for a room in which one guest is removed and one is remaining',
             [{'id': "booking_25", 'type': 'booking_v2'}, {'id': "MarkCancelled_21", 'type': 'mark_cancelled'}], 200,
             None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_31", 'Change stay date for multiple guest in a room',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST_WITHOUT_RATE_PLAN, 200, None, "", "", "", "", False, "", False, False,
             False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_31", 'Change stay date for multiple guest in a room', SINGLE_BOOKING_V2_WITH_TWO_GUEST,
             200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_32", 'Change stay date after reverse check in',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_33", 'Change stay date after reverse no show',
             [{'id': 'booking_01_no_show', 'type': 'booking_v2'}, {'id': 'MarkNoShow_01', 'type': 'mark_no_show'},
              {'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", True, True, True, False, ""),
            ("UpdateStayDuration_34", 'Change stay date after reverse cancel',
             [{'id': "booking_86", 'type': 'booking_v2'}, {'id': "CancelAction_14", 'type': 'cancel'},
              {'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_34", 'Change stay date after reverse cancel',
             [{'id': "booking_10", 'type': 'booking_v2'}, {'id': "CancelAction_14", 'type': 'cancel'},
              {'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_35", 'Change stay date after reverse checkout',
             [{'id': 'booking_77', 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
              {'id': "CheckoutV2_01", 'type': 'checkout_v2'}, {'type': 'delete_booking_action'}], 200, None, "", "",
             "", "", False, "", False, False, False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_35", 'Change stay date after reverse checkout', FULL_CHECKOUT_01_V2 +
             [{'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_36", "Increase roomStay by reducing the current check in date but don't provide price",
             FUTURE_WALK_BOOKING_V2_01_WITHOUT_RATE_PLAN, 400, None, "04010101",
             "Something went wrong. Please contact Escalations team with the booking id.",
             "Please send prices for all the room stay dates where occupancy or room type has changed", "", False, "",
             False, False, False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_36", "Increase roomStay by reducing the current check in date but don't provide price",
             FUTURE_WALK_BOOKING_V2_01, 400, None, "04010101",
             "Something went wrong. Please contact Escalations team with the booking id.",
             "Please send prices for all the room stay dates where occupancy or room type has changed", "", False, "",
             True, True, False, False, ""),
            ("UpdateStayDuration_37",
             "Increase roomStay by increasing the current checkout date but don't provide price",
             SINGLE_BOOKING_01_V2_WITHOUT_RATE_PLAN, 400, None, "04010101",
             "Something went wrong. Please contact Escalations team with the booking id.",
             "Please send prices for all the room stay dates where occupancy or room type has changed", "", False, "",
             False, False, False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_37",
             "Increase roomStay by increasing the current checkout date but don't provide price",
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04010101",
             "Something went wrong. Please contact Escalations team with the booking id.",
             "Please send prices for all the room stay dates where occupancy or room type has changed", "", False, "",
             True, True, False, False, ""),
            ("UpdateStayDuration_38", "Increase room stay by 2 days but provide price for 1 day",
             SINGLE_BOOKING_01_V2_WITHOUT_RATE_PLAN, 400, None, "04010101",
             "Something went wrong. Please contact Escalations team with the booking id.",
             "Please send prices for all the room stay dates where occupancy or room type has changed", "", False, "",
             False, False, False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_38", "Increase room stay by 2 days but provide price for 1 day",
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04010101",
             "Something went wrong. Please contact Escalations team with the booking id.",
             "Please send prices for all the room stay dates where occupancy or room type has changed", "", False, "",
             True, True, False, False, ""),
            ("UpdateStayDuration_39", "Decrease room stay by 1 day but still provide price for new date",
             FUTURE_WALK_BOOKING_V2_01_2_days, 400, None, "04010101",
             "Something went wrong. Please contact Escalations team with the booking id.",
             "Please send prices only for the room stay dates where occupancy or room type has changed, and existing"
             " charges hasn't been consumed", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_39", "Decrease room stay by 1 day but still provide price for new date",
             FUTURE_WALK_BOOKING_V2_01_2_days_WITHOUT_RATE_PLAN, 400, None, "04010101",
             "Something went wrong. Please contact Escalations team with the booking id.",
             "Please send prices only for the room stay dates where occupancy or room type has changed, and existing"
             " charges hasn't been consumed", "", False, "", False, False, False, False,
             {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_40", "Update stay duration as well as rate plan", SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04015949", "Invalid rate plan id", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_41", 'Update stay duration with wrong rate plan id', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04015949", "Invalid rate plan id", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_42", 'Provide rate plan id both in price as well as in rate plan',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_43", 'Provide rate plan id in price only', SINGLE_WALK_BOOKING_V2_01, 200, None, "",
             "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_44", 'Provide rate plan id in rate plan only', SINGLE_WALK_BOOKING_V2_01, 200, None,
             "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_45", 'Does not provide rate plan id in both price as well as in rate plan',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04015949", "Invalid rate plan id", "", "", False,
             "", True, True, False, False, ""),
            ("UpdateStayDuration_46", 'Provide rate plan inclusion for new date',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04015969",
             "Cannot add Rate Plan Inclusions. Rate Plan Inclusions provided are out of stay duration", "", "", False,
             "", True, True, False, False, ""),
            ("UpdateStayDuration_47", 'Provide rate plan inclusion for new date as well as old date',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04015969",
             "Cannot add Rate Plan Inclusions. Rate Plan Inclusions provided are out of stay duration", "", "", False,
             "", True, True, False, False, ""),
            ("UpdateStayDuration_48", 'Increase checkout date for two days but provide inclusion for 1 day',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_49", 'Increase checkout date for two days and also provide inclusion for 2 day',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_50", 'Increase Checkout date but provide future date rate plan inclusion',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04015969",
             "Cannot add Rate Plan Inclusions. Rate Plan Inclusions provided are out of stay duration", "", "", False,
             "", True, True, False, False, ""),
            ("UpdateStayDuration_51",
             'Reduce checkout date for two day booking and still provide rate plan inclusion for new date',
             FUTURE_WALK_BOOKING_V2_01_2_days, 400, None, "04015969",
             "Cannot add Rate Plan Inclusions. Rate Plan Inclusions provided are out of stay duration", "", "", False,
             "", True, True, False, False, ""),
            ("UpdateStayDuration_52", 'Provide Invalid check in date', SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Checkin Date] -> Not a valid datetime.", "", {"field": "checkin_date"}, False, "", True, True, False,
             False, ""),
            ("UpdateStayDuration_53", 'Provide Invalid checkout date', SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Checkout Date] -> Not a valid datetime.", "", {"field": "checkout_date"}, False, "", True, True, False,
             False, ""),
            ("UpdateStayDuration_54", 'Provide invalid bill to type', SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Bill To Type] -> 'INVALID' is not a valid choice for Bill-To Type", "",
             {"field": "prices.0.bill_to_type"}, False, "", True, True, False, False, ""),
            ("UpdateStayDuration_55", 'Provide bill to type as guest', SINGLE_WALK_BOOKING_V2_01, 200, None, "", "",
             "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_56", 'Provide bill to type as company for B2B booking',
             [{'id': "booking_43", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True, False,
             False, ""),
            ("UpdateStayDuration_57", 'Provide bill to type as company for booking without company details',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04010331",
             "Credit charges can not be billed to guest", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_58", 'Provide invalid amount', FUTURE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Pretax Amount] -> Failed creating money object with error: Unable to create money for amount: INVALID"
             " due to reason: [<class 'decimal.ConversionSyntax'>]", "", {"field": "prices.0.pretax_amount"}, False, "",
             True, True, False, False, ""),
            ("UpdateStayDuration_59", 'Provide invalid applicable date in price', FUTURE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Applicable Date] -> Not a valid datetime.", "", {"field": "prices.0.applicable_date"}, False,
             "", True, True, False, False, ""),
            ("UpdateStayDuration_60", 'Provide Booking Id as Null', FUTURE_WALK_BOOKING_V2_01, 404, None, "04010007",
             "Aggregate: Booking with id: null missing.", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_61", 'Provide Booking Id as Empty', FUTURE_WALK_BOOKING_V2_01, 404, None, 404,
             "Exception occurred.", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_62", 'Provide Wrong Booking-Id', FUTURE_WALK_BOOKING_V2_01, 404, None, "04010007",
             "Aggregate: Booking with id: 0000-0000-0000 missing.", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_63", 'Provide Invalid format room stay id', FUTURE_WALK_BOOKING_V2_01, 404, None, 404,
             "Exception occurred.", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_64", 'Provide room stay id as Null', FUTURE_WALK_BOOKING_V2_01, 404, None, 404,
             "Exception occurred.", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_65", 'Provide room stay id as Empty', FUTURE_WALK_BOOKING_V2_01, 404, None, 404,
             "Exception occurred.", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_66", 'Provide Wrong room stay id', FUTURE_WALK_BOOKING_V2_01, 404, None, "04010004",
             "RoomStay not found. Please contact escalations.", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_67", 'Does not provide mandatory fields', FUTURE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[ Schema] -> Both check-in and check-out date can't be empty", "", {"field": "_schema"},
             False, "", True, True, False, False, ""),
            ("UpdateStayDuration_68", 'Does not provide non-mandatory fields',
             FUTURE_WALK_BOOKING_V2_01_WITHOUT_RATE_PLAN, 200, None, "", "", "", "", False, "", False, False, False,
             False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_69", 'Provide both pretax and posttax amount in price', FUTURE_WALK_BOOKING_V2_01, 400,
             None, "04010006", "[ Schema] -> Please provide either pretax_amount or posttax_amount", "",
             {"field": "prices.0._schema"}, False, "", True, True, False, False, ""),
            ("UpdateStayDuration_70", 'Does not provide pretax amount as well as posttax amount in price',
             FUTURE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[ Schema] -> Please provide either pretax_amount or posttax_amount", "", {"field": "prices.0._schema"},
             False, "", True, True, False, False, ""),
            ("UpdateStayDuration_71", 'Send Negative Price', FUTURE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[ Schema] -> pretax_amount or posttax_amount cannot be negative", "", {"field": "prices.0._schema"},
             False, "", True, True, False, False, ""),
            ("UpdateStayDuration_72", 'Change stay date multiple times',
             [{'id': "booking_77", 'type': 'booking_v2'},
              {'id': 'UpdateStayDuration_02', 'type': 'update_stay_duration'},
              {'id': 'UpdateStayDuration_11', 'type': 'update_stay_duration'}], 200, None, "", "", "", "", False, "",
             False, False, False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_72", 'Change stay date multiple times',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': 'UpdateStayDuration_02', 'type': 'update_stay_duration', 'enable_rate_manager': True,
               'is_inclusion_added': True},
              {'id': 'UpdateStayDuration_11', 'type': 'update_stay_duration', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_73", 'Update only rate plan', SINGLE_WALK_BOOKING_V2_01, 400, None, "", "", "", "",
             False, "", True, True, False, False, ""),
            ("UpdateStayDuration_74", 'Change stay date for temporary booking',
             [{'id': "booking_99_hold_till", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", False, False,
             False, False, {"non_rate_plan_booking": True}),
            ("UpdateStayDuration_74", 'Change stay date for temporary booking',
             [{'id': "booking_19_hold_till", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True,
             False, False, ""),
            ("UpdateStayDuration_75", 'Change stay date but provide price as NULL', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04010006", "[Prices] -> Field may not be null.", "", {"field": "prices"}, False, "", True, True,
             False, False, ""),
            ("UpdateStayDuration_76", 'Change stay date but provide price as empty', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04010006", "[Prices] -> Invalid type.", "", {"field": "prices"}, False, "", True, True, False,
             False, ""),
            ("UpdateStayDuration_77", 'Does not provide applicable date in Price', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Applicable Date] -> Please provide applicable date.", "",
             {"field": "prices.0.applicable_date"}, False, "", True, True, False, False, ""),
            ("UpdateStayDuration_78", 'Provide applicable date as NULL in Price', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Applicable Date] -> Applicable date may not be null.", "",
             {"field": "prices.0.applicable_date"}, False, "", True, True, False, False, ""),
            ("UpdateStayDuration_79", 'Provide applicable date empty in Price', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Applicable Date] -> Not a valid datetime.", "", {"field": "prices.0.applicable_date"}, False,
             "", True, True, False, False, ""),
            ("UpdateStayDuration_80", 'Does not provide bill to type in Price', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Bill To Type] -> Please provide bill to type.", "", {"field": "prices.0.bill_to_type"},
             False, "", True, True, False, False, ""),
            ("UpdateStayDuration_81", 'Provide bill to type as NULL in Price', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Bill To Type] -> Bill to type may not be null.", "", {"field": "prices.0.bill_to_type"},
             False, "", True, True, False, False, ""),
            ("UpdateStayDuration_82", 'Provide bill to type as empty in Price', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Bill To Type] -> '' is not a valid choice for Bill-To Type", "",
             {"field": "prices.0.bill_to_type"}, False, "", True, True, False, False, ""),
            ("UpdateStayDuration_83", 'Does not provide type in Price', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Type] -> Please provide price type.", "", {"field": "prices.0.type"}, False, "",
             True, True, False, False, ""),
            ("UpdateStayDuration_84", 'Provide type as NULL in Price', SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Type] -> Price type may not be null.", "", {"field": "prices.0.type"}, False, "",
             True, True, False, False, ""),
            ("UpdateStayDuration_85", 'Provide type as empty in Price', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010006", "[Type] -> '' is not a valid choice for Price Type", "", {"field": "prices.0.type"}, False,
             "", True, True, False, False, ""),
            ("UpdateStayDuration_86", 'Provide start date in rate plan inclusion as NULL', SINGLE_WALK_BOOKING_V2_01,
             400, None, "04010006", "[Start Date] -> Field may not be null.", "",
             {"field": "rate_plan_inclusions.0.start_date"}, False, "", True, True, False, False, ""),
            ("UpdateStayDuration_87", 'Provide end date in rate plan inclusion as NULL', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04010006", "[End Date] -> Field may not be null.", "", {"field": "rate_plan_inclusions.0.end_date"},
             False, "", True, True, False, False, ""),
            ("UpdateStayDuration_88", 'Provide sku id in rate plan inclusion as NULL', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04010006", "[Sku Id] -> sku id may not be null.", "", {"field": "rate_plan_inclusions.0.sku_id"},
             False, "", True, True, False, False, ""),
            ("UpdateStayDuration_89", 'Provide start date in rate plan inclusion as Empty', SINGLE_WALK_BOOKING_V2_01,
             400, None, "04010006", "[Start Date] -> Not a valid date.", "",
             {"field": "rate_plan_inclusions.0.start_date"}, False, "", True, True, False, False, ""),
            ("UpdateStayDuration_90", 'Provide end date in rate plan inclusion as Empty', SINGLE_WALK_BOOKING_V2_01,
             400, None, "04010006", "[End Date] -> Not a valid date.", "", {"field": "rate_plan_inclusions.0.end_date"},
             False, "", True, True, False, False, ""),
            ("UpdateStayDuration_91", 'Provide sku id in rate plan inclusion as Empty', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04015942", "Sku not found", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_92", 'Does not Provide start date in rate plan inclusion', SINGLE_WALK_BOOKING_V2_01,
             400, None, "04010006", "[Start Date] -> Missing data for required field.", "",
             {"field": "rate_plan_inclusions.0.start_date"}, False, "", True, True, False, False, ""),
            ("UpdateStayDuration_93", 'Does not Provide end date in rate plan inclusion', SINGLE_WALK_BOOKING_V2_01,
             400, None, "04010006", "[End Date] -> Missing data for required field.", "",
             {"field": "rate_plan_inclusions.0.end_date"}, False, "", True, True, False, False, ""),
            ("UpdateStayDuration_94", 'Does not Provide sku id in rate plan inclusion', SINGLE_WALK_BOOKING_V2_01, 400,
             None, "04010006", "[Sku Id] -> sku id data missing.", "", {"field": "rate_plan_inclusions.0.sku_id"},
             False, "", True, True, False, False, ""),
            ("UpdateStayDuration_95", 'Provide quantity in rate plan inclusion as negative', SINGLE_WALK_BOOKING_V2_01,
             400, None, "04010006", "[Quantity] -> Value must be greater than 0", "",
             {"field": "rate_plan_inclusions.0.quantity"}, False, "", True, True, False, False, ""),
            ("UpdateStayDuration_96", 'Provide both pretax and posttax amount in rate plan inclusion',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Rate Plan Inclusions] -> Please provide either of posttax_amount or pretax_amount", "",
             {"field": "rate_plan_inclusions.0.rate_plan_inclusions"}, False, "", True, True, False, False, ""),
            ("UpdateStayDuration_97", 'Provide pretax or posttax amount negative in rate plan inclusion',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006",
             "[Rate Plan Inclusions] -> pretax_amount or posttax_amount cannot be negative", "",
             {"field": "rate_plan_inclusions.0.rate_plan_inclusions"}, False, "", True, True, False, False, ""),
            ("UpdateStayDuration_98", 'Does not provide any of the mandatory fields in rate plan inclusion',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04010006", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_99",
             'change stay date with all the details provided like  flexi rate plan, rate plan inclusion',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "", False, "", True, True, False, False, ""),
            ("UpdateStayDuration_100", 'Send pretax price in Price while send posttax price in inclusion',
             SINGLE_WALK_BOOKING_V2_01, 400, None, "04010101",
             "Something went wrong. Please contact Escalations team with the booking id.",
             "pretax_amount provided in price whereas posttax_amount provided in rate_plan_inclusions", "", False, "",
             True, True, False, False, ""),
        ])
    @pytest.mark.regression
    def test_update_stay_duration(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                                  error_code, error_message, dev_message, error_payload, skip_case, skip_message,
                                  enable_rate_plan, is_inclusion_added, perform_night_audit, action_after_night_audit,
                                  is_booking_with_rate_plan):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id, extra=is_booking_with_rate_plan)
                self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
                if action_after_night_audit:
                    self.common_request_caller(client_, action_after_night_audit, hotel_id)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id, extra=is_booking_with_rate_plan)

        if "NO_INVENTORY" in tc_description:
            set_inventory_count(0, hotel_id)

        response = self.booking_request.update_stay_duration(client_, test_case_id, status_code,
                                                             self.booking_request.booking_id, enable_rate_plan,
                                                             is_inclusion_added, user_type)

        if "NO_INVENTORY" in tc_description:
            set_inventory_count(500, hotel_id)

        if test_case_id in ('UpdateStayDuration_24', 'UpdateStayDuration_28', 'UpdateStayDuration_33'):
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.booking_request.booking_id,
                            self.booking_request.bill_id, self.billing_request, self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, booking_id, bill_id, billing_request,
                   expense_request):
        validation = ValidationUpdateStayDuration(client_, test_case_id, response, booking_request)
        validation.validate_response()
        validation.validate_charge_and_expense_status(expense_request, billing_request, bill_id, booking_id)


class TestUpdateStayDurationAfterChargeSplit(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, enable_rate_plan, is_inclusion_added, "
        "is_booking_with_rate_plan, has_slab_based_taxation, is_tax_clubbed", [
            ("UpdateStayDuration_101", 'Update stay duration having slab based taxation',
             SINGLE_WALK_BOOKING_V2_01_SLAB_BASED, 200, None, "", "", "", "", False, "", True, True, "", True, []),
            ("UpdateStayDuration_102", 'Update stay duration having clubbed charge',
             SINGLE_WALK_BOOKING_V2_01_CLUBBED_CHARGE, 200, None, "", "", "", "", False, "", True, True, "", False,
             CLUBBED_TAX_CONFIG),
            ("UpdateStayDuration_103", 'Update stay duration having clubbed charge and slab based taxation',
             SINGLE_WALK_BOOKING_V2_01_CLUBBED_AND_SLAB_BASED, 200, None, "", "", "", "", False, "", True, True, "",
             True, CLUBBED_TAX_CONFIG),
            ("UpdateStayDuration_104", 'Update stay duration after charge split',
             BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_03, 200, None, "", "", "", "", False, "", True, True, "",
             False, []),
            ("UpdateStayDuration_105", 'Update stay duration after charge split having slab based taxation',
             BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_02, 200, None, "", "", "", "", False, "", True, True, "",
             True, []),
            ("UpdateStayDuration_106", 'Update stay duration after charge split having slab based taxation, such that '
                                       'old charge get cancelled and new charge will get created',
             BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_02, 200, None, "", "", "", "", False, "", True, True, "",
             True, []),
            ("UpdateStayDuration_107", 'Update stay duration after charge split having clubbed charge and slab based '
                                       'taxation', BOOKING_WITH_CHARGE_SPLITS_CLUBBED_AND_SLAB_BASED, 200, None,
             "", "", "", "", False, "", True, True, "", True, CLUBBED_TAX_CONFIG),
        ])
    @pytest.mark.regression
    def test_update_stay_duration_after_charge_split(self, client_, test_case_id, tc_description, previous_actions,
                                                     status_code, user_type, error_code, error_message, dev_message,
                                                     error_payload, skip_case, skip_message, enable_rate_plan,
                                                     is_inclusion_added, is_booking_with_rate_plan,
                                                     has_slab_based_taxation, is_tax_clubbed):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=True))

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id, extra=is_booking_with_rate_plan)

        response = self.booking_request.update_stay_duration(client_, test_case_id, status_code,
                                                             self.booking_request.booking_id, enable_rate_plan,
                                                             is_inclusion_added, user_type, has_slab_based_taxation,
                                                             is_tax_clubbed)
        self.billing_request.get_bill_charges(client_, self.booking_request.bill_id, 200)

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=False))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.booking_request.booking_id,
                            self.booking_request.bill_id, self.billing_request, self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, booking_id, bill_id, billing_request,
                   expense_request):
        validation = ValidationUpdateStayDuration(client_, test_case_id, response, booking_request)
        validation.validate_response()
        validation.validate_charge_and_expense_status(expense_request, billing_request, bill_id, booking_id)


class TestUpdateStayDurationWithCommission(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, dev_message"
        ", error_payload, skip_case, skip_message, enable_rate_plan, is_inclusion_added", [
            ("UpdateStayDuration_108",
             "Decrease stay duration by increasing checkin date of a single room multiple days ota booking",
             [{'id': 'booking_233', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_109",
             "Decrease stay duration by increasing checkin date of a single room multiple days hotel walkin booking",
             [{'id': 'booking_06', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_110",
             "Decrease stay duration by increasing checkin date of a multiple rooms multiple days ota booking",
             [{'id': 'booking_234', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_111",
             "Decrease stay duration by increasing checkin date of a ota booking whose rate plan is updated",
             [{'id': 'booking_233', 'type': 'booking_v2'},
              {'id': 'UpdateRatePlan_01_update_stay', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_112",
             "Decrease stay duration by increasing checkin date of a ota booking in which a guest is added",
             [{'id': 'booking_233', 'type': 'booking_v2'},
              {'id': 'AddGuest_01_update_stay', 'type': 'add_multiple_guest_stay', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_113",
             "Decrease stay duration by increasing checkin date of a ota booking in which a guest is removed",
             [{'id': 'booking_20_update_stay', 'type': 'booking_v2'},
              {'id': 'MarkCancelled_02_update_stay', 'type': 'mark_cancelled', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_114", "Decrease stay duration by increasing checkin date of a ota booking in which a"
                                       " guest is added then rate plan is updated",
             [{'id': 'booking_233', 'type': 'booking_v2'},
              {'id': 'AddGuest_01_update_stay', 'type': 'add_multiple_guest_stay', 'enable_rate_manager': True,
               'is_inclusion_added': True},
              {'id': 'UpdateRatePlan_01_update_stay', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_115", "Decrease stay duration by increasing checkin date of a ota booking in which a "
                                       "guest is removed then rate plan is updated",
             [{'id': 'booking_20_update_stay', 'type': 'booking_v2'},
              {'id': 'MarkCancelled_02_update_stay', 'type': 'mark_cancelled', 'enable_rate_manager': True,
               'is_inclusion_added': True},
              {'id': 'UpdateRatePlan_01_update_stay', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_116",
             "Decrease stay duration by increasing checkin date of a ota booking in which a guest is marked no show",
             [{'id': 'booking_20_update_stay', 'type': 'booking_v2'}, {'id': 'MarkNoShow_17', 'type': 'mark_no_show'}],
             200, None, "", "", "", "", True, "", True, True),
            ("UpdateStayDuration_117", "Decrease stay duration by increasing checkin date of a ota booking in which a "
                                       "guest is marked no show then rate plan is updated",
             [{'id': 'booking_20_update_stay', 'type': 'booking_v2'}, {'id': 'MarkNoShow_17', 'type': 'mark_no_show'},
              {'id': 'UpdateRatePlan_01_update_stay', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", True, "", True, True),
            ("UpdateStayDuration_118",
             "Decrease stay duration by increasing checkin date of a ota booking whose charge is edited",
             [{'id': 'booking_233', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1}], 200, None, "", "", "", "",
             False, "", True, True),
            ("UpdateStayDuration_119", "Decrease stay duration by increasing checkin date of a ota booking whose "
                                       "charge is edited then rate plan is updated",
             [{'id': 'booking_233', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1},
              {'id': 'UpdateRatePlan_01_update_stay', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_120", "Decrease stay duration by increasing checkin date of a ota booking whose "
                                       "charge is edited and a guest is added",
             [{'id': 'booking_233', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1},
              {'id': 'AddGuest_01_update_stay', 'type': 'add_multiple_guest_stay', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_121", "Decrease stay duration by increasing checkin date of a ota booking whose "
                                       "charge is edited and a guest is removed",
             [{'id': 'booking_20_update_stay', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1},
              {'id': 'MarkCancelled_02_update_stay', 'type': 'mark_cancelled', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_122", "Decrease stay duration by increasing checkin date of a ota booking whose "
                                       "charge is edited and a guest is marked no show",
             [{'id': 'booking_20_update_stay', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1},
              {'id': 'MarkNoShow_17', 'type': 'mark_no_show'}], 200, None, "", "", "", "", True, "", True, True),
            ("UpdateStayDuration_123", "Decrease stay duration by increasing checkin date of a single room multiple "
                                       "days ota booking having extra expense",
             [{'id': 'booking_233', 'type': 'booking_v2'},
              {'id': 'Multiple_Expense_104', 'type': 'create_expense_V3'}], 200, None, "", "", "", "",
             False, "", True, True),
            ("UpdateStayDuration_124", "Decrease stay duration by increasing checkin date of a single room multiple"
                                       " days ota booking having extra expense and updated rate plan",
             [{'id': 'booking_233', 'type': 'booking_v2'},
              {'id': 'Multiple_Expense_104', 'type': 'create_expense_V3'},
              {'id': 'UpdateRatePlan_01_update_stay', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_125",
             "Decrease stay duration by increasing checkin date of a single room multiple days ota checked in booking",
             [{'id': 'booking_233', 'type': 'booking_v2'},
              {'id': 'checkin_05', 'type': 'checkin_v2'}], 400, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_126",
             "Decrease stay duration by decreasing checkout date of a single room multiple days ota booking",
             [{'id': 'booking_233', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_127", "Decrease stay duration by increasing checkin date and decreasing checkout date"
                                       " of a single room multiple days ota booking",
             [{'id': 'booking_233_update_stay', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True,
             True),
            ("UpdateStayDuration_128",
             "Increase stay duration by decreasing checkin date of a single room single day ota booking",
             [{'id': 'booking_236', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_129",
             "Increase stay duration by increasing checkout date of a single room single day ota booking",
             [{'id': 'booking_212', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_130",
             "Increase stay duration by increasing checkout date of a single room single day hotel walkin booking",
             [{'id': 'booking_01', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_131",
             "Increase stay duration by increasing checkout date of a multiple rooms single day ota booking",
             [{'id': 'booking_232', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_132", "Increase stay duration by increasing checkout date of a single room single day"
                                       " ota booking whose rate plan is updated",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'UpdateRatePlan_01', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_133", "Increase stay duration by increasing checkout date of a single room single day"
                                       " ota booking in which a guest is added",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'AddGuest_01', 'type': 'add_multiple_guest_stay', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_134", "Increase stay duration by increasing checkout date of a single room single day"
                                       " ota booking in which a guest is removed",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'},
              {'id': 'MarkCancelled_02', 'type': 'mark_cancelled', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_135", "Increase stay duration by increasing checkout date of a single room single day"
                                       " ota booking in which a guest is added and rate plan is updated",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'AddGuest_01', 'type': 'add_multiple_guest_stay', 'enable_rate_manager': True,
               'is_inclusion_added': True},
              {'id': 'UpdateRatePlan_01', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_136", "Increase stay duration by increasing checkout date of a single room single day"
                                       " ota booking in which a guest is removed and rate plan is updated",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'},
              {'id': 'MarkCancelled_02', 'type': 'mark_cancelled', 'enable_rate_manager': True,
               'is_inclusion_added': True},
              {'id': 'UpdateRatePlan_01', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_137", "Increase stay duration by increasing checkout date of a single room single day"
                                       " ota booking whose charge is edited",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1}], 200, None, "", "", "", "", False, "",
             True, True),
            ("UpdateStayDuration_138", "Increase stay duration by increasing checkout date of a single room single day"
                                       " ota booking whose charge is edited and rate plan is updated",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1},
              {'id': 'UpdateRatePlan_01', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_139", "Increase stay duration by increasing checkout date of a single room single day"
                                       " ota booking whose charge is edited and a guest is added",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1},
              {'id': 'AddGuest_01', 'type': 'add_multiple_guest_stay', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_140", "Increase stay duration by increasing checkout date of a single room single day"
                                       " ota booking whose charge is edited and a guest is removed",
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1},
              {'id': 'MarkCancelled_02', 'type': 'mark_cancelled', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_141", "Increase stay duration by increasing checkout date of a single room single day"
                                       " ota booking having extra expense",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'Multiple_Expense_104', 'type': 'create_expense_V3'}], 200, None, "", "", "", "",
             False, "", True, True),
            ("UpdateStayDuration_142", "Increase stay duration by increasing checkout date of a single room single day"
                                       " ota booking having extra expense and updated rate plan",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'Multiple_Expense_104', 'type': 'create_expense_V3'},
              {'id': 'UpdateRatePlan_01', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': True}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_143",
             "Increase stay duration by increasing checkout date of a single room single day ota checked in booking",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'checkin_01', 'type': 'checkin_v2'}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_144", "Increase stay duration by decreasing checkin date and increasing checkout date"
                                       " of a single room single day ota booking",
             [{'id': 'booking_236', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_145", "Change both checkin and checkout date such that room nights will not change,"
                                       " booking shifted 1 day in future",
             [{'id': 'booking_212', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_146", "Change both checkin and checkout date of a future booking such that room "
                                       "nights will not change, booking shifted 1 day in past with different price",
             [{'id': 'booking_236', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_147", "Change both checkin and checkout date of a future booking such that room "
                                       "nights will not change, booking shifted 1 day in past with same price",
             [{'id': 'booking_236', 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_148", "Increase stay duration by increasing checkout date of a single room single day"
                                       " ota reverse checked in booking",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'checkin_01', 'type': 'checkin_v2'}, {'id': 'Delete_Checkin_01', 'type': 'delete_booking_action'}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_149", "Increase stay duration by increasing checkout date of a single room single day"
                                       " ota reverse cancelled booking",
             [{'id': 'booking_212', 'type': 'booking_v2'}, {'id': "MarkCancelled_95", 'type': 'mark_cancelled'},
              {'id': 'Delete_Cancel_01', 'type': 'reverse_cancel_action'}], 200, None, "", "", "", "",
             False, "", True, True),
            ("UpdateStayDuration_193",
             "Edit commission then increase stay duration by increasing checkout date of a ota booking",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0}],
             200, None, "", "", "", "", False, "", True, False),
            ("UpdateStayDuration_194",
             "Edit commission then decrease stay duration by increasing checkin date of a ota booking",
             [{'id': 'booking_233', 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_195",
             "Edit commission then change both checkin and checkout date such that room nights will not change",
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0}],
             200, None, "", "", "", "", False, "", True, True),
        ])
    @pytest.mark.regression
    def test_update_stay_duration_with_commission(self, client_, test_case_id, tc_description, previous_actions,
                                                  status_code, user_type, error_code, error_message, dev_message,
                                                  error_payload, skip_case, skip_message, enable_rate_plan,
                                                  is_inclusion_added):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.update_stay_duration(client_, test_case_id, status_code,
                                                             self.booking_request.booking_id, enable_rate_plan,
                                                             is_inclusion_added)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.booking_request.booking_id,
                            self.booking_request.bill_id, self.billing_request, self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, booking_id, bill_id, billing_request,
                   expense_request):
        validation = ValidationUpdateStayDuration(client_, test_case_id, response, booking_request)
        validation.validate_response()
        validation.validate_charge_and_expense_status(expense_request, billing_request, bill_id, booking_id)
        validation.validate_commissions()


class TestUpdateStayDurationWithNewTaxCalc(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, dev_message"
        ", error_payload, skip_case, skip_message, enable_rate_plan, is_inclusion_added", [
            ("UpdateStayDuration_150", "Increase stay duration by increasing checkout date of a booking"
                                       " where sez and lut is false for company",
             [{'id': 'booking_with_lut_04', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_151", "Increase stay duration by increasing checkout date of a booking"
                                       " where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_152", "Increase stay duration by increasing checkout date of a booking"
                                       " where sez is false and lut is true for company",
             [{'id': 'booking_with_lut_03', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_153", "Increase stay duration by increasing checkout date of a booking"
                                       " where sez and lut is true for company",
             [{'id': 'booking_with_lut_01', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_154", "Increase stay duration by increasing checkout date of a booking"
                                       " where sez and lut is false for ta",
             [{'id': 'booking_with_lut_04_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_155", "Increase stay duration by increasing checkout date of a booking"
                                       " where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_156", "Increase stay duration by increasing checkout date of a booking"
                                       " where sez is false and lut is true for ta",
             [{'id': 'booking_with_lut_03_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_157", "Increase stay duration by increasing checkout date of a booking"
                                       " where sez and lut is true for ta",
             [{'id': 'booking_with_lut_01_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_158", "Increase stay duration by increasing checkout date of a booking"
                                       " where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_159", "Increase stay duration by increasing checkout date of a booking"
                                       " with company and travel agent where charges are billed to company",
             [{'id': 'booking_with_lut_02_company_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_160", "Increase stay duration by increasing checkout date of a booking"
                                       " with company and travel agent where charges are billed to ta",
             [{'id': 'booking_with_lut_02_ta_company', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_161", "Increase stay duration by increasing checkout date of multiple rooms booking"
                                       " where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_multiple_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_162", "Increase stay duration by increasing checkout date of a multiple rooms booking"
                                       " where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta_multiple_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_163", "Increase stay duration by increasing checkout date of a multiple rooms booking"
                                       " where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_multiple_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_164", "Increase stay duration by decreasing checkin date of a booking"
                                       " where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_future', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_165",
             "Increase stay duration by decreasing checkin date of a booking where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta_future', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_166",
             "Increase stay duration by decreasing checkin date of a booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_future', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_167", "Increase stay duration by decreasing checkin and increasing checkout date"
                                       " of a booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_future', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_168", "Increase stay duration by decreasing checkin and increasing checkout date"
                                       " of a booking where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta_future', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_169", "Increase stay duration by decreasing checkin and increasing checkout date"
                                       " of a booking where charges are billed to guest",
             [{'id': 'booking_01_with_pg_BE_future', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_170",
             "Decrease stay duration by increasing checkin date of a booking where sez and lut is false for company",
             [{'id': 'booking_with_lut_04_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_171",
             "Decrease stay duration by increasing checkin date of a booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_172",
             "Decrease stay duration by increasing checkin date of a booking where sez is false and lut is true for company",
             [{'id': 'booking_with_lut_03_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_173",
             "Decrease stay duration by increasing checkin date of a booking where sez and lut is true for company",
             [{'id': 'booking_with_lut_01_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_174",
             "Decrease stay duration by increasing checkin date of a booking where sez and lut is false for ta",
             [{'id': 'booking_with_lut_04_ta_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_175",
             "Decrease stay duration by increasing checkin date of a booking where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_176",
             "Decrease stay duration by increasing checkin date of a booking where sez is false and lut is true for ta",
             [{'id': 'booking_with_lut_03_ta_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_177",
             "Decrease stay duration by increasing checkin date of a booking where sez and lut is true for ta",
             [{'id': 'booking_with_lut_01_ta_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_178",
             "Decrease stay duration by increasing checkin date of a booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_179", "Decrease stay duration by increasing checkin date of a booking"
                                       " with company and travel agent where charges are billed to company",
             [{'id': 'booking_with_lut_02_company_ta_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_180", "Decrease stay duration by increasing checkin date of a booking"
                                       " with company and travel agent where charges are billed to ta",
             [{'id': 'booking_with_lut_02_ta_company_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_181", "Decrease stay duration by increasing checkin date of multiple rooms booking"
                                       " where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_multiple_days_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_182", "Decrease stay duration by increasing checkin date of a multiple rooms"
                                       " booking where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta_multiple_days_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_183", "Decrease stay duration by increasing checkin date of a multiple rooms"
                                       " booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_multiple_days_rooms', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_184", "Decrease stay duration by decreasing checkout date of a booking"
                                       " where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_185", "Decrease stay duration by decreasing checkout date of a booking"
                                       " where sez is true and lut is false for ta",
             [{'id': 'booking_with_lut_02_ta_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_186", "Decrease stay duration by decreasing checkout date of a booking"
                                       " where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_187", "Decrease stay duration by decreasing checkout date and increasing checkin date"
                                       " of a booking where sez is true and lut is false for company",
             [{'id': 'booking_with_lut_02_3days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_188", "Decrease stay duration by decreasing checkout date and increasing checkin date"
                                       " of a booking where sez is true and lut is false for travel agent",
             [{'id': 'booking_with_lut_02_ta_3days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_189", "Decrease stay duration by decreasing checkout date and increasing checkin date"
                                       " of a booking where charges are billed to primary guest",
             [{'id': 'booking_01_with_pg_BE_3days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_190", "Change stay dates of a booking where sez is true and lut is false"
                                       " for company by shifting 1 day in future",
             [{'id': 'booking_with_lut_02', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_191", "Change stay dates of a booking where sez is true and lut is false"
                                       " for travel agent by shifting 1 day in future",
             [{'id': 'booking_with_lut_02_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_192",
             "Change stay dates of a booking where charges are billed to primary guest by shifting 1 day in future",
             [{'id': 'booking_01_with_pg_BE', 'type': 'booking_v2', 'new_tax_mocker': True}],
             200, None, "", "", "", "", False, "", True, True),
            ("UpdateStayDuration_196",
             "Change both checkin and checkout date such that room nights will not change for restriction rate plan",
             [{'id': 'restriction_02', 'type': 'booking_v2', 'new_tax_mocker': True}],
             400, None, "04010210", "Booking window of room stay cannot be more than the maximum allowed "
                                    "abw of stay as per the rate plan", "", "", False, "", True, True),
        ])
    @pytest.mark.regression
    def test_update_rate_plan_with_tax_calc(self, client_, test_case_id, tc_description, previous_actions,
                                            status_code, user_type, error_code, error_message, dev_message,
                                            error_payload, skip_case, skip_message, enable_rate_plan,
                                            is_inclusion_added):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.update_stay_duration(client_, test_case_id, status_code,
                                                             self.booking_request.booking_id, enable_rate_plan,
                                                             is_inclusion_added)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request, self.booking_request.booking_id,
                            self.booking_request.bill_id, self.billing_request, self.expense_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_request, booking_id, bill_id, billing_request,
                   expense_request):
        validation = ValidationUpdateStayDuration(client_, test_case_id, response, booking_request)
        validation.validate_response()
        validation.validate_charge_and_expense_status(expense_request, billing_request, bill_id, booking_id)
        validation.validate_commissions()
        validation.validate_charges(billing_request)
