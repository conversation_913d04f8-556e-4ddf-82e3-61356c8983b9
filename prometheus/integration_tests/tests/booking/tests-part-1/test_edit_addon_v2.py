import pytest
from ths_common.constants.catalog_constants import SellerType

from prometheus.integration_tests.config import error_messages
from prometheus.integration_tests.config.common_config import FDM, CR_TEAM
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.booking.validations.validation_edit_addon_v2 import EditAddOnValidations
from prometheus.tests.mockers import mock_tax_calculator_service, mock_catalog_client
from prometheus.integration_tests.config.common_config import *
from treebo_commons.money.constants import CurrencyType


class TestEditAddOnV2(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message", [

            ("EditAddOnV2_01", "Edit a non-linked add-on with price and quantity", SINGLE_BOOKING_01 + CREATE_ADDON_01,
             200,
             None, "", "", "", "", True, ""),
            ("EditAddOnV2_02", "Edit the charge_type for the add_on from non-credit to credit for b2b",
             SINGLE_BOOKING_03 + CREATE_ADDON_02, 200, None, "", "", "", "", False, ""),
            ("EditAddOnV2_03", "Change the date combination of add-on. Change from absolute to relative",
             SINGLE_BOOKING_01 + CREATE_ADDON_01, 200, None, "", "", "", "", False, ""),
            ("EditAddOnV2_04", "Change the date combination from relative to absolute on part-check-in booking.",
             PART_BOOKING_CHECK_IN_01 + CREATE_ADDON_03, 200, None, "", "", "", "", False, ""),
            # ("EditAddOnV2_05",
            #  "Change the date combination from relative to relative plus absolute on checked-in booking",
            #  SINGLE_BOOKING_CHECK_IN_01 + CREATE_ADDON_03, 200, None, "", "", "", "", False, ""),
            ("EditAddOnV2_06",
             "Edit the add-on on part-checkout booking with pre-tax amount where add-on was created with post-tax",
             PART_CHECKOUT_01 + CREATE_ADDON_02, 200, None, "", "", "", "", True, ""),
            # ("EditAddOnV2_07", "Edit the add-on on checkout booking", FULL_CHECKOUT_01 + CREATE_ADDON_03, 200, None, "",
            #  "", "", "", False, ""),
            # ("EditAddOnV2_08", "Edit the linked-add-on quantity and dates on check-in booking",
            #  GROUP_BOOKING_CHECK_IN_01 + CREATE_ADDON_04, 200, None, "", "", "", "", False, ""),
            ("EditAddOnV2_09", "Edit the linked-add-on on checked-out booking", FULL_CHECKOUT_01 + CREATE_ADDON_04, 200,
             None, "", "", "", "", True,
             "Skipping this test case as now we won't allow create addon after charge is invoiced "),
            ("EditAddOnV2_10", "Edit the add-on with wrong charge-type", SINGLE_BOOKING_01 + CREATE_ADDON_01,
             403, None, "", error_messages.CREDIT_CHARGE_ON_NON_B2B, "", "", False, ""),
            ("EditAddOnV2_11", "Edit the add-on with 0 quantity", SINGLE_BOOKING_01 + CREATE_ADDON_01,
             400, None, "", error_messages.CREDIT_CHARGE_ON_NON_B2B, "", "", True, ""),
            ("EditAddOnV2_12", "Edit the add-on on a cancelled booking",
             SINGLE_BOOKING_02 + CREATE_ADDON_01 + CANCEL_FULL_BOOKING,
             400, None, "", error_messages.INACTIVE_BOOKING, "", "", False, ""),
            ("EditAddOnV2_13", "Edit the add-on with providing absolute dates outside the booking",
             SINGLE_BOOKING_02 + CREATE_ADDON_01, 400, None, "", error_messages.INVALID_EXPENSE_DATE, "", "", False,
             ""),
            ("EditAddOnV2_14", "Edit linked add-on with pre-tax amount", SINGLE_BOOKING_01 + CREATE_ADDON_04, 400, None,
             "",
             error_messages.LINKED_ADDON_WITH_PRETAX, "", "", False, ""),
            ("EditAddOnV2_15", "Edit linked add-on by changing end-relative to checkout",
             SINGLE_BOOKING_01 + CREATE_ADDON_04,
             400, None, "", error_messages.LINKED_ADDON_WITH_PRETAX, "", "", False, ""),
            ("EditAddOnV2_16", "Edit the deleted add-on", SINGLE_BOOKING_01 + CREATE_ADDON_04 + REMOVE_ADDON, 404, None,
             "",
             "", "", "", True, ""),
            ("EditAddOnV2_17", "Edit the charge type in edit add on for linked addon",
             SINGLE_BOOKING_01 + CREATE_ADDON_04,
             400, None, "", error_messages.VALIDATION_ERROR, "", "", True, ""),
            ("EditAddOnV2_01", "Edit a non-linked add-on with price and quantity", SINGLE_BOOKING_01 + CREATE_ADDON_01,
             200,
             None, "", "", "", "", True, ""),
            ("EditAddOnV2_02", "(MultiCurrency)Edit the charge_type for the add_on from non-credit to credit for b2b",
             SINGLE_BOOKING_03 + CREATE_ADDON_02, 200, None, "", "", "", "", False, ""),
            ("EditAddOnV2_03", "(MultiCurrency)Change the date combination of add-on. Change from absolute to relative",
             SINGLE_BOOKING_01 + CREATE_ADDON_01, 200, None, "", "", "", "", False, ""),
            ("EditAddOnV2_04",
             "(MultiCurrency)Change the date combination from relative to absolute on part-check-in booking.",
             PART_BOOKING_CHECK_IN_01 + CREATE_ADDON_03, 200, None, "", "", "", "", False, ""),
            # ("EditAddOnV2_05",
            #  "(MultiCurrency)Change the date combination from relative to relative plus absolute on checked-in booking",
            #  GROUP_BOOKING_CHECK_IN_01 + CREATE_ADDON_03, 200, None, "", "", "", "", False, ""),
            ("EditAddOnV2_06",
             "(MultiCurrency)Edit the add-on on part-checkout booking with pre-tax amount where add-on was created with post-tax",
             PART_CHECKOUT_01 + CREATE_ADDON_02, 200, None, "", "", "", "", True, ""),
            # ("EditAddOnV2_07", "(MultiCurrency)Edit the add-on on checkout booking", FULL_CHECKOUT_01 + CREATE_ADDON_03,
            #  200, None, "",
            #  "", "", "", False, ""),
            # ("EditAddOnV2_08", "(MultiCurrency)Edit the linked-add-on quantity and dates on check-in booking",
            #  GROUP_BOOKING_CHECK_IN_01 + CREATE_ADDON_04, 200, None, "", "", "", "", False, ""),
            ("EditAddOnV2_09", "(MultiCurrency)Edit the linked-add-on on checked-out booking",
             FULL_CHECKOUT_01 + CREATE_ADDON_04, 200,
             None, "", "", "", "", True,
             "Skipping this test case as now we won't allow create addon after charge is invoiced "),
            ("EditAddOnV2_10", "(MultiCurrency)Edit the add-on with wrong charge-type",
             SINGLE_BOOKING_01 + CREATE_ADDON_01,
             403, None, "", error_messages.CREDIT_CHARGE_ON_NON_B2B, "", "", False, ""),
            ("EditAddOnV2_11", "(MultiCurrency)Edit the add-on with 0 quantity", SINGLE_BOOKING_01 + CREATE_ADDON_01,
             400, None, "", error_messages.CREDIT_CHARGE_ON_NON_B2B, "", "", True, ""),
            ("EditAddOnV2_12", "(MultiCurrency)Edit the add-on on a cancelled booking",
             SINGLE_BOOKING_02 + CREATE_ADDON_01 + CANCEL_FULL_BOOKING,
             400, None, "", error_messages.INACTIVE_BOOKING, "", "", False, ""),
            ("EditAddOnV2_13", "(MultiCurrency)Edit the add-on with providing absolute dates outside the booking",
             SINGLE_BOOKING_02 + CREATE_ADDON_01, 400, None, "", error_messages.INVALID_EXPENSE_DATE, "", "", True,
             ""),
            ("EditAddOnV2_14", "(MultiCurrency)Edit linked add-on with pre-tax amount",
             SINGLE_BOOKING_01 + CREATE_ADDON_04, 400, None,
             "",
             error_messages.LINKED_ADDON_WITH_PRETAX, "", "", True, ""),
            ("EditAddOnV2_15", "(MultiCurrency)Edit linked add-on by changing end-relative to checkout",
             SINGLE_BOOKING_01 + CREATE_ADDON_04,
             400, None, "", error_messages.LINKED_ADDON_CHECKOUT_DATE, "", "", True, ""),
            ("EditAddOnV2_16", "(MultiCurrency)Edit the deleted add-on",
             SINGLE_BOOKING_01 + CREATE_ADDON_04 + REMOVE_ADDON, 404, None,
             "",
             "", "", "", True, ""),
            ("EditAddOnV2_17", "Edit the charge type in edit add on for linked addon",
             SINGLE_BOOKING_01 + CREATE_ADDON_04,
             400, None, "", error_messages.VALIDATION_ERROR, "", "", True, ""),
        ])
    @pytest.mark.regression
    def test_edit_add_on_v2(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                            error_code, error_message, dev_message, error_payload, skip_case, skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        with mock_tax_calculator_service(currency=CurrencyType(currency)) and mock_catalog_client(
                mocked_seller_type=SellerType.MARKETPLACE.value):
            if previous_actions:  # Running the pre-requisite for the actual test.
                self.common_request_caller(client_, previous_actions, hotel_id)
            # Running the actual test
            response = self.booking_request.edit_add_on_v2(client_, test_case_id,
                                                           self.booking_request.booking_id,
                                                           self.booking_request.addon_id, status_code,
                                                           user_type,currency)
        self.billing_request.get_bill_request(client_, self.booking_request.bill_id, 200);
        # Vaildations
        if status_code in (400, 403, 404):
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in (200, 201):
            self.validation(response, test_case_id, client_, hotel_id)
        else:
            assert False, "Response status code is not matching"

    def validation(self, response, test_case_id, client_, hotel_id):
        validation = EditAddOnValidations(test_case_id, hotel_id)
        validation.validate_response(response, client_, self.booking_request, self.booking_request.booking_id
                                     , self.billing_request, self.booking_request.bill_id)

    ################################################## POLICY ##################################################
    @pytest.mark.parametrize("test_case_id, tc_super_admin ,tc_description, previous_actions, status_code, user_type, "
                             "error_code, error_message, dev_message, error_payload, skip_case, skip_message", [

                                 # ("EditAddOnV2Policy_01", "EditAddOnV2_05", "Edit a add-on by FDM after check-in",
                                 #  GROUP_BOOKING_CHECK_IN_01 +
                                 #  CREATE_ADDON_03, 200, None, "", "", "", "", False, ""),
                                 # ("EditAddOnV2Policy_02", "EditAddOnV2_05", "Edit a add-on by cr-team after check-in",
                                 #  GROUP_BOOKING_CHECK_IN_01 + CREATE_ADDON_03, 200, None, "", "", "", "", False, ""),
                                 # ("EditAddOnV2Policy_03", "EditAddOnV2_06", "Edit a add-on by fdm after part-checkout",
                                 #  PART_CHECKOUT_01 + CREATE_ADDON_02, 200, None, "", "", "", "", True, ""),
                                 # ("EditAddOnV2Policy_04", "EditAddOnV2_07", "Edit a add-on by fdm after checkout",
                                 #  FULL_CHECKOUT_01 + CREATE_ADDON_03,
                                 #  403, FDM, "", error_messages.PAST_DATED_EDIT_ADDON_FDM, "", "", False, ""),
                                 # ("EditAddOnV2Policy_05", "EditAddOnV2_07", "Edit a add-on by cr-team after checkout",
                                 #  FULL_CHECKOUT_01 +
                                 #  CREATE_ADDON_03, 403, CR_TEAM, "", error_messages.ADDON_CHECKOUT_ERROR, "", "", False,
                                 #  ""),
                                 # ("EditAddOnV2Policy_06", "EditAddOnV2_01",
                                 #  "Edit a non linked add-on by fdm which is past dated",
                                 #  PAST_CHECK_IN_01 + CREATE_ADDON_05, 403, FDM, "",
                                 #  error_messages.PAST_DATED_EDIT_ADDON_FDM, "", "", False, ""),
                                 # ("EditAddOnV2Policy_07", "EditAddOnV2_08",
                                 #  "Edit a linked add-on by fdm which is past dated",
                                 #  GROUP_BOOKING_CHECK_IN_01 + CREATE_ADDON_04, 200, FDM, "", "", "", "", False, ""),
                             ])
    @pytest.mark.regression
    def test_edit_add_on_policy(self, client_, test_case_id, tc_super_admin, tc_description, previous_actions,
                                status_code, user_type, error_code, error_message, dev_message, error_payload,
                                skip_case, skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]
        if tc_super_admin is None:
            tc_super_admin = test_case_id
        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        with mock_tax_calculator_service(currency=CurrencyType(currency)) and mock_catalog_client(
                mocked_seller_type=SellerType.MARKETPLACE.value):
            if previous_actions:  # Running the pre-requisite for the actual test.
                self.common_request_caller(client_, previous_actions, hotel_id)
            # Running the actual test
            response = self.booking_request.edit_add_on_v2(client_, tc_super_admin,
                                                           self.booking_request.booking_id,
                                                           self.booking_request.addon_id, status_code,
                                                           user_type)
        # Vaildations
        if status_code in (400, 403):
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in (200, 201):
            self.validation(response, tc_super_admin, client_, hotel_id)
        else:
            assert False, "Response status code is not matching"
