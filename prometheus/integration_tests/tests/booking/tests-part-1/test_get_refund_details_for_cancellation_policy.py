import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.booking.validations.validation_get_refund_details_for_cancellation_policy import \
    ValidationGetRefundDetailsForCancellationPolicy


class TestGetRefundDetailsForCancellationPolicy(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, refund_rule_required", [
            # --------------------------------- Field Related Negative Cases -----------------------------------#
            ("GetRefundDetailsForCancellationPolicy_01", 'Remove Cancellation charge key',
             [{'id': "booking_01", 'type': 'booking_v2'}], 400, None, "04010006",
             "[Cancellation Charge] -> Missing data for required field.", "", {"field": "cancellation_charge"},
             False, "", True),
            ("GetRefundDetailsForCancellationPolicy_02", 'Provide Cancellation amount as NULL',
             [{'id': "booking_01", 'type': 'booking_v2'}], 400, None, "04010006",
             "[Cancellation Charge] -> Field may not be null.", "", {"field": "cancellation_charge"}, False, "", True),
            ("GetRefundDetailsForCancellationPolicy_03", 'Provide Cancellation charge without currency',
             [{'id': "booking_01", 'type': 'booking_v2'}], 400, None, "04010006",
             "[Cancellation Charge] -> Failed creating money object with error: Initialization using float not supported",
             "", {"field": "cancellation_charge"}, False, "", True),
            ("GetRefundDetailsForCancellationPolicy_04", 'Remove Cancellation policy key',
             [{'id': "booking_01", 'type': 'booking_v2'}], 400, None, "04010006",
             "[Cancellation Policy] -> Missing data for required field.", "", {"field": "cancellation_policy"},
             False, "", True),
            ("GetRefundDetailsForCancellationPolicy_05", 'Provide Cancellation policy as NULL',
             [{'id': "booking_01", 'type': 'booking_v2'}], 400, None, "04010006",
             "[Cancellation Policy] -> Field may not be null.", "", {"field": "cancellation_policy"}, False, "", True),
            ("GetRefundDetailsForCancellationPolicy_06", 'Provide Invalid Cancellation policy as NULL',
             [{'id': "booking_01", 'type': 'booking_v2'}], 400, None, "04010006",
             "[Cancellation Policy] -> 'invalid_policy' is not a valid choice for cancellation policy", "",
             {"field": "cancellation_policy"}, False, "", True),
            ("GetRefundDetailsForCancellationPolicy_07", 'Get refund details where refund rule config is not there',
             [{'id': "booking_01", 'type': 'booking_v2'}], 400, None, "04015997", "Auto Refund Not Possible", "", "",
             True, "Changed to make frontend work correctly", False),
            # ------------------------------------------ Positive Cases --------------------------------------------#
            ("GetRefundDetailsForCancellationPolicy_08", 'Get refund details where booking does not haveave any payment',
             [{'id': "booking_01", 'type': 'booking_v2'}], 200, None, "", "", "", "", False, "", True),
            ("GetRefundDetailsForCancellationPolicy_09", 'Get refund details where booking have PAH payment',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': 'AddPaymentV2_01_old', 'type': 'add_payment_v2'}], 200,
             None, "", "", "", "", False, "", True),
            ("GetRefundDetailsForCancellationPolicy_10", 'Get refund details where booking have PTT payment of TCR',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': 'AddPaymentV2_56', 'type': 'add_payment_v2'}], 200,
             None, "", "", "", "", False, "", True),
            ("GetRefundDetailsForCancellationPolicy_11",
             'Get refund details where booking have PAH and PTT payment of TCR',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': 'AddPaymentV2_56', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_01_old', 'type': 'add_payment_v2'}], 200, None, "", "", "", "", False, "", True),
            ("GetRefundDetailsForCancellationPolicy_12", 'Get refund details where booking have PAH and PTT payment of '
                                                         'TCR, such that charge amount is more than PAH payment',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': 'AddPaymentV2_56', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_01_old', 'type': 'add_payment_v2'}], 200, None, "", "", "", "", False, "", True),
            ("GetRefundDetailsForCancellationPolicy_13", 'Get refund details where booking having non refundable payment',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "create_booking_payment_14", 'type': 'add_payment_v2'}],
             200, None, "", "", "", "", False, "", True),
            ("GetRefundDetailsForCancellationPolicy_14",
             'Get refund details where booking having refundable and non refundable payment',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "create_booking_payment_14", 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_01_old', 'type': 'add_payment_v2'}, {'id': 'AddPaymentV2_56', 'type': 'add_payment_v2'}],
             200, None, "", "", "", "", False, "", True),
            ("GetRefundDetailsForCancellationPolicy_15", 'Get refund details where booking having refundable and non '
                                                         'refundable payment, having cash refund with it',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "create_booking_payment_14", 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_01_old', 'type': 'add_payment_v2'}, {'id': 'AddPaymentV2_56', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_33', 'type': 'add_payment_v2'}], 200, None, "", "", "", "", False, "", True),
            ("GetRefundDetailsForCancellationPolicy_16", 'Get refund details where booking having refundable and non '
                                                         'refundable payment, having paid at ota refund with it',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "AddPaymentV2_56", 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_01_old', 'type': 'add_payment_v2'},
              {'id': 'create_booking_payment_14', 'type': 'add_payment_v2'},
              {'id': 'AddPaymentV2_refund_details_16', 'type': 'add_payment_v2'}], 200, None, "", "", "", "", False, "",
             True),
        ])
    @pytest.mark.regression
    def test_get_refund_details_for_cancellation_policy(self, client_, test_case_id, tc_description, previous_actions,
                                                        status_code, user_type, error_code, error_message, dev_message,
                                                        error_payload, skip_case, skip_message, refund_rule_required):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.get_refund_details_for_cancellation_policy_request(
            client_, self.booking_request.booking_id, test_case_id, status_code, user_type, refund_rule_required)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id):
        validation = ValidationGetRefundDetailsForCancellationPolicy(response, test_case_id)
        validation.validate_response()
