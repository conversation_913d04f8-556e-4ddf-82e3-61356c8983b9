import pytest

from prometheus.integration_tests.config.common_config import HOTEL_ID, HOTEL_CURRENCY_MAP
from prometheus.integration_tests.tests import before_test_actions
from prometheus.integration_tests.tests.base_test import BaseTest, mock_tax_calculator_service, CurrencyType
from prometheus.integration_tests.tests.booking.validations.validation_edit_room_stay_prices_v2 import ValidationCharges


class TestEditRoomStayPrices(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, previous_actions, is_mock_rule_req", [
            ("Edit_Charge_Price_01", "edit posttax price of a booked charge", 200, "", "",
             "", "", "", "", before_test_actions.Checkin_Booking01, True),
            ("Edit_Charge_Price_02", "edit pretax price of a booked charge", 200, "", "",
             "", "", "", "", before_test_actions.Checkin_Booking01, True),
            ("Edit_Charge_Price_03", "edit posttax price of a booked charge of non-checkedin booking", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_BOOKING_01, True),
            ("Edit_Charge_Price_04", "edit pretax price of a booked charge of non-checkedin booking", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_BOOKING_01, True),
            ("Edit_Charge_Price_05", "edit pretax price of a booked charge of non-checkedin booking", 200, "", "",
             "", "", "", "", before_test_actions.Booking_With_Inclusion01, True),
            ("Edit_Charge_Price_06", "edit pretax price of a booked charge of non-checkedin booking", 200, "", "",
             "", "", "", "", before_test_actions.Booking_With_Inclusion01, True),
            ("Edit_Charge_Price_07", "while edit booking not sending price of one inclusion", 200, "", "",
             "", "", "", "", before_test_actions.Booking_With_Inclusion01, True),
            ("Edit_Charge_Price_08", "Split the billed_entity of a rateplan", 200, "", "",
             "", "", "", "", before_test_actions.SINGLE_BOOKING_01, True),

        ])
    @pytest.mark.regression
    def test_edit_charges(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                          error_message, dev_message, error_payload, skip_case, previous_actions, is_mock_rule_req):
        if skip_case:
            pytest.skip("")
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]

        currency = HOTEL_CURRENCY_MAP[hotel_id] if hotel_id else 'INR'
        with mock_tax_calculator_service(currency=CurrencyType(currency)):
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.billing_request.edit_charge_price_request(client_, test_case_id,
                                                                  self.booking_request.booking_id,
                                                                  status_code, user_type, is_mock_rule_req)
        if status_code in (400, 403):
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in (200, 201):
            self.validation(response, test_case_id, client_, self.billing_request, self.booking_request.bill_id,
                            status_code, user_type)
        else:
            assert False, "Response status code is not matching"

    def validation(self, response, test_case_id, client_, billing_request, bill_id, status_code, user_type):
        validation = ValidationCharges(client_, test_case_id, response)
        return validation.validate_response(client_, billing_request, status_code, bill_id, user_type)
