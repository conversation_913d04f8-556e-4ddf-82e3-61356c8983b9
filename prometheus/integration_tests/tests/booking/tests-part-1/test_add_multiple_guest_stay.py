from datetime import date, timedelta
import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.utilities.common_utils import query_execute, set_inventory_count
from prometheus.integration_tests.tests.booking.validations.validation_add_multiple_guest_stay import \
    ValidationAddMultipleGuestStay


class TestAddMultipleGuestStay(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, extras", [
            ("AddMultipleGuest_01", 'Add a single guestStay to a room with guest details', SINGLE_BOOKING_01_USING_V2,
             201, None, "", "", "", "", False, "", [False, False, False, False]),
            ("AddMultipleGuest_01", 'Add a single guestStay to a room with guest details',
             Booking_v2_with_rate_plan_and_inclusion, 201, None, "", "", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_02",
             'Add Multiple GuestStay with different CI/CO dates with one GuestStay having guest details',
             SINGLE_BOOKING_02, 201, None, "", "", "", "", False, "", [False, False, False, False]),
            ("AddMultipleGuest_02",
             'Add Multiple GuestStay with different CI/CO dates with one GuestStay having guest details',
             [{'id': "Booking_02", 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}], 201, None, "", "", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_03", 'Add Multiple GuestStay with different CI/CO dates within RoomStay dates',
             SINGLE_BOOKING_02, 201, None, "", "", "", "", False, "", [False, False, False, False]),
            ("AddMultipleGuest_03", 'Add Multiple GuestStay with different CI/CO dates within RoomStay dates',
             [{'id': "Booking_02", 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}], 201, None, "", "", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_04", 'Add multiple guests with past checkin dates', SINGLE_BOOKING_01_USING_V2, 400, None,
             "04010130",
             "Room stay dates and guest stay dates don't match. Please select the appropriate room/guest stay dates.",
             "GuestStay checkin and checkout date should be within room stay checkin, checkout dates", "", False, "",
             [False, False, False, False]),
            ("AddMultipleGuest_05",
             'Add one guest without checkin_date key and another guest without_checkout checkout key',
             SINGLE_BOOKING_01_USING_V2, 400, None, "04010006", "", "", "", False, "", [False, False, False, False]),
            ("AddMultipleGuest_06", 'Add multiple guestStay when booking & room in checkin state',
             SINGLE_BOOKING_CHECK_IN_01, 201, None, "", "", "", "", False, "", [False, False, False, False]),
            ("AddMultipleGuest_06", 'Add multiple guestStay when booking & room in checkin state',
             [{'id': "Booking_01", 'type': 'booking', 'enable_rate_manager': True,
               'is_booking_v2': True, 'is_inclusion_added': True}, {'id': "checkinPost_01", 'type': 'check_in'}], 201,
             None, "", "", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_07", 'Add Multiple GuestStay(adding just one guest) for a part_checked_in room of a '
                                    'part_checked_in single room booking',
             [{'id': "Booking_32", 'type': 'booking'}, {'id': "checkinPost_02", 'type': 'check_in'}],
             201, None, "", "", "", "", False, "", [False, False, False, False]),
            ("AddMultipleGuest_07", 'Add Multiple GuestStay(adding just one guest) for a part_checked_in room of a '
                                    'part_checked_in single room booking',
             [{'id': "Booking_32", 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}, {'id': "checkinPost_02", 'type': 'check_in'}],
             201, None, "", "", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_08", 'Add multiple guests with with different CI/CO dates as booking_outside booking',
             SINGLE_BOOKING_01_USING_V2, 400, None, "04010130",
             "Room stay dates and guest stay dates don't match. Please select the appropriate room/guest stay dates.",
             "GuestStay checkin and checkout date should be within room stay checkin, checkout dates",
             "", False, "", [False, False, False, False]),
            ("AddMultipleGuest_08", 'Add multiple guests with with different CI/CO dates as booking_outside booking',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04010130",
             "Room stay dates and guest stay dates don't match. Please select the appropriate room/guest stay dates.",
             "GuestStay checkin and checkout date should be within room stay checkin, checkout dates",
             "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_09", 'Add multiple guests which exceeds the occupancy allowed int that roomStay',
             SINGLE_BOOKING_01_USING_V2, 400, None, "04010122",
             "You entered a value greater than the max occupancy of the room type. Please check and enter a correct "
             "value for occupancy.", "", "", False, "", [False, False, False, False]),
            ("AddMultipleGuest_09", 'Add multiple guests which exceeds the occupancy allowed int that roomStay',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04010122",
             "You entered a value greater than the max occupancy of the room type. Please check and enter a correct "
             "value for occupancy.", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_10", 'Add multiple guestStay for a cancelled room',
             [{'id': "Booking_cancelAction_02", 'type': 'booking'}, {'id': "CancelAction_06", 'type': 'cancel'}], 400,
             None, "04015005", "Cannot add guest stay. RoomStay is either cancelled or marked noshow", "", "", False,
             "", [False, False, False, False]),
            ("AddMultipleGuest_10", 'Add multiple guestStay for a cancelled room',
             [{'id': "Booking_cancelAction_02", 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}, {'id': "CancelAction_06", 'type': 'cancel'}], 400, None, "04015005",
             "Cannot add guest stay. RoomStay is either cancelled or marked noshow", "", "", False, "", [True, True,
             False, False]),
            ("AddMultipleGuest_11", 'Add multiple guestStay for a cancelled booking',
             [{'id': "Booking_cancelAction_01", 'type': 'booking'}, {'id': "CancelAction_14", 'type': 'cancel'}], 400,
             None, "04015004", "Cannot update booking. Booking is either cancelled or marked noshow", "", "", False, "",
             [False, False, False, False]),
            ("AddMultipleGuest_11", 'Add multiple guestStay for a cancelled booking',
             [{'id': "Booking_cancelAction_01", 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}, {'id': "CancelAction_14", 'type': 'cancel'}], 400,
             None, "04015004", "Cannot update booking. Booking is either cancelled or marked noshow", "", "", False, "",
             [True, True, False, False]),
            ("AddMultipleGuest_12", 'Add multiple guestStay for a noshow room',
             [{'id': "Booking_125", 'type': 'booking'}, {'id': "noshowAction_36", 'type': 'noshow'}], 400,
             None, "04015005", "Cannot add guest stay. RoomStay is either cancelled or marked noshow", "", "", False,
             "", [False, False, True, False]),
            ("AddMultipleGuest_12", 'Add multiple guestStay for a noshow room',
             [{'id': "Booking_125", 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}, {'id': "noshowAction_36", 'type': 'noshow'}], 400, None, "04015005",
             "Cannot add guest stay. RoomStay is either cancelled or marked noshow", "", "", False, "", [True, True,
             True, False]),
            ("AddMultipleGuest_13", 'Add multiple guestStay for a noshow booking',
             [{'id': "Booking_118", 'type': 'booking'}, {'id': "noshowAction_02", 'type': 'noshow'}], 400,
             None, "04015004", "Cannot update booking. Booking is either cancelled or marked noshow", "", "", False, "",
             [False, False, True, False]),
            ("AddMultipleGuest_13", 'Add multiple guestStay for a noshow booking',
             [{'id': "Booking_118", 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}, {'id': "noshowAction_02", 'type': 'noshow'}], 400,
             None, "04015004", "Cannot update booking. Booking is either cancelled or marked noshow", "", "", False, "",
             [True, True, True, False]),
            ("AddMultipleGuest_14", 'Add guest when booking is already checked_out', FULL_CHECKOUT_03, 400, None,
             "04010111", "Booking is checked out", "", "", False, "", [False, False, False, False]),
            ("AddMultipleGuest_14", 'Add guest when booking is already checked_out',
             [{'id': 'Booking_01', 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}, {'id': 'checkinPost_01', 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreview_01', 'type': 'preview_invoice'},
              {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment', 'is_mock_rule_req': True},
              {'id': 'checkoutAction_04', 'type': 'checkout'}], 400, None, "04010111", "Booking is checked out", "", "",
             False, "", [True, True, False, False]),
            ("AddMultipleGuest_15",
             'Add guest to booking where one guest is cancelled and occupancy has gone below max limit',
             [{'id': "Booking_cancelAction_01", 'type': 'booking'}, {'id': "CancelAction_01", 'type': 'cancel'}], 201,
             None, "", "", "", "", False, "", [False, False, False, False]),
            ("AddMultipleGuest_15",
             'Add guest to booking where one guest is cancelled and occupancy has gone below max limit',
             [{'id': "Booking_cancelAction_01", 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}, {'id': "CancelAction_01", 'type': 'cancel'}], 201, None, "", "", "", "",
             True, "", [True, True, False, False]),
            ("AddMultipleGuest_16",
             'Add guest to booking where one guest is marked noshow and occupancy has gone below max limit',
             [{'id': "Booking_118", 'type': 'booking'}, {'id': "noshowAction_35", 'type': 'noshow'}], 201,
             None, "", "", "", "", False, "", [False, False, True, False]),
            ("AddMultipleGuest_16",
             'Add guest to booking where one guest is marked noshow and occupancy has gone below max limit',
             [{'id': "Booking_118", 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}, {'id': "noshowAction_35", 'type': 'noshow'}], 201, None, "", "", "", "",
             False, "", [True, True, True, False]),
            ("AddMultipleGuest_randomCICOtime", 'Add a single guestStay to a room with guest details',
             SINGLE_BOOKING_01_USING_V2, 201, None, "", "", "", "", False, "", [False, False, False, False]),
            ("AddMultipleGuest_randomCICOtime", 'Add a single guestStay to a room with guest details',
             Booking_v2_with_rate_plan_and_inclusion, 201, None, "", "", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_17", 'Add guest on checkout date',
             [{'id': "Booking_112", 'type': 'booking'}, {'id': "checkinPost_78", 'type': 'check_in'}], 400, None,
             "04010130",
             "Room stay dates and guest stay dates don't match. Please select the appropriate room/guest stay dates.",
             "GuestStay checkin and checkout date should be within room stay checkin, checkout dates", "", False,
             "", [False, False, True, False]),
            ("AddMultipleGuest_17", 'Add guest on checkout date',
             [{'id': "Booking_112", 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}, {'id': "checkinPost_78", 'type': 'check_in'}], 400, None, "04010130",
             "Room stay dates and guest stay dates don't match. Please select the appropriate room/guest stay dates.",
             "GuestStay checkin and checkout date should be within room stay checkin, checkout dates", "", False,
             "", [False, False, True, False]),
            ("AddMultipleGuest_18", 'Add guest on checkout date of guest whose check in date same as of room',
             [{'id': "Booking_112", 'type': 'booking'}, {'id': "checkinPost_78", 'type': 'check_in'}], 400, None,
             "04010139", "Cannot add guest stay on dates where charges are already consumed", "", "", False, "", [False,
             False, True, False]),
            ("AddMultipleGuest_18", 'Add guest on checkout date of guest whose check in date same as of room',
             [{'id': "Booking_112", 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}, {'id': "checkinPost_78", 'type': 'check_in'}], 400, None, "04010139",
             "Cannot add guest stay on dates where charges are already consumed", "", "", False, "", [False, False, True,
             False]),
            ("AddMultipleGuest_19", 'Add guest for partial checkout room',
             [{'id': "Booking_76", 'type': 'booking'}, {'id': "checkinPost_03", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreview_65', 'type': 'preview_invoice'}, {'id': 'checkoutAction_35', 'type': 'checkout'}],
             400, None, "04010139", "Cannot add guest stay on dates where charges are already consumed", "", "", False,
             "", [False, False, False, False]),
            ("AddMultipleGuest_19", 'Add guest for partial checkout room',
             [{'id': "Booking_76", 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}, {'id': "checkinPost_03", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreview_65', 'type': 'preview_invoice'}, {'id': 'checkoutAction_35', 'type': 'checkout'}],
             400, None, "04010139", "Cannot add guest stay on dates where charges are already consumed", "", "", False,
             "", [True, True, False, False]),
            ("AddMultipleGuest_20", 'Add multiple guestStay after reverse check in',
             [{'id': "Booking_01", 'type': 'booking'}, {'id': "checkinPost_01", 'type': 'check_in'},
              {'type': 'delete_booking_action'}], 201, None, "", "", "", "", False, "", [False, False, False, False]),
            ("AddMultipleGuest_20", 'Add multiple guestStay after reverse check in',
             [{'id': "Booking_01", 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}, {'id': "checkinPost_01", 'type': 'check_in'},
              {'type': 'delete_booking_action'}], 201, None, "", "", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_21", 'Add multiple guestStay after reverse check out',
             [{'id': 'Booking_01', 'type': 'booking'}, {'id': 'checkinPost_01', 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreview_01', 'type': 'preview_invoice'},
              {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment', 'is_mock_rule_req': True},
              {'id': 'checkoutAction_04', 'type': 'checkout'}, {'type': 'delete_booking_action'}], 400, None,
             "04010139", "Cannot add guest stay on dates where charges are already consumed", "", "", False, "", [False,
             False, False, False]),
            ("AddMultipleGuest_21", 'Add multiple guestStay after reverse check out',
             [{'id': 'Booking_01', 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}, {'id': 'checkinPost_01', 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreview_01', 'type': 'preview_invoice'},
              {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment', 'is_mock_rule_req': True},
              {'id': 'checkoutAction_04', 'type': 'checkout'}, {'type': 'delete_booking_action'}], 400, None,
             "04010139", "Cannot add guest stay on dates where charges are already consumed", "", "", False, "", [True,
             True, False, False]),
            ("AddMultipleGuest_22", 'Add multiple guestStay after reverse cancel of booking',
             [{'id': "Booking_01", 'type': 'booking'}, {'id': "CancelAction_14", 'type': 'cancel'},
              {'type': 'delete_booking_action'}], 201, None, "", "", "", "", False, "", [False, False, False, False]),
            ("AddMultipleGuest_22", 'Add multiple guestStay after reverse cancel of booking',
             [{'id': "Booking_01", 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}, {'id': "CancelAction_14", 'type': 'cancel'},
              {'type': 'delete_booking_action'}], 201, None, "", "", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_23", 'Add multiple guestStay after reverse no show of booking',
             [{'id': "Booking_118", 'type': 'booking'}, {'id': "noshowAction_02", 'type': 'noshow'},
              {'type': 'delete_booking_action'}], 201, None, "", "", "", "", False, "", [False, False, True, False]),
            ("AddMultipleGuest_23", 'Add multiple guestStay after reverse no show of booking',
             [{'id': "Booking_118", 'type': 'booking', 'enable_rate_manager': True, 'is_booking_v2': True,
               'is_inclusion_added': True}, {'id': "noshowAction_02", 'type': 'noshow'},
              {'type': 'delete_booking_action'}], 201, None, "", "", "", "", False, "", [True, True, True, False]),
            ("AddMultipleGuest_24", 'Provide invalid enum in age group', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[Age Group] -> 'INVALID' is not a valid choice for age group.", "", {"field": "guest_stays.0.age_group"},
             False, "", [False, False, False, False]),
            ("AddMultipleGuest_25", 'Provide child as enum in age group', SINGLE_BOOKING_01_USING_V2, 201, None, "", "", "", "",
             False, "", [False, False, False, False]),
            ("AddMultipleGuest_26", 'Provide infant as enum in age group', SINGLE_BOOKING_01_USING_V2, 400, None, "04010101",
             "Something went wrong. Please contact Escalations team with the booking id.", "", "", False, "", [False,
             False, False, False]),
            ("AddMultipleGuest_27", 'Provide age group as NULL', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[Age Group] -> Field may not be null.", "", {"field": "guest_stays.0.age_group"}, False, "", [False, False,
             False, False]),
            ("AddMultipleGuest_28", 'Provide age group as empty', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[Age Group] -> '' is not a valid choice for age group.", "", {"field": "guest_stays.0.age_group"}, False,
             "", [False, False, False, False]),
            ("AddMultipleGuest_29", 'Delete age group key', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[Age Group] -> Missing data for required field.", "", {"field": "guest_stays.0.age_group"}, False, "",
             [False, False, False, False]),
            ("AddMultipleGuest_30", 'Provide invalid checkin date', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[Checkin Date] -> Not a valid datetime.", "", {"field": "guest_stays.0.checkin_date"}, False, "", [False,
             False, False, False]),
            ("AddMultipleGuest_31", 'Provide checkin date as NULL', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[Checkin Date] -> Checkin date may not be null.", "", {"field": "guest_stays.0.checkin_date"}, False, "",
             [False, False, False, False]),
            ("AddMultipleGuest_32", 'Provide checkin date as empty', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[Checkin Date] -> Not a valid datetime.", "", {"field": "guest_stays.0.checkin_date"}, False, "", [False,
             False, False, False]),
            ("AddMultipleGuest_33", 'Provide invalid checkout date', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[Checkout Date] -> Not a valid datetime.", "", {"field": "guest_stays.0.checkout_date"}, False, "", [False,
             False, False, False]),
            ("AddMultipleGuest_34", 'Provide checkout date as NULL', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[Checkout Date] -> Check-out date may not be null.", "", {"field": "guest_stays.0.checkout_date"}, False,
             "", [False, False, False, False]),
            ("AddMultipleGuest_35", 'Provide checkout date as empty', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[Checkout Date] -> Not a valid datetime.", "", {"field": "guest_stays.0.checkout_date"}, False, "", [False,
             False, False, False]),
            ("AddMultipleGuest_36", 'Provide first name as empty in guest', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[First Name] -> Field value cannot be blank", "", {"field": "guest_stays.0.guest.first_name"}, False, "",
             [False, False, False, False]),
            ("AddMultipleGuest_37", 'Provide first name as NULL in guest', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[First Name] -> Field may not be null.", "", {"field": "guest_stays.0.guest.first_name"}, False, "",
             [False, False, False, False]),
            ("AddMultipleGuest_38", 'Provide GST details but does not provide legal name', SINGLE_BOOKING_01_USING_V2, 400, None,
             "04010006", "[Legal Name] -> Please provide legal name.", "",
             {"field": "guest_stays.0.guest.gst_details.legal_name"}, False, "", [False, False, False, False]),
            ("AddMultipleGuest_39", 'Provide legal name as NULL in GST details', SINGLE_BOOKING_01_USING_V2, 400, None,
             "04010006", "[Legal Name] -> Legal name may not be null.", "",
             {"field": "guest_stays.0.guest.gst_details.legal_name"}, False, "", [False, False, False, False]),
            ("AddMultipleGuest_40", 'Provide legal name as empty in GST details', SINGLE_BOOKING_01_USING_V2, 400, None,
             "04010006", "[Legal Name] -> Field value cannot be blank", "",
             {"field": "guest_stays.0.guest.gst_details.legal_name"}, False, "", [False, False, False, False]),
            ("AddMultipleGuest_41", 'Provide Phone field but does not provide number in it', SINGLE_BOOKING_01_USING_V2, 400,
             None, "04010006", "[Number] -> Please provide phone number.", "",
             {"field": "guest_stays.0.guest.phone.number"}, False, "", [False, False, False, False]),
            ("AddMultipleGuest_42", 'Provide Phone field and provide invalid number in it', SINGLE_BOOKING_01_USING_V2, 201,
             None, "", "", "", "", False, "", [False, False, False, False]),
            ("AddMultipleGuest_43", 'Provide Phone field and provide number as NULL', SINGLE_BOOKING_01_USING_V2, 400,
             None, "04010006", "[Number] -> Phone number may not be null.", "",
             {"field": "guest_stays.0.guest.phone.number"}, False, "", [False, False, False, False]),
            ("AddMultipleGuest_44", 'Provide Phone field and provide number as empty', SINGLE_BOOKING_01_USING_V2, 201,
             None, "", "", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_45", 'Provide invalid amount', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006", "", "", "",
             False, "", [True, True, False, False]),
            ("AddMultipleGuest_46", 'Provide invalid applicable date in price', SINGLE_BOOKING_01_USING_V2, 400, None,
             "04010006", "[Applicable Date] -> Not a valid datetime.", "",
             {"field": "new_room_stay_prices.0.applicable_date"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_47", 'Provide invalid bill to type', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[Bill To Type] -> 'INVALID' is not a valid choice for Bill-To Type", "",
             {"field": "new_room_stay_prices.0.bill_to_type"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_48", 'Provide bill to type as guest', SINGLE_BOOKING_01_USING_V2, 201, None, "", "", "", "",
             False, "", [True, True, False, False]),
            ("AddMultipleGuest_49", 'Provide bill to type as company for b2b booking', SINGLE_BOOKING_01_USING_V2, 201, None,
             "", "", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_50", 'Provide bill to type as company for booking without company details',
             SINGLE_BOOKING_01_USING_V2, 201, None, "", "", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_51", 'Does not provide type in Price', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[Type] -> Please provide price type.", "", {"field": "new_room_stay_prices.0.type"}, False, "",
             [True, True, False, False]),
            ("AddMultipleGuest_52", 'Provide type as credit and create walkin booking', SINGLE_BOOKING_01_USING_V2, 201, None,
             "", "", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_53", 'Provide type as NULL in Price', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[Type] -> Price type may not be null.", "", {"field": "new_room_stay_prices.0.type"},
             False, "", [True, True, False, False]),
            ("AddMultipleGuest_54", 'Provide type as empty in Price', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[Type] -> '' is not a valid choice for Price Type", "", {"field": "new_room_stay_prices.0.type"}, False,
             "", [True, True, False, False]),
            ("AddMultipleGuest_55", 'Add guest but provide price as NULL', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[New Room Stay Prices] -> New room stay prices may not be null.", "", {"field": "new_room_stay_prices"},
             False, "", [True, True, False, False]),
            ("AddMultipleGuest_56", 'Add guest but provide price as empty', SINGLE_BOOKING_01_USING_V2, 400, None, "04010006",
             "[New Room Stay Prices] -> Invalid type.", "", {"field": "new_room_stay_prices"}, False, "", [True, True,
             False, False]),
            ("AddMultipleGuest_57", 'Does not provide applicable date in Price',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04010006",
             "[Applicable Date] -> Please provide applicable date.", "",
             {"field": "new_room_stay_prices.0.applicable_date"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_58", 'Provide applicable date as NULL in Price', Booking_v2_with_rate_plan_and_inclusion,
             400, None, "04010006", "[Applicable Date] -> Applicable date may not be null.", "",
             {"field": "new_room_stay_prices.0.applicable_date"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_59", 'Provide applicable date empty in Price', Booking_v2_with_rate_plan_and_inclusion,
             400, None, "04010006", "[Applicable Date] -> Not a valid datetime.", "",
             {"field": "new_room_stay_prices.0.applicable_date"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_60", 'Does not provide bill to type in Price', Booking_v2_with_rate_plan_and_inclusion,
             400, None, "04010006", "[Bill To Type] -> Please provide bill to type.", "",
             {"field": "new_room_stay_prices.0.bill_to_type"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_61", 'Provide bill to type as NULL in Price', Booking_v2_with_rate_plan_and_inclusion,
             400, None, "04010006", "[Bill To Type] -> Bill to type may not be null.", "",
             {"field": "new_room_stay_prices.0.bill_to_type"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_62", 'Provide bill to type as empty in Price', Booking_v2_with_rate_plan_and_inclusion,
             400, None, "04010006", "[Bill To Type] -> '' is not a valid choice for Bill-To Type", "",
             {"field": "new_room_stay_prices.0.bill_to_type"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_63", 'Provide Negative Amount', Booking_v2_with_rate_plan_and_inclusion, 400, None,
             "04010006", "[ Schema] -> pretax_amount or posttax_amount cannot be negative", "",
             {"field": "new_room_stay_prices.0._schema"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_64", 'Provide amount as zero', Booking_v2_with_rate_plan_and_inclusion, 201, None, "",
             "", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_65", 'Does not provide rate plan reference id', Booking_v2_with_rate_plan_and_inclusion,
             201, None, "", "", "", "", False, "", [False, True, False, False]),
            ("AddMultipleGuest_66", 'Does not provide rate plan reference id and inclusions',
             Booking_v2_with_rate_plan_and_inclusion, 201, None, "", "", "", "", False, "", [False, False, False, False]),
            ("AddMultipleGuest_67", 'Provide start date in rate plan inclusion as NULL',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04010006", "[Start Date] -> Field may not be null.",
             "", {"field": "rate_plan_inclusions.0.start_date"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_68", 'Provide end date in rate plan inclusion as NULL',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04010006", "[End Date] -> Field may not be null.", "",
             {"field": "rate_plan_inclusions.0.end_date"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_69", 'Provide sku id in rate plan inclusion as NULL',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04010006", "[Sku Id] -> sku id may not be null.", "",
             {"field": "rate_plan_inclusions.0.sku_id"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_70", 'Provide start date in rate plan inclusion as Empty',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04010006", "[Start Date] -> Not a valid date.", "",
             {"field": "rate_plan_inclusions.0.start_date"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_71", 'Provide end date in rate plan inclusion as Empty',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04010006", "[End Date] -> Not a valid date.", "",
             {"field": "rate_plan_inclusions.0.end_date"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_72", 'Provide sku id in rate plan inclusion as Empty',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04015942", "Sku not found", "", "", False, "", [True,
             True, False, False]),
            ("AddMultipleGuest_73", 'Does not Provide start date in rate plan inclusion',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04010006",
             "[Start Date] -> Missing data for required field.", "", {"field": "rate_plan_inclusions.0.start_date"},
             False, "", [True, True, False, False]),
            ("AddMultipleGuest_74", 'Does not Provide end date in rate plan inclusion ',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04010006",
             "[End Date] -> Missing data for required field.", "", {"field": "rate_plan_inclusions.0.end_date"}, False,
             "", [True, True, False, False]),
            ("AddMultipleGuest_75", 'Does not Provide sku id in rate plan inclusion',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04010006", "[Sku Id] -> sku id data missing.", "",
             {"field": "rate_plan_inclusions.0.sku_id"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_76", 'Provide quantity in rate plan inclusion as negative',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04010006",
             "[Quantity] -> Value must be greater than 0", "", {"field": "rate_plan_inclusions.0.quantity"}, False, "",
             [True, True, False, False]),
            ("AddMultipleGuest_77", 'Provide both pretax and posttax amount in rate plan inclusion',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04010006",
             "[Rate Plan Inclusions] -> Please provide either of posttax_amount or pretax_amount", "",
             {"field": "rate_plan_inclusions.0.rate_plan_inclusions"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_78", 'Provide pretax or posttax amount negative in rate plan inclusion',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04010006",
             "[Rate Plan Inclusions] -> pretax_amount or posttax_amount cannot be negative", "",
             {"field": "rate_plan_inclusions.0.rate_plan_inclusions"}, False, "", [True, True, False, False]),
            ("AddMultipleGuest_79", 'Does not provide any of the mandatory fields in rate plan inclusion',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04010006", "", "", "", False, "", [True, True, False,
             False]),
            ("AddMultipleGuest_80", 'Does not provide mandatory fields', Booking_v2_with_rate_plan_and_inclusion, 400,
             None, "04010006", "", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_81", 'Send pretax price in Price while send posttax price in inclusion',
             Booking_v2_with_rate_plan_and_inclusion, 400, None, "04010101",
             "Something went wrong. Please contact Escalations team with the booking id.",
             "pretax_amount provided in price whereas posttax_amount provided in rate_plan_inclusions", "", False, "",
             [True, True, False, False]),
            ("AddMultipleGuest_82", 'Does not provide non-mandatory fields', Booking_v2_with_rate_plan_and_inclusion,
             201, None, "", "", "", "", False, "", [False, False, False, False]),
            ("AddMultipleGuest_83", 'Provide empty booking id', Booking_v2_with_rate_plan_and_inclusion,
             404, None, 404, "Exception occurred.", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_84", 'Provide Booking id as NULL', Booking_v2_with_rate_plan_and_inclusion,
             404, None, "04010007", "Aggregate: Booking with id: null missing.", "", "", False, "", [True, True, False,
             False]),
            ("AddMultipleGuest_85", 'Provide wrong Booking id', Booking_v2_with_rate_plan_and_inclusion,
             404, None, "04010007", "Aggregate: Booking with id: 0000-0000-0000 missing.", "", "", False, "", [True,
             True, False, False]),
            ("AddMultipleGuest_86", 'Provide wrong room stay id', Booking_v2_with_rate_plan_and_inclusion,
             404, None, "04010004", "RoomStay not found. Please contact escalations.", "", "", False, "", [True, True,
             False, False]),
            ("AddMultipleGuest_87", 'Provide room stay id as NULL', Booking_v2_with_rate_plan_and_inclusion,
             404, None, 404, "Exception occurred.", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_88", 'Provide room stay id as empty', Booking_v2_with_rate_plan_and_inclusion,
             404, None, 404, "Exception occurred.", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_89", 'Provide Invalid room stay id', Booking_v2_with_rate_plan_and_inclusion,
             404, None, 404, "Exception occurred.", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_90", 'Add guestStay to a room with credit type after providing spot credit',
             BOOKING_WITH_SPOT_CREDIT_FOLIO, 400, None, "04010331", "Credit charges can not be billed to guest", "",
             "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_91", 'Add guestStay to a room with non-credit type after providing spot credit',
             BOOKING_WITH_SPOT_CREDIT_FOLIO, 201, None, "", "", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_136", 'Add guest in a room where room charges are billed to credit folio as super-admin',
             [{'id': "booking_171", 'type': 'booking_v2'}], 201, "", "", "", "", "", False, "",
             [True, True, False, False]),
            ("AddMultipleGuest_137", 'Add guest in a room where room charges are billed to credit folio as FDM',
             [{'id': "booking_171", 'type': 'booking_v2'}], 403, FDM, "", "", "", "", False, "",
             [True, True, False, False]),
            ("AddMultipleGuest_138", 'Add guest in booking where payment instruction is changed to pay after checkout '
                                     'for company as FDM',
             [{'id': "booking_18", 'type': 'booking_v2'},
              {'id': "Update_Default_Billing_45", 'type': 'update_default_billing_instructions'}],
             403, FDM, "", "", "", "", False, "", [True, True, False, False]),
            ("AddMultipleGuest_139", 'Add guest in booking where payment instruction is changed to pay after checkout '
                                     'for company as super-admin',
             [{'id': "booking_18", 'type': 'booking_v2'},
              {'id': "Update_Default_Billing_45", 'type': 'update_default_billing_instructions'}],
             201, "", "", "", "", "", False, "", [True, True, False, False]),
        ])
    @pytest.mark.regression
    def test_add_multiple_guest_stay(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                     user_type, error_code, error_message, dev_message, error_payload, skip_case,
                                     skip_message, extras):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        enable_rate_plan, is_inclusion_added, perform_night_audit, action_after_night_audit = extras[0], extras[1], extras[2], extras[3]

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id)
                self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
                if action_after_night_audit:
                    self.common_request_caller(client_, action_after_night_audit, hotel_id)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.add_multiple_guest_stay(client_, test_case_id, status_code,
                                                                self.booking_request.booking_id, enable_rate_plan,
                                                                is_inclusion_added, user_type)
        set_inventory_count(500, hotel_id)

        if test_case_id in ('AddMultipleGuest_12', 'AddMultipleGuest_13', 'AddMultipleGuest_16', 'AddMultipleGuest_18',
                            'AddMultipleGuest_23'):
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request.booking_id, self.booking_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_id, booking_request):
        validation = ValidationAddMultipleGuestStay(client_, test_case_id, response, booking_id, booking_request)
        validation.validate_response()


class TestAddGuestAfterChargeSplit(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, extras, has_slab_based_taxation, is_tax_clubbed",
        [
            ("AddMultipleGuest_92", 'Add a single guestStay to a room with slab based taxation',
             SINGLE_WALK_BOOKING_V2_01_SLAB_BASED, 201, None, "", "", "", "", False, "", [True, True], True, []),
            ("AddMultipleGuest_93", 'Add a single guestStay to a room where rate plan charge is in split condition',
             BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_03, 201, None, "", "", "", "", False, "", [True, True],
             False, []),
            ("AddMultipleGuest_94", 'Add a single guestStay to a room where rate plan charge is in split condition'
                                    ' having slab based taxation', BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_01,
             201, None, "", "", "", "", False, "", [True, True], True, []),
            ("AddMultipleGuest_95", 'Add guest after charge splits, such that higher tax slab get reached and split'
                                    ' charge remain to older tax slab',
             BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_02, 201, None, "", "", "", "", False, "", [True, True],
             True, []),
            ("AddMultipleGuest_96", 'Add guest having clubbed tax', SINGLE_WALK_BOOKING_V2_01_CLUBBED_CHARGE, 201,
             None, "", "", "", "", False, "", [True, True], False,
             [{"config_name": "inclusion_config.club_with_room_rate_for_taxation", "config_value": "true",
               "value_type": "boolean"}]),
            ("AddMultipleGuest_97", 'Add guest having slab based taxation and clubbed tax',
             SINGLE_WALK_BOOKING_V2_01_CLUBBED_AND_SLAB_BASED, 201, None, "", "", "", "", False, "",
             [True, True], True, [{"config_name": "inclusion_config.club_with_room_rate_for_taxation",
                                   "config_value": "true", "value_type": "boolean"}]),
            ("AddMultipleGuest_98", 'Add guest after charge splits having slab based taxation and clubbed tax',
             BOOKING_WITH_CHARGE_SPLITS_CLUBBED_AND_SLAB_BASED, 201, None, "", "", "", "", False, "",
             [True, True], True, [{"config_name": "inclusion_config.club_with_room_rate_for_taxation",
                                   "config_value": "true", "value_type": "boolean"}]),
        ])
    @pytest.mark.regression
    def test_add_guest_after_charge_split(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                          user_type, error_code, error_message, dev_message, error_payload, skip_case,
                                          skip_message, extras, has_slab_based_taxation, is_tax_clubbed):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        enable_rate_plan, is_inclusion_added = extras[0], extras[1]

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=True))

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        set_inventory_count(500, hotel_id)

        response = self.booking_request.add_multiple_guest_stay(client_, test_case_id, status_code,
                                                                self.booking_request.booking_id, enable_rate_plan,
                                                                is_inclusion_added, user_type, has_slab_based_taxation,
                                                                hotel_id, is_tax_clubbed)

        if has_slab_based_taxation:
            query_execute(db_queries.UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION.format(
                has_slab_based_taxation=False))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request.booking_id, self.booking_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_id, booking_request):
        validation = ValidationAddMultipleGuestStay(client_, test_case_id, response, booking_id, booking_request)
        validation.validate_response()


class TestAddGuestWithCommission(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, extras", [
            ("AddMultipleGuest_99", 'Add a single guestStay to a room in single day single room ota booking',
             [{'id': 'booking_212', 'type': 'booking_v2'}], 201, None, "", "", "", "", False, "", [True, False]),
            ("AddMultipleGuest_100",
             'Add a single guestStay with inclusions to a room in single day single room ota booking',
             [{'id': 'booking_212', 'type': 'booking_v2'}], 201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_101", 'Add a single guestStay to a room in single day single room hotel walkin booking',
             [{'id': 'booking_01', 'type': 'booking_v2'}], 201, None, "", "", "", "", False, "", [True, False]),
            ("AddMultipleGuest_102", 'Add a single guestStay to a room in multiple days single room ota booking',
             [{'id': 'booking_233', 'type': 'booking_v2'}], 201, None, "", "", "", "", False, "", [True, False]),
            ("AddMultipleGuest_103", 'Add a single guestStay to a room in multiple days multiple rooms ota booking',
             [{'id': 'booking_234', 'type': 'booking_v2'}], 201, None, "", "", "", "", False, "", [True, False]),
            ("AddMultipleGuest_104", 'Add multiple guestStay to a room in single day single room ota booking',
             [{'id': 'booking_212', 'type': 'booking_v2'}], 201, None, "", "", "", "", False, "", [True, False]),
            ("AddMultipleGuest_105", 'Add multiple guestStay to a room in multiple days single room ota booking',
             [{'id': 'booking_233', 'type': 'booking_v2'}], 201, None, "", "", "", "", False, "", [True, False]),
            ("AddMultipleGuest_106", 'Add multiple guestStay to a room in multiple days multiple rooms ota booking',
             [{'id': 'booking_234', 'type': 'booking_v2'}], 201, None, "", "", "", "", False, "", [True, False]),
            ("AddMultipleGuest_107",
             'Add a single guestStay to a room in single day single room ota booking whose commission got updated',
             [{'id': 'booking_226', 'type': 'booking_v2'}, {'id': 'put_booking_105', 'type': 'put_booking_v2'}],
             201, None, "", "", "", "", False, "", [True, False]),
            ("AddMultipleGuest_108",
             'Add a single guestStay to a room in single day single room ota booking whose rate plan is updated',
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'UpdateRatePlan_99', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 201, None, "", "", "", "", False, "", [True, False]),
            ("AddMultipleGuest_109", 'Add a single guestStay in single day single room ota checked in booking',
             [{'id': 'booking_212', 'type': 'booking_v2'}, {'id': 'checkin_01', 'type': 'checkin_v2'}], 201, None, "",
             "", "", "", False, "", [True, False]),
            ("AddMultipleGuest_110",
             'Add a single guestStay to a room in single day single room ota booking whose room stay charge is updated',
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1}], 201, None, "", "", "", "", False, "",
             [True, False]),
            ("AddMultipleGuest_111", 'Add a single guestStay to a room in single day single room ota booking whose '
                                     'room stay charge and rate plan is updated',
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'EditCharge_75', 'type': 'update_expense', 'charge_id': 1},
              {'id': 'UpdateRatePlan_99', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 201, None, "", "", "", "", False, "", [True, False]),
            ("AddMultipleGuest_112",
             'Add a single guestStay to a room in single day single room ota booking having extra expense',
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'Multiple_Expense_104', 'type': 'create_expense_V3'}], 201, None, "", "", "", "",
             False, "", [True, False]),
            ("AddMultipleGuest_113", 'Add a single guestStay to a room in single day single room ota booking '
                                     'having extra expense and updated rate plan',
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'Multiple_Expense_104', 'type': 'create_expense_V3'},
              {'id': 'UpdateRatePlan_99', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 201, None, "", "", "", "", False, "", [True, False]),
            ("AddMultipleGuest_114", 'Add a single guestStay in single day single room ota reverse checked in booking',
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'checkin_01', 'type': 'checkin_v2'}, {'id': 'Delete_Checkin_01', 'type': 'delete_booking_action'}],
             201, None, "", "", "", "", False, "", [True, False]),
            ("AddMultipleGuest_115", 'Add a single guestStay in single day single room ota reverse cancelled booking',
             [{'id': 'booking_212', 'type': 'booking_v2'}, {'id': "MarkCancelled_95", 'type': 'mark_cancelled'},
              {'id': 'Delete_Cancel_01', 'type': 'reverse_cancel_action'}], 201, None, "", "", "", "", False, "",
             [True, False]),
            ("AddMultipleGuest_116",
             'Add a single guestStay to a room in single day single room multiple guest ota booking',
             [{'id': 'booking_23_update_stay', 'type': 'booking_v2'}], 201, None, "", "", "", "",
             False, "", [True, False]),
            ("AddMultipleGuest_117",
             'Add a single guestStay to a room in multiple days single room multiple guest ota booking',
             [{'id': 'booking_24_ota', 'type': 'booking_v2'}], 201, None, "", "", "", "", False, "", [True, False]),
            ("AddMultipleGuest_118",
             'Add a single guestStay to a room in single day single room ota booking in which stay duration is updated',
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'UpdateStayDuration_129', 'type': 'update_stay_duration', 'enable_rate_manager': True,
               'is_inclusion_added': False}], 201, None, "", "", "", "", False, "", [True, False]),
            ("AddMultipleGuest_140", 'Edit commission then add a guest in a single room ota booking',
             [{'id': 'booking_212', 'type': 'booking_v2'},
              {'id': 'update_ta_commission_01', 'type': 'update_ta_commission', 'commission_value': 10.0}],
             201, None, "", "", "", "", False, "", [True, False]),
        ])
    @pytest.mark.regression
    def test_add_guest_with_commission(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                       user_type, error_code, error_message, dev_message, error_payload, skip_case,
                                       skip_message, extras):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        enable_rate_plan, is_inclusion_added = extras[0], extras[1]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.add_multiple_guest_stay(client_, test_case_id, status_code,
                                                                self.booking_request.booking_id, enable_rate_plan,
                                                                is_inclusion_added)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request.booking_id, self.booking_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_id, booking_request):
        validation = ValidationAddMultipleGuestStay(client_, test_case_id, response, booking_id, booking_request)
        validation.validate_response()
        validation.validate_commissions()


class TestAddGuestWithNewTaxCalc(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, extras", [
            ("AddMultipleGuest_119", 'Add a single guest in a booking where sez and false for company',
             [{'id': 'booking_with_lut_04', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_120", 'Add a single guest in a booking where sez is true and lut is false for company',
             [{'id': 'booking_with_lut_02', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_121", 'Add a single guest in a booking where sez is false and lut is true for company',
             [{'id': 'booking_with_lut_03', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_122", 'Add a single guest in a booking where sez and lut is true for company',
             [{'id': 'booking_with_lut_01', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_123", 'Add a single guest in a booking where sez and false for ta',
             [{'id': 'booking_with_lut_04_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_124", 'Add a single guest in a booking where sez is true and lut is false for ta',
             [{'id': 'booking_with_lut_02_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_125", 'Add a single guest in a booking where sez is false and lut is true for ta',
             [{'id': 'booking_with_lut_03_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_126", 'Add a single guest in a booking where sez and lut is true for ta',
             [{'id': 'booking_with_lut_01_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_127", 'Add a single guest in a booking where room charges are billed to primary guest',
             [{'id': 'booking_01_with_pg_BE', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_128",
             'Add a single guest in a booking with company and travel agent where charges are billed to company',
             [{'id': 'booking_with_lut_02_company_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_129",
             'Add a single guest in a booking with company and travel agent where charges are billed to ta',
             [{'id': 'booking_with_lut_02_ta_company', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_130", 'Add multiple guests in a booking where sez is true and lut is false for company',
             [{'id': 'booking_with_lut_02', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_131", 'Add multiple guests in a booking where sez is true and lut is false for ta',
             [{'id': 'booking_with_lut_02_ta', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_132", 'Add multiple guests in a booking where charges are billed to primary guest',
             [{'id': 'booking_01_with_pg_BE', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_133",
             'Add a single guest in a multiple days booking where sez is true and lut is false for company',
             [{'id': 'booking_with_lut_02_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_134",
             'Add a single guest in a multiple days booking where sez is true and lut is false for ta',
             [{'id': 'booking_with_lut_02_ta_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
            ("AddMultipleGuest_135",
             'Add a single guest to a room in a multiple days booking where charges are billed to primary guest',
             [{'id': 'booking_01_with_pg_BE_multiple_days', 'type': 'booking_v2', 'new_tax_mocker': True}],
             201, None, "", "", "", "", False, "", [True, True]),
        ])
    @pytest.mark.regression
    def test_add_guest_with_new_tax_calc(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                         user_type, error_code, error_message, dev_message, error_payload, skip_case,
                                         skip_message, extras):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        enable_rate_plan, is_inclusion_added = extras[0], extras[1]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.booking_request.add_multiple_guest_stay(client_, test_case_id, status_code,
                                                                self.booking_request.booking_id, enable_rate_plan,
                                                                is_inclusion_added)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.booking_request.booking_id, self.booking_request,
                            self.billing_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, booking_id, booking_request, billing_request):
        validation = ValidationAddMultipleGuestStay(client_, test_case_id, response, booking_id, booking_request)
        validation.validate_response()
        validation.validate_commissions()
        validation.validate_charges(billing_request)
