import json

from prometheus.integration_tests.config import sheet_names, common_config
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_blank, increment_date, \
    sanitize_test_data, return_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationAddMultipleGuestStay(BaseValidations):
    def __init__(self, client_, test_case_id, response, booking_id, booking_request):
        self.test_data = get_test_case_data(sheet_names.add_guest_stay_sheet_name_api, test_case_id)
        self.response = response
        self.client = client_
        self.booking_id = booking_id
        self.booking_request = booking_request

    def validate_response(self):
        for actual_guest_data, expected_guest_data in zip(self.response['data']['guest_stays'], self.test_data):
            if sanitize_blank(expected_guest_data["age_group"]):
                assert_(actual_guest_data["age_group"], expected_guest_data["age_group"])
            if sanitize_blank(expected_guest_data["expected_checkin_date"]):
                expected_check_in_date = increment_date(int(expected_guest_data['expected_checkin_date']),
                                                        common_config.CHECK_IN_TIME_ZONE)
                assert_(actual_guest_data["checkin_date"], expected_check_in_date)
            if sanitize_blank(expected_guest_data["expected_checkout_date"]):
                expected_checkout_date = increment_date(int(expected_guest_data['expected_checkout_date']),
                                                        common_config.CHECKOUT_TIME_ZONE)
                assert_(actual_guest_data["checkout_date"], expected_checkout_date)
            if sanitize_blank(actual_guest_data["guest_allocation"]):
                assert_(actual_guest_data["guest_allocation"]["guest_allocation_id"], 1)
                if sanitize_blank(expected_guest_data["guest_id"]):
                    assert_(actual_guest_data["guest_allocation"]["guest_id"], expected_guest_data["guest_id"])
            if sanitize_blank(expected_guest_data["guest_stay_id"]):
                assert_(str(actual_guest_data["guest_stay_id"]), expected_guest_data["guest_stay_id"])
            if sanitize_blank(expected_guest_data["expected_status"]):
                assert_(actual_guest_data["status"], expected_guest_data["expected_status"])
            if sanitize_blank(expected_guest_data["guest_allowedActions"]):
                assert_(actual_guest_data["allowed_actions"].sort(),
                        expected_guest_data["guest_allowedActions"].split(",").sort())

        # validation on guest stays using get booking
        if self.test_data[0]['TC_ID'] not in ['AddMultipleGuest_20', 'AddMultipleGuest_23', 'AddMultipleGuest_103',
                                              'AddMultipleGuest_106']:
            guest_stay_response = \
                self.booking_request.get_booking_request_v2(self.client, self.booking_id, 200)['data']['room_stays'][
                    int(self.test_data[0]['room_stay_id']) - 1]['guest_stays'][-1]
            expected_guest_stay_data = self.test_data[int(guest_stay_response['guest_stay_id']) - 2] if len(
                self.test_data) != 1 else self.test_data[0]

            if sanitize_blank(expected_guest_stay_data["age_group"]):
                assert_(guest_stay_response["age_group"], expected_guest_stay_data["age_group"])
            if sanitize_blank(expected_guest_stay_data["expected_checkin_date"]):
                expected_check_in_date = increment_date(int(expected_guest_stay_data['expected_checkin_date']),
                                                        common_config.CHECK_IN_TIME_ZONE)
                assert_(guest_stay_response["checkin_date"], expected_check_in_date)
            if sanitize_blank(expected_guest_stay_data["expected_checkout_date"]):
                expected_checkout_date = increment_date(int(expected_guest_stay_data['expected_checkout_date']),
                                                        common_config.CHECKOUT_TIME_ZONE)
                assert_(guest_stay_response["checkout_date"], expected_checkout_date)
            if sanitize_blank(expected_guest_stay_data["guest_stay_id"]):
                assert_(str(guest_stay_response["guest_stay_id"]), expected_guest_stay_data["guest_stay_id"])
            if sanitize_blank(expected_guest_stay_data["expected_status"]):
                assert_(guest_stay_response["status"], expected_guest_stay_data["expected_status"])
            if sanitize_blank(expected_guest_stay_data["guest_allowedActions"]):
                assert_(guest_stay_response["allowed_actions"].sort(),
                        expected_guest_stay_data["guest_allowedActions"].split(",").sort())

    def validate_commissions(self):
        expected_data = self.test_data[0]
        if sanitize_test_data(expected_data['expected_commissions']):
            self.validate_ta_commissions(json.loads(expected_data['expected_commissions']))
        else:
            self.validate_ta_commissions()

    def validate_charges(self, billing_request):
        expected_data = self.test_data[0]['expected_charge_details']
        if expected_data:
            actual_charge_details = billing_request.get_bill_charges(self.client,
                                                                          self.booking_request.bill_id, 200)['data']
            expected_charge_details = json.loads(expected_data)
            sorted_actual_charge_details = sorted(actual_charge_details, key=lambda i: i['charge_id'])
            sorted_expected_charge_details = sorted(expected_charge_details, key=lambda i: i['charge_id'])
            for actual_charge_detail, expected_charge_detail in zip(sorted_actual_charge_details,
                                                                    sorted_expected_charge_details):
                actual_charge_detail['applicable_date'] = actual_charge_detail['applicable_date'].split('T')[0]
                expected_charge_detail['applicable_date'] = str(return_date(expected_charge_detail['applicable_date']))
                self.validate_charge(actual_charge_detail, expected_charge_detail)
