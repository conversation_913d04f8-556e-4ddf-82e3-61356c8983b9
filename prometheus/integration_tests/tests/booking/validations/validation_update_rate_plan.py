import json

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_test_data, return_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.builders import update_rate_plan_builder


class ValidationUpdateRatePlan(BaseValidations):
    def __init__(self, client_, test_case_id, response, booking_request):
        self.test_data = get_test_case_data(sheet_names.update_rate_plan_sheet_name, test_case_id)
        self.response = response
        self.client = client_
        self.booking_request = booking_request
        self.test_case_id = test_case_id

    def validate_response(self):
        room_stay_expected_response = self.booking_request.get_room_stay_details(self.client, self.test_case_id, 200,
                                                                                 sheet_names.
                                                                                 update_rate_plan_sheet_name)
        actual_response = self.response['data']
        expected_response = room_stay_expected_response['data']
        assert_(actual_response['actual_checkin_date'], expected_response['actual_checkin_date'])
        assert_(actual_response['actual_checkout_date'], expected_response['actual_checkout_date'])
        assert_(actual_response['allowed_actions'].sort(), expected_response['allowed_actions'].sort())
        assert_(actual_response['checkin_date'], expected_response['checkin_date'])
        assert_(actual_response['checkout_date'], expected_response['checkout_date'])
        assert_(actual_response['created_at'], expected_response['created_at'])
        assert_(actual_response['disallow_charge_addition'], expected_response['disallow_charge_addition'])

        for actual_date_wise_charge_ids, expected_date_wise_charge_ids in zip(
                actual_response['date_wise_charge_ids'], expected_response['date_wise_charge_ids']):
            assert_(actual_date_wise_charge_ids['charge_date'], expected_date_wise_charge_ids['charge_date'])
            assert_(actual_date_wise_charge_ids['charge_id'], expected_date_wise_charge_ids['charge_id'])

        for actual_guest_data, expected_guest_data in zip(actual_response['guest_stays'],
                                                          expected_response['guest_stays']):
            self.validate_guest_stay(actual_guest_data, expected_guest_data)

        assert_(actual_response['is_overflow'], expected_response['is_overflow'])
        assert_(actual_response['room_allocation'], expected_response['room_allocation'])

        for actual_room_rents, expected_room_rents in zip(actual_response['room_rents'],
                                                          expected_response['room_rents']):
            self.validate_room_stay_price_data(actual_room_rents, expected_room_rents)

        assert_(actual_response['room_stay_id'], expected_response['room_stay_id'])
        assert_(actual_response['room_type_id'], expected_response['room_type_id'])
        assert_(actual_response['status'], expected_response['status'])
        assert_(actual_response['stay_end'], expected_response['stay_end'])
        assert_(actual_response['stay_start'], expected_response['stay_start'])
        assert_(actual_response['type'], expected_response['type'])

    @staticmethod
    def validate_guest_stay(actual_guest_data, expected_guest_data):
        assert_(actual_guest_data['actual_checkin_date'], expected_guest_data['actual_checkin_date'])
        assert_(actual_guest_data['actual_checkout_date'], expected_guest_data['actual_checkout_date'])
        assert_(actual_guest_data['created_at'], expected_guest_data['created_at'])
        assert_(actual_guest_data["age_group"], expected_guest_data["age_group"])
        assert_(actual_guest_data['checkin_date'], expected_guest_data['checkin_date'])
        assert_(actual_guest_data["stay_start"], expected_guest_data['stay_start'])
        assert_(actual_guest_data["checkout_date"], expected_guest_data['checkout_date'])
        assert_(actual_guest_data['stay_end'], expected_guest_data['stay_end'])
        if sanitize_test_data(actual_guest_data["guest_allocation"]):
            assert_(actual_guest_data["guest_allocation"]["guest_allocation_id"],
                    expected_guest_data['guest_allocation']['guest_allocation_id'])
            assert_(actual_guest_data["guest_allocation"]["guest_id"],
                    expected_guest_data['guest_allocation']["guest_id"])
        assert_(actual_guest_data["guest_stay_id"], expected_guest_data["guest_stay_id"])
        assert_(actual_guest_data["status"], expected_guest_data["status"])
        assert_(actual_guest_data["allowed_actions"].sort(), expected_guest_data["allowed_actions"].sort())

    @staticmethod
    def validate_room_stay_price_data(actual_price_data, expected_price_data):
        assert_(actual_price_data["applicable_date"], expected_price_data['applicable_date'])
        assert_(actual_price_data['posttax_amount'], expected_price_data['posttax_amount'])

    def validate_charge_and_expense_status(self, expense_request, billing_request, bill_id, booking_id):
        expenses_response = expense_request.get_expense_detail(self.client, 200, booking_id)['data']
        for expense in expenses_response:
            get_charge_detail = billing_request.get_charge_request(self.client, bill_id, 200,
                                                                   expense['charge_id'])['data']
            if expense['status'] == 'cancelled' or get_charge_detail['status'] == 'cancelled':
                assert_(expense['status'], get_charge_detail['status'])

    def validate_commissions(self):
        expected_data = self.test_data[0]
        if sanitize_test_data(expected_data['expected_commissions']):
            self.validate_ta_commissions(json.loads(expected_data['expected_commissions']))
        else:
            self.validate_ta_commissions()

    def validate_charges(self, billing_request):
        expected_data = self.test_data[0]['expected_charge_details']
        if expected_data:
            actual_charge_details = billing_request.get_bill_charges(self.client,
                                                                          self.booking_request.bill_id, 200)['data']
            expected_charge_details = json.loads(expected_data)
            sorted_actual_charge_details = sorted(actual_charge_details, key=lambda i: i['charge_id'])
            sorted_expected_charge_details = sorted(expected_charge_details, key=lambda i: i['charge_id'])
            for actual_charge_detail, expected_charge_detail in zip(sorted_actual_charge_details,
                                                                    sorted_expected_charge_details):
                actual_charge_detail['applicable_date'] = actual_charge_detail['applicable_date'].split('T')[0]
                expected_charge_detail['applicable_date'] = str(return_date(expected_charge_detail['applicable_date']))
                self.validate_charge(actual_charge_detail, expected_charge_detail)
