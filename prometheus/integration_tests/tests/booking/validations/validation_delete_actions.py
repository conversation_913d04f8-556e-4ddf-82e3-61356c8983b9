import json

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities.common_utils import (
    assert_,
    query_execute,
    return_date,
    sanitize_test_data,
)
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from ths_common.utils.common_utils import flatten_list


class ValidationDeleteActions(BaseValidations):
    def __init__(
        self,
        client,
        response,
        test_case_id,
        booking_request,
        booking_id,
        billing_request,
        bill_id,
        expected_bill_response,
    ):
        self.client = client
        self.test_case_id = test_case_id
        self.response = response
        self.booking_request = booking_request
        self.booking_id = booking_id
        self.bill_id = bill_id
        self.billing_request = billing_request
        self.expected_bill_response = expected_bill_response

    def validate_response(self):
        test_data = get_test_case_data(
            sheet_names.delete_action_sheet_name, self.test_case_id
        )[0]
        billing_response = self.billing_request.get_bill_request_v2(
            self.client, self.bill_id, 200
        )
        room_stay_count = 0
        for bill_data in billing_response['data']['charges']:
            if (
                bill_data['item']['name'] == 'RoomStay'
                and bill_data['item']['sku_category_id'] == 'stay'
            ):
                room_stay_count += 1
            if bill_data['item']['name'] == 'Charges: No Show':
                assert_(bill_data['status'], 'cancelled')
            elif bill_data['item']['name'] == 'Charges: Booking Cancellation':
                if bill_data['charge_splits'][0]['allowances']:
                    assert_(bill_data['status'], 'consumed')
                else:
                    assert_(bill_data['status'], 'cancelled')
            else:
                assert_(bill_data['status'], 'created')
        expense_response = self.booking_request.get_all_expense_by_booking_id(
            self.client, self.booking_id, 200
        )
        for expense_data in expense_response['data']:
            if expense_data['expense_item_id'] == 'no_show':
                assert_(expense_data['status'], 'cancelled')
            elif (
                expense_data['expense_item_id'] == 'booking_cancellation'
                and self.test_case_id == 'Delete_Cancel_14'
            ):
                assert_(expense_data['status'], 'cancelled')
            elif expense_data['expense_item_id'] == 'booking_cancellation':
                assert_(expense_data['status'], 'consumed')
            else:
                assert_(expense_data['status'], 'created')
        for ecd in self.expected_bill_response['data']['charges']:
            for acd in billing_response['data']['charges']:
                if acd['charge_id'] == ecd['charge_id']:
                    assert_(acd['posttax_amount'], ecd['posttax_amount'])
                    assert_(acd['pretax_amount'], ecd['pretax_amount'])
                    assert_(acd['tax_amount'], ecd['tax_amount'])
        if sanitize_test_data(test_data['expected_billed_entity_response']):
            actual_billed_entity_response = (
                self.billing_request.get_bill_entity_request(
                    self.client, self.bill_id, 200
                )
            )
            expected_billed_entity_response = json.loads(
                test_data['expected_billed_entity_response']
            )
            actual_billed_entity_response_sorted = sorted(
                actual_billed_entity_response['data'],
                key=lambda i: i['billed_entity_id'],
            )
            expected_billed_entity_response_sorted = sorted(
                expected_billed_entity_response, key=lambda i: i['billed_entity_id']
            )
            self.validate_billed_entity(
                actual_billed_entity_response_sorted,
                expected_billed_entity_response_sorted,
            )

    def validate_checkout_response(self):
        test_data = get_test_case_data(
            sheet_names.delete_action_sheet_name, self.test_case_id
        )[0]
        billing_response = self.billing_request.get_bill_request_v2(
            self.client, self.bill_id, 200
        )
        room_stay_count = 0
        for bill_data in billing_response['data']['charges']:
            allowance_not_generated = True
            if (
                bill_data['item']['name'] == 'RoomStay'
                and bill_data['item']['sku_category_id'] == 'stay'
            ):
                for charge_split in bill_data['charge_splits']:
                    for allowance in charge_split['allowances']:
                        if allowance['credit_note_id'] is not None:
                            allowance_not_generated = False
                            break
                if allowance_not_generated:
                    room_stay_count += 1
        charge_id_map = flatten_list(
            query_execute(db_queries.GET_CHARGE_ID_MAP).fetchall()
        )
        charge_id_map_count = 0
        for ci in charge_id_map:
            charge_id_map_count += len(ci)
        assert_(charge_id_map_count, room_stay_count)
        if self.test_case_id != 'Delete_Checkout_12':
            for ecd in self.expected_bill_response['data']['charges']:
                for acd in billing_response['data']['charges']:
                    if acd['charge_id'] == ecd['charge_id']:
                        (
                            assert_(acd['status'], 'created')
                            if ecd['status'] == 'cancelled'
                            else assert_(acd['status'], ecd['status'])
                        )
                        assert_(acd['posttax_amount'], ecd['posttax_amount'])
                        assert_(acd['pretax_amount'], ecd['pretax_amount'])
                        assert_(acd['tax_amount'], ecd['tax_amount'])
        if (
            test_data['expected_charge_details']
            and self.test_case_id != 'Delete_Checkout_07'
        ):
            expected_charge_details = json.loads(test_data['expected_charge_details'])
            sorted_expected_charge_details = sorted(
                expected_charge_details, key=lambda i: i['charge_id']
            )
            sorted_actual_charge_details = sorted(
                billing_response['data']['charges'], key=lambda i: i['charge_id']
            )
            for actual_charge_detail, expected_charge_detail in zip(
                sorted_actual_charge_details, sorted_expected_charge_details
            ):
                actual_charge_detail['applicable_date'] = actual_charge_detail[
                    'applicable_date'
                ].split('T')[0]
                expected_charge_detail['applicable_date'] = str(
                    return_date(expected_charge_detail['applicable_date'])
                )
                self.validate_charge(actual_charge_detail, expected_charge_detail)

    def validate_commissions(self):
        expected_test_data = get_test_case_data(
            sheet_names.delete_action_sheet_name, self.test_case_id
        )[0]
        if sanitize_test_data(expected_test_data['expected_commissions']):
            self.validate_ta_commissions(
                json.loads(expected_test_data['expected_commissions'])
            )
        else:
            self.validate_ta_commissions()

    def validate_charge_and_expense_status(self, expense_request):
        expenses_response = expense_request.get_expense_detail(
            self.client, 200, self.booking_id
        )['data']
        for expense in expenses_response:
            get_charge_detail = self.billing_request.get_charge_request(
                self.client, self.bill_id, 200, expense['charge_id']
            )['data']
            if (
                expense['status'] == 'cancelled'
                or get_charge_detail['status'] == 'cancelled'
            ):
                assert_(expense['status'], get_charge_detail['status'])


class ValidationBeforeDeleteActions:
    def __init__(self, test_case_id):
        self.test_case_id = test_case_id

    def validate_data(self):
        test_data = get_test_case_data(
            sheet_names.delete_action_sheet_name, self.test_case_id
        )[0]
        charge_id_map = flatten_list(
            query_execute(db_queries.GET_CHARGE_ID_MAP).fetchall()
        )
        if self.test_case_id not in ['Delete_NoShow_04', 'Delete_NoShow_08']:
            assert len(charge_id_map) > 0
        booking_side_effects = flatten_list(
            query_execute(db_queries.BOOKING_SIDE_EFFECTS).fetchall()
        )
        expected_side_effects = json.loads(test_data['expected_side_effects'])
        assert_(
            sorted(expected_side_effects['expenses']),
            sorted(
                booking_side_effects[0]['bill']['grouped_cancelled_charges']['expenses']
            ),
        )
        assert_(
            expected_side_effects['room_stay_charges'],
            booking_side_effects[0]['bill']['grouped_cancelled_charges'][
                'room_stay_charges'
            ],
        )
