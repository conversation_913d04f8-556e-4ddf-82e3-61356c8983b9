from flask import json
from prometheus.integration_tests.config import sheet_names, common_config
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_blank, increment_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.requests.billing_requests import BillingRequests


class ValidationAddGuestStay:
    def __init__(self, client_, test_case_id, response, booking_id):
        self.test_data = get_test_case_data(sheet_names.add_guest_stay_sheet_name_api, test_case_id)[0]
        self.response = response
        self.client = client_
        self.booking_id = booking_id

    def validate_response(self):
        if sanitize_blank(self.test_data["age_group"]):
            assert_(self.response["data"]["age_group"], self.test_data["age_group"])
        if sanitize_blank(self.test_data["expected_checkin_date"]):
            expected_check_in_date = increment_date(int(self.test_data['expected_checkin_date']),
                                                    common_config.CHECK_IN_TIME_ZONE)
            assert_(self.response["data"]["checkin_date"], expected_check_in_date)
        if sanitize_blank(self.test_data["expected_checkout_date"]):
            expected_checkout_date = increment_date(int(self.test_data['expected_checkout_date']),
                                                    common_config.CHECKOUT_TIME_ZONE)
            assert_(self.response["data"]["checkout_date"], expected_checkout_date)
        if sanitize_blank(self.response["data"]["guest_allocation"]):
            assert_(self.response["data"]["guest_allocation"]["guest_allocation_id"], 1)
            if sanitize_blank(self.test_data["guest_id"]):
                assert_(self.response["data"]["guest_allocation"]["guest_id"], self.test_data["guest_id"])
        if sanitize_blank(self.test_data["guest_stay_id"]):
            assert_(str(self.response["data"]["guest_stay_id"]), self.test_data["guest_stay_id"])
        if sanitize_blank(self.test_data["expected_status"]):
            assert_(self.response["data"]["status"], self.test_data["expected_status"])
        if sanitize_blank(self.test_data["guest_allowedActions"]):
            assert_(self.response["data"]["allowed_actions"].sort(),
                    self.test_data["guest_allowedActions"].split(",").sort())
