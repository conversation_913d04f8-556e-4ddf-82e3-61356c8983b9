from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.config.common_config import ROOM_TYPE_MAP
from prometheus.integration_tests.utilities.common_utils import assert_, increment_date, return_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationUpdateRoomStay:

    def __init__(self, client_, test_case_id, response, hotel_id):
        self.test_data = get_test_case_data(sheet_names.update_room_stay_sheet_name, test_case_id)[0]
        self.test_case_id = test_case_id
        self.response = response
        self.client = client_
        self.hotel_id = hotel_id

    def validate_response(self, booking_request, booking_id):

        booking_response = booking_request.get_booking_request(client=self.client, booking_id=booking_id,
                                                               status_code=200)
        checkin_date = booking_response['data']['checkin_date'] if self.test_data['checkin_date'] is not None \
            else return_date(self.test_data['checkin_date'])
        checkout_date = booking_response['data']['checkout_date'] if self.test_data['checkout_date'] is not None \
            else return_date(self.test_data['checkout_date'])

        available_rooms = booking_request.get_available_rooms(hotel_id=self.hotel_id, client=self.client, from_date=str(
            checkin_date), to_date=str(checkout_date), room_type_id=ROOM_TYPE_MAP[
            self.test_data['room_type_id'].lower()], status_code=200, room_id_list_required=True)

        if self.test_data['new_checkin_date']:
            assert (booking_response['data']['checkin_date'], self.test_data['new_checkin_date'])

        if self.test_data['new_checkout_date']:
            assert (booking_response['data']['checkout_date'], self.test_data['new_checkout_date'])

        room_stays = booking_response['data']['room_stays']
        for per_room_stay in room_stays:
            if per_room_stay['room_stay_id'] == int(self.test_data['room_stay_id']):
                if per_room_stay['room_allocation']:
                    room_allocation = per_room_stay['room_allocation']
                    assert_(room_allocation['room_id'], int(self.test_data['room_id_roomAllocation']))
                    assert_(room_allocation['room_type_id'], ROOM_TYPE_MAP[self.test_data['room_type_id'].lower()])
                    assert_(room_allocation['checkin_date'].split('T')[0],
                            increment_date(self.test_data['expected_checkin_date']).split('T')[0])
                    assert room_allocation['room_id'] not in available_rooms

    def validate_disallow_charge_response(self, booking_request, booking_id):
        expected_data = get_test_case_data(sheet_names.update_room_stay_sheet_name, self.test_case_id)
        booking_response = booking_request.get_booking_request(client=self.client, booking_id=booking_id,
                                                               status_code=200)['data']
        for rs in booking_response['room_stays']:
            for ers in expected_data:
                if rs['room_stay_id'] == ers['room_stay_id']:
                    assert_(str(rs['disallow_charge_addition']),  ers['disallow_charge_addition'])
