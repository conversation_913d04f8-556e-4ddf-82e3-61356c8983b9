import ast
import json
import unittest

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities.common_utils import (
    assert_,
    query_execute,
    return_date,
    sanitize_test_data,
)
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationMarkCancelled(BaseValidations, unittest.TestCase):
    def __init__(self, client_, test_case_id, response, booking_request, billing_request, booking_id, bill_id, hotel_id,
                 user_type, expense_request):
        self.response = response
        self.client = client_
        self.booking_request = booking_request
        self.booking_id = booking_id
        self.test_case_id = test_case_id
        self.hotel_id = hotel_id
        self.billing_request = billing_request
        self.user_type = user_type
        self.bill_id = bill_id
        self.expense_request = expense_request

    def validate_response(self):
        response = self.response['data']
        self.test_data = get_test_case_data(sheet_names.mark_cancelled_sheet_name, self.test_case_id)[0]
        assert response['action_id'] is not None
        assert_(response['action_type'], sanitize_test_data(self.test_data['expected_action_type']))
        assert_(response['reversal'], sanitize_test_data(self.test_data['expected_reversal_action']))
        assert_(response['reversal_side_effects'], json.loads(self.test_data['expected_reversal_side_effects']))
        assert_(response['status'], sanitize_test_data(self.test_data['expected_status']))

    def validate_get_booking(self):
        get_booking_response = self.booking_request.get_booking_request_v2(self.client, self.booking_id, 200)
        response = get_booking_response['data']
        for room_stay in response['room_stays']:
            assert len(room_stay['date_wise_charge_ids']) > 0
        mark_cancel_data_from_sheet = get_test_case_data(sheet_names.mark_cancelled_sheet_name, self.test_case_id)[0]

        assert_(response['allowed_actions'],
                ast.literal_eval(mark_cancel_data_from_sheet['expected_allowed_actions']))
        if "cancel_booking" in self.test_case_id:
            response_cancel_date = response['cancellation_datetime'].split('T')[0]
            assert_(response_cancel_date, str(return_date(mark_cancel_data_from_sheet['cancellation_datetime'])))
            assert_(response['status'], 'cancelled')
            assert_(response['comments'], mark_cancel_data_from_sheet['expected_comment'])
            comment_on_relocated_booking = query_execute(db_queries.GET_COMMENT_FROM_BOOKING.format(
                booking_id=self.booking_id)).fetchall()
            if comment_on_relocated_booking:
                assert_(comment_on_relocated_booking[0][0], mark_cancel_data_from_sheet['expected_comment_on_relocated_booking'])
            for room_stay in response['room_stays']:
                assert_(room_stay['status'], 'cancelled')
                assert_(room_stay['allowed_actions'], [])
                for guest_stay in room_stay['guest_stays']:
                    assert_(guest_stay['status'], 'cancelled')
        else:
            assert response['cancellation_datetime'] is None
            room_stays_test_data = ast.literal_eval(mark_cancel_data_from_sheet['expected_room_and_guest_stay'])
            assert_(response['status'], sanitize_test_data(mark_cancel_data_from_sheet['status']))
            for room_stay in room_stays_test_data:
                for response_room_stay in response['room_stays']:
                    if room_stay['room_stay_id'] == response_room_stay['room_stay_id']:
                        assert_(response_room_stay['status'], sanitize_test_data(room_stay['status']))
                        for guest_stay_test_data in room_stay['guest_stay']:
                            for response_guest_stay in response_room_stay['guest_stays']:
                                if guest_stay_test_data['guest_stay_id'] == response_guest_stay['guest_stay_id']:
                                    assert_(response_guest_stay['status'],
                                            sanitize_test_data(guest_stay_test_data['status']))

        response_bill_summary = response['bill_summary']
        test_data_bill_summary = json.loads(mark_cancel_data_from_sheet['expected_bill_summary'])
        self.validate_bill_summary(response_bill_summary, test_data_bill_summary, self.hotel_id)

    def validate_get_bill(self):
        get_bill_response = self.billing_request.get_bill_request_v2(self.client, self.bill_id, 200, self.user_type)
        response_data = get_bill_response['data']
        actual_data = get_test_case_data(sheet_names.mark_cancelled_sheet_name, self.test_case_id)[0]
        expected_charge_details = sorted(json.loads(actual_data['expected_charge_details']),
                                         key=lambda i: i['charge_id'])
        actual_charge_details = sorted(response_data['charges'], key=lambda i: i['charge_id'])
        for expected_charge, actual_charge in zip(expected_charge_details, actual_charge_details):
            assert_(actual_charge['applicable_date'].split('T')[0],
                    str(return_date(expected_charge['applicable_date'])))
            assert_(actual_charge['charge_id'], expected_charge['charge_id'])
            self.assertCountEqual(expected_charge, actual_charge)
        assert_(response_data['parent_reference_number'], self.booking_id)
        if sanitize_test_data(actual_data['expected_payment_details']):
            actual_payments = sorted(response_data['payments'], key=lambda i: i['payment_id'])
            expected_payments = sorted(json.loads(actual_data['expected_payment_details']),
                                       key=lambda i: i['payment_id'])
            for actual_payment, expected_payment in zip(actual_payments, expected_payments):
                assert_(actual_payment['date_of_payment'].split('T')[0],
                        str(return_date(expected_payment['date_of_payment'])))
                assert_(actual_payment['payment_id'], expected_payment['payment_id'])
                assert_(actual_payment['payment_mode'], expected_payment['payment_mode'])
                assert_(actual_payment['payment_mode_sub_type'], expected_payment['payment_mode_sub_type'])
                if actual_payment['status'] and actual_payment['status'] == 'cancelled':
                    assert_(actual_payment['status'], expected_payment['status'])
                else:
                    assert_(actual_payment['status'], 'posted')
                assert_(actual_payment['amount'], expected_payment['amount'])
                if actual_payment['payment_mode_sub_type'] and actual_payment['payment_mode_sub_type'] == 'payout_link':
                    assert actual_payment['payout_details'] is not None
                actual_payment_splits = sorted(actual_payment['payment_splits'], key=lambda i: i['payment_split_id'])
                expected_payment_splits = sorted(expected_payment['payment_splits'], key=lambda i: i['payment_split_id'])
                for actual_payment_split, expected_payment_split in zip(actual_payment_splits, expected_payment_splits):
                    assert_(actual_payment_split['payment_mode'], expected_payment_split['payment_mode'])
                    assert_(actual_payment_split['amount'], expected_payment_split['amount'])
                    assert_(actual_payment_split['payment_split_id'], expected_payment_split['payment_split_id'])
                    assert_(actual_payment_split['payment_split_id'], expected_payment_split['payment_split_id'])
                    assert_(actual_payment_split['billed_entity_account']['account_number'],
                            expected_payment_split['billed_entity_account']['account_number'])
                    assert_(actual_payment_split['billed_entity_account']['billed_entity_id'],
                            expected_payment_split['billed_entity_account']['billed_entity_id'])
        else:
            assert len(response_data['payments']) is 0

        actual_bill_summary = response_data['summary']
        expected_bill_summary = json.loads(actual_data['expected_bill_summary'])
        self.validate_bill_summary(actual_bill_summary, expected_bill_summary, self.hotel_id)
        assert_(response_data['vendor_id'], self.hotel_id)

    def compare_get_booking_and_bill_summary(self):
        get_booking_request = self.booking_request.get_booking_request_v2(self.client, self.booking_id, 200,
                                                                          self.user_type)
        get_bill_request = self.billing_request.get_bill_request_v2(self.client, self.bill_id, 200, self.user_type)
        booking_bill_summary = get_booking_request['data']['bill_summary']
        bills_bill_summary = get_bill_request['data']['summary']
        self.validate_bill_summary(booking_bill_summary, bills_bill_summary, self.hotel_id)

    def validate_billed_entity_response(self, billing_request, bill_id):
        actual_billed_entity_response = billing_request.get_bill_entity_request(self.client, bill_id, 200)
        expected_billed_entity_response = json.loads(self.test_data['expected_billed_entity_response'])
        actual_billed_entity_response_sorted = sorted(actual_billed_entity_response['data'],
                                                      key=lambda i: i['billed_entity_id'])
        expected_billed_entity_response_sorted = sorted(expected_billed_entity_response,
                                                        key=lambda i: i['billed_entity_id'])
        self.validate_billed_entity(actual_billed_entity_response_sorted, expected_billed_entity_response_sorted)

    def validate_charge_and_expense_status(self):
        expenses_response = self.expense_request.get_expense_detail(self.client, 200, self.booking_id)['data']
        for expense in expenses_response:
            get_charge_detail = self.billing_request.get_charge_request(self.client, self.bill_id, 200,
                                                                        expense['charge_id'])['data']
            assert_(expense['status'], get_charge_detail['status'])

    def validate_commissions(self):
        expected_data = get_test_case_data(sheet_names.mark_cancelled_sheet_name, self.test_case_id)[0]
        if sanitize_test_data(expected_data['expected_commissions']):
            self.validate_ta_commissions(json.loads(expected_data['expected_commissions']))
        else:
            self.validate_ta_commissions()

    def validate_charges(self):
        expected_data = self.test_data['expected_charge_data']
        if expected_data:
            actual_charge_details = self.billing_request.get_bill_charges(self.client,
                                                                          self.booking_request.bill_id, 200)['data']
            expected_charge_details = json.loads(expected_data)
            sorted_actual_charge_details = sorted(actual_charge_details, key=lambda i: i['charge_id'])
            sorted_expected_charge_details = sorted(expected_charge_details, key=lambda i: i['charge_id'])
            for actual_charge_detail, expected_charge_detail in zip(sorted_actual_charge_details,
                                                                    sorted_expected_charge_details):
                actual_charge_detail['applicable_date'] = actual_charge_detail['applicable_date'].split('T')[0]
                expected_charge_detail['applicable_date'] = str(return_date(expected_charge_detail['applicable_date']))
                self.validate_charge(actual_charge_detail, expected_charge_detail)


    def validate_rewards_refund(self, booking_id):
        actual_data =  query_execute(db_queries.GET_FUNDING_STATUS.format(booking_id=booking_id)).fetchall()
        print(actual_data)
        assert actual_data[0][0] == 'refunded'
