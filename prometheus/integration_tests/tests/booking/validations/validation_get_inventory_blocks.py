from dateutil.parser import parse
from pos.integration_tests.config import common_config
from prometheus.integration_tests.utilities.common_utils import assert_, increment_date, sanitize_test_data
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.config import sheet_names


class ValidationGetInventoryBlockDetails:

    def __init__(self, test_case_id, response):
        self.test_case_data = get_test_case_data(sheet_names.eci_lco_inventory_blocks_sheet_name, test_case_id)[0]
        self.response = response["data"]

    def validate_response(self):
        assert len(self.response) == 1, f"Expected 1 inventory block, found {len(self.response)}"
        self.response = self.response[0]

        assert_(
            str(self.response["block_type"]),
            str(self.test_case_data["expected_block_type"]),
            "Block type mismatch"
        )

        assert_(
            str(self.response["start_date"]),
            str(parse(increment_date(
                int(self.test_case_data["expected_start_date"]),
                common_config.CHECK_IN_TIME_ZONE
            )).date()),
            "Start date mismatch"
        )

        assert_(
            str(self.response["end_date"]),
            str(parse(increment_date(
                int(self.test_case_data["expected_end_date"]),
                common_config.CHECKOUT_TIME_ZONE
            )).date()),
            "End date mismatch"
        )

        if (sanitize_test_data(self.test_case_data["expected_room_stay_id"]) and
                self.test_case_data["expected_room_stay_id"] != 'NULL'):
            assert_(
                int(self.response["room_stay_id"]),
                int(self.test_case_data["expected_room_stay_id"]),
                "Room stay ID mismatch"
            )

        assert_(
            str(self.response["room_type_id"]),
            str(self.test_case_data["expected_room_type_id"]),
            "Room type ID mismatch"
        )

        assert_(
            str(self.response["status"]),
            str(self.test_case_data["expected_status"]),
            "Status mismatch"
        )
