from flask import json
from prometheus.integration_tests.config import sheet_names, common_config
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_blank, increment_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.requests.billing_requests import BillingRequests


class ValidationGetHouseStatistics:
    def __init__(self, client_, response, test_case_id):
        self.test_data = get_test_case_data(sheet_names.house_statistics_validation_data, test_case_id)[0]
        self.response = response
        self.client = client_

    def validate_response(self):
        assert_(self.response['data']['arr'], str(self.test_data['arr']))
        assert_(self.response['data']['available_rooms'], int(self.test_data['available_rooms']))
        assert_(self.response['data']['day_of_arrival_cancellations'],
                int(self.test_data['day_of_arrival_cancellations']))
        assert_(self.response['data']['day_use_rooms'], int(self.test_data['day_use_rooms']))
        assert_(self.response['data']['dnr_rooms'], int(self.test_data['dnr_rooms']))
        assert_(self.response['data']['early_departures'], int(self.test_data['early_departures']))
        assert_(self.response['data']['eod_projected_rooms_available_for_sale'],
                int(self.test_data['eod_projected_rooms_available_for_sale']))
        assert_(self.response['data']['occupancy'], str(self.test_data['occupancy']))
        assert_(self.response['data']['occupied_rooms'], int(self.test_data['occupied_rooms']))
        assert_(self.response['data']['total_eod_projected_dnr_rooms'],
                int(self.test_data['total_eod_projected_dnr_rooms']))
        assert_(self.response['data']['total_room_revenue'], str(self.test_data['total_room_revenue']))
        assert_(self.response['data']['total_rooms'], int(self.test_data['total_rooms']))
        assert_(self.response['data']['total_rooms_and_guests_under_group_booking']['guests'],
                int(self.test_data['group_guest']))
        assert_(self.response['data']['total_rooms_and_guests_under_group_booking']['rooms'],
                int(self.test_data['group_room']))
        assert_(self.response['data']['total_rooms_and_guests_under_group_booking']['vip_guests'],
                int(self.test_data['group_vip_guest']))
        assert_(self.response['data']['total_rooms_and_guests_under_individual_booking']['guests'],
                int(self.test_data['individual_guests']))
        assert_(self.response['data']['total_rooms_and_guests_under_individual_booking']['rooms'],
                int(self.test_data['individual_room']))
        assert_(self.response['data']['total_rooms_and_guests_under_individual_booking']['vip_guests'],
                int(self.test_data['individual_vip_guests']))




