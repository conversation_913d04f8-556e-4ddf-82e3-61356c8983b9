import json

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities.common_utils import sanitize_test_data, assert_
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationUpdateTACommission(BaseValidations):
    def __init__(self, client_, test_case_id, response, booking_request):
        self.test_data = get_test_case_data(sheet_names.update_ta_commission, test_case_id)[0]
        self.response = response
        self.client = client_
        self.booking_request = booking_request

    def validate_response(self):
        booking_v2_response_data = \
            self.booking_request.get_booking_request_v2(self.client, self.booking_request.booking_id, 200)['data']
        response_data = self.response['data']
        assert_(float(response_data['ta_commission_amount']),
                float(booking_v2_response_data['ta_commission_amount']))

        if sanitize_test_data(response_data['travel_agent_details']['ta_commission_details']):
            actual_ta_commission_details = response_data['travel_agent_details']['ta_commission_details']
            expected_ta_commission_details = booking_v2_response_data['travel_agent_details']['ta_commission_details']

            assert_(actual_ta_commission_details['commission_type'], expected_ta_commission_details['commission_type'])
            assert_(float(actual_ta_commission_details['commission_value']),
                    float(expected_ta_commission_details['commission_value']))
            assert_(actual_ta_commission_details['post_commission_amount'],
                    expected_ta_commission_details['post_commission_amount'])

            # Validate commission_tax if present in expected data
            if 'commission_tax' in expected_ta_commission_details and expected_ta_commission_details['commission_tax']:
                actual_tax = actual_ta_commission_details.get('commission_tax', {})
                expected_tax = expected_ta_commission_details['commission_tax']

                for tax_field in ['tax', 'tcs', 'tds', 'rcm']:
                    if tax_field in expected_tax and expected_tax[tax_field] is not None:
                        actual_value = float(actual_tax.get(tax_field)) if actual_tax.get(tax_field) is not None else None
                        expected_value = float(expected_tax.get(tax_field)) if expected_tax.get(tax_field) is not None else None
                        assert_(actual_value, expected_value)

            # Validate recalculate_commission_on_booking_modification if present in expected data
            if 'recalculate_commission_on_booking_modification' in expected_ta_commission_details:
                assert_(actual_ta_commission_details.get('recalculate_commission_on_booking_modification'),
                        expected_ta_commission_details['recalculate_commission_on_booking_modification'])

    def validate_audit_trail(self):
        actual_audit_trail_response = \
            self.booking_request.get_booking_audit_trail(self.client, self.booking_request.booking_id, 200)
        actual_commission_audit = []
        for actual_audit_trail in actual_audit_trail_response['data']:
            if actual_audit_trail['audit_type'] == 'TA Commission Details Modified':
                actual_commission_audit.append(actual_audit_trail)
        expected_commission_audit = json.loads(self.test_data['expected_audit_trail'])
        for actual_audit_trail, expected_audit_trail in zip(actual_commission_audit, expected_commission_audit):
            assert_(actual_audit_trail['application'], expected_audit_trail['application'])
            assert_(actual_audit_trail['user'], expected_audit_trail['user'])
            assert_(actual_audit_trail['user_type'], expected_audit_trail['user_type'])
            assert_(actual_audit_trail['audit_type'], expected_audit_trail['audit_type'])

            for actual_domain_events, expected_domain_events in zip(
                actual_audit_trail['audit_payload']['domain_events'],
                    expected_audit_trail['audit_payload']['domain_events']):

                if actual_domain_events['event_type'] == "TA Commission Updated":
                    assert_(actual_domain_events['event_detail']['old_commission_amount'],
                            expected_domain_events['event_detail']['old_commission_amount'])
                    assert_(actual_domain_events['event_detail']['new_commission_amount'],
                            expected_domain_events['event_detail']['new_commission_amount'])

                elif actual_domain_events['event_type'] == "Booking Details Modified":
                    for commission_modified_details, expected_commission_modified_details in zip(
                            actual_domain_events['event_detail'], expected_domain_events['event_detail']):
                        assert_(commission_modified_details['attribute'],
                                expected_commission_modified_details['attribute'])
                        assert_(commission_modified_details['new_value'],
                                expected_commission_modified_details['new_value'])
                        assert_(commission_modified_details['old_value'],
                                expected_commission_modified_details['old_value'])

    def validate_commissions(self):
        expected_data = self.test_data
        if sanitize_test_data(expected_data['expected_commissions']):
            self.validate_ta_commissions(json.loads(expected_data['expected_commissions']))
        else:
            self.validate_ta_commissions()
