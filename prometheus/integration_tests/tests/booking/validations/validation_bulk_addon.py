from flask import json

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_blank, return_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class BulkAddOnValidations(object):
    def __init__(self, test_case_id, extra_data=None):
        test_data = get_test_case_data(sheet_names.bulk_addon_sheet_name, test_case_id)[0]
        self.add_ons_data = get_test_case_data(sheet_names.add_ons_sheet_name,
                                               test_data['Add_ons_ID'])
        self.extra_data = extra_data

    def validate_response(self, response, client, booking_request, booking_id, billing_request, bill_id):
        for expected_test_data, add_on_response in zip(self.add_ons_data, response['data']):

            if sanitize_blank(expected_test_data['added_by']) is None:
                assert_(add_on_response['added_by'], 'treebo')
            else:
                assert_(add_on_response['added_by'], expected_test_data['added_by'])
            assert add_on_response['addon_id'] is not None

            if expected_test_data['linked_addon']:  # In case of linked add-on below fields will be None
                assert_(add_on_response['bill_to_type'], None)
                assert_(add_on_response['charge_type'], None)
            else:
                assert_(add_on_response['bill_to_type'], expected_test_data['bill_to_type'])
                assert_(add_on_response['charge_type'], expected_test_data['charge_type'])

            assert_(add_on_response['end_date'], str(return_date(expected_test_data['expected_end_date'])))
            assert_(add_on_response['end_relative'], sanitize_blank(expected_test_data['end_relative']))
            assert_(add_on_response['start_date'], str(return_date(expected_test_data['expected_start_date'])))
            assert_(add_on_response['start_relative'], sanitize_blank(expected_test_data['start_relative']))
            assert_(add_on_response['expense_item_id'], expected_test_data['expense_item_id'])
            assert_(add_on_response['name'], expected_test_data['name'])

            if sanitize_blank(expected_test_data['posttax_price']) is None:
                assert_(add_on_response['posttax_price'], expected_test_data['posttax_price'])
            else:
                assert_(add_on_response['posttax_price'], expected_test_data['expected_posttax_price'])
            if sanitize_blank(expected_test_data['pretax_price']) is None:
                assert_(add_on_response['pretax_price'], expected_test_data['pretax_price'])
            else:
                assert_(add_on_response['pretax_price'], expected_test_data['expected_pretax_price'])

            assert_(add_on_response['quantity'], int(expected_test_data['quantity']))
            assert_(add_on_response['room_stay_id'], int(expected_test_data['room_stay_id']))

            if expected_test_data['linked_addon']:
                assert_(add_on_response['expense_ids'], [])
                expected_charge_data = json.loads(expected_test_data['expected_charge_data'])
                for charge in expected_charge_data:
                    self.validate_charge(client, billing_request, bill_id, expected_test_data, charge)
            else:
                expected_expense_data = json.loads(expected_test_data['expected_expense_data'])
                assert_(add_on_response['expense_ids'], [str(id_['expense_id']) for id_ in expected_expense_data])
                for expense_data in expected_expense_data:
                    self.validate_expenses(expense_data['expense_id'], expected_test_data, client, booking_request,
                                           booking_id, expense_data['charge_id'])
                    self.validate_charge(client, billing_request, bill_id, expected_test_data, expense_data)
            assert_(add_on_response['version'], 1)

    def validate_expenses(self, expense_id, expected_data, client, booking_request, booking_id, charge_id):
        expense_response = booking_request.get_expense(client, booking_id, expense_id, 200)['data']
        assert_(expense_response['charge_id'], int(charge_id))
        assert_(expense_response['expense_item_id'], expected_data['expense_item_id'])
        assert_(expense_response['room_stay_id'], int(expected_data['room_stay_id']))
        assert_(expense_response['status'], 'created')
        assert_(expense_response['via_addon'], True)
        assert_(expense_response['comments'], expected_data['name'])

    def validate_charge(self, client, billing_request, bill_id, expected_data_excel, expected_charge_data):
        charge_response = billing_request.get_charge_request(client, bill_id, 200, expected_charge_data['charge_id'])[
            'data']
        assert_(charge_response['status'], expected_charge_data['status'])
        if charge_response['status'] == 'consumed':
            assert_(charge_response['charge_split_type'], 'equal_split')
        if sanitize_blank(expected_charge_data['assign_to']) is not None:
            assert_([x['charge_to'] for x in charge_response['charge_splits']].sort(),
                    expected_charge_data['assign_to'].sort())
        else:
            assert_(int(charge_response['charge_splits'][0]['percentage']), 100)
        # Charge component
        if expected_data_excel['linked_addon']:
            assert_(charge_response['charge_components'], expected_charge_data['charge_components'])
            assert_(charge_response['posttax_amount'], expected_charge_data['expected_posttax_price'])
            assert_(charge_response['pretax_amount'], expected_charge_data['expected_pretax_price'])
        else:
            assert_(charge_response['charge_components'], None)
            assert_(charge_response['posttax_amount'], expected_data_excel['expected_posttax_price'])
            assert_(charge_response['pretax_amount'], expected_data_excel['expected_pretax_price'])

        # Tax Amount
        assert_(charge_response['tax_amount'], expected_data_excel['expected_tax_amount'])

        if self.extra_data and 'include_kerala_cess' in self.extra_data and self.extra_data['include_kerala_cess']:
            assert_(list(charge_response['tax_details']), expected_data_excel['expected_tax_details'])
        else:
            assert_(str(charge_response['tax_details']), expected_data_excel['expected_tax_details'])
