from prometheus.integration_tests.utilities.common_utils import assert_


class GetAllAddOnValidations(object):
    def validate_response(self, get_addons_response, addon_ids, expected_addon_id):
        assert_(len(get_addons_response['data']), len(expected_addon_id))
        count = 0
        for addon in get_addons_response['data']:
            for expected_id in expected_addon_id:
                if addon['addon_id'] == addon_ids[expected_id]:
                    count = count + 1
                    break
        assert_(count, len(expected_addon_id))
