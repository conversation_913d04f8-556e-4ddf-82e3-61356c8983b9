from flask import json
from prometheus.integration_tests.config import sheet_names, common_config
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_test_data, increment_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationAddRoom:
    def __init__(self, client_, test_case_id, response, booking_id):
        self.test_data = get_test_case_data(sheet_names.add_room_stay_sheet_name, test_case_id)[0]
        self.client = client_
        self.response = response
        self.booking_id = booking_id

    def validate_response(self):
        if sanitize_test_data(self.test_data["expected_checkin_date"]):
            expected_check_in_date = increment_date(int(self.test_data['expected_checkin_date']),
                                                    common_config.CHECK_IN_TIME_ZONE)
            assert_(self.response["data"]["checkin_date"], expected_check_in_date)

        if sanitize_test_data(self.test_data["expected_checkout_date"]):
            expected_checkout_date = increment_date(int(self.test_data['expected_checkout_date']),
                                                    common_config.CHECKOUT_TIME_ZONE)
            assert_(self.response["data"]["checkout_date"], expected_checkout_date)

        if sanitize_test_data(self.test_data["room_type_id"]):
            assert_(str(self.response["data"]["room_type_id"]), ROOM_TYPE_MAP[self.test_data["room_type_id"].lower()])

    def validate_guest_stay(self):
        test_case_data = get_test_case_data(sheet_names.add_guest_stay_sheet_name, self.test_data['GuestStay'])[0]

        if sanitize_test_data(test_case_data["age_group"]):
            assert_(self.response["data"]["guest_stays"][0]["age_group"], test_case_data["age_group"])
        if sanitize_test_data(test_case_data["expected_checkin_date"]):
            expected_check_in_date = increment_date(int(test_case_data['expected_checkin_date']),
                                                    common_config.CHECK_IN_TIME_ZONE)
            assert_(self.response["data"]["guest_stays"][0]["checkin_date"], expected_check_in_date)
        if sanitize_test_data(test_case_data["expected_checkout_date"]):
            expected_checkout_date = increment_date(int(test_case_data['expected_checkout_date']),
                                                    common_config.CHECKOUT_TIME_ZONE)
            assert_(self.response["data"]["guest_stays"][0]["checkout_date"], expected_checkout_date)
        if sanitize_test_data(self.response["data"]["guest_stays"][0]["guest_allocation"]):
            assert_(self.response["data"]["guest_stays"][0]["guest_allocation"]["guest_allocation_id"], 1)
            # if sanitize_test_data(test_case_data["guest_id"]):
            #     assert_(self.response["data"]["guest_stays"][0]["guest_allocation"]["guest_id"],
            #             self.test_data["guest_id"])
        if sanitize_test_data(test_case_data["guest_stay_id"]):
            assert_(str(self.response["data"]["guest_stays"][0]["guest_stay_id"]), test_case_data["guest_stay_id"])
        if sanitize_test_data(test_case_data["expected_status"]):
            assert_(self.response["data"]["guest_stays"][0]["status"], test_case_data["expected_status"])
        if sanitize_test_data(test_case_data["expected_allowed_actions"]):
            assert_(self.response["data"]["guest_stays"][0]["allowed_actions"].sort(),
                    test_case_data["expected_allowed_actions"].split(",").sort())

    def validate_prices(self):
        test_case_data = get_test_case_data(sheet_names.add_prices_sheet_name, self.test_data['Prices'])[0]

        if sanitize_test_data(test_case_data["applicable_date"]):
            applicable_date = increment_date(int(test_case_data['applicable_date']), common_config.CHECK_IN_TIME_ZONE)
            assert_(self.response["data"]["checkin_date"], applicable_date)

        if sanitize_test_data(test_case_data["posttax_amount"]):
            assert_(self.response["data"]['total_posttax_amount'], test_case_data['total_posttax_amount'])
