import json
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import assert_
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.tests.base_validations import BaseValidations


class ValidationBulkEditGuestStay:

    def __init__(self, client_, test_case_id, response, booking_id):
        self.test_data = get_test_case_data(sheet_names.bulk_edit_guest_stay_sheet_name, test_case_id)[0]
        self.room_stay_data = []
        for room_data in json.loads(self.test_data['Room_Stay_Guest_Stay_Map']):
            guest_data = get_test_case_data(sheet_names.add_guest_stay_sheet_name_api, room_data['GuestData'])[0]
            guest_details_data = dict(get_test_case_data(sheet_names.customer_data_sheet_name, guest_data['guest_details'])[0])
            guest_details_data['room_stay_id'] = room_data['room_stay_id']
            guest_details_data['guest_stay_id'] = room_data['guest_stay_id']
            self.room_stay_data.append(guest_details_data)

        self.response = response
        self.client = client_
        self.booking_id = booking_id


    def validate_booking_response(self, booking_request):
        booking_response = booking_request.get_booking_request(client=self.client, booking_id=self.booking_id,
                                                               status_code=200)['data']
        for room_stay_test_data in self.room_stay_data:
            room_index = int(room_stay_test_data['room_stay_id'] - 1)
            guest_index = int(room_stay_test_data['guest_stay_id']) - 1
            assert_(booking_response['room_stays'][room_index]['room_stay_id'],
                    room_stay_test_data['room_stay_id'])
            assert_(booking_response['room_stays'][room_index]['guest_stays'][guest_index]['guest_stay_id'],
                    int(room_stay_test_data['guest_stay_id']))
            guest_id = booking_response['room_stays'][room_index]['guest_stays'][guest_index]['guest_allocation'][
                'guest_id']
            for customer_data in booking_response['customers']:
                if customer_data['customer_id'] == guest_id:
                    BaseValidations().validate_customer_details(customer_data, room_stay_test_data,False)
