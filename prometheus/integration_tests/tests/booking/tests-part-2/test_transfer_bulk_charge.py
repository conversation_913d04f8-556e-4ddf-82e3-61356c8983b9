import pytest, datetime
from datetime import date, timedelta
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.booking.validations.validation_transfer_charge import ValidationTransferCharge
from prometheus.integration_tests.utilities.common_utils import get_club_with_room_rate_for_taxation
from prometheus.integration_tests.utilities.common_utils import query_execute
from prometheus.integration_tests.resources.db_queries import *


class TestTransferBulkCharge(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, user_type, error_code, error_message,"
        "dev_message, error_payload, skip_case, previous_actions, charge_ids, expected_charge_ids,extras",
        [
            ("transfer_bulk_charge_24", "Transfer Multiple Day charges to a checked in booking", 200, "", "",
             "", "", "", "", TRANSFER_BULK_CHARGE_24, ["1", "2"], BULK_CHARGE_24, {'perform_night_audit': True}),
            ("transfer_bulk_charge_25", "Transfer Multiple Day charges to a checked in booking", 200, "", "",
             "", "", "", "", TRANSFER_BULK_CHARGE_24, ["1", "2"], BULK_CHARGE_25,
             {'perform_night_audit': True, 'inclusion_club_with_room_rate': True}),
            ("transfer_pos_charge_01", "Transfer a consumed non-credit charge as well as POS on checkedin booking", 200, "", "", "",
             "", "", "", WALK_IN_ONE_ROOM_BOOKING_CHECK_IN_V2 + [
                 {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '1'}] + SINGLE_BOOKING_CHECK_IN_V2_02,
             ["1", "2", "3", "4", "5", "6"], POS_CHARGE_01, {'pos_enabled': True}),
            ("transfer_bulk_charge_01", "Transfer a consumed non-credit charge on checkedin booking", 200, "", "", "",
             "", "", "", WALK_IN_ONE_ROOM_BOOKING_CHECK_IN_V2 + [
                 {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '1'}] + SINGLE_BOOKING_CHECK_IN_V2_02,
             ["1", "2", "3", "4", "5"], BULK_CHARGE_01, None),
            ("transfer_bulk_charge_02", "Transfer a consumed non-credit non linked charge on checkedin booking", 200,
             "", "", "", "", "", "", TRANSFER_BULK_CHARGE_02, ["6", "7"], BULK_CHARGE_02, None),
            ("transfer_bulk_charge_03", "Transfer non-credit created charge on checkedin booking", 400, "", "04010393",
             "", "", "", "", TRANSFER_BULK_CHARGE_03, ["1", "2"], None, None),
            ("transfer_bulk_charge_04", "Transfer non-credit created charge on Reserved  booking", 400, "", "",
             "", "", "", "", TRANSFER_BULK_CHARGE_03, ["2", "3"], None, None),
            ("transfer_bulk_charge_05", "Transfer non-linked as well as linked non credit consumed"
                                        " charge on Reserved booking", 200, "", "", "", "", "", "",
             POSTED_NON_LINKED_CHARGE_FOR_CHECKEDIN_BOOKING + [
                 {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '1'}] + SINGLE_WALK_BOOKING_V2_02,
             ["1", "6"], BULK_CHARGE_05, None),
            ("transfer_bulk_charge_06", "Transfer non-credit consumed charge on Reserved booking", 200, "", "",
             "", "", "", "", POSTED_NON_LINKED_CHARGE_FOR_CHECKEDIN_BOOKING + SINGLE_WALK_BOOKING_V2_02,
             ["6"], BULK_CHARGE_06, None),
            ("transfer_bulk_charge_07", "Transfer non credit non linked consumed charge on a Future Booking", 400, "",
             "04015970", "", "", "", "", POSTED_NON_LINKED_CHARGE_FOR_CHECKEDIN_BOOKING + FUTURE_WALKIN_BOOKING_V2_01,
             ["6"], None, None),
            ("transfer_bulk_charge_08", "Transfer non credit consumed charges from to check-out booking", 400, "",
             "04015945", "", "", "", "", POST_NON_LINKED_CHARGES_FOR_CHECKEDIN + FULL_CHECKOUT_02_V2,
             ["6", "7"], None, None),
            ("transfer_bulk_charge_09", "Transfer credit linked charges from company to Walkin Booking", 400, "",
             "04010331", "", "", "", "", POST_LINKED_CHARGES_FOR_2_ROOM_B2B_CHECKIN + SINGLE_WALK_BOOKING_V2_02,
             ["1", "2"], None, None),
            ("transfer_bulk_charge_10", "Transfer credit linked charges from company to Travel Agent Reserved Booking",
             200, "", "", "", "", "", "",
             POST_LINKED_CHARGES_FOR_2_ROOM_B2B_CHECKIN + TRAVEL_AGENT_SINGLE_ROOM_BOOKING_V2,
             ["1", "2"], BULK_CHARGE_10, None),
            ("transfer_bulk_charge_11", "Transfer cancelled change from B1 to B2", 400, "", "04010393", "", "", "",
             "", TRANSFER_BULK_CHARGE_11, ["1", "6", "7"], None, None),
            ("transfer_bulk_charge_12", "Transfer invoiced change from B1 to B2", 400, "", "04010393", "", "", "",
             "", PARTIAL_BOOKING_CHECKOUT_ROOM_V2_01 + [{'id': "booking_01_different_ref", 'type': 'booking_v2'}],
             ["1", "6", "7"], None, None),
            ("transfer_bulk_charge_13", "Transfer split non credit charges from Checked in Booking B1 to B2", 200, "",
             "", "", "", "", "", TRANSFER_BULK_CHARGE_13 + SINGLE_BOOKING_CHECK_IN_V2_02, ["6", "7"],
             BULK_CHARGE_13, None),
            ("transfer_bulk_charge_14", "Transfer split non credit non linked as well as linked charges from "
                                        "Checked in Booking B1 to B2", 200, "", "", "", "", "", "",
             TRANSFER_BULK_CHARGE_13 + [{'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '1'}] +
             SINGLE_BOOKING_CHECK_IN_V2_02, ["1", "7"], BULK_CHARGE_11, None),
            ("transfer_bulk_charge_15", "Transfer already transferred charges", 400, "", "04010431", "", "", "", "",
             WALK_IN_ONE_ROOM_BOOKING_CHECK_IN_V2 +
             [{'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '1'}] + SINGLE_BOOKING_CHECK_IN_V2_02 +
             [{'id': 'transfer_bulk_charge_01', 'type': 'transfer_bulk_charge', 'charge_ids': ["1"]}],
             ["1"], BULK_CHARGE_10, None),
            ("transfer_bulk_charge_16", "Transfer charge To checked in Booking after edit booking", 200, "", "",
             "", "", "", "", TRANSFER_BULK_CHARGE_16, ["6", "7", "8", "9", "10"], BULK_CHARGE_01_16, None),

            # --------------------------------- Inclusion Club With Room Rate ---------------------------------------
            ("transfer_bulk_charge_16", "Transfer a consumed non-credit charge on checkedin booking", 200, "", "", "",
             "", "", "", WALK_IN_ONE_ROOM_BOOKING_CHECK_IN_V2 + [
                 {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '1'}] + SINGLE_BOOKING_CHECK_IN_V2_02,
             ["1", "2", "3", "4", "5"], BULK_CHARGE_16, {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_17", "Transfer a consumed non-credit non linked charge on checkedin booking", 200,
             "", "", "", "", "", "", TRANSFER_BULK_CHARGE_02, ["6", "7"], BULK_CHARGE_17,
             {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_03", "Transfer non-credit created charge on checkedin booking", 400, "", "04010393",
             "", "", "", "", TRANSFER_BULK_CHARGE_03, ["1", "2"], None, {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_04", "Transfer non-credit created charge on Reserved  booking", 400, "", "",
             "", "", "", "", TRANSFER_BULK_CHARGE_03, ["2", "3"], None, {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_18", "Transfer non-linked as well as linked non credit consumed"
                                        " charge on Reserved booking", 200, "", "", "", "", "", "",
             POSTED_NON_LINKED_CHARGE_FOR_CHECKEDIN_BOOKING + [
                 {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '1'}] + SINGLE_WALK_BOOKING_V2_02,
             ["1", "6"], BULK_CHARGE_18, {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_19", "Transfer non-credit consumed charge on Reserved booking", 200, "", "",
             "", "", "", "", POSTED_NON_LINKED_CHARGE_FOR_CHECKEDIN_BOOKING + SINGLE_WALK_BOOKING_V2_02,
             ["6"], BULK_CHARGE_19, {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_07", "Transfer non credit non linked consumed charge on a Future Booking", 400, "",
             "04015970", "", "", "", "", POSTED_NON_LINKED_CHARGE_FOR_CHECKEDIN_BOOKING + FUTURE_WALKIN_BOOKING_V2_01,
             ["6"], None, {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_08", "Transfer non credit consumed charges from to check-out booking", 400, "",
             "04015945", "", "", "", "", POST_NON_LINKED_CHARGES_FOR_CHECKEDIN + FULL_CHECKOUT_02_V2,
             ["6", "7"], None, {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_09", "Transfer credit linked charges from company to Walkin Booking", 400, "",
             "04010331", "", "", "", "", POST_LINKED_CHARGES_FOR_2_ROOM_B2B_CHECKIN + SINGLE_WALK_BOOKING_V2_02,
             ["1", "2"], None, {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_20", "Transfer credit linked charges from company to Travel Agent Reserved Booking",
             200, "", "04010331", "", "", "", "",
             POST_LINKED_CHARGES_FOR_2_ROOM_B2B_CHECKIN + TRAVEL_AGENT_SINGLE_ROOM_BOOKING_V2,
             ["1", "2"], BULK_CHARGE_20, {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_11", "Transfer cancelled change from B1 to B2", 400, "", "04010393", "", "", "",
             "", TRANSFER_BULK_CHARGE_11, ["1", "6", "7"], None, {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_12", "Transfer invoiced change from B1 to B2", 400, "", "04010393", "", "", "",
             "", PARTIAL_BOOKING_CHECKOUT_ROOM_V2_01 + [{'id': "booking_01_different_ref", 'type': 'booking_v2'}],
             ["1", "6", "7"], None, {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_23", "Transfer split non credit charges from Checked in Booking B1 to B2", 200, "",
             "", "", "", "", "", TRANSFER_BULK_CHARGE_13 + SINGLE_BOOKING_CHECK_IN_V2_02, ["6", "7"],
             BULK_CHARGE_23, {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_15", "Transfer already transferred charges", 400, "", "04010431", "", "", "", "",
             WALK_IN_ONE_ROOM_BOOKING_CHECK_IN_V2 +
             [{'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '1'}] + SINGLE_BOOKING_CHECK_IN_V2_02 +
             [{'id': 'transfer_bulk_charge_01', 'type': 'transfer_bulk_charge', 'charge_ids': ["1"]}],
             ["1"], BULK_CHARGE_10, {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_26", "Transfer non credit linked charges from Company A to Company B which has only "
                                        "credit charges", 200, "", "", "", "", "", "", TRANSFER_BULK_CHARGE_25,
             ["1", "2"], BULK_CHARGE_26, {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_27", "Transfer non credit non linked charges from  Company A to Company B  which has"
                                        " only credit charges", 200, "", "", "", "", "", "", TRANSFER_BULK_CHARGE_26,
             ["10", "11"], BULK_CHARGE_27, {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_28", "Transfer consumed non credit charge to company booking whose billed entity"
                                        " is guest for 2 rooms", 200, "", "", "", "", "", "", TRANSFER_BULK_CHARGE_28,
             ["1", "2"], BULK_CHARGE_28, {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_29", "Transfer a consumed non credit charge to company booking whose default billed "
                                        "entity  is guest for 2 rooms and 1st room is in cancelled state", 200, "", "",
             "", "", "", "", TRANSFER_BULK_CHARGE_29, ["1", "2"], BULK_CHARGE_29,
             {'inclusion_club_with_room_rate': True}),
            ("transfer_bulk_charge_30", "Transfer a consumed non-credit charge on checkedin booking", 200, "", "", "",
             "", "", "", [{'id': "booking_212", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                          {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '1'}] +
             SINGLE_BOOKING_CHECK_IN_V2_02, ["1", "2", "3", "4", "5"], BULK_CHARGE_01, None),
            ("transfer_bulk_charge_31", "Transfer a consumed non-credit charge on checkedin booking", 200, "", "", "",
             "", "", "", SINGLE_BOOKING_CHECK_IN_V2_02 + [
                 {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '1'}] +
             [{'id': "booking_212", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}],
             ["1", "2", "3", "4", "5"], BULK_CHARGE_31, None),
        ])
    @pytest.mark.regression
    def test_transfer_charges(self, client_, test_case_id, tc_description, status_code, user_type, error_code,
                              error_message, dev_message, error_payload, skip_case, previous_actions, charge_ids,
                              expected_charge_ids, extras):
        if skip_case:
            pytest.skip("skipped")

        hotel_id = HOTEL_ID[0]
        self.booking_request.booking_ids.clear()
        self.booking_request.bill_ids.clear()
        self.booking_request.reference_numbers.clear()

        if extras and 'perform_night_audit' in extras:
            query_execute(UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id, extra=extras)

        tenant_config = []
        if extras and 'inclusion_club_with_room_rate' in extras:
            tenant_config.append(get_club_with_room_rate_for_taxation())

        if extras and 'pos_enabled' in extras:
            query_execute(POS_CHARGE.format(bill_id=self.booking_request.bill_ids[0], tax_details=POS_TAX_DETAILS,
                                            charge_item_detail=POS_CHARGE_ITEM_DETAIL,
                                            applicable_date=str(date.today()) + " 23:59:00+05:30",
                                            charge_to='{2}', posting_date=date.today(),
                                            applicable_business_date=date.today()))
            query_execute(POS_CHARGE_SPLIT.format(bill_id=self.booking_request.bill_ids[0],
                                                  tax_details=POS_TAX_DETAILS))
            query_execute(POS_EXPENSE.format(booking_id=self.booking_request.booking_ids[0],
                                             applicable_date=str(date.today()) + " 23:59:00+05:30",
                                             applicable_business_date=date.today()))
        details_of_charge_to_be_transferred = self.billing_request. \
            get_charges_request(client_, self.booking_request.bill_ids[0], 200)
        response = self.billing_request.transfer_bulk_charge_request(client_, test_case_id,
                                                                     self.booking_request.booking_ids,
                                                                     self.booking_request.bill_ids, charge_ids,
                                                                     status_code, user_type, tenant_config)

        if extras and 'perform_night_audit' in extras:
            query_execute(UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message,
                                                                   error_payload, test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, details_of_charge_to_be_transferred, self.billing_request,
                            self.booking_request.booking_ids, self.booking_request.bill_ids, charge_ids,
                            expected_charge_ids, self.booking_request.reference_numbers, test_case_id,
                            self.booking_request, extras)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client_, response, details_of_charge_to_be_transferred, billing_request, booking_ids, bill_ids,
                   charge_ids, expected_charge_ids, reference_number, test_case_id, booking_request, extras):
        validation = ValidationTransferCharge(client_, test_case_id, booking_request, extras)
        validation.validate_bulk_response(response, details_of_charge_to_be_transferred,
                                          billing_request, booking_ids, bill_ids, charge_ids, expected_charge_ids,
                                          reference_number)
        validation.validate_commissions()
