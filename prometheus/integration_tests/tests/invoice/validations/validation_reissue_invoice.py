import json
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.config.sheet_names import credit_note_line_item_sheet_name
from prometheus.integration_tests.utilities.common_utils import assert_, return_date, sanitize_blank
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from unittest.case import TestCase
from prometheus.integration_tests.config.common_config import *


class ValidationReIssueInvoice:
    def __init__(self, client_, test_case_id, response, bill_id, hotel_id):
        self.test_data = get_test_case_data(sheet_names.reissue_invoice_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client_
        self.bill_id = bill_id
        self.hotel_id = hotel_id

    def validate_response(self, invoice_id_bill_to_type_mapping, bill_request, user_type):
        expected_credit_notes = []
        credit_note_line_items = sanitize_blank(self.test_data['credit_note_line_items']).split(',')
        for credit_note_line_item in credit_note_line_items:
            credit_note_line_item_test_data = get_test_case_data(credit_note_line_item_sheet_name,
                                                                 test_case_id=credit_note_line_item)
            for per_credit_note_line_item in credit_note_line_item_test_data:
                fetched_per_credit_note_line_item = {}
                fetched_per_credit_note_line_item['applicable_date'] = str(
                    return_date(sanitize_blank(per_credit_note_line_item['applicable_date'])))
                fetched_per_credit_note_line_item['expected_amount_taxes'] = json.loads(
                    sanitize_blank(per_credit_note_line_item['expected_amount_taxes']))
                fetched_per_credit_note_line_item['invoice_id'] = invoice_id_bill_to_type_mapping[
                    per_credit_note_line_item['invoice_id']]
                fetched_per_credit_note_line_item['posttax_amount'] = per_credit_note_line_item['posttax_amount']
                fetched_per_credit_note_line_item['credit_note_line_item_id'] = per_credit_note_line_item[
                    'credit_note_line_item_id']
                fetched_per_credit_note_line_item['invoice_charge_id'] = per_credit_note_line_item['invoice_charge_id']
                expected_credit_notes.append(fetched_per_credit_note_line_item)
        response_credit_notes = self.response['data']['credit_notes']
        fetched_credit_note = []
        for credit_note in response_credit_notes:
            for response_credit_note_line_items in credit_note['credit_note_line_items']:
                charge_id = response_credit_note_line_items['charge_id']
                bill_charge_post_tax_price = bill_request.get_charge_request(self.client, self.bill_id, 200, charge_id,
                                                                             user_type=user_type)['data'][
                    'posttax_amount']
                if self.hotel_id != HOTEL_ID[0]:
                    currency = HOTEL_CURRENCY_MAP[self.hotel_id]
                    assert_(bill_charge_post_tax_price.split(' ')[0], currency)
                    assert_('-' + response_credit_note_line_items['posttax_amount'].split(' ')[1],
                            bill_charge_post_tax_price.split(' ')[1])
                else:
                    assert_('-' + response_credit_note_line_items['posttax_amount'], bill_charge_post_tax_price)
                fetched_credit_notes = {}
                fetched_credit_notes['invoice_charge_id'] = str(response_credit_note_line_items['invoice_charge_id'])
                fetched_credit_notes['credit_note_line_item_id'] = str(
                    response_credit_note_line_items['credit_note_line_item_id'])
                fetched_credit_notes['applicable_date'] = response_credit_note_line_items['applicable_date'].split('T')[
                    0]
                fetched_credit_notes['invoice_id'] = response_credit_note_line_items['invoice_id']
                if self.hotel_id != HOTEL_ID[0]:
                    assert_(response_credit_note_line_items['posttax_amount'].split(' ')[0],
                            HOTEL_CURRENCY_MAP[self.hotel_id])
                    assert_(response_credit_note_line_items['pretax_amount'].split(' ')[0],
                            HOTEL_CURRENCY_MAP[self.hotel_id])
                    assert_(response_credit_note_line_items['tax_amount'].split(' ')[0],
                            HOTEL_CURRENCY_MAP[self.hotel_id])
                    fetched_credit_notes['posttax_amount'] = \
                        response_credit_note_line_items['posttax_amount'].split(' ')[1]
                    amount_dict = {
                        'posttax_amount': response_credit_note_line_items['posttax_amount'].split(' ')[1],
                        'pretax_amount': response_credit_note_line_items['pretax_amount'].split(' ')[1],
                        'tax_amount': response_credit_note_line_items['tax_amount'].split(' ')[1]
                    }
                else:
                    fetched_credit_notes['posttax_amount'] = response_credit_note_line_items['posttax_amount']
                    amount_dict = {
                        'posttax_amount': response_credit_note_line_items['posttax_amount'],
                        'pretax_amount': response_credit_note_line_items['pretax_amount'],
                        'tax_amount': response_credit_note_line_items['tax_amount']
                    }
                fetched_credit_notes['expected_amount_taxes'] = amount_dict
                fetched_credit_note.append(fetched_credit_notes)
        assert_(len(expected_credit_notes), len(fetched_credit_note))
        assert [i for i in expected_credit_notes if i not in fetched_credit_note] == []
        # TestCase.assertCountEqual(self, expected_credit_notes, fetched_credit_note)
        #################################### Invoices Assertion ########################################
        total_new_invoices = len(self.response['data']['invoices'])
        expected_total_new_invoices = self.test_data['expected_invoices']
        assert_(total_new_invoices, expected_total_new_invoices)
        fetched_new_invoices = []
        fetched_new_invoices_data = self.response['data']['invoices']
        for invoice_data in fetched_new_invoices_data:
            invoice_dict = {}
            if self.hotel_id != HOTEL_ID[0]:
                assert_(invoice_data['credit_note_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[self.hotel_id])
                assert_(invoice_data['credit_payable'].split(' ')[0], HOTEL_CURRENCY_MAP[self.hotel_id])
                assert_(invoice_data['posttax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[self.hotel_id])
                assert_(invoice_data['pretax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[self.hotel_id])
                assert_(invoice_data['tax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[self.hotel_id])
            if invoice_data['bill_to_type'] == 'guest':
                invoice_dict['customer_id'] = invoice_data['bill_to']['customer_id']
            invoice_dict['bill_to_type'] = invoice_data['bill_to_type']
            if self.hotel_id != HOTEL_ID[0]:
                invoice_dict['posttax_price'] = invoice_data['posttax_amount'].split(' ')[1]
            else:
                invoice_dict['posttax_price'] = invoice_data['posttax_amount']
            invoice_dict['status'] = invoice_data['status']
            invoice_dict['allowed_charge_type'] = invoice_data['allowed_charge_types'][0]
            fetched_new_invoices.append(invoice_dict)
        expected_invoices_data = json.loads(sanitize_blank(self.test_data['expected_invoice_data']))
        assert_(len(expected_invoices_data), len(fetched_new_invoices))
        assert [i for i in expected_invoices_data if i not in fetched_new_invoices] == []
        # TestCase.assertCountEqual(self, expected_invoices_data, fetched_new_invoices)
