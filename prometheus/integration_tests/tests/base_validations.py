import ast
import collections
import json

from prometheus.integration_tests.config import common_config, sheet_names
from datetime import datetime, date
from decimal import Decimal

from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.resources.db_queries import GET_INTEGRATION_EVENT_FOR_BOOKING, GET_TA_COMMISSION, \
    GET_TA_COMMISSION_PERCENTAGE
from prometheus.integration_tests.utilities.common_utils import (
    assert_,
    assert_amount_related_fields,
    increment_date,
    return_date,
    sanitize_test_data,
    assert_gstin_regex,
    query_execute,
    convert_to_decimal, query_execute_and_convert_to_json)
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class CustomEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return str(obj)
        return super().default(obj)


class BaseValidations(object):

    def validate_customer_details(self, actual_data, expected_data, customer_id_validation_required, test_case_id=None):
        if sanitize_test_data(actual_data['age']):
            assert_(str(actual_data['age']), expected_data['age'])
        if customer_id_validation_required:
            assert_(actual_data['customer_id'], expected_data['customer_id'])
        assert_(actual_data['date_of_birth'], sanitize_test_data(expected_data['date_of_birth']))
        if sanitize_test_data(expected_data['attachment_id']) and sanitize_test_data(expected_data['id_number']):
            assert_(actual_data['id_proof']['attachment_id'], expected_data['attachment_id'])
        if sanitize_test_data(actual_data['email']):
            assert_(actual_data['email'], expected_data['email'])
        assert_(actual_data['eregcard_status'], sanitize_test_data(expected_data['eregcard_status']))
        assert_(actual_data['eregcard_url'], None)
        assert_(actual_data['first_name'], expected_data['first_name'])
        assert_(actual_data['gender'], sanitize_test_data(expected_data['gender']))
        if sanitize_test_data(expected_data['image_url']):
            assert_(actual_data['image_url'], expected_data['image_url'])
        if sanitize_test_data(expected_data['is_primary']):
            assert_(actual_data['is_primary'], sanitize_test_data(expected_data['is_primary']))
        if sanitize_test_data(expected_data['last_name']):
            assert_(actual_data['last_name'], expected_data['last_name'])
        assert_(actual_data['nationality'], sanitize_test_data(expected_data['nationality']))
        assert_(actual_data['profile_type'], expected_data['profile_type'])
        assert_(actual_data['reference_id'], sanitize_test_data(expected_data['reference_id']))
        assert_(actual_data['user_profile_id'], sanitize_test_data(expected_data['user_profile_id']))
        assert_(actual_data['verifier_signature'], sanitize_test_data(expected_data['verifier_signature']))
        if sanitize_test_data(expected_data['company_name']):
            assert_(actual_data['employment_details']['company_name'], expected_data['company_name'])
            assert_(actual_data['employment_details']['is_destination_employed'],
                    sanitize_test_data(expected_data['is_destination_employed']))
        if sanitize_test_data(expected_data['city']):
            assert_(actual_data['address']['city'], expected_data['city'])
            assert_(actual_data['gst_details']['address']['city'], expected_data['city'])
        if sanitize_test_data(expected_data['country']):
            assert_(actual_data['address']['country'], expected_data['country'])
            assert_(actual_data['gst_details']['address']['country'], expected_data['country'])
        if sanitize_test_data(expected_data['field_1']):
            assert_(actual_data['address']['field_1'], expected_data['field_1'])
            assert_(actual_data['gst_details']['address']['field_1'], expected_data['field_1'])
        if sanitize_test_data(expected_data['field_2']):
            assert_(actual_data['address']['field_2'], expected_data['field_2'])
            assert_(actual_data['gst_details']['address']['field_2'], expected_data['field_2'])
        if sanitize_test_data(expected_data['pincode']):
            assert_(actual_data['address']['pincode'], expected_data['pincode'])
            assert_(actual_data['gst_details']['address']['pincode'], expected_data['pincode'])
        if sanitize_test_data(expected_data['state']):
            assert_(actual_data['address']['state'], expected_data['state'])
            assert_(actual_data['gst_details']['address']['state'], expected_data['state'])
        if sanitize_test_data(expected_data['gstin_num']):
            assert_(actual_data['gst_details']['gstin_num'], expected_data['gstin_num'])
        if sanitize_test_data(expected_data['has_lut']):
            assert_(actual_data['gst_details']['has_lut'], True)
        if sanitize_test_data(expected_data['is_sez']):
            assert_(actual_data['gst_details']['is_sez'], True)
        if sanitize_test_data(expected_data['legal_name']):
            assert_(actual_data['gst_details']['legal_name'], expected_data['legal_name'])
        if sanitize_test_data(expected_data['id_kyc_url']):
            assert_(actual_data['id_proof']['id_kyc_url'], expected_data['id_kyc_url'])
        if sanitize_test_data(expected_data['id_number']):
            assert_(actual_data['id_proof']['id_number'], expected_data['id_number'])
        if sanitize_test_data(expected_data['id_proof_country_code']):
            assert_(actual_data['id_proof']['id_proof_country_code'], expected_data['id_proof_country_code'])
        if sanitize_test_data(expected_data['id_proof_issued_date']):
            assert_(actual_data['id_proof']['issued_date'].split('T')[0],
                    increment_date(expected_data['id_proof_issued_date']).split('T')[0])
        if sanitize_test_data(expected_data['id_proof_issued_place']):
            assert_(actual_data['id_proof']['issued_place'], expected_data['id_proof_issued_place'])
        if sanitize_test_data(expected_data['id_proof_type']):
            assert_(actual_data['id_proof']['id_proof_type'], expected_data['id_proof_type'])
        if sanitize_test_data(expected_data['country_code']):
            assert_(actual_data['phone']['country_code'], expected_data['country_code'])
            assert_(actual_data['phone']['number'], expected_data['number'])
        if sanitize_test_data(expected_data['arrival_from']):
            assert_(actual_data['travel_details']['arrival_from'], expected_data['arrival_from'])
        if sanitize_test_data(expected_data['next_destination']):
            assert_(actual_data['travel_details']['next_destination'], expected_data['next_destination'])
        if sanitize_test_data(expected_data['visit_purpose']):
            assert_(actual_data['travel_details']['visit_purpose'], expected_data['visit_purpose'])
        if sanitize_test_data(expected_data['visa']):
            expected_visa_data = json.loads(expected_data['visa'])
            assert_(actual_data['travel_details']['visa']['registration_number'],
                    expected_visa_data['registration_number'])
            assert_(actual_data['travel_details']['visa']['issued_place'], expected_visa_data['issued_place'])
            assert_(actual_data['travel_details']['visa']['issued_date'],
                    increment_date(expected_visa_data['issued_date']))
            assert_(actual_data['travel_details']['visa']['destination_stay_duration'],
                    expected_visa_data['destination_stay_duration'])
            assert_(actual_data['travel_details']['visa']['destination_arrival_date'],
                    increment_date(expected_visa_data['destination_arrival_date']))
            if sanitize_test_data(expected_data['gstin_num']):
                gstin_num = actual_data['gst_details']['gstin_num']
                assert_gstin_regex(test_case_id, gstin_num)

    def validate_billed_entity(self, actual_billed_entity_response, expected_billed_entity_response,
                               hotel_id='0016932'):
        actual_folio_numbers, expected_folio_numbers = [], []
        for actual_response, expected_response in zip(actual_billed_entity_response, expected_billed_entity_response):
            assert_(actual_response['billed_entity_id'], expected_response['billed_entity_id'])
            assert_(actual_response['category'], expected_response['category'])
            assert_(actual_response['name']['first_name'], expected_response['name']['first_name'])
            assert_(actual_response['name']['full_name'], expected_response['name']['full_name'])
            assert_(actual_response['name']['last_name'], expected_response['name']['last_name'])
            assert_(actual_response['name']['middle_name'], expected_response['name']['middle_name'])
            assert_(actual_response['name']['salutation'], expected_response['name']['salutation'])
            assert_(actual_response['status'], expected_response['status'])
            actual_accounts_sorted = sorted(actual_response['accounts'], key=lambda i: i['account_number'])
            expected_accounts_sorted = sorted(expected_response['accounts'], key=lambda i: i['account_number'])
            for actual_account_response, expected_account_response in zip(actual_accounts_sorted,
                                                                          expected_accounts_sorted):
                assert_(actual_account_response['account_number'], expected_account_response['account_number'])
                assert_(actual_account_response['account_type'], expected_account_response['account_type'])
                if expected_account_response['folio_number']:
                    actual_folio_numbers.append(actual_account_response['folio_number'])
                    expected_folio_numbers.append(expected_account_response['folio_number'])
                assert_(actual_account_response['invoiced'], expected_account_response['invoiced'])
                assert_(actual_account_response['is_allowance_account'],
                        expected_account_response['is_allowance_account'])
                assert_(actual_account_response['locked'], expected_account_response['locked'])
                assert_amount_related_fields(actual_account_response['net_balance'],
                                             expected_account_response['net_balance'], hotel_id)
                assert_amount_related_fields(actual_account_response['total_credit_charge'],
                                             expected_account_response['total_credit_charge'], hotel_id)
                assert_amount_related_fields(actual_account_response['total_credit_charge_allowance'],
                                             expected_account_response['total_credit_charge_allowance'], hotel_id)
                assert_amount_related_fields(actual_account_response['total_non_credit_charge'],
                                             expected_account_response['total_non_credit_charge'], hotel_id)
                assert_amount_related_fields(actual_account_response['total_non_credit_charge_allowance'],
                                             expected_account_response['total_non_credit_charge_allowance'], hotel_id)
                assert_amount_related_fields(actual_account_response['total_payment'],
                                             expected_account_response['total_payment'], hotel_id)
                assert_amount_related_fields(actual_account_response['total_refund'],
                                             expected_account_response['total_refund'], hotel_id)
        assert_(sorted(actual_folio_numbers), sorted(expected_folio_numbers))

    def validate_company_details(self, actual_result, response_result):
        response_data_company_details = response_result['company_details']['legal_details']
        expected_company_details = actual_result['legal_details']
        assert_(response_data_company_details['legal_name'], sanitize_test_data(expected_company_details['legal_name']))
        if "client_internal_code" in expected_company_details.keys():
            assert_(response_data_company_details['client_internal_code'],
                    sanitize_test_data(expected_company_details['client_internal_code']))
        if "email" in actual_result['legal_details'].keys():
            assert_(response_data_company_details['email'], sanitize_test_data(expected_company_details['email']))
        if "external_reference_id" in expected_company_details.keys():
            assert_(response_data_company_details['external_reference_id'],
                    sanitize_test_data(expected_company_details['external_reference_id']))
        if "has_lut" in expected_company_details.keys():
            assert_(response_data_company_details['has_lut'], sanitize_test_data(expected_company_details['has_lut']))
        if "is_sez" in expected_company_details.keys():
            assert_(response_data_company_details['is_sez'], sanitize_test_data(expected_company_details['is_sez']))
        if "tin" in expected_company_details.keys():
            assert_(response_data_company_details['tin'], sanitize_test_data(expected_company_details['tin']))
        if "address" in expected_company_details.keys():
            assert_(response_data_company_details['address']['city'],
                    sanitize_test_data(expected_company_details['address']['city']))
            assert_(response_data_company_details['address']['country'],
                    sanitize_test_data(expected_company_details['address']['country']))
            assert_(response_data_company_details['address']['field_1'],
                    sanitize_test_data(expected_company_details['address']['field_1']))
            assert_(response_data_company_details['address']['field_2'],
                    sanitize_test_data(expected_company_details['address']['field_2']))
            assert_(response_data_company_details['address']['pincode'],
                    sanitize_test_data(expected_company_details['address']['pincode']))
            assert_(response_data_company_details['address']['state'],
                    sanitize_test_data(expected_company_details['address']['state']))
        if "phone" in expected_company_details.keys():
            assert_(response_data_company_details['number'],
                    sanitize_test_data(expected_company_details['phone']['number']))
            if "country_code" in expected_company_details['country_code'].keys():
                assert_(response_data_company_details['country_code'],
                        sanitize_test_data(expected_company_details['phone']['country_code']))

    def validate_travel_agent_details(self, actual_result, response_result):
        response_data_company_details = response_result['travel_agent_details']['legal_details']
        expected_company_details = actual_result['legal_details']
        assert_(response_data_company_details['legal_name'], sanitize_test_data(expected_company_details['legal_name']))
        if "client_internal_code" in expected_company_details.keys():
            assert_(response_data_company_details['client_internal_code'],
                    sanitize_test_data(expected_company_details['client_internal_code']))
        if "email" in actual_result['legal_details'].keys():
            assert_(response_data_company_details['email'], sanitize_test_data(expected_company_details['email']))
        if "external_reference_id" in expected_company_details.keys():
            assert_(response_data_company_details['external_reference_id'],
                    sanitize_test_data(expected_company_details['external_reference_id']))
        if "has_lut" in expected_company_details.keys():
            assert_(response_data_company_details['has_lut'], sanitize_test_data(expected_company_details['has_lut']))
        if "is_sez" in expected_company_details.keys():
            assert_(response_data_company_details['is_sez'], sanitize_test_data(expected_company_details['is_sez']))
        if "tin" in expected_company_details.keys():
            assert_(response_data_company_details['tin'], sanitize_test_data(expected_company_details['tin']))
        if "address" in expected_company_details.keys():
            assert_(response_data_company_details['address']['city'],
                    sanitize_test_data(expected_company_details['address']['city']))
            assert_(response_data_company_details['address']['country'],
                    sanitize_test_data(expected_company_details['address']['country']))
            assert_(response_data_company_details['address']['field_1'],
                    sanitize_test_data(expected_company_details['address']['field_1']))
            assert_(response_data_company_details['address']['field_2'],
                    sanitize_test_data(expected_company_details['address']['field_2']))
            assert_(response_data_company_details['address']['pincode'],
                    sanitize_test_data(expected_company_details['address']['pincode']))
            assert_(response_data_company_details['address']['state'],
                    sanitize_test_data(expected_company_details['address']['state']))
        if "phone" in expected_company_details.keys():
            assert_(response_data_company_details['number'],
                    sanitize_test_data(expected_company_details['phone']['number']))
            if "country_code" in expected_company_details['country_code'].keys():
                assert_(response_data_company_details['country_code'],
                        sanitize_test_data(expected_company_details['phone']['country_code']))

    def validate_common_create_booking_fields(self, actual_data, response_data):
        expected_actual_checkin_checkout_date = json.loads(actual_data['expected_actual_checkin_checkout_date'])
        if expected_actual_checkin_checkout_date['expected_checkin'] or expected_actual_checkin_checkout_date[
            'expected_checkin'] == 0:
            expected_checkin = str(return_date(expected_actual_checkin_checkout_date['expected_checkin']))
            response_checkin = response_data['actual_checkin_date'].split('T')[0]
            assert_(response_checkin, expected_checkin)
        else:
            assert_(response_data['actual_checkin_date'],
                    sanitize_test_data(expected_actual_checkin_checkout_date['expected_checkin']))
        if expected_actual_checkin_checkout_date['expected_checkout'] or expected_actual_checkin_checkout_date[
            'expected_checkout'] == 0:
            expected_checkout = str(return_date(expected_actual_checkin_checkout_date['expected_checkout']))
            response_checkout = response_data['actual_checkout_date'].split('T')[0]
            assert_(response_checkout, expected_checkout)

        assert_(response_data['allowed_actions'], ast.literal_eval(actual_data['expected_allowed_actions']))
        assert_(response_data['reference_number'], sanitize_test_data(actual_data['reference_number']))
        assert_(response_data['seller_model'], sanitize_test_data(actual_data['seller_model']))
        assert_(response_data['status'], sanitize_test_data(actual_data['status']))
        assert_(response_data['stay_start'], str(return_date(actual_data['expected_checkin'])))
        # assert_(response_data['stay_end'], str(return_date(actual_data['expected_checkout'])))
        assert response_data['version'] is not None
        source = json.loads(actual_data['source'])
        assert_(response_data['source']['application_code'], sanitize_test_data(source['application_code']))
        assert_(response_data['source']['channel_code'], sanitize_test_data(source['channel_code']))
        assert_(response_data['source']['subchannel_code'], sanitize_test_data(source['subchannel_code']))
        assert_(response_data['cancellation_datetime'], sanitize_test_data(actual_data['cancellation_datetime']))
        assert_(response_data['checkin_date'], increment_date(int(actual_data['expected_checkin']),
                                                              common_config.CHECK_IN_TIME_ZONE))
        assert_(response_data['checkout_date'], increment_date(int(actual_data['expected_checkout']),
                                                               common_config.CHECKOUT_TIME_ZONE))
        assert_(response_data['comments'], sanitize_test_data(actual_data['comments']))
        assert_(response_data['default_billed_entity_category'],
                sanitize_test_data(actual_data['expected_default_billed_entity_category']))
        assert_(response_data['default_billed_entity_category_for_extras'],
                sanitize_test_data(actual_data['expected_default_billed_entity_category_for_extras']))
        assert_(response_data['default_payment_instruction'],
                sanitize_test_data(actual_data['expected_default_payment_instruction']))
        assert_(response_data['default_payment_instruction_for_extras'],
                sanitize_test_data(actual_data['expected_default_payment_instruction_for_extras']))

    def validate_booking_owner_details(self, actual_data, response_data):
        assert_(response_data['email'], sanitize_test_data(actual_data['owner_email']))
        if 'name' in response_data.keys():
            assert_(response_data['name']['salutation'], sanitize_test_data(actual_data['owner_salutation']))
            assert_(response_data['name']['first_name'], sanitize_test_data(actual_data['owner_first_name']))
            assert_(response_data['name']['last_name'], sanitize_test_data(actual_data['owner_last_name']))
        else:
            assert_(response_data['salutation'], sanitize_test_data(actual_data['owner_salutation']))
            assert_(response_data['first_name'], sanitize_test_data(actual_data['owner_first_name']))
            assert_(response_data['last_name'], sanitize_test_data(actual_data['owner_last_name']))
        if sanitize_test_data(actual_data['owner_phone']):
            owner_phone = json.loads(actual_data['owner_phone']) if isinstance(actual_data['owner_phone'], str) else \
                actual_data['owner_phone']
            assert_(response_data['phone']['country_code'],
                    sanitize_test_data(owner_phone['country_code']))
            assert_(response_data['phone']['number'], sanitize_test_data(owner_phone['number']))

    def validate_room_rate_plans(self, actual_data, expected_data, is_put_booking_v2=False, booking_id=None):
        for mocked_data, response_data in zip(expected_data, actual_data):
            assert_(response_data['rate_plan_name'], mocked_data['name'])
            assert_(response_data['rate_plan_reference_id'], mocked_data['rate_plan_id'])
            if not is_put_booking_v2:
                assert_(int(response_data['rate_plan_id']), mocked_data['rate_plan_id_for_validation'])
            assert_(response_data['is_active'], mocked_data['is_active'])
            assert_(response_data['is_flexi'], mocked_data['is_flexi'])
            assert_(response_data['package']['package_id'], mocked_data['package_data']['package_id'])
            assert_(response_data['package']['package_name'], mocked_data['package_data']['package_name'])
            if mocked_data.get('restrictions'):
                rate_plan_restrictions = query_execute_and_convert_to_json(
                    db_queries.GET_BOOKING_RATE_PLANS.format(booking_id=booking_id))[0].get('restrictions')
                assert_(mocked_data['restrictions'], rate_plan_restrictions)
            if 'print_rate' in mocked_data:
                assert_(response_data['print_rate'],mocked_data['print_rate'])
            if 'suppress_rate' in mocked_data:
                assert_(response_data['suppress_rate'],mocked_data['suppress_rate'])
            else:
                assert_(response_data['print_rate'], True)
                assert_(response_data['suppress_rate'], False)


    def validate_bill_summary(self, actual_bill_summary, expected_bill_summary, hotel_id):
        assert_amount_related_fields(actual_bill_summary['balance'], expected_bill_summary['balance'], hotel_id)
        assert_amount_related_fields(actual_bill_summary['balance_to_clear_after_checkout'],
                                     expected_bill_summary['balance_to_clear_after_checkout'], hotel_id)
        assert_amount_related_fields(actual_bill_summary['balance_to_clear_before_checkout'],
                                     expected_bill_summary['balance_to_clear_before_checkout'], hotel_id)

        # Bill Summary Credit Related Assertions
        expected_bill_credit_summary = expected_bill_summary['credit_summary']
        actual_bill_credit_summary = actual_bill_summary['credit_summary']
        assert_amount_related_fields(actual_bill_credit_summary['total_confirmed_payment'],
                                     expected_bill_credit_summary['total_confirmed_payment'], hotel_id)
        assert_amount_related_fields(actual_bill_credit_summary['total_credit'],
                                     expected_bill_credit_summary['total_credit'], hotel_id)
        assert_amount_related_fields(actual_bill_credit_summary['total_credit_offered'],
                                     expected_bill_credit_summary['total_credit_offered'], hotel_id)
        assert_amount_related_fields(actual_bill_credit_summary['total_refund'],
                                     expected_bill_credit_summary['total_refund'], hotel_id)
        assert_amount_related_fields(actual_bill_credit_summary['total_unconfirmed_payment'],
                                     expected_bill_credit_summary['total_unconfirmed_payment'], hotel_id)

        # Bill Summary Debit Related Assertions
        expected_bill_debit_summary = expected_bill_summary['debit_summary']
        actual_bill_debit_summary = actual_bill_summary['debit_summary']
        assert_amount_related_fields(actual_bill_debit_summary['total_allowance'],
                                     expected_bill_debit_summary['total_allowance'], hotel_id)
        assert_amount_related_fields(actual_bill_debit_summary['total_charge'],
                                     expected_bill_debit_summary['total_charge'], hotel_id)
        assert_amount_related_fields(actual_bill_debit_summary['total_credit_allowance'],
                                     expected_bill_debit_summary['total_credit_allowance'], hotel_id)
        assert_amount_related_fields(actual_bill_debit_summary['total_credit_charge'],
                                     expected_bill_debit_summary['total_credit_charge'], hotel_id)
        assert_amount_related_fields(actual_bill_debit_summary['total_debit'],
                                     expected_bill_debit_summary['total_debit'], hotel_id)
        assert_amount_related_fields(actual_bill_debit_summary['total_debit_payable_after_checkout'],
                                     expected_bill_debit_summary['total_debit_payable_after_checkout'], hotel_id)
        assert_amount_related_fields(actual_bill_debit_summary['total_debit_payable_at_checkout'],
                                     expected_bill_debit_summary['total_debit_payable_at_checkout'], hotel_id)
        assert_amount_related_fields(actual_bill_debit_summary['total_non_credit_allowance'],
                                     expected_bill_debit_summary['total_non_credit_allowance'], hotel_id)
        assert_amount_related_fields(actual_bill_debit_summary['total_non_credit_charge'],
                                     expected_bill_debit_summary['total_non_credit_charge'], hotel_id)

    def validate_booking_allowed_actions(self, booking_allowed_action_api_response, booking_allowed_actions,
                                         room_stay_allowed_actions=None):
        actual_data = booking_allowed_action_api_response['data']
        assert_(actual_data['allowed_actions'], ast.literal_eval(booking_allowed_actions),
                "booking allowed actions didn't match")
        if sanitize_test_data(room_stay_allowed_actions):
            for actual_room_stay_allowed_action, expected_room_stay_allowed_action in zip(
                    actual_data['room_stay_allowed_actions'], json.loads(room_stay_allowed_actions)):
                room_stay_id = actual_room_stay_allowed_action['room_stay_id']
                expected_allowed_actions = expected_room_stay_allowed_action[str(room_stay_id)]
                actual_allowed_actions = actual_room_stay_allowed_action['allowed_actions']
                assert_(actual_allowed_actions, ast.literal_eval(expected_allowed_actions),
                        "Room stay actual and expected allowed actions didn't match")

    def validate_room_stay_common_fields(self, response_data, expected_data, validation_guest_stay=False,
                                         test_case_id=None):
        assert_(response_data['actual_checkin_date'], sanitize_test_data(
            json.loads(expected_data['expected_actual_checkin_checkout_date'])['expected_checkin']))
        assert_(response_data['actual_checkout_date'], sanitize_test_data(
            json.loads(expected_data['expected_actual_checkin_checkout_date'])['expected_checkout']))
        assert (lambda x, y: collections.Counter(response_data['allowed_actions']) == collections.Counter(
            ast.literal_eval(expected_data['expected_allowed_actions'])))
        assert_(response_data['checkin_date'], increment_date(int(expected_data['checkin_date']),
                                                              common_config.CHECK_IN_TIME_ZONE))
        assert_(response_data['checkout_date'], increment_date(int(expected_data['checkout_date']),
                                                               common_config.CHECKOUT_TIME_ZONE))
        if not validation_guest_stay:
            assert_(response_data['disallow_charge_addition'],
                    sanitize_test_data(expected_data['disallow_charge_addition']))
            if "over_booking" in test_case_id or test_case_id == 'put_booking_79':
                assert_(response_data['is_overflow'], True)
            else:
                assert_(response_data['is_overflow'], sanitize_test_data(expected_data['is_overflow']))
            assert_(response_data['room_type_id'], sanitize_test_data(expected_data['room_type_id']))
            assert_(response_data['status'], sanitize_test_data(expected_data['expected_status']))
            assert_(response_data['stay_start'], str(return_date(expected_data['checkin_date'])))
            assert_(response_data['stay_end'], str(return_date(expected_data['checkout_date'])))

    def validate_common_invoice_fields(self, invoice_preview_data_request, expected_preview_data_request,
                                       test_case_id=None):
        for actual_invoice_preview_data, expected_invoice_preview_data in zip(
                invoice_preview_data_request, expected_preview_data_request):
            assert_(actual_invoice_preview_data['invoice_date'], expected_invoice_preview_data['invoice_date'])
            assert_(actual_invoice_preview_data['issued_to_type'], expected_invoice_preview_data['issued_to_type'])
            assert_(actual_invoice_preview_data['posttax_amount'], expected_invoice_preview_data['posttax_amount'])
            assert_(actual_invoice_preview_data['tax_amount'], expected_invoice_preview_data['tax_amount'])
            assert_(actual_invoice_preview_data['invoice_id'], expected_invoice_preview_data['invoice_id'])
            assert_(actual_invoice_preview_data['pretax_amount'], expected_invoice_preview_data['pretax_amount'])
            assert_(actual_invoice_preview_data['signed_url'], expected_invoice_preview_data['signed_url'])
            assert_(actual_invoice_preview_data['issued_by_type'], expected_invoice_preview_data['issued_by_type'])
            assert_(actual_invoice_preview_data['bill_id'], expected_invoice_preview_data['bill_id'])
            assert_(actual_invoice_preview_data['version'], expected_invoice_preview_data['version'])
            assert_(actual_invoice_preview_data['hotel_invoice_id'],
                    expected_invoice_preview_data['hotel_invoice_id'])
            assert_(actual_invoice_preview_data['invoice_number'], expected_invoice_preview_data['invoice_number'])
            assert_(actual_invoice_preview_data['invoice_url'], expected_invoice_preview_data['invoice_url'])
            assert_(actual_invoice_preview_data['is_einvoice'], expected_invoice_preview_data['is_einvoice'])
            assert_(actual_invoice_preview_data['allowed_charge_types'],
                    expected_invoice_preview_data['allowed_charge_types'])
            if sanitize_test_data(actual_invoice_preview_data['bill_to']['gstin_num']):
                gstin_num = actual_invoice_preview_data['bill_to']['gstin_num']
                assert_gstin_regex(test_case_id, gstin_num)

    def validate_common_audit_trail_field(self, actual_audit_trail, expected_audit_trail):
        assert_(actual_audit_trail['application'], expected_audit_trail['application'])
        assert_(actual_audit_trail['user'], expected_audit_trail['user'])
        assert_(actual_audit_trail['user_type'], expected_audit_trail['user_type'])

    def validate_charge(self, actual_charge_detail, expected_charge_detail, hotel_id='0016932'):
        assert_(actual_charge_detail['applicable_date'], expected_charge_detail['applicable_date'])
        if 'bill_to_type' in actual_charge_detail:
            assert_(actual_charge_detail['bill_to_type'], expected_charge_detail['bill_to_type'])
        if 'charge_components' in actual_charge_detail:
            assert_(actual_charge_detail['charge_components'], expected_charge_detail['charge_components'])
        assert_(actual_charge_detail['charge_id'], expected_charge_detail['charge_id'])
        if 'charge_split_type' in actual_charge_detail:
            assert_(actual_charge_detail['charge_split_type'], expected_charge_detail['charge_split_type'])
        assert_(actual_charge_detail['charge_to'], expected_charge_detail['charge_to'])
        assert_(actual_charge_detail['comment'], expected_charge_detail['comment'])
        if 'created_by' in actual_charge_detail:
            assert_(actual_charge_detail['created_by'], expected_charge_detail['created_by'])
        assert_(actual_charge_detail['inclusion_charge_ids'], expected_charge_detail['inclusion_charge_ids'])
        assert_(actual_charge_detail['is_inclusion_charge'], expected_charge_detail['is_inclusion_charge'])
        assert_((actual_charge_detail['item']['name']).replace(" ", ""),
                (expected_charge_detail['item']['name']).replace(" ", ""))
        assert_(actual_charge_detail['item']['item_id'], expected_charge_detail['item']['item_id'])
        assert_(actual_charge_detail['item']['sku_category_id'], expected_charge_detail['item']['sku_category_id'])
        actual_charge_item_details = actual_charge_detail['item']['details']
        expected_charge_item_details = expected_charge_detail['item']['details']
        if 'is_pos_charge' in actual_charge_item_details:
            assert_(actual_charge_item_details['is_pos_charge'], expected_charge_item_details['is_pos_charge'])
        assert_(actual_charge_item_details['occupancy'], expected_charge_item_details['occupancy'])
        assert_(actual_charge_item_details['room_no'], expected_charge_item_details['room_no'])
        if actual_charge_detail['status'] == 'consumed':
            assert actual_charge_item_details['room_stay_id'] is not None
            if 'room_stay_id' in expected_charge_item_details and \
                    sanitize_test_data(expected_charge_item_details['room_stay_id']):
                assert_(actual_charge_item_details['room_stay_id'], expected_charge_item_details['room_stay_id'])
        assert_(actual_charge_item_details['room_type'], expected_charge_item_details['room_type'])
        assert_(actual_charge_item_details['room_type_code'], expected_charge_item_details['room_type_code'])
        if 'source_booking_id' in actual_charge_item_details:
            assert_(actual_charge_item_details['source_booking_id'], expected_charge_item_details['source_booking_id'])
        if 'source_charge_id' in actual_charge_item_details:
            assert_(actual_charge_item_details['source_charge_id'], expected_charge_item_details['source_charge_id'])
        assert_(actual_charge_detail['item']['item_code']['code_type'],
                expected_charge_detail['item']['item_code']['code_type'])
        assert_(actual_charge_detail['item']['item_code']['value'],
                expected_charge_detail['item']['item_code']['value'])
        assert_(actual_charge_detail['linked_addon_charge_ids'], expected_charge_detail['linked_addon_charge_ids'])
        assert_amount_related_fields(actual_charge_detail['posttax_amount'], expected_charge_detail['posttax_amount'],
                                     hotel_id)
        if 'posttax_amount_post_allowance' in actual_charge_detail:
            assert_amount_related_fields(actual_charge_detail['posttax_amount_post_allowance'],
                                         expected_charge_detail['posttax_amount_post_allowance'], hotel_id)
        assert_amount_related_fields(actual_charge_detail['pretax_amount'], expected_charge_detail['pretax_amount'],
                                     hotel_id)
        if 'pretax_amount_post_allowance' in actual_charge_detail:
            assert_amount_related_fields(actual_charge_detail['pretax_amount_post_allowance'],
                                         expected_charge_detail['pretax_amount_post_allowance'], hotel_id)
        assert_(actual_charge_detail['split_allowed'], expected_charge_detail['split_allowed'])
        assert_(actual_charge_detail['status'], expected_charge_detail['status'])
        assert_amount_related_fields(actual_charge_detail['tax_amount'], expected_charge_detail['tax_amount'], hotel_id)
        if 'tax_amount_post_allowance' in actual_charge_detail:
            assert_amount_related_fields(actual_charge_detail['tax_amount_post_allowance'],
                                         expected_charge_detail['tax_amount_post_allowance'], hotel_id)
        if 'type' in actual_charge_detail:
            assert_(actual_charge_detail['type'], expected_charge_detail['type'])
        if 'tax_details' in actual_charge_detail:
            for actual_tax_details, expected_tax_details in zip(actual_charge_detail['tax_details'],
                                                                expected_charge_detail['tax_details']):
                assert_amount_related_fields(actual_tax_details['amount'], expected_tax_details['amount'], hotel_id)
                assert_(actual_tax_details['percentage'], expected_tax_details['percentage'])
                assert_(actual_tax_details['tax_type'], expected_tax_details['tax_type'])
        if 'tax_details_post_allowance' in actual_charge_detail:
            for actual_tax_details_post_allowance, expected_tax_details_post_allowance in \
                    zip(actual_charge_detail['tax_details_post_allowance'],
                        expected_charge_detail['tax_details_post_allowance']):
                assert_(actual_tax_details_post_allowance['amount'], expected_tax_details_post_allowance['amount'])
                assert_(actual_tax_details_post_allowance['percentage'],
                        expected_tax_details_post_allowance['percentage'])
                assert_(actual_tax_details_post_allowance['tax_type'], expected_tax_details_post_allowance['tax_type'])
        sorted_actual_charge_split_details = sorted(actual_charge_detail['charge_splits'],
                                                    key=lambda i: i['charge_split_id'])
        sorted_expected_charge_split_details = sorted(expected_charge_detail['charge_splits'],
                                                      key=lambda i: i['charge_split_id'])
        if len(sorted_actual_charge_split_details) == len(sorted_expected_charge_split_details):
            for actual_charge_split_details, expected_charge_split_details in zip(sorted_actual_charge_split_details,
                                                                                  sorted_expected_charge_split_details):
                if actual_charge_split_details['allowances']:
                    for actual_allowance_details, expected_allowance_details in \
                            zip(actual_charge_split_details['allowances'], expected_charge_split_details['allowances']):
                        assert_(actual_allowance_details['allowance_id'], expected_allowance_details['allowance_id'])
                        assert_(actual_allowance_details['is_active'], expected_allowance_details['is_active'])
                        assert_amount_related_fields(actual_allowance_details['posttax_amount'],
                                                     expected_allowance_details['posttax_amount'], hotel_id)
                        assert_amount_related_fields(actual_allowance_details['pretax_amount'],
                                                     expected_allowance_details['pretax_amount'], hotel_id)
                        assert_amount_related_fields(actual_allowance_details['tax_amount'],
                                                     expected_allowance_details['tax_amount'], hotel_id)
                        assert_(actual_allowance_details['status'], expected_allowance_details['status'])
                        if 'remarks' in actual_allowance_details:
                            assert_(actual_allowance_details['remarks'], expected_allowance_details['remarks'])
                assert_(actual_charge_split_details['bill_to_type'], expected_charge_split_details['bill_to_type'])
                assert_(actual_charge_split_details['billed_entity_account']['account_number'],
                        expected_charge_split_details['billed_entity_account']['account_number'])
                assert_(actual_charge_split_details['billed_entity_account']['billed_entity_id'],
                        expected_charge_split_details['billed_entity_account']['billed_entity_id'])
                if 'charge_to' in actual_charge_split_details:
                    assert_(actual_charge_split_details['charge_to'], expected_charge_split_details['charge_to'])
                assert_(actual_charge_split_details['charge_type'], expected_charge_split_details['charge_type'])
                assert_(actual_charge_split_details['percentage'], expected_charge_split_details['percentage'])
                assert_amount_related_fields(actual_charge_split_details['post_tax'],
                                             expected_charge_split_details['post_tax'], hotel_id)
                if 'posttax_amount_post_allowance' in actual_charge_split_details:
                    assert_amount_related_fields(actual_charge_split_details['posttax_amount_post_allowance'],
                                                 expected_charge_split_details['posttax_amount_post_allowance'],
                                                 hotel_id)
                assert_amount_related_fields(actual_charge_split_details['pre_tax'],
                                             expected_charge_split_details['pre_tax'], hotel_id)
                if 'pretax_amount_post_allowance' in actual_charge_split_details:
                    assert_amount_related_fields(actual_charge_split_details['pretax_amount_post_allowance'],
                                                 expected_charge_split_details['pretax_amount_post_allowance'],
                                                 hotel_id)
                assert_amount_related_fields(actual_charge_split_details['tax'], expected_charge_split_details['tax'],
                                             hotel_id)
                if 'tax_amount_post_allowance' in actual_charge_split_details:
                    assert_amount_related_fields(actual_charge_split_details['tax_amount_post_allowance'],
                                                 expected_charge_split_details['tax_amount_post_allowance'], hotel_id)
                if 'tax_details' in actual_charge_split_details:
                    assert_(actual_charge_split_details['tax_details'], expected_charge_split_details['tax_details'])
                if 'tax_details_post_allowance' in actual_charge_split_details:
                    assert_(actual_charge_split_details['tax_details_post_allowance'],
                            expected_charge_split_details['tax_details_post_allowance'])

                # validation for pretax+tax=posttax
                assert_(convert_to_decimal(actual_charge_split_details['pre_tax']) + convert_to_decimal(
                    actual_charge_split_details['tax']), convert_to_decimal(actual_charge_split_details['post_tax']))

    def validate_proforma_invoice(self, client_, booking_request, billing_request, bill_id, get_booking_response,
                                  extra_data):
        proforma_invoice_response = \
            booking_request.get_booking_proforma_invoices(client_, extra_data, booking_request.booking_id, 200,
                                                          user_type=None)['data']
        response = billing_request.get_bill_v2_request(client_, bill_id, 200, user_type=None)['data']
        get_booking_response = get_booking_response['data'] if get_booking_response else None
        for invoice_templates_index, invoice_templates in enumerate(proforma_invoice_response['invoice_templates']):
            proforma_invoice_bill_summary = proforma_invoice_response['invoice_templates'][invoice_templates_index][
                'bill_summary']
            assert_(response['app_id'], proforma_invoice_bill_summary['app_id'])
            assert_(bill_id, proforma_invoice_bill_summary['bill_id'])
            assert_(response['net_paid_amount'], proforma_invoice_bill_summary['net_paid_amount'])
            assert_(response['net_payable'], proforma_invoice_bill_summary['net_payable'])
            assert_(response['paid_amount'], proforma_invoice_bill_summary['paid_amount'])
            assert_(response['refund_amount'], proforma_invoice_bill_summary['refund_amount'])
            assert_(response['total_credit_posttax_amount'],
                    proforma_invoice_bill_summary['total_credit_posttax_amount'])
            assert_(response['total_credit_posttax_amount_post_allowance'],
                    proforma_invoice_bill_summary['total_credit_posttax_amount_post_allowance'])
            assert_(response['total_non_credit_invoiced_amount'],
                    proforma_invoice_bill_summary['total_non_credit_invoiced_amount'])
            assert_(response['total_non_credit_reversal_amount'],
                    proforma_invoice_bill_summary['total_non_credit_reversal_amount'])
            assert_(response['total_posttax_amount_post_allowance'],
                    proforma_invoice_bill_summary['total_posttax_amount_post_allowance'])
            assert_(response['total_pretax_amount'], proforma_invoice_bill_summary['total_pretax_amount'])
            assert_(response['total_tax_amount'], proforma_invoice_bill_summary['total_tax_amount'])

            # validation on summary
            assert_(response['summary']['balance'], proforma_invoice_bill_summary['summary']['balance'])
            assert_(response['summary']['balance_to_clear_after_checkout'],
                    proforma_invoice_bill_summary['summary']['balance_to_clear_after_checkout'])
            assert_(response['summary']['balance_to_clear_before_checkout'],
                    proforma_invoice_bill_summary['summary']['balance_to_clear_before_checkout'])
            assert_(response['summary']['credit_summary']['total_confirmed_payment'],
                    proforma_invoice_bill_summary['summary']['credit_summary']['total_confirmed_payment'])
            assert_(response['summary']['credit_summary']['total_credit'],
                    proforma_invoice_bill_summary['summary']['credit_summary']['total_credit'])
            assert_(response['summary']['credit_summary']['total_credit_offered'],
                    proforma_invoice_bill_summary['summary']['credit_summary']['total_credit_offered'])
            assert_(response['summary']['credit_summary']['total_refund'],
                    proforma_invoice_bill_summary['summary']['credit_summary']['total_refund'])
            assert_(response['summary']['credit_summary']['total_unconfirmed_payment'],
                    proforma_invoice_bill_summary['summary']['credit_summary']['total_unconfirmed_payment'])
            assert_(response['summary']['debit_summary']['total_allowance'],
                    proforma_invoice_bill_summary['summary']['debit_summary']['total_allowance'])
            assert_(response['summary']['debit_summary']['total_charge'],
                    proforma_invoice_bill_summary['summary']['debit_summary']['total_charge'])
            assert_(response['summary']['debit_summary']['total_credit_allowance'],
                    proforma_invoice_bill_summary['summary']['debit_summary']['total_credit_allowance'])
            assert_(response['summary']['debit_summary']['total_credit_charge'],
                    proforma_invoice_bill_summary['summary']['debit_summary']['total_credit_charge'])
            assert_(response['summary']['debit_summary']['total_debit'],
                    proforma_invoice_bill_summary['summary']['debit_summary']['total_debit'])
            assert_(response['summary']['debit_summary']['total_debit_payable_after_checkout'],
                    proforma_invoice_bill_summary['summary']['debit_summary']['total_debit_payable_after_checkout'])
            assert_(response['summary']['debit_summary']['total_debit_payable_at_checkout'],
                    proforma_invoice_bill_summary['summary']['debit_summary']['total_debit_payable_at_checkout'])
            assert_(response['summary']['debit_summary']['total_non_credit_allowance'],
                    proforma_invoice_bill_summary['summary']['debit_summary']['total_non_credit_allowance'])
            assert_(response['summary']['debit_summary']['total_non_credit_charge'],
                    proforma_invoice_bill_summary['summary']['debit_summary']['total_non_credit_charge'])

            if len(proforma_invoice_response['invoice_templates'][invoice_templates_index]['payments']) > 0:
                for payments_index, payments in enumerate(
                        proforma_invoice_response['invoice_templates'][invoice_templates_index]['payments']):
                    if response['total_credit_posttax_amount'] == 0:
                        assert_(response['payments'][payments_index]['amount'],
                                proforma_invoice_response['invoice_templates'][invoice_templates_index]['payments'][
                                    payments_index]['amount'])
                        assert_(response['payments'][payments_index]['comment'],
                                proforma_invoice_response['invoice_templates'][invoice_templates_index]['payments'][
                                    payments_index]['comment'])
                        # assert_(actual_payment_data[payments_index]['date_of_payment'],proforma_invoice_response['invoice_templates'][invoice_templates_index]['payments'][payments_index]['date_of_payment'])
                        assert_(response['payments'][payments_index]['payment_mode'],
                                proforma_invoice_response['invoice_templates'][invoice_templates_index]['payments'][
                                    payments_index]['payment_mode'])
                        assert_(response['payments'][payments_index]['payment_type'],
                                proforma_invoice_response['invoice_templates'][invoice_templates_index]['payments'][
                                    payments_index]['payment_type'].lower())

            assert_(booking_request.booking_id,
                    proforma_invoice_response['invoice_templates'][invoice_templates_index]['booking']['booking_id'])
            if get_booking_response:
                assert_(get_booking_response['checkin_date'][0:10],
                        proforma_invoice_response['invoice_templates'][invoice_templates_index]['booking'][
                            'checkin_date'][0:10])
                assert_(get_booking_response['checkout_date'][0:10],
                        proforma_invoice_response['invoice_templates'][invoice_templates_index]['booking'][
                            'checkout_date'][0:10])

    def validate_mark_guest_as_booker(self, response, test_case_id, client_, booking_request, booking_id,
                                      billing_request, bill_id, sheet_name):
        test_data = get_test_case_data(sheet_name, test_case_id)[0]
        booking_response = booking_request.get_booking_request(client_, booking_id, 200)['data']
        billed_entity_response = billing_request.get_bill_entity_request(client_, bill_id, 200)['data']
        expected_booking_customer = [ebc['customer_id'] for ebc in json.loads(test_data['expected_booking_customers'])]
        actual_booking_customer = [abc['customer_id'] for abc in booking_response['customers']]
        assert_(expected_booking_customer.sort(), actual_booking_customer.sort())
        assert_(sorted(json.loads(test_data['expected_billed_entities']), key=lambda i: i['billed_entity_id']),
                sorted(billed_entity_response, key=lambda i: i['billed_entity_id']))

    def validate_integration_event(self, booking_id, user_action):
        data = query_execute(GET_INTEGRATION_EVENT_FOR_BOOKING.format(booking_id=booking_id,
                                                                      user_action=user_action)).fetchall()
        assert len(data)

    def validate_ta_commissions(self, expected_commissions_data=None):
        actual_commissions_data = query_execute_and_convert_to_json(GET_TA_COMMISSION)
        if expected_commissions_data:
            assert_(len(actual_commissions_data), len(expected_commissions_data))
            actual_commissions_data = sorted(actual_commissions_data, key=lambda i: (
                i['room_stay_id'], i['applicable_on'], i['ta_commission_id']))
            expected_commissions_data = sorted(expected_commissions_data, key=lambda i: (
                i['room_stay_id'], i['applicable_on'], i['ta_commission_id']))
            for actual_commission_data, expected_commission_data in zip(actual_commissions_data,
                                                                        expected_commissions_data):
                actual_travel_agent_details = query_execute_and_convert_to_json(GET_TA_COMMISSION_PERCENTAGE.format(
                    booking_id=actual_commission_data['booking_id']))[0]['travel_agent_details']
                actual_commissions_percentage = actual_travel_agent_details['ta_commission_details'][
                    'commission_value'] if actual_travel_agent_details and actual_travel_agent_details[
                    'ta_commission_details'] else 0
                assert_(actual_commission_data['room_stay_id'], expected_commission_data['room_stay_id'])
                assert_(actual_commission_data['status'], expected_commission_data['status'])
                if actual_commission_data['status'] != 'cancelled':
                    if actual_travel_agent_details['ta_commission_details']['commission_type'] == 'fixed':
                        assert_(float(actual_commission_data['pretax_amount']), float(
                            expected_commission_data['commission_value']))
                    elif 'commission_value' in expected_commission_data:
                        assert_(float(actual_commission_data['pretax_amount']), round(float(
                            expected_commission_data['room_night_price']) * (float(
                            expected_commission_data['commission_value']) / 100), 2))
                    else:
                        assert_(float(actual_commission_data['pretax_amount']), round(float(
                            expected_commission_data['room_night_price']) * (float(
                            actual_commissions_percentage) / 100), 2))
                assert_(actual_commission_data['room_night_price'], expected_commission_data['room_night_price'])
                assert_(actual_commission_data['is_allowance'], expected_commission_data['is_allowance'])
                assert_(actual_commission_data['applicable_on'],
                        str(return_date(expected_commission_data['applicable_on'])))
                if 'locked_on' in expected_commission_data and (sanitize_test_data(
                        expected_commission_data['locked_on']) or expected_commission_data['locked_on']) == 0:
                    assert_(actual_commission_data['locked_on'],
                            str(return_date(expected_commission_data['locked_on'])))
                else:
                    assert_(actual_commission_data['locked_on'], None)
                if sanitize_test_data(expected_commission_data['reissued_on']) or \
                        expected_commission_data['reissued_on'] == 0:
                    assert_(actual_commission_data['reissued_on'],
                            str(return_date(expected_commission_data['reissued_on'])))
                else:
                    assert_(actual_commission_data['reissued_on'], None)
        else:
            assert_(len(actual_commissions_data), 0)

    def validate_all_payments_response(self, test_data, bill_id=None, client=None, billing_request=None,
                                       actual_payments_data=None):
        if not actual_payments_data:
            actual_payments_data = billing_request.get_payments_request(client, bill_id, 200)['data']
        expected_payments_data = json.loads(test_data['expected_payments_data']) if \
            test_data['expected_payments_data'] else []

        actual_payments_data = sorted(actual_payments_data, key=lambda i: i['payment_id'])
        expected_payments_data = sorted(expected_payments_data, key=lambda i: i['payment_id'])

        for act_payments_data, exp_payments_data in zip(actual_payments_data, expected_payments_data):
            assert_(act_payments_data['amount'], exp_payments_data['amount'])
            assert_(act_payments_data['comment'], exp_payments_data['comment'])
            assert_(act_payments_data['confirmed'], exp_payments_data['confirmed'])
            assert_(act_payments_data['paid_by'], exp_payments_data['paid_by'])
            assert_(act_payments_data['paid_to'], exp_payments_data['paid_to'])
            assert_(act_payments_data['payment_id'], exp_payments_data['payment_id'])
            assert_(act_payments_data['payment_mode'], exp_payments_data['payment_mode'])
            assert_(act_payments_data['payment_ref_id'], exp_payments_data['payment_ref_id'])
            assert_(act_payments_data['payment_type'], exp_payments_data['payment_type'])
            assert_(act_payments_data['payor_billed_entity_id'], exp_payments_data['payor_billed_entity_id'])
            assert_(act_payments_data['status'], exp_payments_data['status'])
            if 'payment_channel' in act_payments_data:
                assert_(act_payments_data['payment_channel'], exp_payments_data['payment_channel'])
            if 'amount_in_payment_currency' in act_payments_data:
                assert_(act_payments_data['amount_in_payment_currency'],
                        exp_payments_data['amount_in_payment_currency'])
