from importlib import import_module
from webbrowser import get
import pytest

from prometheus.integration_tests.config.common_config import (
    HOTEL_ID,
    CANCELLED_ROOM_STATUS,
    SUCCESS_CODES,
    BOOKING_STATUS_INVALID
)
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import (
    CREATE_BOOKING_WITH_MULTIPLE_DIFFERENT_ROOM_TYPES,
    CREATE_BOOKING_WITH_MULTIPLE_DIFFERENT_ROOM_TYPES_AND_ADD_REMOVE_ROOM,
    CREATE_BOOKING_WITH_MULTIPLE_DIFFERENT_ROOM_TYPES_AND_ADD_ROOMS,
    CREATE_BOOKING_WITH_MULTIPLE_DIFFERENT_ROOM_TYPES_AND_REMOVE_ROOMS,
    CREATE_BOOKING_WITH_MULTIPLE_DIFFERENT_ROOM_TYPES_AND_REVERSE_CANCEL_BOOKING,
    CREATE_BOOKING_WITH_MULTIPLE_SAME_ROOM_TYPES,
    NO_SHOW_MARKED_BOOKING
)
from prometheus.integration_tests.tests.inventory.Validations.get_inventory_validation import (
    GetInventoryValidation,
)
from prometheus.integration_tests.resources.db_queries import UPDATE_BUSINESS_DATE
from datetime import date,timedelta
from prometheus.integration_tests.utilities.common_utils import query_execute

@pytest.mark.parametrize(
    "test_case_id, previous_actions, tc_description, status_code, user_type, error_code, error_message, dev_message, error_payload, skip_message , perform_night_audit",
    [
        ("get_inventory_02", CREATE_BOOKING_WITH_MULTIPLE_SAME_ROOM_TYPES,
         "create a multiple room booking of same room type and verify room count is decreased in get inventory API",
         200, "super-admin", "", "", "", "", "",False),
        ("get_inventory_03", CREATE_BOOKING_WITH_MULTIPLE_DIFFERENT_ROOM_TYPES,
         "create a multiple room booking of different room type and verify respective room count is decreased in get inventory API",
         200, "super-admin", "", "", "", "", "",False),
        ("get_inventory_04", CREATE_BOOKING_WITH_MULTIPLE_DIFFERENT_ROOM_TYPES_AND_ADD_ROOMS,
         "create a multiple room booking of different room types, and edit booking to add room and verify inventory",
         200, "super-admin", "", "", "", "", "",False),
        ("get_inventory_05", CREATE_BOOKING_WITH_MULTIPLE_DIFFERENT_ROOM_TYPES_AND_REMOVE_ROOMS,
         "create a multiple room booking of different room types, and edit booking to remove room then add room and verify inventory",
         200, "super-admin", "", "", "", "", "",False),
        ("get_inventory_06", CREATE_BOOKING_WITH_MULTIPLE_DIFFERENT_ROOM_TYPES_AND_ADD_REMOVE_ROOM,
         "create a multiple room booking of different room types, and edit booking to remove room then add room and verify inventory",
         200, "super-admin", "", "", "", "", "",False),
        ("get_inventory_07", CREATE_BOOKING_WITH_MULTIPLE_DIFFERENT_ROOM_TYPES_AND_REVERSE_CANCEL_BOOKING,
         "create a multiple room booking of different room types,and reverse cancel booking and verify inventory", 200,
         "super-admin", "", "", "", "", "",False),
        ("get_inventory_08",NO_SHOW_MARKED_BOOKING,
         "create a multiple room booking of different room types,and mark no show a booking and verify inventory", 200,
         "super-admin", "", "", "", "", "",True),
        ("get_inventory_09",CREATE_BOOKING_WITH_MULTIPLE_SAME_ROOM_TYPES,"create DNR and create a booking and verify inventory", 200,
         "super-admin", "", "", "", "", "",False),
        # Early-Checkin(ECI), Late-Checkout(LCO) Block Inventory Management Cases
        ("get_inventory_10",
         [{'id': "booking_312", 'type': 'booking_v2'},
          {'id': "Create_Expense_92", 'type': 'create_expense_V3'}],
         "Verify inventory update when Early-Checkin(ECI) block is added to a single-room, single-day booking.",
         200, "super-admin", "", "", "", "", "", False),
        ("get_inventory_11",
         [{'id': "booking_313", 'type': 'booking_v2'},
          {'id': "Create_Expense_93", 'type': 'create_expense_V3'}],
         "Verify inventory update when Late-Checkout(LCO) block is added to a single-room, single-day booking.",
         200, "super-admin", "", "", "", "", "", False),
        ("get_inventory_12",
         [{'id': "booking_312", 'type': 'booking_v2'},
          {'id': "CreateInventoryBlock_01", 'type': 'create_inventory_block'}],
         "Verify inventory update when Temporary-Block(temp) is added to a single-room, single-day booking.",
         200, "super-admin", "", "", "", "", "", False),
        ("get_inventory_13",
         [{'id': "booking_312", 'type': 'booking_v2'},
          {'id': "Create_Expense_92", 'type': 'create_expense_V3'},
          {'id': "put_booking_22", 'type': 'edit_booking_v2'}],
         "Verify inventory update for single-day, single-room booking when Early-Checkin(ECI) block is auto-released "
         "after booking edit", 200, "super-admin", "", "", "", "", "", False),
        ("get_inventory_14",
         [{'id': "booking_313", 'type': 'booking_v2'},
          {'id': "Create_Expense_93", 'type': 'create_expense_V3'},
          {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '6'}],
         "Verify inventory update for single-day, single-room booking when Late-Checkout(LCO) block is auto-released "
         "after LCO charge cancellation", 200, "super-admin", "", "", "", "", "", False),
    ])
class TestInventory(BaseTest):

    def test_inventory(self, client_, test_case_id, previous_actions, tc_description, status_code, user_type,
                       error_code, error_message, dev_message, error_payload, skip_message,perform_night_audit):

        if skip_message:
            pytest.skip(skip_message)

        # capture inventory before doing any actions
        hotel_id = HOTEL_ID[0]
        
        if test_case_id == 'get_inventory_09':
            self.common_request_caller(client_,[{'id': "createDnr_08", 'type': 'create_dnr', 'user_type': 'super-admin'}],hotel_id)
            
        inventories = self.inventory_request.get_all_inverntory_counts(client_, hotel_id, test_case_id, status_code=200)
        inventory_count_before_booking = {inventory['room_type_id']: inventory['actual_count'] for
                                          inventory in inventories['data']}
        if perform_night_audit:
            query_execute(UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, HOTEL_ID[0])
                self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
                
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, HOTEL_ID[0])

        get_booking_response = self.booking_request.get_booking_request_v2(client_, self.booking_request.booking_id,
                                                                           200)['data']
        # capture valid rooms in current booking
        rooms_in_current_booking = dict()
        if get_booking_response['status'] not in BOOKING_STATUS_INVALID:
            for room_stay in get_booking_response['room_stays']:
                count = 0
                if room_stay['status'] not in CANCELLED_ROOM_STATUS:
                    if room_stay['room_type_id'] not in rooms_in_current_booking.keys():
                        rooms_in_current_booking.update({room_stay['room_type_id']: count + 1})
                    else:
                        rooms_in_current_booking.update(
                            {room_stay['room_type_id']: rooms_in_current_booking[room_stay['room_type_id']] + 1})

        get_inventory_response = self.inventory_request.get_all_inverntory_counts(client_, hotel_id, test_case_id, 200)[
            'data']
        current_inventory = {inventory['room_type_id']: inventory['actual_count'] for inventory in
                             get_inventory_response}

        if status_code in SUCCESS_CODES:
            validate = GetInventoryValidation()
            validate.validate_response(inventory_count_before_booking, current_inventory,
                                       rooms_in_current_booking)
