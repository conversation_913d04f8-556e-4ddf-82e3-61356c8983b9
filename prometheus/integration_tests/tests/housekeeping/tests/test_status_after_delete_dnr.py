import pytest

from prometheus.integration_tests.config import common_config
from prometheus.integration_tests.config.common_config import SUCCESS_CODES, ERROR_CODES
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.housekeeping.validations.validation_housekeeping_status import \
    ValidationHouseKeepingStatus


class TestStatusAfterDeleteDnr(BaseTest):

    @pytest.mark.parametrize("test_case_id, previous_actions, tc_description, status_code, user_type, error_code, "
                             "error_message, dev_message, error_payload, skip_message ",
                             [
                                 ("DeleteDnr_45", HOSUE_KEEPING_STATUS_DIRTY + [CREATE_DNR_34],
                                  "Delete Dnr for an active dnrId (HouseKeeping = Dirty)", 200, 'super-admin', "", "",
                                  "", "", ""),
                                 ("DeleteDnr_46", HOSUE_KEEPING_STATUS_CLEAN + [CREATE_DNR_34],
                                  "Delete Dnr for an active dnrId (HouseKeeping = Clean)", 200, 'super-admin', "", "",
                                  "", "", ""),
                             ])
    @pytest.mark.regression
    def test_status_after_delete_dnr(self, client_, test_case_id, previous_actions, tc_description, status_code,
                                     user_type,
                                     error_code, error_message, dev_message, error_payload, skip_message):

        if skip_message:
            pytest.skip(skip_message)

        if previous_actions:
            self.common_request_caller(client_, previous_actions)
            dnr_id = self.dnr_request.dnr_id
        else:
            dnr_id = None

        self.dnr_request.delete_dnr_request(client_, status_code, user_type, test_case_id, dnr_id)

        response = self.housekeeping_request.get_roomstatus_request(client_, common_config.HOTEL_ID[0], 200)
        room_id = self.dnr_request.room_id

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(test_case_id, room_id, response)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(test_case_id, room_id, response):
        validation = ValidationHouseKeepingStatus(test_case_id, 'DNRs')
        validation.validate_status(response, room_id, 'true')
