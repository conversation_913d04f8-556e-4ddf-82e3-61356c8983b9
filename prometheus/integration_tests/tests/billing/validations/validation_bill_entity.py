import json

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import assert_
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationBillEntity:

    def __init__(self, client_, test_case_id, response, bill_entity_count):
        self.test_data = get_test_case_data(sheet_names.new_booking_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client_
        self.bill_entity_count = bill_entity_count

    def billed_entity(self, billing_request, bill_entity_count):
        category = {}
        for bill_entity in range(0, int(bill_entity_count)):
            if (billing_request['data'][bill_entity]['category']) in category:
                category[(billing_request['data'][bill_entity]['category'])] = category[(
                billing_request['data'][bill_entity]['category'])] + 1
            else:
                category[(billing_request['data'][bill_entity]['category'])] = 1

        assert (self.validate_bill_entity_values(category, json.loads(self.test_data['bill_entity'])))

    def validate_bill_entity_values(self, actual_result, expected_result):
        for bill_entity in expected_result:
            if actual_result[bill_entity] != expected_result[bill_entity]:
                return False
        return True

    def category_name(self, billing_request, bill_entity_count, test_case_id):
        category_name = {}
        for entity_category_name in range(0, int(bill_entity_count)):
            if (billing_request['data'][entity_category_name]['category'] == 'booker') & (
                    test_case_id == "EditCustomer_02"):
                assert_(billing_request['data'][entity_category_name]['name']['first_name'],
                        get_test_case_data(sheet_names.customer_data_sheet_name, test_case_id)[0]['first_name'])
                category_name[billing_request['data'][entity_category_name]['category']] = \
                billing_request['data'][entity_category_name]['name']['first_name']
