import json

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_test_data
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationGetPaymentByRefId:
    def __init__(self, response, test_case_id):
        self.test_data = get_test_case_data(sheet_names.payment_v2_sheet_name, test_case_id)[0]
        self.response = response

    def validate_response(self):
        for actual_data, expected_data in zip(self.response['data'], json.loads(self.test_data['expected_payments_data'])):
            assert_(actual_data['amount'], expected_data['amount'])
            assert_(actual_data['payment_channel'], expected_data['payment_channel'])
            assert_(actual_data['payment_mode'], expected_data['payment_mode'])
            assert_(actual_data['payment_ref_id'], expected_data['payment_ref_id'])
            assert_(actual_data['payment_type'], expected_data['payment_type'])
