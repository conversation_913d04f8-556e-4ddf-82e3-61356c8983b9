import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.config.sheet_names import payment_v2_sheet_name
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.billing.validations.common_validation import CommonValidation
from prometheus.integration_tests.utilities.common_utils import query_execute


class TestAddPaymentV2(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, "
        "error_code, error_message, dev_message, error_payload, extras, skip_message", [
            ("AddPaymentV2_01", 'Adding confirmed payment on non-credit account', SINGLE_WALK_BOOKING_V2_01, 200, None,
             "", "", "", "", False, ""),
            ("AddPaymentV2_02", 'Adding confirmed payment on non-credit account with splits', SINGLE_WALK_BOOKING_V2_01,
             200, None, "", "", "", "", False, ""),
            ("AddPaymentV2_03", 'Adding unconfirmed payment', SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             False, ""),
            ("AddPaymentV2_04", 'Adding confirmed payment without amount in payment currency',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "", False, ""),
            ("AddPaymentV2_05", 'Adding confirmed payment without comment', SINGLE_WALK_BOOKING_V2_01, 200, None, "",
             "", "", "", False, ""),
            ("AddPaymentV2_06", 'Adding payment without confirmed', SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "",
             "", False, ""),
            ("AddPaymentV2_07", 'Adding payment without paid_by', SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             False, ""),
            ("AddPaymentV2_08", 'Adding payment invalid paid_by', SINGLE_WALK_BOOKING_V2_01, 400, None, "", "", "", "",
             False, ""),
            ("AddPaymentV2_09", 'Adding payment without paid_to', SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             False, ""),
            ("AddPaymentV2_10", 'Adding payment invalid paid_to', SINGLE_WALK_BOOKING_V2_01, 400, None, "", "", "", "",
             False, ""),
            ("AddPaymentV2_11", 'Adding payment without paid_channel', SINGLE_WALK_BOOKING_V2_01, 400, None, "", "", "",
             "", False, ""),
            ("AddPaymentV2_12", 'Adding payment invalid paid_channel', SINGLE_WALK_BOOKING_V2_01, 400, None, "", "", "",
             "", False, ""),
            ("AddPaymentV2_13", 'Adding confirmed payment with payment_mode_sub_type', SINGLE_WALK_BOOKING_V2_01, 200,
             None, "", "", "", "", False, ""),
            ("AddPaymentV2_14", 'Adding payment invalid payment_mode', SINGLE_WALK_BOOKING_V2_01, 400, None, "", "", "",
             "", False, ""),
            ("AddPaymentV2_15", 'Adding payment without payment_mode', SINGLE_WALK_BOOKING_V2_01, 400, None, "", "", "",
             "", False, ""),
            ("AddPaymentV2_16", 'Adding payment without payment_type', SINGLE_WALK_BOOKING_V2_01, 400, None, "", "", "",
             "", False, ""),
            ("AddPaymentV2_17", 'Adding payment with invalid payment_type', SINGLE_WALK_BOOKING_V2_01, 400, None, "",
             "", "", "", False, ""),
            ("AddPaymentV2_18", 'Adding payment with status unconfirmed and confirmed=true', SINGLE_WALK_BOOKING_V2_01,
             200, None, "", "", "", "", False, ""),
            ("AddPaymentV2_19", 'Adding payment with status unconfirmed and confirmed=false', SINGLE_WALK_BOOKING_V2_01,
             200, None, "", "", "", "", False, ""),
            ("AddPaymentV2_20", 'Adding cancelled payment on non-credit account', SINGLE_WALK_BOOKING_V2_01, 200, None,
             "", "", "", "", False, ""),
            ("AddPaymentV2_21", 'Adding pending payment on non-credit account', SINGLE_WALK_BOOKING_V2_01, 200, None,
             "", "", "", "", False, ""),
            ("AddPaymentV2_22", 'Adding posted payment on non-credit account', SINGLE_WALK_BOOKING_V2_01, 200, None, "",
             "", "", "", False, ""),
            ("AddPaymentV2_23", 'Adding payment on account not in sequence', SINGLE_WALK_BOOKING_V2_01, 400, None, "",
             "", "", "", False, ""),
            ("AddPaymentV2_24", 'Adding payment on invalid billed entity', SINGLE_WALK_BOOKING_V2_01, 404, None, "", "",
             "", "", False, ""),
            ("AddPaymentV2_25", 'Adding confirmed payment without ref number', SINGLE_WALK_BOOKING_V2_01, 200, None, "",
             "", "", "", False, ""),
            ("AddPaymentV2_26", 'Adding confirmed payment on credit account', MULTIPLE_ROOM_BOOKING_02, 400, None, "",
             "", "", "", True, "Bug: It goes on first non-credit non-locked account"),
            ("AddPaymentV2_27", 'Adding payment in locked account', SINGLE_WALK_BOOKING_V2_01, 400, None, "", "", "",
             "", False, ""),
            ("AddPaymentV2_28", 'Refunding Amount equal to payment amount', ADD_CONFIRMED_PAYMENT_01, 200, None, "", "",
             "", "", False, ""),
            ("AddPaymentV2_29", 'Refunding Amount greater to payment amount', ADD_CONFIRMED_PAYMENT_01, 400, None, "",
             "", "", "", True, "Bug"),
            ("AddPaymentV2_30", 'Refunding Amount on invalid amount', ADD_CONFIRMED_PAYMENT_01, 400, None, "", "", "",
             "", False, ""),
            ("AddPaymentV2_31", 'Refunding greater Amount on credit account', ADD_CONFIRMED_PAYMENT_02, 400, None, "",
             "", "", "", False, ""),
            ("AddPaymentV2_32", 'Refunding equal Amount on credit account', ADD_CONFIRMED_PAYMENT_02, 400, None, "", "",
             "", "", False, ""),
            ("AddPaymentV2_33", 'Refunding less Amount on credit account', ADD_CONFIRMED_PAYMENT_02, 200, None, "", "",
             "", "", False, ""),
            ("AddPaymentV2_34", 'Adding confirmed payment on spot credit account', BOOKING_WITH_SPOT_CREDIT_FOLIO, 200,
             None, "", "", "", "", True, "Need to fix"),
            ("AddPaymentV2_35", 'Adding confirmed payment on spot credit account', BOOKING_WITH_SPOT_CREDIT_FOLIO, 200,
             None, "", "", "", "", True, "Need to fix"),
            ("AddPaymentV2_36", 'Add payment on payor entity as booker', ADD_CONFIRMED_PAYMENT_02, 200, None, "", "",
             "", "", {"payor_billed_entity_id": "1"}, ""),
            ("AddPaymentV2_37", 'Add payment on payor entity as primary guest', ADD_CONFIRMED_PAYMENT_02, 200, None, "",
             "",
             "", "", {"payor_billed_entity_id": "2"}, ""),
            ("AddPaymentV2_38", 'Add payment on payor entity as wrong', ADD_CONFIRMED_PAYMENT_02, 404, None, "", "",
             "", "", {"payor_billed_entity_id": "10"}, ""),
            ("AddPaymentV2_39", 'Refunding Amount equal to payment amount with payor be as 1', ADD_CONFIRMED_PAYMENT_01,
             200, None, "", "", "", "", {"payor_billed_entity_id": "1"}, ""),
            ("AddPaymentV2_40", 'Refunding Amount equal to payment amount c payor be as 10', ADD_CONFIRMED_PAYMENT_01,
             404, None, "", "", "", "", {"payor_billed_entity_id": "10"}, ""),
        ])
    @pytest.mark.regression
    def test_add_payment_v2(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                            error_code, error_message, dev_message, error_payload, extras, skip_message):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        if test_case_id == 'AddPaymentV2_27':
            query_execute(db_queries.UPDATE_ACCOUNT_TO_LOCKED.format(bill_id=self.booking_request.bill_id))

        payor_billed_entity_id = extras['payor_billed_entity_id'] if extras else None

        response = self.billing_request.add_payment_v2_request(client_, test_case_id, self.booking_request.bill_id,
                                                               status_code, user_type,
                                                               payor_billed_entity_id=payor_billed_entity_id)
        self.booking_request.get_booking_audit_trail(client_, self.booking_request.booking_id)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(self.billing_request, self.booking_request, test_case_id, client_,
                            self.booking_request.bill_id,
                            self.billing_request.payment_id, payor_billed_entity_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(billing_request, booking_request, test_case_id, client_, bill_id, payment_id,
                   payor_billed_entity_id=None):
        validation = CommonValidation(client_, test_case_id, billing_request, bill_id, payment_id,
                                      payment_v2_sheet_name, payor_billed_entity_id=payor_billed_entity_id)
        validation.validate_payment_response()
        validation.validate_payment_splits()
        validation.validate_billing_entity()
        validation.validate_audit_trail_add_payment(booking_request)


class TestAddPaymentV2PaidByPaidToMatrix(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, "
        "error_code, error_message, dev_message, error_payload, extras, skip_message", [
            ("AddPaymentV2_41", 'Adding confirmed payment where payment method is cash and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_42", 'Adding confirmed payment where payment method is cash, payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_43", 'Adding confirmed payment where payment method is cash,payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_44", 'Adding confirmed payment where payment method is cash, payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_45", 'Adding confirmed payment where payment method is cash, payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_46",
             'Adding confirmed payment where payment method is razorpay_api and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_47",
             'Adding confirmed payment where payment method is razorpay_api and payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_48",
             'Adding confirmed payment where payment method is razorpay_api and payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_49",
             'Adding confirmed payment where payment method is razorpay_api and payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_50",
             'Adding confirmed payment where payment method is razorpay_api and payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_51",
             'Adding confirmed payment where payment method is paid_by_treebo and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_52",
             'Adding confirmed payment where payment method is paid_by_treebo and payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_53",
             'Adding confirmed payment where payment method is paid_by_treebo and payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_54",
             'Adding confirmed payment where payment method is paid_by_treebo and payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_55",
             'Adding confirmed payment where payment method is paid_by_treebo and payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_56",
             'Adding confirmed payment where payment method is treebo_corporate_rewards and payor entity is booker',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_57", 'Adding confirmed payment where payment method is '
                                'treebo_corporate_rewards and payor entity is primary guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 3}, ""),
            ("AddPaymentV2_58", 'Adding confirmed payment where payment method is '
                                'treebo_corporate_rewards and payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_59", 'Adding confirmed payment where payment method is '
                                'treebo_corporate_rewards and payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_60", 'Adding confirmed payment where payment method is '
                                'treebo_corporate_rewards and payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_61",
             'Adding confirmed payment where payment method is hotel_collectible and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_62",
             'Adding confirmed payment where payment method is hotel_collectible and payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_63",
             'Adding confirmed payment where payment method is hotel_collectible and payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_64",
             'Adding confirmed payment where payment method is hotel_collectible and payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_65",
             'Adding confirmed payment where payment method is hotel_collectible and payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_66", 'Adding confirmed payment where payment method is UPI and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_67", 'Adding confirmed payment where payment method is UPI, payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_68", 'Adding confirmed payment where payment method is UPI, payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_69", 'Adding confirmed payment where payment method is UPI, payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_70", 'Adding confirmed payment where payment method is UPI, payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_71",
             'Adding confirmed payment where payment method is amazon_pay and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_72",
             'Adding confirmed payment where payment method is amazon_pay and payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_73",
             'Adding confirmed payment where payment method is amazon_pay and payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_74",
             'Adding confirmed payment where payment method is amazon_pay and payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_75",
             'Adding confirmed payment where payment method is amazon_pay and payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_76",
             'Adding confirmed payment where payment method is debit_card and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_77",
             'Adding confirmed payment where payment method is debit_card and payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_78",
             'Adding confirmed payment where payment method is debit_card and payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_79",
             'Adding confirmed payment where payment method is debit_card and payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_80",
             'Adding confirmed payment where payment method is debit_card and payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_81",
             'Adding confirmed payment where payment method is credit_card and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_82",
             'Adding confirmed payment where payment method is credit_card and payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_83",
             'Adding confirmed payment where payment method is credit_card and payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_84",
             'Adding confirmed payment where payment method is credit_card and payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_85",
             'Adding confirmed payment where payment method is credit_card and payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_86",
             'Adding confirmed payment where payment method is paid_at_ota and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_87",
             'Adding confirmed payment where payment method is paid_at_ota and payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_88",
             'Adding confirmed payment where payment method is paid_at_ota and payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_89",
             'Adding confirmed payment where payment method is paid_at_ota and payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_90",
             'Adding confirmed payment where payment method is paid_at_ota and payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_91",
             'Adding confirmed payment where payment method is phone_pe and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_92",
             'Adding confirmed payment where payment method is phone_pe and payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_93",
             'Adding confirmed payment where payment method is phone_pe and payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_94",
             'Adding confirmed payment where payment method is phone_pe and payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_95",
             'Adding confirmed payment where payment method is phone_pe and payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_96",
             'Adding confirmed payment where payment method is air_pay and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_97",
             'Adding confirmed payment where payment method is air_pay and payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_98",
             'Adding confirmed payment where payment method is air_pay and payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_99",
             'Adding confirmed payment where payment method is air_pay and payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_100",
             'Adding confirmed payment where payment method is air_pay and payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_101",
             'Adding confirmed payment where payment method is payment_service and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_102",
             'Adding confirmed payment where payment method is payment_service and payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_103",
             'Adding confirmed payment where payment method is payment_service and payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_104",
             'Adding confirmed payment where payment method is payment_service and payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_105",
             'Adding confirmed payment where payment method is payment_service and payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_106",
             'Adding confirmed payment where payment method is payment_link and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_107",
             'Adding confirmed payment where payment method is payment_link and payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_108",
             'Adding confirmed payment where payment method is payment_link and payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_109",
             'Adding confirmed payment where payment method is payment_link and payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_110",
             'Adding confirmed payment where payment method is payment_link and payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_111",
             'Adding confirmed payment where payment method is credit_shell and payor entity is booker',
             Credit_Shell_07, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_112",
             'Adding confirmed payment where payment method is credit_shell and payor entity is primary guest',
             Credit_Shell_07, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_116",
             'Adding confirmed payment where payment method is razorpay_payment_gateway and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_117",
             'Adding confirmed payment where payment method is razorpay_payment_gateway, payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_118", 'Adding confirmed payment where payment method is '
                                 'razorpay_payment_gateway, payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_119", 'Adding confirmed payment where payment method is '
                                 'razorpay_payment_gateway, payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_120",
             'Adding confirmed payment where payment method is razorpay_payment_gateway, payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_121",
             'Adding confirmed payment where payment method is bank_transfer_treebo and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_122",
             'Adding confirmed payment where payment method is bank_transfer_treebo and payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_123",
             'Adding confirmed payment where payment method is bank_transfer_treebo, payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_124",
             'Adding confirmed payment where payment method is bank_transfer_treebo and payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_125",
             'Adding confirmed payment where payment method is bank_transfer_treebo and payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_126",
             'Adding confirmed payment where payment method is other and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_127",
             'Adding confirmed payment where payment method is other and payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_128",
             'Adding confirmed payment where payment method is other and payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_129",
             'Adding confirmed payment where payment method is other and payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_130",
             'Adding confirmed payment where payment method is other and payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_131",
             'Adding confirmed payment where payment method is bank_transfer_hotel and payor entity is booker',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_132",
             'Adding confirmed payment where payment method is bank_transfer_hotel and payor entity is primary guest',
             SINGLE_WALK_BOOKING_V2_01, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_133",
             'Adding confirmed payment where payment method is bank_transfer_hotel and payor entity is consuming guest',
             SINGLE_BOOKING_V2_WITH_TWO_GUEST, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_134",
             'Adding confirmed payment where payment method is bank_transfer_hotel and payor entity is booker company',
             SINGLE_B2B_BOOKING_01_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_135",
             'Adding confirmed payment where payment method is bank_transfer_hotel and payor entity is travel agent',
             SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_136", 'Refund amount to booker via cash',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_137", 'Refund amount to primary guest via cash',
             ADD_CONFIRMED_PAYMENT_18, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_138", 'Refund amount to consuming guests via cash',
             ADD_CONFIRMED_PAYMENT_19, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_139", 'Refund amount to booker company via cash',
             ADD_CONFIRMED_PAYMENT_20, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_140", 'Refund amount to travel agent via cash',
             ADD_CONFIRMED_PAYMENT_25, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_141", 'Refund amount to guest via cash where payment is done by corporate',
             ADD_CONFIRMED_PAYMENT_20, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_142", 'Refund amount to booker via razorpay_api',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_143", 'Refund amount to primary guest via razorpay_api',
             ADD_CONFIRMED_PAYMENT_18, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_144", 'Refund amount to consuming guests via razorpay_api',
             ADD_CONFIRMED_PAYMENT_19, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_145", 'Refund amount to booker company via razorpay_api',
             ADD_CONFIRMED_PAYMENT_20, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_146", 'Refund amount to travel agent via razorpay_api',
             ADD_CONFIRMED_PAYMENT_25, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_147", 'Refund amount to booker via paid_by_treebo',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_148", 'Refund amount to primary guest via paid_by_treebo',
             ADD_CONFIRMED_PAYMENT_18, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_149", 'Refund amount to consuming guests via paid_by_treebo',
             ADD_CONFIRMED_PAYMENT_19, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_150", 'Refund amount to booker company via paid_by_treebo',
             ADD_CONFIRMED_PAYMENT_20, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_151", 'Refund amount to travel agent via paid_by_treebo',
             ADD_CONFIRMED_PAYMENT_25, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_152", 'Refund amount to booker company via bank_transfer_hotel',
             ADD_CONFIRMED_PAYMENT_20, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_153", 'Refund amount to primary guest via debit_card',
             ADD_CONFIRMED_PAYMENT_18, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_154", 'Refund amount to travel agent via credit_card',
             ADD_CONFIRMED_PAYMENT_25, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_155", 'Refund amount to booker via UPI',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_156", 'Refund amount to booker company via other',
             ADD_CONFIRMED_PAYMENT_20, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_157", 'Refund amount to booker company via razorpay_payment_gateway',
             ADD_CONFIRMED_PAYMENT_20, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_158", 'Refund amount to primary guest via payment_service',
             ADD_CONFIRMED_PAYMENT_18, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_159", 'Refund amount to travel agent via amazon_pay',
             ADD_CONFIRMED_PAYMENT_25, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_160", 'Refund amount to booker via bank_transfer_treebo',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_161", 'Refund amount to booker via phone_pe',
             ADD_CONFIRMED_PAYMENT_19, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 4}, ""),
            ("AddPaymentV2_162", 'Refund amount to booker company via bank_transfer_treebo',
             ADD_CONFIRMED_PAYMENT_20, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_163", 'Refund amount to travel agent via paid_at_ota',
             ADD_CONFIRMED_PAYMENT_25, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_164", 'Refund amount to booker via air_pay',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1}, ""),
            ("AddPaymentV2_165", 'Multiple payments in a booking',
             ADD_CONFIRMED_PAYMENT_25, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 2}, ""),
            ("AddPaymentV2_166", 'Adding payment more than K*booking amount', SINGLE_WALK_BOOKING_V2_01, 400, None, "",
             "", "", "", {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_167", 'Adding payment equal to K*booking amount', SINGLE_WALK_BOOKING_V2_01, 200, None, "",
             "", "", "", {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_168", 'Adding payment less than K*booking amount', SINGLE_WALK_BOOKING_V2_01, 200, None, "",
             "", "", "", {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_169", 'Adding two payments, first payment less than booking amount and '
                                 'second payment more than K*remaining amount', ADD_CONFIRMED_PAYMENT_32, 400, None,
             "", "", "", "", {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_170", 'Adding two payments, first payment less than booking amount and '
                                 'second payment equal to K*remaining amount', ADD_CONFIRMED_PAYMENT_32, 200, None,
             "", "", "", "", {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_171", 'Adding two payments, first payment less than booking amount and '
                                 'second payment less than K*remaining amount', ADD_CONFIRMED_PAYMENT_32, 200, None,
             "", "", "", "", {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_172", 'Adding two payments, first payment equal to booking amount and '
                                 'second payment of any amount', ADD_CONFIRMED_PAYMENT_33, 400, None,
             "", "", "", "", {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_173", 'Adding two payments, first payment more than booking amount but less than K*booking '
                                 'amount and second payment of any amount', ADD_CONFIRMED_PAYMENT_34, 400, None,
             "", "", "", "", {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_174", 'Adding payment more than K*booking amount when K is float', SINGLE_WALK_BOOKING_V2_01,
             400, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "multiplier": 1.5, "payment_constraint": True}, ""),
            ("AddPaymentV2_175", 'Adding payment equal to K*booking amount when K is float', SINGLE_WALK_BOOKING_V2_01,
             200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "multiplier": 1.5, "payment_constraint": True}, ""),
            ("AddPaymentV2_176", 'PAH refund when payment is PTT', ADD_CONFIRMED_PAYMENT_27, 400, None, "",
             "", "", "", {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_177", 'PAH refund when payment is PAH and refund amount is more than payment',
             ADD_CONFIRMED_PAYMENT_17, 400, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_178", 'PAH refund when payment is PAH and refund amount is equal to payment',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_179", 'PAH refund when payment is PAH and refund amount is less than payment',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_180", 'PTT refund when payment is PAH+PTT and refund amount is greater than payment',
             ADD_CONFIRMED_PAYMENT_41, 400, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_181", 'PTT refund when payment is PAH+PTT and refund amount is equal to payment',
             ADD_CONFIRMED_PAYMENT_41, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_182", 'PTT refund when payment is PAH+PTT and refund amount is less than payment',
             ADD_CONFIRMED_PAYMENT_41, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_183", 'PTT refund when payment is PAH+PTT and '
                                 'refund amount is greater than PTT payment but less than PTT+PAH payment',
             ADD_CONFIRMED_PAYMENT_41, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_184", 'PTT refund when payment is PAH+PTT and '
                                 'refund amount is greater than PAH payment but less than PTT+PAH payment',
             ADD_CONFIRMED_PAYMENT_41, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_185", 'PTT refund when payment is only PAH', ADD_CONFIRMED_PAYMENT_17, 200, None, "", "",
             "", "", {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_186", 'PTT refund when payment is only PTT', ADD_CONFIRMED_PAYMENT_27, 200, None, "", "",
             "", "", {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_187", 'PAH refund that exceeds PAH payment in a booking already having PAH refund',
             ADD_CONFIRMED_PAYMENT_42, 400, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_188", 'PTT refund that exceeds PAH+PTT payment in a booking already having PTT refund',
             ADD_CONFIRMED_PAYMENT_27, 400, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_189", 'Cancel a applicable PAH refund then again issue a refund with PAH payment amount',
             ADD_CONFIRMED_PAYMENT_43, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_190", 'Cancel a applicable PTT refund then again issue a refund with PAH+PTT payment amount',
             ADD_CONFIRMED_PAYMENT_44, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_191", 'Refund with reason Cancellation - Overbooking and type payment reversal',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_192",
             'Refund with reason Stay Date Changes - Wrong dates booked by the agent and type payment reversal',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_193", 'Refund with reason Relocation - Unmarried Couple and type payment reversal',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_194", 'Refund with reason Adjustment - Manual Error and type payment reversal',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_195", 'Refund with reason Adjustment - System Error and type payment reversal',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_196", 'Refund with reason Cancellation - Overbooking and type refund and recover from hotel',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_197", 'Refund with reason Stay Date Changes - Wrong dates booked by the agent '
                                 'and type refund and recover from hotel',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_198",
             'Refund with reason Relocation - Unmarried Couple and type refund and recover from hotel',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_199", 'Refund with reason Adjustment - Manual Error and type refund and recover from hotel',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_200", 'Refund with reason Adjustment - System Error and type refund and recover from hotel',
             ADD_CONFIRMED_PAYMENT_17, 200, None, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True}, ""),
            ("AddPaymentV2_204", 'Refund of >10k via payout link as cr team',
             ADD_CONFIRMED_PAYMENT_54, 400, CR_TEAM, "", "", "", "",
             {"payment_matrix": True, "payor_billed_entity_id": 1, "payment_constraint": True,
              "refund_rule": True}, ""),
        ])
    @ pytest.mark.regression
    def test_add_payment_v2_paid_by_paid_to_matrix(self, client_, test_case_id, tc_description, previous_actions,
                                                   status_code, user_type, error_code, error_message, dev_message,
                                                   error_payload, extras, skip_message):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]
        payment_matrix = extras['payment_matrix'] if 'payment_matrix' in extras else False
        payor_billed_entity_id = extras['payor_billed_entity_id'] if 'payor_billed_entity_id' in extras else None
        multiplier = extras['multiplier'] if 'multiplier' in extras else 2
        payment_constraint = extras['payment_constraint'] if 'payment_constraint' in extras else False
        refund_rule = extras['refund_rule'] if 'refund_rule' in extras else False

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        self.credit_shell_request.credit_shell_id = None
        self.credit_shell_request.get_credit_shells_request(client_, 200, self.booking_request.booking_id)
        response = self.billing_request.add_payment_v2_request(client_, test_case_id, self.booking_request.bill_id,
                                                               status_code, user_type, credit_shell_id=
                                                               self.credit_shell_request.credit_shell_id,
                                                               payor_billed_entity_id=payor_billed_entity_id,
                                                               payment_matrix=payment_matrix, multiplier=multiplier,
                                                               payment_constraint=payment_constraint,
                                                               refund_rule= refund_rule)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(self.billing_request, self.booking_request, test_case_id, client_,
                            self.booking_request.bill_id, self.billing_request.payment_id,
                            credit_shell_id=self.credit_shell_request.credit_shell_id,
                            payor_billed_entity_id=payor_billed_entity_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(billing_request, booking_request, test_case_id, client_, bill_id, payment_id,
                   credit_shell_id=None, payor_billed_entity_id=None):
        validation = CommonValidation(client_, test_case_id, billing_request, bill_id, payment_id,
                                      payment_v2_sheet_name, credit_shell_id=credit_shell_id,
                                      payor_billed_entity_id=payor_billed_entity_id)
        validation.validate_all_payment_response()
        validation.validate_payment_response()
        validation.validate_payment_splits()
        validation.validate_billing_entity()
        validation.validate_audit_trail_add_payment(booking_request)

