import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.billing.validations.validation_add_payment import ValidationAddPayment


class TestAddPayment(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, "
        "error_code, error_message, dev_message, error_payload, skip_case, skip_message,is_mock_rule_req", [
            ("AddPayment_01", 'Payment in the existing booking', SINGLE_BOOKING_01, 200, None, "", "", "", "", False,
             "", True),
            ("AddPayment_02", 'Booking , created without any payments', SINGLE_BOOKING_02, 200, None, "", "", "", "",
             False, "", True),
            ("AddPayment_03", 'payment more than the due amount in a booking', SINGLE_BOOKING_01, 200, None, "", "", "",
             "", False, "", True),
            ("AddPayment_04", 'full payment in reserved state and credit payment', SINGLE_BOOKING_01, 200, None, "", "",
             "", "", False, "", True),
            ("AddPayment_05", 'Add payment by selecting "payment_type" as REFUND', SINGLE_BOOKING_01, 200, None, "", "",
             "", "", True, "Refund amount cannot be larger than account balance", True),
            ("AddPayment_06", 'Add payment by selecting "payment_type" as CREDIT', SINGLE_BOOKING_01, 200, None, "", "",
             "", "", False, "", True),
            ("AddPayment_07", 'Add payment for "status" as "cancelled"', SINGLE_BOOKING_02, 200, None, "", "", "", "",
             False, "", True),
            ("AddPayment_08", 'Add payment for "status" as "pending"', SINGLE_BOOKING_01, 200, None, "", "", "", "",
             False, "", True),
            ("AddPayment_09", 'Verify that for a payment accepts a Decimal value for "amount"', SINGLE_BOOKING_01, 200,
             None, "", "", "", "", False, "", True),
            ("AddPayment_11", 'Verify that Payment post API accepts future payments', SINGLE_BOOKING_01, 200,
             None, "", "", "", "", True, "Refund amount cannot be larger than account balance", True),
            ("AddPayment_13", 'Remove the mandatory fields from request(amount,dop,paid_by,paid_to)', SINGLE_BOOKING_01,
             400, None, "", "", "", "", False, "", True),
            ("AddPayment_14", 'Remove the mandatory fields from request(payment_channel,payment_mode,status,type)',
             SINGLE_BOOKING_01, 400, None, "", "", "", "", False, "", True),
            ("AddPayment_15", 'Verify removing non-mandatory fields from payload', SINGLE_BOOKING_01, 200, None, "", "",
             "", "", False, "", True),
            ("AddPayment_16", 'Provide wrong ENUM values for the keys that accepts enums', SINGLE_BOOKING_02, 400, None,
             "", "", "", "", False, "", True),
            ("AddPayment_18", 'Add a payment with 0 amount', SINGLE_BOOKING_01, 400, None, "", "", "", "", False, "",
             True),
            ("AddPayment_19", 'Add -ve payment and hit the request', SINGLE_BOOKING_01, 400, None, "", "", "",
             "", False, "", True),
            ("AddPayment_26_NULL_CHECK", 'Add Comment as null and verify', SINGLE_BOOKING_01, 200, None, "", "", "", "",
             False, "", True),
            ("AddPayment_27", 'Add payment as phone_pe payment mode and subtype as none', SINGLE_BOOKING_01, 200, None,
             "", "", "", "", False, "", True),
            ("AddPayment_28", 'Add payment for part checkout', [{'id': "Booking_76", 'type': 'booking'}], 200, None, "",
             "", "", "", False, "", True),
            ("AddPayment_29", 'Pay Full 1 day 2 guest payment', SINGLE_BOOKING_01, 200, None, "", "", "", "",
             False, "", True),
            ("AddPayment_42", 'Add payment in credit account', MULTIPLE_ROOM_BOOKING_02, 200, None, "", "", "", "",
             False, "", True),
            ("AddPayment_38", 'payment more than the due amount in a booking with show_receipt as true',
             SINGLE_BOOKING_01, 200, None, "", "", "",
             "", False, "", True),
            ("AddPayment_39", 'payment more than the due amount in a booking with show receipt as false',
             SINGLE_BOOKING_01, 200, None, "", "", "",
             "", False, "", True),
            ("AddPayment_40", 'show_receipt with non boolean value',
             SINGLE_BOOKING_01, 400, None, "", "[Show Receipt] -> Not a valid boolean.", "",
             "", False, "", True),
            ("AddPayment_41", 'show receipt with null value',
             SINGLE_BOOKING_01, 400, None, "", "[Show Receipt] -> Field may not be null.", "",
             "", False, "", True)

        ])
    @pytest.mark.regression
    def test_add_payment(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                         error_code, error_message, dev_message, error_payload, skip_case, skip_message,
                         is_mock_rule_req):
        if skip_case:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]
        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        response = self.billing_request.add_payment_request(client_, test_case_id, self.booking_request.bill_id,
                                                            status_code, user_type, is_mock_rule_req)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.billing_request, self.booking_request.bill_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, billing_request, bill_id):
        validation = ValidationAddPayment(client_, test_case_id, response, bill_id)
        if test_case_id != 'AddPayment_42':
            validation.validate_response()
            validation.validate_billing(billing_request)
        else:
            validation.validate_billing_entity()

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message,is_mock_rule_req", [
            ("AddPayment_03_REFUND", "Add a Refund payment for single room booking which is part_checked_out",
             [{'id': 'Booking_41_FullPayment_singleRoom', 'type': 'booking'},
              {'id': 'checkinBookingStatus_37', 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreview_37', 'type': 'preview_invoice'}, {'id': 'checkoutAction_10', 'type': 'checkout'}],
             200, FDM, "", "", "", "", False, "", True),
            ("AddPayment_04_REFUND", "Add a Refund payment for single room booking which is fully checkout",
             [{'id': 'Booking_41_FullPayment_singleRoom', 'type': 'booking'},
              {'id': 'checkinBookingStatus_37', 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreview_08', 'type': 'preview_invoice'}, {'id': 'checkoutAction_08', 'type': 'checkout'}],
             403, FDM, "", "", "", "", False, "", False),
            ("AddPayment_05_REFUND", "Add a Refund payment for single room booking which is fully checkout",
             [{'id': 'Booking_41_FullPayment_singleRoom', 'type': 'booking'},
              {'id': 'checkinBookingStatus_37', 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreview_08', 'type': 'preview_invoice'}, {'id': 'checkoutAction_08', 'type': 'checkout'}],
             403, CR_TEAM, "", "", "", "", False, "", False),
            ("AddPayment_06_PaymentLink", "Adding payment for payment mode-payment_link",
             [{'id': 'Booking_41_FullPayment_singleRoom', 'type': 'booking'},
              {'id': 'checkinBookingStatus_37', 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreview_08', 'type': 'preview_invoice'}, {'id': 'checkoutAction_08', 'type': 'checkout'}],
             403, FDM, "", "", "", "", False, "", False),
            ("AddPayment_07_PaymentService", "Adding payment for payment mode-payment_service",
             [{'id': 'Booking_41_FullPayment_singleRoom', 'type': 'booking'},
              {'id': 'checkinBookingStatus_37', 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreview_08', 'type': 'preview_invoice'}, {'id': 'checkoutAction_08', 'type': 'checkout'}],
             403, FDM, "", "", "", "", False, "", False),
            ("RecordPaymentPrivilege_01", "Record Payment with FDM role for channel other than Hotel before checkin",
             SINGLE_B2B_BOOKING_01_USING_V2, 403, CR_TEAM, "", "", "", "", False, "", False),
            ("RecordPaymentPrivilege_02", "Record Payment with FDM role for channel other than Hotel after checkin",
             SINGLE_B2B_BOOKING_CHECK_IN_V2_01, 200, CR_TEAM, "", "", "", "", False, "", False),
            ("RecordPaymentPrivilege_03", "Record Payment with FDM role for channel Hotel and partial checkin",
             PARTIAL_CHECK_IN_BOOKING_V2, 200, CR_TEAM, "", "", "", "", False, "", False),
            ("RecordPaymentPrivilege_04", "Record Payment with FDM role for channel other than Hotel and partial checkin",
             PARTIAL_CHECKIN_OTA_BOOKING_V2, 200, CR_TEAM, "", "", "", "", False, "", False),
            ("RecordPaymentPrivilege_05", "Record payment with FDM role for channel Hotel after reverse checkin",
             REVERSE_BOOKING_01, 200, CR_TEAM, "", "", "", "", False, "", False),
            ("RecordPaymentPrivilege_06", "Record Payment with FDM role for channel other than Hotel and reverse checkin",
             REVERSE_BOOKING_B2B, 403, CR_TEAM,  "", "", "", "", False, "", False),

        ])
    @pytest.mark.regression
    def test_add_payment_policy(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                                error_code, error_message, dev_message, error_payload, skip_case, skip_message,
                                is_mock_rule_req):
        if skip_case:
            pytest.skip(skip_message)
        if "MultiCurrency" in tc_description:
            hotel_id = HOTEL_ID[1]
        else:
            hotel_id = HOTEL_ID[0]
        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        response = self.billing_request.add_payment_request(client_, test_case_id, self.booking_request.bill_id,
                                                            status_code, user_type, is_mock_rule_req)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.billing_request, self.booking_request.bill_id)
        else:
            assert False, "Response status code is not matching"
