import json
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import assert_, return_date, sanitize_blank
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from unittest.case import TestCase
from prometheus.integration_tests.builders.common_request_builder import booking_repo, bill_repo
from prometheus.integration_tests.config.common_config import HOTEL_CURRENCY_MAP, HOTEL_ID


class ValidationReverseCancel:

    def __init__(self, client_, test_case_id, response, user_type, booking_request, billing_request,
                 before_cancel_booking_response, before_cancel_billing_response):
        self.test_data = get_test_case_data(sheet_names.correction_mode_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client_
        self.user_type = user_type
        self.booking_request = booking_request
        self.billing_request = billing_request
        self.before_cancel_billing_response = before_cancel_billing_response
        self.before_cancel_booking_response = before_cancel_booking_response

    def validate_response(self, action_type):
        assert_(self.response['data']['action_id'], self.booking_request.action_id)
        assert_(self.response['data']['action_type'], action_type)
        assert_(self.response['data']['reversal'], 'irreversible')
        assert_(self.response['data']['status'], 'reversed')

    def validate_inventory(self, after_cancel_room_inventory, inventory_request):
        after_reverse_cancel_room_inventory = inventory_request.get_room_wise_inventory(self.client, 200,
                                                                                        self.before_cancel_booking_response)
        if sanitize_blank(self.test_data['room_wise_inventory_changes_new']):
            for room_wise_inventory in json.loads(self.test_data['room_wise_inventory_changes_new']):
                if sanitize_blank(room_wise_inventory['inventory_change']) is None:
                    pass
                else:
                    room_stay_id = int(sanitize_blank(room_wise_inventory['room_stay_id']))
                    change_in_dates = sanitize_blank(room_wise_inventory['date']).split(',')
                    inventory_changes = sanitize_blank(room_wise_inventory['inventory_change']).split(',')
                    for change_in_date, inventory_change in zip(change_in_dates, inventory_changes):
                        date = str(return_date(int(change_in_date)))
                        room_stay_inventory_after_cancel = after_cancel_room_inventory[room_stay_id]
                        room_stay_inventory_after_reverse_cancel = after_reverse_cancel_room_inventory[room_stay_id]
                        for inventory in room_stay_inventory_after_reverse_cancel:
                            if inventory['date'] == date:
                                inventory['actual_count'] += int(inventory_change)
                    assert_(len(room_stay_inventory_after_cancel),
                            len(room_stay_inventory_after_reverse_cancel))
                    assert [i for i in room_stay_inventory_after_cancel if
                            i not in room_stay_inventory_after_reverse_cancel] == []
                    TestCase.assertCountEqual(self, room_stay_inventory_after_cancel,
                                              room_stay_inventory_after_reverse_cancel)

    def validate_booking_response(self):
        after_reverse_cancel_booking_response = \
            self.booking_request.get_booking_request(self.client, self.booking_request.booking_id, 200, self.user_type)[
                'data']
        print(after_reverse_cancel_booking_response)
        if self.before_cancel_booking_response == 'confirmed':
            assert_(after_reverse_cancel_booking_response['allowed_actions'], self.before_cancel_booking_response[
                'allowed_actions'])
        assert_(after_reverse_cancel_booking_response['actual_checkin_date'], self.before_cancel_booking_response[
            'actual_checkin_date'])
        assert_(after_reverse_cancel_booking_response['actual_checkout_date'], self.before_cancel_booking_response[
            'actual_checkout_date'])
        assert_(after_reverse_cancel_booking_response['bill_id'], self.before_cancel_booking_response['bill_id'])
        assert_(after_reverse_cancel_booking_response['booking_id'], self.before_cancel_booking_response['booking_id'])
        assert_(after_reverse_cancel_booking_response['booking_owner_id'], self.before_cancel_booking_response[
            'booking_owner_id'])
        assert_(after_reverse_cancel_booking_response['cancellation_datetime'], self.before_cancel_booking_response[
            'cancellation_datetime'])
        assert_(after_reverse_cancel_booking_response['cancellation_reason'], self.before_cancel_booking_response[
            'cancellation_reason'])
        assert_(after_reverse_cancel_booking_response['checkin_date'], self.before_cancel_booking_response[
            'checkin_date'])
        assert_(after_reverse_cancel_booking_response['checkout_date'], self.before_cancel_booking_response[
            'checkout_date'])
        assert_(after_reverse_cancel_booking_response['comments'], self.before_cancel_booking_response['comments'])
        assert_(after_reverse_cancel_booking_response['extra_information'], self.before_cancel_booking_response[
            'extra_information'])
        assert_(after_reverse_cancel_booking_response['hold_till'], self.before_cancel_booking_response['hold_till'])
        assert_(after_reverse_cancel_booking_response['hotel_id'], self.before_cancel_booking_response['hotel_id'])
        assert_(after_reverse_cancel_booking_response['reference_number'], self.before_cancel_booking_response[
            'reference_number'])
        booking_version = booking_repo().load(self.booking_request.booking_id).booking.version
        assert_(after_reverse_cancel_booking_response['version'], booking_version)
        assert_(after_reverse_cancel_booking_response['status'], self.before_cancel_booking_response['status'])
        assert_(after_reverse_cancel_booking_response['stay_start'], self.before_cancel_booking_response['stay_start'])
        assert_(after_reverse_cancel_booking_response['stay_end'], self.before_cancel_booking_response['stay_end'])
        assert_(len(after_reverse_cancel_booking_response['customers']),
                len(self.before_cancel_booking_response['customers']))
        assert [i for i in self.before_cancel_booking_response['customers'] if
                i not in after_reverse_cancel_booking_response['customers']] == []
        TestCase.assertCountEqual(self, self.before_cancel_booking_response['customers'],
                                  after_reverse_cancel_booking_response['customers'])
        assert_(self.before_cancel_booking_response['extra_information'],
                after_reverse_cancel_booking_response['extra_information'])
        if self.before_cancel_booking_response['hotel_id'] != HOTEL_ID[0]:
            for after_reverse_cancel_room_stay in after_reverse_cancel_booking_response['room_stays']:
                for room_rent in after_reverse_cancel_room_stay['room_rents']:
                    posttax_currency = room_rent['posttax_amount'].split(' ')[0]
                    assert_(posttax_currency, HOTEL_CURRENCY_MAP[self.before_cancel_booking_response['hotel_id']])

    def validate_bill(self):
        after_reverse_cancel_billing_response = \
            self.billing_request.get_bill_request(self.client, self.booking_request.bill_id, 200, self.user_type)[
                'data']
        hotel_id = self.before_cancel_booking_response['hotel_id']
        if "AddChargesAfterCheckout" not in self.test_data['TC_Description']:
            if self.before_cancel_booking_response['hotel_id'] != HOTEL_ID[0]:
                assert_(after_reverse_cancel_billing_response['net_paid_amount'].split(' ')[0],
                        HOTEL_CURRENCY_MAP[hotel_id])
                assert_(after_reverse_cancel_billing_response['net_payable'].split(' ')[0],
                        HOTEL_CURRENCY_MAP[hotel_id])
                assert_(after_reverse_cancel_billing_response['paid_amount'].split(' ')[0],
                        HOTEL_CURRENCY_MAP[hotel_id])
                for payment in after_reverse_cancel_billing_response['payments']:
                    assert_(payment['amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                assert_(after_reverse_cancel_billing_response['refund_amount'].split(' ')[0],
                        HOTEL_CURRENCY_MAP[hotel_id])
                assert_(after_reverse_cancel_billing_response['total_credit_posttax_amount'].split(' ')[0],
                        HOTEL_CURRENCY_MAP[hotel_id])
                assert_(after_reverse_cancel_billing_response['total_non_credit_invoiced_amount'].split(' ')[0],
                        HOTEL_CURRENCY_MAP[hotel_id])
                assert_(after_reverse_cancel_billing_response['total_non_credit_reversal_amount'].split(' ')[0],
                        HOTEL_CURRENCY_MAP[hotel_id])
                assert_(after_reverse_cancel_billing_response['total_posttax_amount'].split(' ')[0],
                        HOTEL_CURRENCY_MAP[hotel_id])
                assert_(after_reverse_cancel_billing_response['total_tax_amount'].split(' ')[0],
                        HOTEL_CURRENCY_MAP[hotel_id])
                assert_(after_reverse_cancel_billing_response['total_pretax_amount'].split(' ')[0],
                        HOTEL_CURRENCY_MAP[hotel_id])

            assert_(after_reverse_cancel_billing_response['bill_id'], self.before_cancel_billing_response['bill_id'])
            assert_(after_reverse_cancel_billing_response['net_paid_amount'],
                    self.before_cancel_billing_response['net_paid_amount'])
            assert_(after_reverse_cancel_billing_response['net_payable'],
                    self.before_cancel_billing_response['net_payable'])
            assert_(after_reverse_cancel_billing_response['paid_amount'],
                    self.before_cancel_billing_response['paid_amount'])
            assert_(after_reverse_cancel_billing_response['parent_reference_number'],
                    self.before_cancel_billing_response['parent_reference_number'])
            assert_(after_reverse_cancel_billing_response['payments'], self.before_cancel_billing_response['payments'])
            assert_(after_reverse_cancel_billing_response['refund_amount'],
                    self.before_cancel_billing_response['refund_amount'])
            assert_(after_reverse_cancel_billing_response['total_credit_posttax_amount'],
                    self.before_cancel_billing_response['total_credit_posttax_amount'])

            # assert_(after_reverse_cancel_billing_response['total_non_credit_invoiced_amount'],
            #         self.before_cancel_billing_response['total_non_credit_invoiced_amount'])
            # assert_(after_reverse_cancel_billing_response['total_non_credit_reversal_amount'],
            #         self.before_cancel_billing_response['total_non_credit_reversal_amount'])
            assert_(after_reverse_cancel_billing_response['total_posttax_amount'],
                    self.before_cancel_billing_response['total_posttax_amount'])
            assert_(after_reverse_cancel_billing_response['total_pretax_amount'],
                    self.before_cancel_billing_response['total_pretax_amount'])

            assert_(after_reverse_cancel_billing_response['total_tax_amount'],
                    self.before_cancel_billing_response['total_tax_amount'])
            billing_version = bill_repo().load(self.booking_request.bill_id).bill.version
            assert_(after_reverse_cancel_billing_response['version'], billing_version)
            assert [i for i in after_reverse_cancel_billing_response['payments'] if
                    i not in self.before_cancel_billing_response['payments']] == []
            if sanitize_blank(self.test_data['expected_bill_charges']):
                fetched_bill_charges = []
                for bill_charge in after_reverse_cancel_billing_response['charges']:
                    charge = {
                        'posttax_amount': bill_charge['posttax_amount'],
                        'status': bill_charge['status'],
                        'item_id': bill_charge['item']['item_id'],
                        'name': bill_charge['item']['name']
                    }
                    if self.before_cancel_booking_response['hotel_id'] != HOTEL_ID[0]:
                        assert_(bill_charge['posttax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                        assert_(bill_charge['pretax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                        assert_(bill_charge['tax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                        for tax_detail in bill_charge['tax_details']:
                            assert_(tax_detail['amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                        charge['posttax_amount'] = charge['posttax_amount'].split(' ')[1]

                    fetched_bill_charges.append(charge)
                # assert [i for i in fetched_bill_charges if
                #         i not in sanitize_blank(json.loads(self.test_data['expected_bill_charges']))] == []
                assert_(fetched_bill_charges,json.loads(self.test_data['expected_bill_charges']))

    def validate_credit_note(self):
        bill_credit_notes = self.billing_request.get_credit_notes(self.client,
                                                                  self.before_cancel_booking_response['bill_id'],
                                                                  200, self.user_type)['data']
        after_reverse_cancel_invoices = \
            self.booking_request.get_booking_invoices(self.client, self.before_cancel_booking_response['booking_id'],
                                                      200, user_type=self.user_type)['data']
        hotel_id = self.before_cancel_booking_response['hotel_id']
        for invoice in after_reverse_cancel_invoices:
            if self.before_cancel_booking_response['hotel_id'] != HOTEL_ID[0]:
                assert_(invoice['credit_note_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                assert_(invoice['credit_payable'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                assert_(invoice['posttax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                assert_(invoice['pretax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                assert_(invoice['tax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
            invoice_id = invoice['invoice_id']
            invoice_charges = invoice['invoice_charges']
            for credit_note in bill_credit_notes:
                if credit_note['credit_note_line_items'][0]['invoice_id'] == invoice_id:
                    if self.before_cancel_booking_response['hotel_id'] != HOTEL_ID[0]:
                        assert_(credit_note['posttax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                        assert_(credit_note['pretax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                        assert_(credit_note['tax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                        for tax_detail in credit_note['tax_details']:
                            assert_(tax_detail['amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                        for credit_note_line_item in credit_note['credit_note_line_items']:
                            assert_(credit_note_line_item['posttax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                            assert_(credit_note_line_item['pretax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                            assert_(credit_note_line_item['tax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                            for tax_detail in credit_note_line_item['tax_details']:
                                assert_(tax_detail['amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                    assert_(invoice['posttax_amount'], credit_note['posttax_amount'])
                    assert_(invoice['pretax_amount'], credit_note['pretax_amount'])
                    assert_(invoice['issued_by_type'], credit_note['issued_by_type'])
                    assert_(invoice['issued_to_type'], credit_note['issued_to_type'])
                    assert_(invoice['credit_note_amount'], credit_note['posttax_amount'])
                    assert_(len(invoice_charges), len(credit_note['credit_note_line_items']))
                    assert_(credit_note['status'], 'created')
                    for invoice_charge in invoice_charges:
                        for credit_note_line_item in credit_note['credit_note_line_items']:
                            if credit_note_line_item['invoice_charge_id'] == invoice_charge['invoice_charge_id']:
                                if self.before_cancel_booking_response['hotel_id'] != HOTEL_ID[0]:
                                    assert_(invoice_charge['posttax_amount'].split(' ')[0],
                                            HOTEL_CURRENCY_MAP[hotel_id])
                                    assert_(invoice_charge['pretax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                                    assert_(invoice_charge['tax_amount'].split(' ')[0], HOTEL_CURRENCY_MAP[hotel_id])
                                    if 'credit_note_generated_amount' in invoice_charge:
                                        assert_(invoice_charge['credit_note_generated_amount'].split(' ')[0],
                                                HOTEL_CURRENCY_MAP[hotel_id])
                                assert_(invoice_charge['posttax_amount'], credit_note_line_item['posttax_amount'])
                                assert_(invoice_charge['pretax_amount'], credit_note_line_item['pretax_amount'])
                                assert_(invoice_charge['tax_amount'], credit_note_line_item['tax_amount'])
                                assert_(invoice_charge['credit_note_generated_amount'],
                                        credit_note_line_item['posttax_amount'])
