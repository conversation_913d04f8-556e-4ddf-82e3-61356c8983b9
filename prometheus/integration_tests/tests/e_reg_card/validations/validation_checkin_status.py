from prometheus.integration_tests.requests.booking_requests import BookingRequests
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_test_data


class ValidationCheckinStatus:

    def __init__(self, client, response, test_case_id, booking_request):
        self.booking_request = booking_request
        self.response = response
        self.test_data = get_test_case_data(sheet_names.booking_action_sheet_name, test_case_id)[0]
        self.room_stay_data = get_test_case_data(sheet_names.add_room_stay_sheet_name,
                                                 self.test_data['room_stays'])
        self.room_stay_details = []
        for room_data in self.room_stay_data:
            self.guest_stay_data = get_test_case_data(sheet_names.add_guest_stay_sheet_name,
                                                      room_data['GuestStay'])
            self.guest_details = []
            for guest_data in self.guest_stay_data:
                self.guest_details.append(get_test_case_data(sheet_names.customer_data_sheet_name,
                                                             guest_data['guest_details'])[0])
            self.room_stay_details.append(self.guest_details)
        self.checkin_response_from_get_request = self.booking_request.get_booking_actions(client)

    def validate_checkin_status(self):
        self.validate_checkin_response_with_sheet()
        self.validate_checkin_response_with_get_response()

    def validate_checkin_response_with_sheet(self):
        room_stay_details = self.room_stay_details
        for room_stay_count in range(len(room_stay_details)):
            for guest_count in range(len(room_stay_details[room_stay_count])):
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count]['guest']['first_name'], room_stay_details[room_stay_count][guest_count]['first_name'])
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count]['guest']['last_name'], room_stay_details[room_stay_count][guest_count]['last_name'])
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count]['guest']['profile_type'], room_stay_details[room_stay_count][guest_count]['profile_type'])
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count]['guest']['gender'], room_stay_details[room_stay_count][guest_count]['gender'])
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count]['guest']['age'], int(room_stay_details[room_stay_count][guest_count]['age']))
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count]['guest']['email'], room_stay_details[room_stay_count][guest_count]['email'])
                if sanitize_test_data(room_stay_details[room_stay_count][guest_count]['eregcard_status']):
                    assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                                guest_count]['guest']['eregcard_status'], room_stay_details[room_stay_count][guest_count]['eregcard_status'])
                if sanitize_test_data(room_stay_details[room_stay_count][guest_count]['verifier_signature']):
                    assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count]['guest']['verifier_signature'], room_stay_details[room_stay_count][guest_count]['verifier_signature'])
                if sanitize_test_data(room_stay_details[room_stay_count][guest_count]['nationality']):
                    assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count]['guest']['nationality'], room_stay_details[room_stay_count][guest_count]['nationality'])
                if sanitize_test_data(room_stay_details[room_stay_count][guest_count]['verifier_signature']):
                    assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count]['guest']['image_url'], room_stay_details[room_stay_count][guest_count]['image_url'])
                if sanitize_test_data(room_stay_details[room_stay_count][guest_count]['is_primary']):
                    assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count]['guest']['is_primary'], sanitize_test_data(room_stay_details[room_stay_count][guest_count]['is_primary']))
        assert_(self.response['data']['status'], "created")
        assert_(self.response['data']['action_type'], "checkin")

    def validate_checkin_response_with_get_response(self):
        for checkin_get_response in self.checkin_response_from_get_request['data']:
            if checkin_get_response['action_id'] == self.response['data']['action_id']:
                valid_checkin_get_response = checkin_get_response
            else:
                raise Exception("This action_id doesn't exist " + str(self.response['data']['action_id']))

        for room_stay_count in range(len(self.room_stay_details)):
            for guest_count in range(len(self.room_stay_details[room_stay_count])):
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['first_name'],
                        valid_checkin_get_response['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['first_name'])
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['last_name'],
                        valid_checkin_get_response['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['last_name'])
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['profile_type'],
                        valid_checkin_get_response['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['profile_type'])
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['gender'],
                        valid_checkin_get_response['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['gender'])
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['age'],
                        valid_checkin_get_response['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['age'])
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['email'],
                        valid_checkin_get_response['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['email'])
                assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['first_name'],
                        valid_checkin_get_response['payload']['checkin']['room_stays'][room_stay_count]
                        ['guest_stays'][guest_count]['guest']['first_name'])
                if sanitize_test_data(self.room_stay_details[room_stay_count][guest_count]['eregcard_status']):
                    assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                                guest_count]['guest']['eregcard_status'], self.room_stay_details[room_stay_count][guest_count]['eregcard_status'])
                if sanitize_test_data(self.room_stay_details[room_stay_count][guest_count]['verifier_signature']):
                    assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count]['guest']['verifier_signature'], self.room_stay_details[room_stay_count][guest_count]['verifier_signature'])
                if sanitize_test_data(self.room_stay_details[room_stay_count][guest_count]['nationality']):
                    assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count]['guest']['nationality'], self.room_stay_details[room_stay_count][guest_count]['nationality'])
                if sanitize_test_data(self.room_stay_details[room_stay_count][guest_count]['verifier_signature']):
                    assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count]['guest']['image_url'], self.room_stay_details[room_stay_count][guest_count]['image_url'])
                if sanitize_test_data(self.room_stay_details[room_stay_count][guest_count]['is_primary']):
                    assert_(self.response['data']['payload']['checkin']['room_stays'][room_stay_count]['guest_stays'][
                            guest_count]['guest']['is_primary'], sanitize_test_data(self.room_stay_details[room_stay_count][guest_count]['is_primary']))

        assert_(self.response['data']['status'], valid_checkin_get_response['status'])
        assert_(self.response['data']['action_type'], valid_checkin_get_response['action_type'])
