from prometheus.integration_tests.requests.e_reg_card_requests import ERegCardRequests
from prometheus.integration_tests.config.sheet_names import create_e_reg_card_sheet_name, \
    guest_data_e_reg_card_sheet_name
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import assert_


class ValidationEditERegCard:

    def __init__(self, client, response, test_case_id, booking_id):
        self.response = response['data']
        self.e_reg_card_id = [response_data['e_reg_card_id'] for response_data in self.response]
        test_data = excel_utils.get_test_case_data(create_e_reg_card_sheet_name, test_case_id)[0]
        self.e_reg_card_data_from_sheet = excel_utils.get_test_case_data(guest_data_e_reg_card_sheet_name,
                                                                         test_data['Guest_Data'])
        self.e_reg_card_response_from_get_request = ERegCardRequests().get_e_reg_card_request(client, 200, booking_id)

    def validate_values(self):
        self.validate_patch_response_with_sheet()
        self.validate_get_response_with_patch_response()

    def validate_patch_response_with_sheet(self):
        for e_reg_card_data_sheet, e_reg_card_data_patch_response in zip(self.e_reg_card_data_from_sheet,
                                                                         self.response):
            assert_(e_reg_card_data_patch_response['employment_details']['company_name'],
                    e_reg_card_data_sheet['company_name'])
            assert_(e_reg_card_data_patch_response['id_proof']['id_number'],
                    e_reg_card_data_sheet['id_number'])
            assert_(e_reg_card_data_patch_response['id_proof']['id_proof_type'],
                    e_reg_card_data_sheet['id_proof_type'])
            assert_(e_reg_card_data_patch_response['id_proof']['issued_place'],
                    e_reg_card_data_sheet['issued_place'])
            assert_(e_reg_card_data_patch_response['id_proof']['id_proof_country_code'],
                    e_reg_card_data_sheet['id_proof_country_code'])
            assert_(e_reg_card_data_patch_response['is_primary'], e_reg_card_data_sheet['is_primary'])
            assert_(e_reg_card_data_patch_response['status'], e_reg_card_data_sheet['status'])
            assert_(e_reg_card_data_patch_response['travel_details']['arrival_from'],
                    e_reg_card_data_sheet['arrival_from'])
            assert_(e_reg_card_data_patch_response['travel_details']['next_destination'],
                    e_reg_card_data_sheet['next_destination'])
            assert_(e_reg_card_data_patch_response['travel_details']['destination_stay_duration'],
                    e_reg_card_data_sheet['destination_stay_duration'])
            assert_(e_reg_card_data_patch_response['travel_details']['visa']['destination_stay_duration'],
                    e_reg_card_data_sheet['visa_destination_stay_duration'])
            assert_(e_reg_card_data_patch_response['travel_details']['visa']['issued_place'],
                    e_reg_card_data_sheet['visa_issued_place'])
            assert_(e_reg_card_data_patch_response['travel_details']['visa']['registration_number'],
                    e_reg_card_data_sheet['visa_registration_number'])
            assert_(e_reg_card_data_patch_response['ups_user_id'], e_reg_card_data_sheet['ups_user_id'])

    def validate_get_response_with_patch_response(self):
        for guest_data_in_get_response in self.e_reg_card_response_from_get_request['data']:
            for guest_data_in_patch_response in self.response:
                if guest_data_in_get_response['e_reg_card_id'] == guest_data_in_patch_response['e_reg_card_id']:
                    assert_(guest_data_in_get_response['employment_details']['company_name'],
                            guest_data_in_patch_response['employment_details']['company_name'])
                    assert_(guest_data_in_get_response['id_proof']['id_number'],
                            guest_data_in_patch_response['id_proof']['id_number'])
                    assert_(guest_data_in_get_response['id_proof']['id_proof_type'],
                            guest_data_in_patch_response['id_proof']['id_proof_type'])
                    assert_(guest_data_in_get_response['id_proof']['issued_place'],
                            guest_data_in_patch_response['id_proof']['issued_place'])
                    assert_(guest_data_in_get_response['id_proof']['id_proof_country_code'],
                            guest_data_in_patch_response['id_proof']['id_proof_country_code'])
                    assert_(guest_data_in_get_response['is_primary'], guest_data_in_patch_response['is_primary'])
                    assert_(guest_data_in_get_response['status'], guest_data_in_patch_response['status'])
                    assert_(guest_data_in_get_response['travel_details']['arrival_from'],
                            guest_data_in_patch_response['travel_details']['arrival_from'])
                    assert_(guest_data_in_get_response['travel_details']['next_destination'],
                            guest_data_in_patch_response['travel_details']['next_destination'])
                    assert_(guest_data_in_get_response['travel_details']['destination_stay_duration'],
                            guest_data_in_patch_response['travel_details']['destination_stay_duration'])
                    assert_(guest_data_in_get_response['travel_details']['visa']['destination_stay_duration'],
                            guest_data_in_patch_response['travel_details']['visa']['destination_stay_duration'])
                    assert_(guest_data_in_get_response['travel_details']['visa']['issued_place'],
                            guest_data_in_patch_response['travel_details']['visa']['issued_place'])
                    assert_(guest_data_in_get_response['travel_details']['visa']['registration_number'],
                            guest_data_in_patch_response['travel_details']['visa']['registration_number'])
                    assert_(guest_data_in_get_response['ups_user_id'], guest_data_in_patch_response['ups_user_id'])
