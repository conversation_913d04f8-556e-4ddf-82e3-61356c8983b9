from datetime import date, timed<PERSON>ta
import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.house_status.validations.valdiation_current_house_status import \
    ValidationCurrentHouseStatus
from prometheus.integration_tests.utilities.common_utils import query_execute


class TestCurrentHouseStatus(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, ,error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, perform_night_audit, action_after_night_audit", [
            ("House_Status_01", 'Create a Booking and assign a room ',
             HOSUE_KEEPING_STATUS_CLEAN + SINGLE_BOOKING_01 +
             [{'id': "UpdateRoomStay_14_AssignTodayCheckinBooking", 'type': 'room_update'}], 200, None, "", "", "", "",
             False, "", False, False),
            ("House_Status_02", 'Create a Booking and check in the room',
             HOSUE_KEEPING_STATUS_CLEAN + SINGLE_BOOKING_CHECK_IN_01, 200, None, "", "", "", "", False, "", False,
             False),
            ("House_Status_03", 'Create a early checkout booking',
             HOSUE_KEEPING_STATUS_CLEAN + FULL_CHECKOUT_03, 200, None, "", "", "", "", False, "", False, False),
            ("House_Status_04", 'Create a booking and checkin a room which has already early checkout booking',
             HOSUE_KEEPING_STATUS_CLEAN + FULL_CHECKOUT_03 + SINGLE_BOOKING_CHECK_IN_01, 200, None, "", "", "", "",
             False, "", False, False),
            ("House_Status_05", 'Create a booking with one guest checked in while other guest has not checked in yet',
             HOSUE_KEEPING_STATUS_CLEAN + PART_BOOKING_CHECK_IN_01, 200, None, "", "", "", "", False, "", False, False),
            ("House_Status_06", 'Create a booking and assign a room which has already early checkout booking',
             HOSUE_KEEPING_STATUS_CLEAN + [{'id': 'Booking_01', 'type': 'booking'},
                                           {'id': 'checkinPost_77', 'type': 'check_in'},
                                           {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                           {'id': 'invoicePreview_01', 'type': 'preview_invoice'},
                                           {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment',
                                            'is_mock_rule_req': True},
                                           {'id': 'checkoutAction_04', 'type': 'checkout'}] + SINGLE_BOOKING_01 +
             [{'id': "UpdateRoomStay_14_AssignTodayCheckinBooking", 'type': 'room_update'}], 200, None, "", "", "", "",
             False, "", False, False),
            ("House_Status_07", 'Create a booking and assign a room, now change the assigned room',
             HOSUE_KEEPING_STATUS_CLEAN + SINGLE_BOOKING_01 +
             [{'id': "UpdateRoomStay_14_AssignTodayCheckinBooking", 'type': 'room_update'}] +
             [{'id': "UpdateRoomStay_15_Change_room", 'type': 'room_update', 'resource_version': 2}], 200, None, "", "",
             "", "", False, "", False, False),
            ("House_Status_08", 'Create a Booking and check in the room,  perform night audit',
             HOSUE_KEEPING_STATUS_CLEAN +
             [{'id': "Booking_112", 'type': 'booking'}, {'id': "checkinPost_78", 'type': 'check_in'}], 200, None, "",
             "", "", "", False, "", True, False),
            ("House_Status_09", 'Create a 2 day booking and perform night audit',
             HOSUE_KEEPING_STATUS_CLEAN +
             [{'id': "Booking_117", 'type': 'booking'}, {'id': "checkinPost_79", 'type': 'check_in'}], 200, None, "",
             "", "", "", False, "", True, False),
            ("House_Status_10",
             'Create a checked in booking and increase stay duration by changing checkout date from today',
             HOSUE_KEEPING_STATUS_CLEAN +
             [{'id': "Booking_112", 'type': 'booking'}, {'id': "checkinPost_78", 'type': 'check_in'}], 200, None, "",
             "", "", "", False, "", True, INCREASE_CHECKOUT_DATE),
            ("House_Status_11",
             'Create a checked in booking and decrease stay duration by changing checkout date from future',
             HOSUE_KEEPING_STATUS_CLEAN +
             [{'id': "Booking_117", 'type': 'booking'}, {'id': "checkinPost_79", 'type': 'check_in'}], 200, None, "",
             "", "", "", False, "", True, DECREASE_CHECKOUT_DATE),
            ("House_Status_12", 'Create a future date booking and reduce checkin date to today and assign a room',
             HOSUE_KEEPING_STATUS_CLEAN +
             [{'id': "Booking_27", 'type': 'booking'}, {'id': "UpdateRoomStay_82", 'type': 'room_update'},
              {'id': "UpdateRoomStay_09_AssignTodayCheckinBooking", 'type': 'room_update', 'resource_version': 2}], 200,
             None, "", "", "", "", False, "", False, False),
            ("House_Status_13", 'Create a future date booking and reduce checkin date to today and checkin a room',
             HOSUE_KEEPING_STATUS_CLEAN +
             [{'id': "Booking_27", 'type': 'booking'}, {'id': "UpdateRoomStay_82", 'type': 'room_update'},
              {'id': "checkinPost_01", 'type': 'check_in'}], 200, None, "", "", "", "", False, "", False, False),
            ("House_Status_14", 'Create a checked out booking',
             HOSUE_KEEPING_STATUS_CLEAN +
             [{'id': "Booking_112", 'type': 'booking'}, {'id': "checkinPost_78", 'type': 'check_in'}], 200, None, "",
             "", "", "", False, "", True, ACTION_FOR_CHECKOUT),
            ("House_Status_15", 'Create a checked out booking and assign a room',
             HOSUE_KEEPING_STATUS_CLEAN +
             [{'id': "Booking_112", 'type': 'booking'}, {'id': "checkinPost_78", 'type': 'check_in'}], 200, None, "",
             "", "", "", False, "", True, ACTION_FOR_CHECKOUT_ASSIGN_SAME_ROOM),
            ("House_Status_16", 'Create a checked out booking and check in a room',
             HOSUE_KEEPING_STATUS_CLEAN +
             [{'id': "Booking_112", 'type': 'booking'}, {'id': "checkinPost_78", 'type': 'check_in'}], 200, None, "",
             "", "", "", False, "", True, ACTION_FOR_CHECKOUT_CHECK_IN_SAME_ROOM),
            ("House_Status_17",
             'Create a booking which is expected to have checkout today and assign a same room for coming guest',
             HOSUE_KEEPING_STATUS_CLEAN +
             [{'id': "Booking_112", 'type': 'booking'}, {'id': "checkinPost_78", 'type': 'check_in'}], 200, None, "",
             "", "", "", False, "", True, SINGLE_BOOKING_ASSIGN_ROOM),
            ("House_Status_18",
             'Create a partial checkin room', HOSUE_KEEPING_STATUS_CLEAN +
             [{'id': "Booking_76", 'type': 'booking'}, {'id': "checkinPost_02", 'type': 'check_in'}], 200, None, "",
             "", "", "", False, "", False, False),
            ("House_Status_19",
             'Create a booking with one guest early checkout and other guest has checked in and check in date is equal '
             'to today', HOSUE_KEEPING_STATUS_CLEAN +
             [{'id': "Booking_76", 'type': 'booking'}, {'id': "checkinPost_03", 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreview_65', 'type': 'preview_invoice'}, {'id': 'checkoutAction_35', 'type': 'checkout'}],
             200, None, "", "", "", "", False, "", False, False),
            ("House_Status_20",
             'Create a booking with one guest checked out and other guest has not checked out yet',
             HOSUE_KEEPING_STATUS_CLEAN +
             [{'id': "Booking_118", 'type': 'booking'}, {'id': "checkinPost_03", 'type': 'check_in'}],
             200, None, "", "", "", "", False, "", True, PART_CHECKOUT_BOOKING),
            ("House_Status_21",
             'Create a booking with one guest early checkout and other guest has checked in and check in date is less '
             'than today and checkout date greater than today', HOSUE_KEEPING_STATUS_CLEAN +
             [{'id': "Booking_119", 'type': 'booking'}, {'id': "checkinPost_03", 'type': 'check_in'}],
             200, None, "", "", "", "", False, "", True, PART_CHECKOUT_BOOKING_WITH_POSTED_CHARGE),
            ("House_Status_22", 'Create a checked in booking and do reverse check in', HOSUE_KEEPING_STATUS_CLEAN +
             SINGLE_BOOKING_CHECK_IN_01 + [{'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "",
             False, False),
            ("House_Status_23", 'Create a checked out booking and do reverse checkout where checked in date is today',
             HOSUE_KEEPING_STATUS_CLEAN + FULL_CHECKOUT_03 + [{'type': 'delete_booking_action'}], 200, None, "", "", "",
             "", False, "", False, False),
            ("House_Status_24", 'Create a checked out booking and do reverse checkout where checked out date is today',
             HOSUE_KEEPING_STATUS_CLEAN +
             [{'id': "Booking_112", 'type': 'booking'}, {'id': "checkinPost_78", 'type': 'check_in'}], 200, None, "",
             "", "", "", False, "", True, ACTION_FOR_REVERSE_CHECKOUT),
            ("House_Status_25", 'Create a checked out booking and do reverse checkout where checked out date is today',
             HOSUE_KEEPING_STATUS_CLEAN +
             [{'id': "Booking_117", 'type': 'booking'}, {'id': "checkinPost_78", 'type': 'check_in'}], 200, None, "",
             "", "", "", False, "", True, ACTION_FOR_REVERSE_CHECKOUT_WITH_POSTED_CHARGE),
        ])
    @pytest.mark.regression
    def test_current_house_status(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                                  error_code, error_message, dev_message, error_payload, skip_case, skip_message,
                                  perform_night_audit, action_after_night_audit):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id)
                self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
                if action_after_night_audit:
                    self.common_request_caller(client_, action_after_night_audit, hotel_id)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.house_status_request.get_current_house_status(client_, status_code, hotel_id, user_type)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_)
        else:
            assert False, "Response status code is not matching"

    def validation(self, response, test_case_id, client_):
        validation = ValidationCurrentHouseStatus(client_, test_case_id, response, self.booking_request,
                                                  self.booking_request.booking_id)
        validation.validate_response()
