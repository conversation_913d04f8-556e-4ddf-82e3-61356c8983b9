import pytest

from prometheus.integration_tests.resources import db_queries
from datetime import date, timedelta
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.house_status.validations.validation_arrival_and_departure import \
    ValidationArrivalDeparture
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.utilities.common_utils import query_execute


class TestArrivalDeparture(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, ,error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message, perform_night_audit, action_after_night_audit", [
            ("Arrival_Departure_01", 'Create a booking', SINGLE_BOOKING_01, 200, None, "", "", "", "", False, "",
             False, False),
            ("Arrival_Departure_02", 'Create a booking and assign a room', SINGLE_BOOKING_01 +
             [{'id': "UpdateRoomStay_09_AssignTodayCheckinBooking", 'type': 'room_update'}], 200, None, "", "", "", "",
             False, "", False, False),
            ("Arrival_Departure_03", 'Create a checked in booking', SINGLE_BOOKING_CHECK_IN_01, 200, None, "", "", "",
             "", False, "", False, False),
            ("Arrival_Departure_04", 'Create a early checked out booking', FULL_CHECKOUT_03, 200, None, "", "", "", "",
             False, "", False, False),
            ("Arrival_Departure_05", 'Create a Due Out booking',
             [{'id': "Booking_112", 'type': 'booking'}, {'id': "checkinPost_78", 'type': 'check_in'}], 200, None, "",
             "", "", "", False, "", True, False),
            ("Arrival_Departure_06", 'Create a checked out booking',
             [{'id': "Booking_112", 'type': 'booking'}, {'id': "checkinPost_78", 'type': 'check_in'}],
             200, None, "", "", "", "", False, "", True, ACTION_FOR_CHECKOUT),
            ("Arrival_Departure_07", 'Create a booking with zero price', SINGLE_BOOKING_WITH_ZERO_PRICE, 200, None, "",
             "", "", "", False, "", False, False),
            ("Arrival_Departure_08", 'Create a booking and assign a room with zero price',
             SINGLE_BOOKING_WITH_ZERO_PRICE +
             [{'id': "UpdateRoomStay_09_AssignTodayCheckinBooking", 'type': 'room_update'}], 200, None, "", "", "", "",
             False, "", False, False),
            ("Arrival_Departure_09", 'Create a checked in booking with zero price', SINGLE_BOOKING_WITH_ZERO_PRICE +
             [{'id': "checkinPost_01", 'type': 'check_in'}], 200, None, "", "", "", "", False, "", False, False),
            ("Arrival_Departure_10", 'Create a early checked out booking with zero price',
             SINGLE_BOOKING_WITH_ZERO_PRICE +
             [{'id': 'checkinPost_01', 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreview_01', 'type': 'preview_invoice'},
              {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment', 'is_mock_rule_req': True},
              {'id': 'checkoutAction_04', 'type': 'checkout'}], 200, None, "", "", "", "", False, "", False, False),
            ("Arrival_Departure_11", 'Create a Due Out booking with zero price',
             [{'id': "Booking_120", 'type': 'booking'}, {'id': "checkinPost_78", 'type': 'check_in'}], 200, None, "",
             "", "", "", False, "", True, False),
            ("Arrival_Departure_12", 'Create a checked out booking with zero price',
             [{'id': "Booking_120", 'type': 'booking'}, {'id': "checkinPost_78", 'type': 'check_in'}], 200, None, "",
             "", "", "", False, "", True, ACTION_FOR_CHECKOUT),
            ("Arrival_Departure_13", 'Create a booking with HSE rate plan', Booking_With_Inclusion01, 200, None, "", "",
             "", "", False, "", False, False),
            ("Arrival_Departure_14", 'Create a booking and assign a room with HSE rate plan',
             [{'id': "Booking_01", 'type': 'booking', 'enable_rate_manager': True},
              {'id': "UpdateRoomStay_14_AssignTodayCheckinBooking", 'type': 'room_update'}], 200, None, "", "", "", "",
             False, "", False, False),
            ("Arrival_Departure_15", 'Create a checked in booking with HSE rate plan',
             [{'id': "Booking_01", 'type': 'booking', 'enable_rate_manager': True},
              {'id': "checkinPost_01", 'type': 'check_in'}], 200, None, "", "", "", "", False, "", False, False),
            ("Arrival_Departure_16", 'Create a early checked out booking with HSE rate plan',
             [{'id': 'Booking_01', 'type': 'booking', 'enable_rate_manager': True},
              {'id': 'checkinPost_01', 'type': 'check_in'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': 'invoicePreview_01', 'type': 'preview_invoice'},
              {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment', 'is_mock_rule_req': True},
              {'id': 'checkoutAction_04', 'type': 'checkout'}], 200, None, "", "", "", "", False, "", False, False),
            ("Arrival_Departure_17", 'Create a Due Out booking with HSE rate plan',
             [{'id': "Booking_112", 'type': 'booking', 'enable_rate_manager': True},
              {'id': "checkinPost_78", 'type': 'check_in'}], 200, None, "", "", "", "", False, "", True, False),
            ("Arrival_Departure_18", 'Create a checked out booking with HSE rate plan',
             [{'id': "Booking_112", 'type': 'booking', 'enable_rate_manager': True},
              {'id': "checkinPost_78", 'type': 'check_in'}], 200, None, "", "", "", "", False, "", True,
             ACTION_FOR_CHECKOUT),
            ("Arrival_Departure_19", 'Create a cancelled booking',
             [{'id': "Booking_cancelAction_01", 'type': 'booking'}, {'id': "CancelAction_14", 'type': 'cancel'}], 200,
             None, "", "", "", "", False, "", False, False),
            ("Arrival_Departure_20", 'Create a multi room booking with one room cancelled',
             [{'id': "Booking_04", 'type': 'booking'}, {'id': "CancelAction_39", 'type': 'cancel'}], 200, None, "", "",
             "", "", False, "", False, False),
            ("Arrival_Departure_21", 'Create a multi room booking', [{'id': "Booking_04", 'type': 'booking'}], 200,
             None, "", "", "", "", False, "", False, False),
            ("Arrival_Departure_22", 'Create a multi room booking with one room checked in',
             [{'id': "Booking_04", 'type': 'booking'}, {'id': "checkinPost_07", 'type': 'check_in'}], 200, None, "", "",
             "", "", False, "", False, False),
            ("Arrival_Departure_23", 'Create a two room booking and change stay date of one room',
             [{'id': "Booking_04", 'type': 'booking'}, {'id': "UpdateRoomStay_32", 'type': 'room_update'}], 200, None,
             "", "", "", "", False, "", False, False),
            ("Arrival_Departure_24", 'Create a checked in booking and do reverse check in', SINGLE_BOOKING_CHECK_IN_01
             + [{'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", False, False),
            ("Arrival_Departure_25",
             'Create a checked out booking and do reverse checkout where checked in date is today', FULL_CHECKOUT_03 +
             [{'type': 'delete_booking_action'}], 200, None, "", "", "", "", False, "", False, False),
            ("Arrival_Departure_26",
             'Create a checked out booking and do reverse checkout where checked out date is today',
             [{'id': "Booking_112", 'type': 'booking'}, {'id': "checkinPost_78", 'type': 'check_in'}], 200, None, "",
             "", "", "", False, "", True, ACTION_FOR_REVERSE_CHECKOUT),
        ])
    @pytest.mark.regression
    def test_arrival_departure(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                               error_code, error_message, dev_message, error_payload, skip_case, skip_message,
                               perform_night_audit, action_after_night_audit):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today()-timedelta(days=1), hotel_id))
            if previous_actions:
                self.common_request_caller(client_, previous_actions, hotel_id)
                self.booking_request.perform_night_audit_test(client_, 200, hotel_id, user_type)
                if action_after_night_audit:
                    self.common_request_caller(client_, action_after_night_audit, hotel_id)
        elif previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.house_status_request.get_arrival_departure_details(client_, status_code, hotel_id, user_type)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id):
        validation = ValidationArrivalDeparture(test_case_id, response)
        validation.validate_response()
