import pytest

from prometheus.integration_tests.config.common_config import ERROR_CODES, HOTEL_ID, SUCCESS_CODES
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.credit_notes.validations.validation_upload_credit_note_template import \
    UploadCreditNotesValidation
from prometheus.integration_tests.utilities.common_utils import query_execute


class TestCreditNotes(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, dev_message, "
        "error_payload, skip_case, action_during_lock_invoice",
        [
            ("UploadCreditNoteTemplate_01", "Generate and upload credit note template for a given credit note",
             Credit_Shell_01, 200, "", "", "", "", False, None),
            ("UploadCreditNoteTemplate_02",
             "Generate and upload credit note template for a given credit note after adding add_on", Credit_Shell_05,
             200, "", "", "", "", True, None),
            ("UploadCreditNoteTemplate_03", "Generate and upload a credit note template for a given credit note",
             Credit_Shell_01, 200, "", "", "", "", False, None),
            ("UploadCreditNoteTemplate_04", "Generate and upload credit notes after adding add_on", Credit_Shell_05,
             200, "", "", "", "", True, None),
            ("UploadCreditNoteTemplate_05", "Generate and upload credit note template for a b2b booking",
             Credit_Shell_03, 200, "", "", "", "", True, None),
            ("UploadCreditNoteTemplate_06",
             "Generate and upload credit note template for a b2b booking after adding addon", Credit_Shell_04, 200, "",
             "", "", "", True, None),
            ("UploadCreditNoteTemplate_07",
             "Credit Note Template for a room having different checkout dates using reissue",
             [{'id': "booking_checkout_146", 'type': 'booking_v2'},
              {'id': "checkin_preview_invoice_137", 'type': 'checkin_v2'},
              {'id': "invoicePreview_checkout_130_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentV2_checkout_125", 'type': 'add_payment_v2'},
              {'id': "CheckoutV2_01", 'type': 'checkout_v2'}, {'type': 'get_bill_invoices'},
              {'id': 'Modify_Locked_Invoice_01', 'type': 'modify_locked_invoice'}], 200, "", "", "", "", False, None),
            ("UploadCreditNoteTemplate_08",
             "Credit Note Template for a room having different checkout dates using reverse checkout",
             [{'id': "booking_checkout_146", 'type': 'booking_v2'},
              {'id': "checkin_preview_invoice_137", 'type': 'checkin_v2'},
              {'id': "invoicePreview_checkout_130_01", 'type': 'preview_invoice'},
              {'id': "AddPaymentV2_checkout_125", 'type': 'add_payment_v2'},
              {'id': "CheckoutV2_01", 'type': 'checkout_v2'}], 200, "", "", "", "", False,
             [{'id': 'Delete_Checkout_01', 'type': 'reverse_checkout'}]),
         ])
    @pytest.mark.regression
    def test_credit_notes(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                          error_code, dev_message, error_payload, skip_case, action_during_lock_invoice):
        if skip_case:
            pytest.skip()
        hotel_id = HOTEL_ID[0]
        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        if action_during_lock_invoice:
            query_execute(db_queries.UPDATE_INVOICE_STATUS_TO_LOCKED)
            self.common_request_caller(client_, action_during_lock_invoice, hotel_id)
        self.credit_note_template_request.get_credit_notes_v2(client_, 200, self.booking_request.bill_id)
        response = self.credit_note_template_request.upload_credit_note_template(client_, 200,
                                                                                 self.booking_request.bill_id,
                                                                                 self.credit_note_template_request.
                                                                                 credit_note_ids[0])
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request, self.billing_request, hotel_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, response, test_case_id, booking_request, billing_request, hotel_id):
        validation = UploadCreditNotesValidation(response, test_case_id)
        validation.validate_response(client, booking_request, billing_request, hotel_id)
