from prometheus.integration_tests.config.common_config import CLUBBED_TAX_CONFIG, RESELLER_CONFIG

#################### Bookings ###################

SINGLE_BOOKING_01 = [{'id': "Booking_01", 'type': 'booking'}]
SINGLE_WALK_BOOKING_V2_01 = [{'id': "booking_01", 'type': 'booking_v2'}]
SINGLE_WALK_BOOKING_V2_02 = [{'id': "booking_01_different_ref", 'type': 'booking_v2'}]
SINGLE_ROOM_OTA_BOOKING_V2_01 = [{'id': "booking_212", 'type': 'booking_v2'}]
SINGLE_ROOM_TA_BOOKING_V2_01 = [{'id': "booking_217", 'type': 'booking_v2'}]
SINGLE_ROOM_B2B_TA_BOOKING_V2_01 = [{'id': "booking_226", 'type': 'booking_v2'}]
MULTIPLE_ROOM_OTA_BOOKING_V2_01 = [{'id': "booking_232", 'type': 'booking_v2'}]
MULTIPLE_DAY_OTA_BOOKING_V2_01 = [{'id': "booking_233", 'type': 'booking_v2'}]
FUTURE_SINGLE_ROOM_OTA_BOOKING_V2_01 = [{'id': "booking_236", 'type': 'booking_v2'}]
FUTURE_WALKIN_BOOKING_V2_01 = [{'id': 'booking_01_without_booking_future', 'type': 'booking_v2'}]
MULTIPLE_DAYS_WALKIN_BOOKING_V2 = [{'id': "booking_05", 'type': 'booking_v2'}]
TRAVEL_AGENT_SINGLE_ROOM_BOOKING_V2 = [{'id': 'booking_156', 'type': 'booking_v2'}]
FUTURE_WALK_BOOKING_V2_01 = [{'id': "booking_10", 'type': 'booking_v2'}]
FUTURE_WALK_BOOKING_V2_01_2_days = [{'id': "booking_10_future_2_days", 'type': 'booking_v2'}]
FUTURE_WALK_BOOKING_V2_01_WITHOUT_RATE_PLAN = [{'id': "booking_86", 'type': 'booking_v2'}]
FUTURE_WALK_BOOKING_V2_01_2_days_WITHOUT_RATE_PLAN = [{'id': "booking_86_future_2_days", 'type': 'booking_v2'}]
MULTIPLE_ROOMS_DIFFERENT_GUEST_WALKIN_BOOKING_V2 = [{'id': "booking_19_different_guest_01", 'type': 'booking_v2'}]
SINGLE_PAY_AFTER_CHECKOUT_WALK_BOOKING_V2_01 = [{'id': "booking_03", 'type': 'booking_v2'}]
SINGLE_PAY_AFTER_CHECKOUT_B2B_BOOKING_V2_01 = [{'id': "booking_29_credit", 'type': 'booking_v2'}]
SINGLE_PAY_AT_CHECKOUT_BILL_TO_COMPANY_B2B_BOOKING_V2_01 = [{'id': "booking_48", 'type': 'booking_v2'}]
WALK_IN_ONE_ROOM_BOOKING_CHECK_IN_V2 = [{'id': "booking_01", 'type': 'booking_v2'},
                                        {'id': "checkin_01", 'type': 'checkin_v2'}]
MULTIPLE_ROOM_CREDIT_NON_CREDIT_BOOKING_V2_01 = [{'id': "credit_non_credit_booking_01", 'type': 'booking_v2'}]
MULTIPLE_ROOM_CREDIT_NON_CREDIT_BOOKING_V2_02 = [{'id': "credit_non_credit_booking_01", 'type': 'booking_v2'},
                                                 {'id': "Create_Expense_32", 'type': 'expense'}]
PARTIAL_CHECK_IN_BOOKING_V2 = [{'id': "booking_135_checkin_06", 'type': 'booking_v2'},
                               {'id': "checkin_06", 'type': 'checkin_v2'}]
PARTIAL_CHECKIN_OTA_BOOKING_V2 = [{'id': "booking_232", 'type': 'booking_v2'},
                                  {'id': "checkin_06", 'type': 'checkin_v2'}]
CANCELLED_BOOKING_V2 = [{'id': "booking_01", 'type': 'booking_v2'},
                        {'id': "cancel_booking_01", 'type': 'mark_cancel'}]
NO_SHOW_SINGLE_DAY_BOOKING = [{'id': "booking_01_no_show", 'type': 'booking_v2'}]
NO_SHOW_MULTIPLE_DAY_BOOKING = [{'id': "booking_02_no_show", 'type': 'booking_v2'}]
NO_SHOW_DIFFERENT_ROOM_STAY_DATE_BOOKING = [{'id': "booking_09_no_show", 'type': 'booking_v2'}]
NO_SHOW_SINGLE_DAY_MULTIPLE_ROOM_BOOKING = [{'id': "booking_03_no_show", 'type': 'booking_v2'}]
NO_SHOW_SINGLE_DAY_MULTIPLE_ROOM_BOOKING_V2 = [{'id': "booking_03_no_show_diff", 'type': 'booking_v2'},
                                               {'id': "create_booking_payment_35", 'type': 'add_payment_v2'}]
NO_SHOW_SINGLE_DAY_MULTIPLE_ROOM_BOOKING_V3 = [{'id': "booking_03_no_show", 'type': 'booking_v2'},
                                            {'id': "create_booking_payment_35", 'type': 'add_payment_v2'}]
CANCEL_SINGLE_DAY_MULTIPLE_ROOM_BOOKING_V1 = [{'id': "booking_01_with_pg_BE_3rooms", 'type': 'booking_v2'},
                                               {'id': "create_booking_payment_35", 'type': 'add_payment_v2'}]
CANCEL_SINGLE_DAY_MULTIPLE_ROOM_BOOKING_V2 = [{'id': "booking_01_with_pg_BE_multiple_rooms", 'type': 'booking_v2'},
                                               {'id': "create_booking_payment_35", 'type': 'add_payment_v2'}]

NO_SHOW_SINGLE_DAY_THREE_ROOM_BOOKING = [{'id': "booking_08_no_show", 'type': 'booking_v2'}]
NO_SHOW_MULTIPLE_DAY_MULTIPLE_ROOM_BOOKING = [{'id': "booking_04_no_show", 'type': 'booking_v2'}]
NO_SHOW_SINGLE_DAY_SINGLE_ROOM_MULTIPLE_GUEST_BOOKING = [{'id': "booking_05_no_show", 'type': 'booking_v2'}]
NO_SHOW_MULTIPLE_DAY_MULTIPLE_ROOM_MULTIPLE_GUEST_BOOKING = [{'id': "booking_06_no_show", 'type': 'booking_v2'}]
NO_SHOW_SINGLE_DAY_SINGLE_ROOM_THREE_GUEST_BOOKING = [{'id': "booking_07_no_show", 'type': 'booking_v2'}]
NO_SHOW_MARKED_BOOKING = [{'id': "booking_01_no_show", 'type': 'booking_v2'},
                          {'id': "MarkNoShow_01", 'type': 'mark_no_show'}]
NO_SHOW_MARKED_GUEST = [{'id': "booking_05_no_show", 'type': 'booking_v2'},
                        {'id': "MarkNoShow_17", 'type': 'mark_no_show'}]
NO_SHOW_MARKED_ROOM = [{'id': "booking_03_no_show", 'type': 'booking_v2'},
                       {'id': "MarkNoShow_25", 'type': 'mark_no_show'}]
NO_SHOW_CHECK_IN_BOOKING = [{'id': "booking_01_no_show", 'type': 'booking_v2'},
                            {'id': "no_show_checkin_01", 'type': 'checkin_v2'}]
NO_SHOW_PARTIAL_CHECK_IN_BOOKING = [{'id': "booking_03_no_show", 'type': 'booking_v2'},
                                    {'id': "no_show_checkin_02", 'type': 'checkin_v2'}]
NO_SHOW_PARTIAL_CHECK_IN_ROOM = [{'id': "booking_05_no_show", 'type': 'booking_v2'},
                                 {'id': "no_show_checkin_03", 'type': 'checkin_v2'}]
NO_SHOW_CHECK_IN_ALL_GUEST_IN_ROOM = [{'id': "booking_05_no_show", 'type': 'booking_v2'},
                                      {'id': "no_show_checkin_04", 'type': 'checkin_v2'}]
NO_SHOW_CANCELLED_BOOKING = [{'id': "booking_01_no_show", 'type': 'booking_v2'},
                             {'id': "no_show_cancel_booking_01", 'type': 'mark_cancel'}]
NO_SHOW_CANCELLED_ROOM = [{'id': "booking_03_no_show", 'type': 'booking_v2'},
                          {'id': "no_show_cancel_booking_02", 'type': 'mark_cancel'}]

SINGLE_BOOKING_01_USING_V2 = [{'id': "Booking_01", 'type': 'booking', 'is_booking_v2': True}]  # RS1 Acacia (0,1)
SINGLE_BOOKING_01_V2_WITHOUT_RATE_PLAN = [{'id': "booking_77", 'type': 'booking_v2'}]
SINGLE_BOOKING_01_V2_WITHOUT_RATE_PLAN_INCLUSION = [{'id': "booking_01_without_inclusions", 'type': 'booking_v2'}]
SINGLE_BOOKING_V2_WITH_TWO_GUEST = [{'id': "booking_19_01_checkin_04", 'type': 'booking_v2'}]
SINGLE_BOOKING_V2_WITH_TWO_GUEST_WITHOUT_RATE_PLAN = [{'id': "booking_99", 'type': 'booking_v2'}]
MULTIPLE_ROOM_MULTIPLE_GUEST_BOOKING_V2 = [{'id': "booking_19", 'type': 'booking_v2'}]
MULTIPLE_ROOM_MULTIPLE_GUEST_BOOKING_V2_WITHOUT_RATE_PLAN = [{'id': "booking_95", 'type': 'booking_v2'}]
SINGLE_BOOKING_WITH_GUEST_DIFF_CHECKIN_DATES_V2 = [{'id': "booking_06_diff_guest_stay_dates", 'type': 'booking_v2'}]
SINGLE_BOOKING_WITH_GUEST_DIFF_CHECKIN_DATES_V2_WITHOUT_RATE_PLAN = [{'id': "booking_83_diff_guest_stay_dates",
                                                                      'type': 'booking_v2'}]
SINGLE_BOOKING_WITH_GUEST_DIFF_CHECKOUT_DATES_V2 = [{'id': "booking_06_guest_diff_checkout_dates_future",
                                                     'type': 'booking_v2'}]
SINGLE_BOOKING_WITH_GUEST_DIFF_CHECKOUT_DATES_V2_WITHOUT_RATE_PLAN = \
    [{'id': "booking_83_guest_diff_checkout_dates_future", 'type': 'booking_v2'}]
SINGLE_BOOKING_02 = [{'id': "Booking_02", 'type': 'booking'}]  # RS1 Maple (0,2)
SINGLE_BOOKING_WITH_ZERO_PRICE = [{'id': "Booking_113", 'type': 'booking'}]
SINGLE_B2B_BOOKING_01 = [{'id': "Booking_11", 'type': 'booking'}]
SINGLE_B2B_BOOKING_01_USING_V2 = [{'id': "booking_27", 'is_booking_v2': True, 'type': 'booking_v2'}]
SINGLE_B2B_BOOKING_02_USING_V2 = [{'id': "booking_27_diff_ref", 'type': 'booking_v2'}]
SINGLE_B2B_BOOKING_01_WITH_TA_DETAILS_USING_V2 = [{'id': "booking_48", 'is_booking_v2': True, 'type': 'booking_v2'}]
MULTIPLE_DAYS_BOOKING = [{'id': "Booking_05", 'type': 'booking'}]
SINGLE_BOOKING_03 = [{'id': "Booking_12", 'type': 'booking'}]
MULTIPLE_ROOM_BOOKING_01 = [{'id': "Booking_04", 'type': 'booking'}]
MULTIPLE_ROOM_BOOKING_01_USING_V2 = [{'id': "Booking_04", 'type': 'booking', 'is_booking_v2': True}]
MULTIPLE_ROOM_BOOKING_02 = [{'id': "Booking_114", 'type': 'booking'}]  # RS1 Acacia (0,1) RS2 Acacia (0,1) B2B booking
MULTIPLE_ROOM_BOOKING_03 = [{'id': "Booking_114", 'type': 'booking'}, {'id': "Create_Expense_32", 'type': 'expense'}]
FUTURE_BOOKING_01 = [{'id': "Booking_27", 'type': 'booking'}]  # RS1 Maple (0,1) RS2 Acacia (0,1)
KERALA_CESS_BOOKING_01 = [{'id': "Booking_01", 'type': 'booking', 'kerala_cess': True}]  # RS1 Acacia (0,1); Walkin
KERALA_CESS_BOOKING_02 = [{'id': "Booking_03", 'type': 'booking', 'kerala_cess': True}]  # RS1 Acacia (0,1); Direct
KERALA_CESS_BOOKING_03 = [{'id': "Booking_04", 'type': 'booking', 'kerala_cess': True}]  # RS1 Acacia (0,1); Ota

REVERSE_BOOKING_01 = [{'id': "booking_01", 'type': 'booking_v2'}, {'id': 'checkin_01', 'type': 'checkin_v2'},
                      {'id': 'ReverseBookingAction_01', 'type': 'delete_booking_action'}]
REVERSE_BOOKING_B2B = [{'id': "booking_27", 'type': 'booking_v2'}, {'id': 'checkin_01', 'type': 'checkin_v2'},
                       {'id': 'ReverseBookingAction_01', 'type': 'delete_booking_action'}]
SINGLE_DAY_B2B_BOOKING_01 = [{'id': "Booking_11", 'type': 'booking'}]
SINGLE_WALK_BOOKING_V2_01_SLAB_BASED = [{'id': "booking_207", 'type': 'booking_v2',
                                         'extras': {"has_slab_based_taxation": True}}]
SINGLE_WALK_BOOKING_V2_02_SLAB_BASED = [{'id': "booking_01", 'type': 'booking_v2',
                                         'extras': {"has_slab_based_taxation": True}}]
SINGLE_WALK_BOOKING_V2_01_CLUBBED_CHARGE = [{'id': "booking_206", 'type': 'booking_v2', 'extras':
    {"hotel_level_config": CLUBBED_TAX_CONFIG}}]
SINGLE_WALK_BOOKING_V2_01_CLUBBED_AND_SLAB_BASED = [{'id': "booking_205", 'type': 'booking_v2',
                                                     'extras': {"has_slab_based_taxation": True,
                                                                "hotel_level_config": CLUBBED_TAX_CONFIG}}]
MULTIPLE_GUEST_WALK_BOOKING_V2_01_SLAB_BASED = [{'id': "booking_19_01_checkin_04", 'type': 'booking_v2',
                                                 'extras': {"has_slab_based_taxation": True}}]
MULTIPLE_GUEST_WALK_BOOKING_V2_02_SLAB_BASED = [{'id': "booking_19_slab_based", 'type': 'booking_v2',
                                                 'extras': {"has_slab_based_taxation": True}}]
MULTIPLE_GUEST_WALK_BOOKING_V2_01_CLUBBED_CHARGE = [{'id': "booking_19_01_checkin_04", 'type': 'booking_v2', 'extras':
    {"hotel_level_config": CLUBBED_TAX_CONFIG}}]
MULTIPLE_GUEST_WALK_BOOKING_V2_01_CLUBBED_AND_SLAB_BASED = [{'id': "booking_19_slab_based", 'type': 'booking_v2',
                                                             'extras': {"hotel_level_config": CLUBBED_TAX_CONFIG}}]

#################### Inventories ###################

CREATE_BOOKING_WITH_MULTIPLE_SAME_ROOM_TYPES = [{'id': "booking_04", 'type': 'booking_v2'}]
CREATE_BOOKING_WITH_MULTIPLE_DIFFERENT_ROOM_TYPES = [{'id': "booking_04_01", 'type': 'booking_v2'}]
CREATE_BOOKING_WITH_MULTIPLE_DIFFERENT_ROOM_TYPES_AND_ADD_ROOMS = [{'id': "booking_04_01", 'type': 'booking_v2'},
                                                                   {'id': "AddMultipleRoom_01",
                                                                    'type': 'add_multiple_room'}]
CREATE_BOOKING_WITH_MULTIPLE_DIFFERENT_ROOM_TYPES_AND_REMOVE_ROOMS = [{'id': "booking_08", 'type': 'booking_v2'},
                                                                      {'id': "cancel_room_04", 'type': 'mark_cancel'}]
CREATE_BOOKING_WITH_MULTIPLE_DIFFERENT_ROOM_TYPES_AND_ADD_REMOVE_ROOM = [{'id': "booking_08", 'type': 'booking_v2'},
                                                                         {'id': "cancel_room_04",
                                                                          'type': 'mark_cancel'},
                                                                         {'id': "AddMultipleRoom_01",
                                                                          'type': 'add_multiple_room'}]
CREATE_BOOKING_WITH_MULTIPLE_DIFFERENT_ROOM_TYPES_AND_REVERSE_CANCEL_BOOKING = [
    {'id': "booking_08", 'type': 'booking_v2'}, {'id': 'CancelAction_15', 'type': 'cancel'},
    {'id': 'ReverseCancelAction_02', 'type': 'delete_booking_action'}]

#################### Checkout ###################

PART_EARLY_CHECKOUT_01 = [{'id': "Booking_42_FullPayment_multipleRooms", 'type': 'booking'},  # Guest(8,9) checkout RS1
                          {'id': 'checkinBookingStatus_38', 'type': 'check_in'},  # 2 room-stays with 3 guest each
                          {'id': 'invoicePreview_53', 'type': 'preview_invoice'},  # Booking days (-1,1)
                          {'id': 'checkoutAction_06', 'type': 'checkout'}]  # checkout on 0th day

PART_CHECKOUT_01 = [{'id': "Booking_42_FullPayment_multipleRooms", 'type': 'booking'},  # Guest checkout 8,9 from RS1
                    {'id': 'checkinBookingStatus_38', 'type': 'check_in'},  # Booking days (-1,1)
                    {'id': 'invoicePreview_51', 'type': 'preview_invoice'},  # Checkout date: 0
                    {'id': 'checkoutAction_06', 'type': 'checkout'}]

PART_CHECKOUT_02 = [{'id': "Booking_42_FullPayment_multipleRooms", 'type': 'booking'},  # Guest checkout 8,9 from RS1
                    {'id': 'checkinBookingStatus_38', 'type': 'check_in'},  # Booking days (-1,1)
                    {'id': 'invoicePreview_53', 'type': 'preview_invoice'},  # Checkout date: -1
                    {'id': 'checkoutAction_06', 'type': 'checkout'}]  # 2 RS

PARTIAL_BOOKING_CHECKOUT_01 = [{'id': "Booking_04", 'type': 'booking'},
                               {'id': 'checkinPost_06', 'type': 'check_in'},  # Booking days (0,1)
                               {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                               {'id': 'invoicePreview_37', 'type': 'preview_invoice'},  # Checkout date: 0
                               {'id': 'checkoutAction_11', 'type': 'checkout'}]  # Checkout one room out of 2

PARTIAL_BOOKING_CHECKOUT_ROOM_V2_01 = [{'id': "booking_04", 'type': 'booking_v2'},
                                       {'id': "checkin_02", 'type': 'checkin_v2'},
                                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                       {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                                       {'id': 'CheckoutV2_04', 'type': 'checkout_v2'}]

PARTIAL_BOOKING_CHECKOUT_V2_01 = [{'id': "booking_19_01_checkin_04", 'type': 'booking_v2'},
                                  {'id': "checkin_04", 'type': 'checkin_v2'},
                                  {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                                  {'id': 'CheckoutV2_08', 'type': 'checkout_v2'}]

FULL_CHECKOUT_01 = [{'id': "Booking_41_FullPayment_singleRoom", 'type': 'booking'},  # Guest checkout 5,6,7 from RS1
                    {'id': 'checkinBookingStatus_37', 'type': 'check_in'},  # 1 RS (-1,0)
                    {'id': 'invoicePreview_08', 'type': 'preview_invoice'},
                    {'id': 'AddPayment_03_preview', 'type': 'add_payment', 'is_mock_rule_req': True},
                    {'id': 'checkoutAction_08', 'type': 'checkout'}]

FULL_CHECKOUT_01_V2 = [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                       {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                       {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
                       {'id': "CheckoutV2_01", 'type': 'checkout_v2'}]

FULL_CHECKOUT_02_V2 = [{'id': "booking_01_different_ref", 'type': 'booking_v2'},
                       {'id': "checkin_01", 'type': 'checkin_v2'},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                       {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                       {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
                       {'id': "CheckoutV2_01", 'type': 'checkout_v2'}]

FULL_CHECKOUT_03_V2 = [{'id': "booking_with_lut_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                       {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                       {'id': "AddPaymentV2_checkout_117", 'type': 'add_payment_v2'},
                       {'id': "CheckoutV2_01", 'type': 'checkout_v2'}]

FULL_CHECKOUT_04_V2 = [{'id': "booking_with_lut_02", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                       {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                       {'id': "AddPaymentV2_checkout_117", 'type': 'add_payment_v2'},
                       {'id': "CheckoutV2_01", 'type': 'checkout_v2'}]

FULL_CHECKOUT_05_V2 = [{'id': "booking_with_lut_03", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                       {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                       {'id': "AddPaymentV2_checkout_117", 'type': 'add_payment_v2'},
                       {'id': "CheckoutV2_01", 'type': 'checkout_v2'}]

FULL_CHECKOUT_06_V2 = [{'id': "booking_with_lut_04", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                       {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                       {'id': "AddPaymentV2_checkout_117", 'type': 'add_payment_v2'},
                       {'id': "CheckoutV2_01", 'type': 'checkout_v2'}]

FULL_CHECKOUT_07_V2 = [{'id': "booking_with_lut_05", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                       {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                       {'id': "AddPaymentV2_Redistribute_22", 'type': 'add_payment_v2'},
                       {'id': "CheckoutV2_01", 'type': 'checkout_v2'}]

FULL_CHECKOUT_08_V2 = [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                       {'id': 'Multiple_Expense_97', 'type': 'create_expense_V3'},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                       {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                       {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
                       {'id': "AddPaymentV2_spot_credit_43", 'type': 'add_payment_v2'},
                       {'id': "CheckoutV2_01", 'type': 'checkout_v2'}]

FULL_CHECKOUT_09_V2 = [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                       {'id': 'Multiple_Expense_99', 'type': 'create_expense_V3'},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                       {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                       {'id': "AddPaymentV2_checkout_102", 'type': 'add_payment_v2'},
                       {'id': "CheckoutV2_01", 'type': 'checkout_v2'}]

FULL_CHECKOUT_10_V2 = [{'id': "booking_212", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                       {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                       {'id': "AddPaymentV2_checkout_117", 'type': 'add_payment_v2'},
                       {'id': "CheckoutV2_01", 'type': 'checkout_v2'}]

FULL_CHECKOUT_11_V2 = [{'id': "booking_226", 'type': 'booking_v2'}, {'id': "checkin_reissue_27", 'type': 'checkin_v2'},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                       {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                       {'id': "CheckoutV2_01", 'type': 'checkout_v2'}]

FULL_CHECKOUT_12_V2 = [{'id': "booking_with_lut_01_non_credit", 'type': 'booking_v2', 'seller_type': 'RESELLER',
                        'extras': {"hotel_level_config": RESELLER_CONFIG}},
                       {'id': "checkin_01", 'type': 'checkin_v2'},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                       {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice', 'seller_type': 'RESELLER'},
                       {'id': "AddPaymentV2_Redistribute_22", 'type': 'add_payment_v2'},
                       {'id': "CheckoutV2_01", 'type': 'checkout_v2', 'seller_type': 'RESELLER'}]

FULL_CHECKOUT_13_V2 = [{'id': "booking_with_lut_01_non_credit", 'type': 'booking_v2', 'seller_type': 'RESELLER'},
                       {'id': "checkin_01", 'type': 'checkin_v2'},
                       {'id': 'Multiple_Expense_99', 'type': 'create_expense_V3'},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                       {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice', 'seller_type': 'RESELLER'},
                       {'id': "AddPaymentV2_checkout_102", 'type': 'add_payment_v2'},
                       {'id': "CheckoutV2_01", 'type': 'checkout_v2', 'seller_type': 'RESELLER'}]

FULL_CHECKOUT_14_V2 = [{'id': "booking_with_lut_01_non_credit", 'type': 'booking_v2', 'seller_type': 'RESELLER'},
                       {'id': "checkin_01", 'type': 'checkin_v2'},
                       {'id': 'Multiple_Expense_99', 'type': 'create_expense_V3'},
                       {'id': 'Multiple_Expense_97_reissue_05', 'type': 'create_expense_V3'},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 7},
                       {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice', 'seller_type': 'RESELLER'},
                       {'id': "AddPaymentV2_reissue_05", 'type': 'add_payment_v2'},
                       {'id': "CheckoutV2_01", 'type': 'checkout_v2', 'seller_type': 'RESELLER'}]

FULL_CHECKOUT_V2_SLAB_BASED = [{'id': "booking_207", 'type': 'booking_v2', 'extras': {"has_slab_based_taxation": True}},
                               {'id': "checkin_01", 'type': 'checkin_v2'},
                               {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                               {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                               {'id': "AddPaymentV2_edit_charge_61", 'type': 'add_payment_v2'},
                               {'id': "CheckoutV2_01", 'type': 'checkout_v2'}]

FULL_CHECKOUT_03 = [{'id': 'Booking_01', 'type': 'booking'},
                    {'id': 'checkinPost_01', 'type': 'check_in'},
                    {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                    {'id': 'invoicePreview_01', 'type': 'preview_invoice'},
                    {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment', 'is_mock_rule_req': True},
                    {'type': 'get_booking'}, {'type': 'get_bill'}, {'type': 'get_bill_charges'},
                    {'type': 'get_booking_preview_invoices'},
                    {'id': 'checkoutAction_04', 'type': 'checkout'}]

UPDATE_TA_AFTER_FULL_CHECKOUT_V2_01 = [{'id': "booking_226", 'type': 'booking_v2'},
                                       {'id': "checkin_reissue_27", 'type': 'checkin_v2'},
                                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                       {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                                       {'id': "CheckoutV2_01", 'type': 'checkout_v2'},
                                       {'id': "patch_booking_67", 'type': 'patch_booking'}]

#################### Checkin ####################

SINGLE_BOOKING_CHECK_IN_01 = [{'id': "Booking_01", 'type': 'booking'},  # single booking check in 1 day (0,1)
                              {'id': "checkinPost_01", 'type': 'check_in'}]

SINGLE_BOOKING_CHECK_IN_02 = [{'id': "Booking_01", 'type': 'booking'},  # single booking check in 1 day (0,1)
                              {'id': "checkinPost_32", 'type': 'check_in'}]

SINGLE_B2B_BOOKING_CHECK_IN_01 = [{'id': "Booking_11", 'type': 'booking'},  # single booking check in 1 day (0,1)
                                  {'id': "checkinPost_01", 'type': 'check_in'}]

SINGLE_BOOKING_CHECK_IN_V2_02 = [{'id': "booking_01_different_ref", 'type': 'booking_v2'},
                                 {'id': "checkin_01", 'type': 'checkin_v2'}]

SINGLE_BOOKING_CHECK_IN_V2_SLAB_BASED_01 = [{'id': "booking_01_different_ref", 'type': 'booking_v2',
                                             'extras': {"has_slab_based_taxation": True}},
                                            {'id': "checkin_01", 'type': 'checkin_v2'}]

SINGLE_BOOKING_CHECK_IN_V2_SLAB_BASED_02 = [{'id': "booking_207", 'type': 'booking_v2',
                                             'extras': {"has_slab_based_taxation": True}},
                                            {'id': "checkin_01", 'type': 'checkin_v2'}]

SINGLE_B2B_BOOKING_CHECK_IN_V2_01 = [{'id': "booking_27", 'type': 'booking_v2'},
                                     {'id': "checkin_01", 'type': 'checkin_v2'}]

SINGLE_B2B_BOOKING_CHECK_IN_V2_02 = [{'id': "booking_27_diff_ref", 'type': 'booking_v2'},
                                     {'id': "checkin_01", 'type': 'checkin_v2'}]

GROUP_BOOKING_CHECK_IN_01 = [{'id': "Booking_49", 'type': 'booking'},  # (0,2) full booking check-in
                             {'id': 'checkinPost_50', 'type': 'check_in'}]

PARTIAL_BOOKING_CHECK_IN_01 = [{'id': "Booking_04", 'type': 'booking'},  # Check-in 1 room out of 2 room for 1 day(0,1)
                               {'id': "checkinPost_07", 'type': 'check_in'}]

PARTIAL_BOOKING_CHECK_IN_02 = [{'id': "Booking_04", 'type': 'booking'},  # Check-in 1 room out of 2 room for 1 day(0,1)
                               {'id': "checkinPost_07", 'type': 'check_in'}, {'id': 'PartialBookingAction_01'
                                   , 'type': 'delete_booking_action'}]

PART_BOOKING_CHECK_IN_01 = [{'id': "Booking_33", 'type': 'booking'},  # Checkin 1-1 guest from each room (0,2)
                            {'id': "checkinPost_08", 'type': 'check_in'}]

PAST_CHECK_IN_01 = [{'id': "Booking_41_FullPayment_singleRoom", 'type': 'booking'},  # 1 RS (-1,0), all guest checked_in
                    {'id': 'checkinBookingStatus_37', 'type': 'check_in'}]

#################### Cancel Booking ####################

CANCEL_FULL_BOOKING = [{'id': 'CancelAction_14', 'type': 'cancel'}]
CancelAction_38 = [{'id': 'Booking_cancelAction_07', 'type': 'booking'}]
###################### AddOns ##############
CREATE_ADDON_01 = [{'id': 'CreateAddOnV2_01', 'type': 'create_addon'}]
CREATE_ADDON_02 = [{'id': 'CreateAddOnV2_02', 'type': 'create_addon'}]
CREATE_ADDON_03 = [{'id': 'CreateAddOnV2_03', 'type': 'create_addon'}]
CREATE_ADDON_04 = [{'id': 'CreateAddOnV2_09', 'type': 'create_addon'}]
CREATE_ADDON_05 = [{'id': 'CreateAddOnV2_05', 'type': 'create_addon'}]
REMOVE_ADDON = [{'type': 'remove_addon'}]

CREATE_DNR_01 = {'id': "createDnr_01", 'type': 'create_dnr', 'user_type': 'super-admin'}
CREATE_DNR_09 = {'id': "createDnr_09", 'type': 'create_dnr', 'user_type': 'super-admin'}
CREATE_DNR_16 = {'id': "createDnr_16", 'type': 'create_dnr', 'user_type': 'super-admin'}
CREATE_DNR_33 = {'id': "createDnr_33", 'type': 'create_dnr', 'user_type': 'super-admin'}
CREATE_DNR_34 = {'id': "createDnr_34_Acacia", 'type': 'create_dnr', 'user_type': 'super-admin'}
CREATE_DNR_04 = {'id': "createDnr_04", 'type': 'create_dnr', 'user_type': 'super-admin'}
CREATE_DNR_08 = {'id': "createDnr_08", 'type': 'create_dnr', 'user_type': 'super-admin'}
CREATE_DNR_02 = {'id': "createDnr_02", 'type': 'create_dnr', 'user_type': 'super-admin'}
CREATE_DNR_03 = {'id': "createDnr_03", 'type': 'create_dnr', 'user_type': 'super-admin'}

###################### Assign Room Pre requisite Actions ##############
ASSIGN_ROOM_01 = [{'id': "Future_Booking_01", 'type': 'booking'}]
ASSIGN_ROOM_02 = [{'id': "Booking_04", 'type': 'booking'}]
ASSING_ROOM_03 = [{'id': "Booking_63", 'type': 'booking'}]
ASSING_ROOM_04 = [{'id': "Future_Booking_01", 'type': 'booking'},
                  {'id': 'UpdateRoomStay_04_AssignAlreadyBookedRoom', 'type': 'room_update'}]
ASSING_ROOM_05 = [{'id': 'Booking_01', 'type': 'booking'},
                  {'id': 'checkinPost_01', 'type': 'check_in'},
                  {'id': 'invoicePreview_01', 'type': 'preview_invoice'},
                  {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment'},
                  {'id': 'checkoutAction_04', 'type': 'checkout'},
                  {'id': 'Booking_01', 'type': 'booking'}]
ASSIGN_ROOM_07 = [{'id': 'Booking_01', 'type': 'booking'},
                  {'id': "createDnr_34_Acacia", 'type': 'create_dnr', 'user_type': 'super-admin'}]
ASSIGN_ROOM_08 = [{'id': 'Booking_01', 'type': 'booking'},
                  {'id': 'checkinPost_01', 'type': 'check_in'},
                  {'id': "Future_Booking_01", 'type': 'booking'}]

DISALLOW_CHARGE_ADDITION_01 = [{'id': 'Booking_01', 'type': 'booking'},
                               {'id': 'UpdateRoomStay_11_DisallowChargeAddition', 'type': 'room_update'}]

##################### House Keeping ###########
HOSUE_KEEPING_STATUS_DIRTY = [{'id': "Housekeeper_03", 'type': 'edit_housekeeping_status'}]
HOSUE_KEEPING_STATUS_CLEAN = [{'id': "Housekeeper_04", 'type': 'edit_housekeeping_status'}]

################### Reissue Invoice Pre Requisite Actions ##################################
REISSUE_INVOICE_01 = [{'id': 'Booking_77', 'type': 'booking'},
                      {'id': 'checkinPost_61', 'type': 'check_in'},
                      {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                      {'id': 'invoicePreview_55', 'type': 'preview_invoice'},
                      {'type': 'get_booking'}, {'type': 'get_bill'}, {'type': 'get_bill_charges'},
                      {'type': 'get_booking_preview_invoices'},
                      {'id': 'checkoutAction_38', 'type': 'checkout'}]

REISSUE_INVOICE_02 = [{'id': 'Booking_77', 'type': 'booking'},
                      {'id': 'checkinPost_61', 'type': 'check_in'},
                      {'id': 'AddOns_39', 'type': 'create_addon'},
                      {'id': 'invoicePreview_55', 'type': 'preview_invoice'},
                      {'id': 'checkoutAction_38', 'type': 'checkout'}]

REISSUE_INVOICE_03 = [{'id': 'Booking_79', 'type': 'booking'},
                      {'id': 'checkinPost_63', 'type': 'check_in'},
                      {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                      {'id': 'invoicePreview_57', 'type': 'preview_invoice'},
                      {'id': 'checkoutAction_40', 'type': 'checkout'},
                      {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                      {'id': 'invoicePreview_58', 'type': 'preview_invoice'},
                      {'type': 'get_booking'}, {'type': 'get_bill'}, {'type': 'get_bill_charges'},
                      {'type': 'get_booking_preview_invoices'},
                      {'id': 'checkoutAction_40', 'type': 'checkout'}]

REISSUE_INVOICE_04 = [{'id': 'Booking_77', 'type': 'booking'},
                      {'id': 'checkinPost_61', 'type': 'check_in'},
                      {'id': 'AddOns_40', 'type': 'create_addon'},
                      {'id': 'invoicePreview_55', 'type': 'preview_invoice'},
                      {'id': 'checkoutAction_38', 'type': 'checkout'}]

REISSUE_INVOICE_05 = [{'id': 'Booking_81', 'type': 'booking'},
                      {'id': 'checkinPost_64', 'type': 'check_in'},
                      {'id': 'AddPayment_01_preview', 'type': 'add_payment'},
                      {'id': 'invoicePreview_59', 'type': 'preview_invoice'},
                      {'id': 'checkoutAction_40', 'type': 'checkout'},
                      {'id': 'invoicePreview_60', 'type': 'preview_invoice'},
                      {'id': 'checkoutAction_40', 'type': 'checkout'}
                      ]

REISSUE_INVOICE_06 = [{'id': 'Booking_82', 'type': 'booking'},
                      {'id': 'checkinPost_65', 'type': 'check_in'},
                      {'id': 'invoicePreview_61', 'type': 'preview_invoice'},
                      {'id': 'checkoutAction_40', 'type': 'checkout'}]

REISSUE_INVOICE_08 = [{'id': 'Booking_77', 'type': 'booking'},
                      {'id': 'checkinPost_61', 'type': 'check_in'},
                      {'id': 'AddOns_40', 'type': 'create_addon'},
                      {'id': 'Payment_Single_Addon', 'type': 'add_payment'},
                      {'id': 'invoicePreview_55', 'type': 'preview_invoice'},
                      {'id': 'checkoutAction_38', 'type': 'checkout'}]

################################# Reverse Checkout ###############################
REVERSE_CHECKOUT_01 = [{'id': 'Booking_41_FullPayment_singleRoom', 'type': 'booking'},
                       {'id': 'checkinBookingStatus_37', 'type': 'check_in'},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                       {'id': 'invoicePreview_08', 'type': 'preview_invoice'}, {'type': 'get_booking'},
                       {'type': 'get_bill'}, {'type': 'get_bill_charges'}, {'type': 'get_booking_preview_invoices'},
                       {'id': 'checkoutAction_08', 'type': 'checkout'}]

REVERSE_CHECKOUT_03 = [{'id': 'Booking_42_FullPayment_multipleRooms', 'type': 'booking'},
                       {'id': 'checkinBookingStatus_38', 'type': 'check_in'},
                       {'id': 'invoicePreview_08_CO', 'type': 'preview_invoice'}, {'type': 'get_booking'},
                       {'type': 'get_bill'}, {'type': 'get_bill_charges'}, {'type': 'get_booking_preview_invoices'},
                       {'id': 'checkoutAction_23', 'type': 'checkout'}]

REVERSE_CHECKOUT_04 = [{'id': 'Booking_30', 'type': 'booking'}, {'id': 'checkinPost_02', 'type': 'check_in'},
                       {'id': 'AddPayment_03', 'type': 'add_payment'},
                       {'id': 'invoicePreview_41', 'type': 'preview_invoice'}, {'type': 'get_booking'},
                       {'type': 'get_bill'}, {'type': 'get_bill_charges'}, {'type': 'get_booking_preview_invoices'},
                       {'id': 'checkoutAction_01', 'type': 'checkout'}]

REVERSE_CHECKOUT_08 = [{'id': 'Booking_42_FullPayment_multipleRooms', 'type': 'booking'},
                       {'id': 'checkinBookingStatus_37', 'type': 'check_in'},
                       {'id': 'invoicePreview_07_CO', 'type': 'preview_invoice'}, {'type': 'get_booking'},
                       {'type': 'get_bill'}, {'type': 'get_bill_charges'}, {'type': 'get_booking_preview_invoices'},
                       {'id': 'checkoutAction_09', 'type': 'checkout'}]

REVERSE_CHECKOUT_12 = [{'id': 'Booking_48', 'type': 'booking'}, {'id': 'checkinPost_01', 'type': 'check_in'},
                       {'id': 'AddPayment_03', 'type': 'add_payment'},
                       {'id': 'invoicePreview_41', 'type': 'preview_invoice'}, {'type': 'get_booking'},
                       {'type': 'get_bill'}, {'type': 'get_bill_charges'}, {'type': 'get_booking_preview_invoices'},
                       {'id': 'checkoutAction_01', 'type': 'checkout'},
                       {'id': 'UpdateRoomStay_71', 'type': 'room_update'}, {'type': 'get_booking'},
                       {'type': 'get_bill'}, {'type': 'get_bill_charges'}, {'type': 'get_booking_preview_invoices'}]

REVERSE_CHECKOUT_13 = [{'id': 'Booking_32', 'type': 'booking'},
                       {'id': 'CancelAction_35', 'type': 'cancel'}, {'id': 'checkinPost_02', 'type': 'check_in'},
                       {'id': 'AddPayment_01_preview', 'type': 'add_payment'},
                       {'id': 'invoicePreview_42', 'type': 'preview_invoice'}, {'type': 'get_booking'},
                       {'type': 'get_bill'}, {'type': 'get_bill_charges'}, {'type': 'get_booking_preview_invoices'},
                       {'id': 'checkoutAction_01', 'type': 'checkout'}]

REVERSE_CHECKOUT_15 = [{'id': 'Booking_42_FullPayment_multipleRooms', 'type': 'booking'},
                       {'id': 'checkinBookingStatus_38', 'type': 'check_in'},
                       {'id': 'CHECKOUT_GUEST_invoicePreview_AddGuest_02', 'type': 'preview_invoice'},
                       {'id': 'Checkout_01_extra', 'type': 'checkout'},
                       {'id': 'invoicePreview_09_CO', 'type': 'preview_invoice'},
                       {'type': 'get_booking'},
                       {'type': 'get_bill'}, {'type': 'get_bill_charges'}, {'type': 'get_booking_preview_invoices'},
                       {'id': 'checkoutAction_26', 'type': 'checkout'}]

REVERSE_CHECKOUT_16 = [{'id': 'Booking_41_FullPayment_singleRoom', 'type': 'booking'},
                       {'id': 'checkinBookingStatus_37', 'type': 'check_in'},
                       {'id': 'AddPayment_03_preview', 'type': 'add_payment', 'is_mock_rule_req': True},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                       {'id': 'invoicePreview_08', 'type': 'preview_invoice'}, {'type': 'get_booking'},
                       {'type': 'get_bill'}, {'type': 'get_bill_charges'}, {'type': 'get_booking_preview_invoices'},
                       {'id': 'checkoutAction_08', 'type': 'checkout'}]

REVERSE_CHECKOUT_22 = [{'id': 'Booking_04', 'type': 'booking'},
                       {'id': 'checkinPost_06', 'type': 'check_in'},
                       {'id': 'AddPayment_03_preview', 'type': 'add_payment', 'is_mock_rule_req': True},
                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                       {'id': 'invoicePreview_61', 'type': 'preview_invoice'},
                       {'type': 'get_booking'}, {'type': 'get_bill'}, {'type': 'get_bill_charges'},
                       {'type': 'get_booking_preview_invoices'}, {'id': 'checkoutAction_40', 'type': 'checkout'}]

ReverseCheckout_25 = [{'id': 'Booking_77', 'type': 'booking'},
                      {'id': 'checkinPost_61', 'type': 'check_in'}, {'id': 'AddOns_39', 'type': 'create_addon'},
                      {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                      {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                      {'id': 'invoicePreview_55', 'type': 'preview_invoice'},
                      {'type': 'get_booking'}, {'type': 'get_bill'}, {'type': 'get_bill_charges'},
                      {'type': 'get_booking_preview_invoices'}, {'id': 'checkoutAction_38', 'type': 'checkout'}]

ReverseCheckout_26 = [{'id': 'Booking_04', 'type': 'booking'}, {'id': 'checkinPost_06', 'type': 'check_in'},
                      {'id': 'invoicePreview_62', 'type': 'preview_invoice'},
                      {'id': 'AddPayment_01_preview', 'type': 'add_payment'},
                      {'type': 'get_booking'}, {'type': 'get_bill'}, {'type': 'get_bill_charges'},
                      {'type': 'get_booking_preview_invoices'}, {'id': 'checkoutAction_43', 'type': 'checkout'}]

ReverseCheckout_28 = [{'id': 'Booking_04', 'type': 'booking'}, {'id': 'CancelAction_39', 'type': 'cancel'},
                      {'id': 'checkinPost_67', 'type': 'check_in'},
                      {'id': 'invoicePreview_13', 'type': 'preview_invoice'},
                      {'id': 'AddPayment_01_preview', 'type': 'add_payment'}, {'type': 'get_booking'},
                      {'type': 'get_bill'}, {'type': 'get_bill_charges'}, {'type': 'get_booking_preview_invoices'},
                      {'id': 'checkoutAction_43', 'type': 'checkout'}]

ReverseCheckout_29 = [{'id': 'Booking_83', 'type': 'booking'}, {'id': 'noshowAction_33', 'type': 'noshow'},
                      {'id': 'checkinPost_68', 'type': 'check_in'},
                      {'id': 'invoicePreview_64', 'type': 'preview_invoice'},
                      {'id': 'AddPayment_01_preview', 'type': 'add_payment'}, {'type': 'get_booking'},
                      {'type': 'get_bill'}, {'type': 'get_bill_charges'}, {'type': 'get_booking_preview_invoices'},
                      {'id': 'checkoutAction_44', 'type': 'checkout'}]

REVERSENOSHOW_01 = [{'id': 'Booking_noshowAction_01', 'type': 'booking'},
                    {'id': 'noshowAction_03', 'type': 'noshow'}]

REVERSENOSHOW_02 = [{'id': 'Booking_noshowAction_01', 'type': 'booking'},
                    {'id': 'noshowAction_34', 'type': 'noshow'}]

REVERSENOSHOW_03 = [{'id': 'Booking_noshowAction_01', 'type': 'booking'},
                    {'id': 'noshowAction_35', 'type': 'noshow'}]

REVERSENOSHOW_04 = [{'id': 'Booking_noshowAction_02', 'type': 'booking'},
                    {'id': 'checkinPost_43', 'type': 'check_in'},
                    {'id': 'noshowAction_24', 'type': 'noshow'}]

REVERSENOSHOW_05 = [{'id': 'Booking_noshowAction_01', 'type': 'booking'},
                    {'id': 'checkinBookingStatus_35', 'type': 'check_in'},
                    {'id': 'noshowAction_21', 'type': 'noshow'}]

REVERSENOSHOW_06 = [{'id': 'Booking_noshowAction_05', 'type': 'booking'},
                    {'id': 'checkinPost_74', 'type': 'check_in'},
                    {'id': 'noshowAction_24', 'type': 'noshow'}]

CreditNotes_01 = [{'id': 'Booking_01', 'type': 'booking'},
                  {'id': 'checkinPost_32', 'type': 'check_in'},
                  {'id': 'invoicePreview_01', 'type': 'preview_invoice'},
                  {'id': 'AddPayment_30', 'type': 'add_payment'},
                  {'id': 'checkoutAction_04', 'type': 'checkout'},
                  {'type': 'delete_booking_action'}]

CreditNotes_02 = [{'id': 'Booking_01', 'type': 'booking'},
                  {'id': 'checkinPost_01', 'type': 'check_in'},
                  {'id': 'CreateAddOnV2_36', 'type': 'create_addon'},
                  {'id': 'invoicePreview_01', 'type': 'preview_invoice'},
                  {'id': 'AddPayment_31', 'type': 'add_payment'},
                  {'id': 'checkoutAction_04', 'type': 'checkout'},
                  {'type': 'delete_booking_action'}]

CreditNotes_03 = [{'id': 'Booking_52_FullPayment_singleRoom', 'type': 'booking'},
                  {'id': 'checkinBookingStatus_37', 'type': 'check_in'},
                  {'id': 'AddPayment_03_preview', 'type': 'add_payment'},
                  {'id': 'invoicePreview_28', 'type': 'preview_invoice'},
                  {'id': 'checkoutAction_08', 'type': 'checkout'}]

CreditNotes_04 = [{'id': 'Booking_52_FullPayment_singleRoom', 'type': 'booking'},
                  {'id': 'checkinBookingStatus_37', 'type': 'check_in'},
                  {'id': 'AddPayment_03_preview', 'type': 'add_payment'},
                  {'id': 'invoicePreview_46', 'type': 'preview_invoice'},
                  {'id': 'checkoutAction_08', 'type': 'checkout'}]

CreditNotes_05 = [{'id': 'Booking_52_FullPayment_singleRoom', 'type': 'booking'},
                  {'id': 'checkinBookingStatus_37', 'type': 'check_in'},
                  {'id': 'AddPayment_03_preview', 'type': 'add_payment'},
                  {'id': 'AddExpense_33', 'type': 'add_expense'},
                  {'id': 'invoicePreview_28', 'type': 'preview_invoice'},
                  {'id': 'checkoutAction_08', 'type': 'checkout'}]

CreditNotes_06 = [{'id': 'Booking_52_FullPayment_singleRoom', 'type': 'booking'},
                  {'id': 'checkinBookingStatus_37', 'type': 'check_in'},
                  {'id': 'AddPayment_03_preview', 'type': 'add_payment'},
                  {'id': 'invoicePreview_45', 'type': 'preview_invoice'},
                  {'id': 'checkoutAction_08', 'type': 'checkout'},
                  {'id': 'InvoiceRegeneration_01', 'type': 'invoiceRegeneration'}]

CreditNotes_07 = [{'id': "booking_27", 'is_booking_v2': True, 'type': 'booking_v2'},
                  {'id': 'checkinBookingStatus_37', 'type': 'check_in'},
                  {'id': 'AddPayment_03_preview', 'type': 'add_payment'},
                  {'id': 'invoicePreview_45', 'type': 'preview_invoice'},
                  {'id': 'checkoutAction_08', 'type': 'checkout'},
                  {'id': 'InvoiceRegeneration_01', 'type': 'invoiceRegeneration'}]

Patch_Web_check_in_01 = [{'id': 'Booking_01', 'type': 'booking'},
                         {'id': 'UpdateRoomStay_77', 'type': 'bulk_room_update'},
                         {'id': 'Add_Attachment_01', 'type': 'add_attachment'},
                         {'id': 'BulkEditCustomer_08', 'type': 'edit_customer_details'},
                         {'id': 'Edit_Attachment_01', 'type': 'edit_attachment'},
                         {'id': 'WebCheckin_01', 'type': 'web_checkin'}]

Patch_Web_check_in_02 = [{'id': 'Booking_63', 'type': 'booking'},
                         {'id': 'UpdateRoomStay_77', 'type': 'bulk_room_update'},
                         {'id': 'Add_Attachment_01', 'type': 'add_attachment'},
                         {'id': 'BulkEditCustomer_08', 'type': 'edit_customer_details'},
                         {'id': 'Edit_Attachment_01', 'type': 'edit_attachment'},
                         {'id': 'WebCheckin_01', 'type': 'web_checkin'}]

Patch_Web_check_in_03 = [{'id': 'Booking_95', 'type': 'booking'},
                         {'id': 'UpdateMultipleRoomStay_19', 'type': 'bulk_room_update'},
                         {'id': 'Add_Attachment_02', 'type': 'add_attachment'},
                         {'id': 'Add_Attachment_03', 'type': 'add_attachment'},
                         {'id': 'Add_Attachment_04', 'type': 'add_attachment'},
                         {'id': 'Add_Attachment_05', 'type': 'add_attachment'},
                         {'id': 'BulkEditCustomer_09', 'type': 'edit_customer_details'},
                         {'id': 'Edit_Attachment_02', 'type': 'edit_attachment'},
                         {'id': 'Edit_Attachment_03', 'type': 'edit_attachment'},
                         {'id': 'Edit_Attachment_04', 'type': 'edit_attachment'},
                         {'id': 'Edit_Attachment_05', 'type': 'edit_attachment'},
                         {'id': 'WebCheckin_02', 'type': 'web_checkin'}]

Patch_Web_check_in_04 = [{'id': 'Booking_95', 'type': 'booking'},
                         {'id': 'UpdateMultipleRoomStay_20', 'type': 'bulk_room_update'},
                         {'id': 'UpdateMultipleRoomStay_21', 'type': 'bulk_room_update'},
                         {'id': 'Add_Attachment_02', 'type': 'add_attachment'},
                         {'id': 'Add_Attachment_03', 'type': 'add_attachment'},
                         {'id': 'Add_Attachment_04', 'type': 'add_attachment'},
                         {'id': 'Add_Attachment_05', 'type': 'add_attachment'},
                         {'id': 'BulkEditCustomer_09', 'type': 'edit_customer_details'},
                         {'id': 'Edit_Attachment_02', 'type': 'edit_attachment'},
                         {'id': 'Edit_Attachment_03', 'type': 'edit_attachment'},
                         {'id': 'Edit_Attachment_04', 'type': 'edit_attachment'},
                         {'id': 'Edit_Attachment_05', 'type': 'edit_attachment'},
                         {'id': 'WebCheckin_02', 'type': 'web_checkin'}]

Patch_Web_check_in_05 = [{'id': 'Booking_01', 'type': 'booking'},
                         {'id': 'Add_Attachment_01', 'type': 'add_attachment'},
                         {'id': 'BulkEditCustomer_08', 'type': 'edit_customer_details'},
                         {'id': 'Edit_Attachment_01', 'type': 'edit_attachment'},
                         {'id': 'WebCheckin_01', 'type': 'web_checkin'}]

Patch_Web_check_in_06 = [{'id': 'Booking_01', 'type': 'booking'},
                         {'id': 'UpdateRoomStay_77', 'type': 'bulk_room_update'},
                         {'id': 'Add_Attachment_01', 'type': 'add_attachment'},
                         {'id': 'BulkEditCustomer_08', 'type': 'edit_customer_details'},
                         {'id': 'WebCheckin_01', 'type': 'web_checkin'}]

Patch_Web_check_in_07 = [{'id': 'Booking_01', 'type': 'booking'},
                         {'id': 'UpdateRoomStay_77', 'type': 'bulk_room_update'},
                         {'id': 'Add_Attachment_01', 'type': 'add_attachment'},
                         {'id': 'BulkEditCustomer_08', 'type': 'edit_customer_details'},
                         {'id': 'Edit_Attachment_01', 'type': 'edit_attachment'},
                         {'id': 'WebCheckin_03', 'type': 'web_checkin'}]

Create_Web_check_in_01 = [{'id': 'Booking_01', 'type': 'booking'},
                          {'id': 'UpdateRoomStay_77', 'type': 'bulk_room_update'},
                          {'id': 'Add_Attachment_01', 'type': 'add_attachment'},
                          {'id': 'BulkEditCustomer_08', 'type': 'edit_customer_details'},
                          {'id': 'Edit_Attachment_01', 'type': 'edit_attachment'}]

Create_Web_check_in_02 = [{'id': 'Booking_132', 'type': 'booking'},
                          {'id': 'UpdateMultipleRoomStay_19', 'type': 'bulk_room_update'},
                          {'id': 'Add_Attachment_02', 'type': 'add_attachment'},
                          {'id': 'Add_Attachment_03', 'type': 'add_attachment'},
                          {'id': 'Add_Attachment_04', 'type': 'add_attachment'},
                          {'id': 'Add_Attachment_05', 'type': 'add_attachment'},
                          {'id': 'BulkEditCustomer_09', 'type': 'edit_customer_details'},
                          {'id': 'Edit_Attachment_02', 'type': 'edit_attachment'},
                          {'id': 'Edit_Attachment_03', 'type': 'edit_attachment'},
                          {'id': 'Edit_Attachment_04', 'type': 'edit_attachment'},
                          {'id': 'Edit_Attachment_05', 'type': 'edit_attachment'}]

Create_Web_check_in_03 = [{'id': 'Booking_63', 'type': 'booking'},
                          {'id': 'UpdateRoomStay_77', 'type': 'bulk_room_update'},
                          {'id': 'Add_Attachment_01', 'type': 'add_attachment'},
                          {'id': 'BulkEditCustomer_08', 'type': 'edit_customer_details'},
                          {'id': 'Edit_Attachment_01', 'type': 'edit_attachment'}]

Create_Web_check_in_04 = [{'id': 'Booking_01', 'type': 'booking'},
                          {'id': 'UpdateRoomStay_77', 'type': 'bulk_room_update'},
                          {'id': 'Add_Attachment_01', 'type': 'add_attachment'},
                          {'id': 'BulkEditCustomer_08', 'type': 'edit_customer_details'},
                          {'id': 'Edit_Attachment_06', 'type': 'edit_attachment'}]

Create_Web_check_in_05 = [{'id': 'Booking_01', 'type': 'booking'},
                          {'id': 'Add_Attachment_01', 'type': 'add_attachment'},
                          {'id': 'BulkEditCustomer_08', 'type': 'edit_customer_details'},
                          {'id': 'Edit_Attachment_01', 'type': 'edit_attachment'}]

################################# Cashier Session Payment ###############################
SESSION_PAYMENT_01 = [
    {'id': 'CreateRegister_01', 'type': 'create_register', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'CashierSession_01', 'type': 'create_register_session', 'user': 'automation', 'user_type': 'super-admin'}]

SESSION_PAYMENT_02 = [
    {'id': 'CreateRegister_01', 'type': 'create_register', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'CashierSession_01', 'type': 'create_register_session', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'SessionPayment_01', 'type': 'create_session_payment', 'user': 'automation', 'user_type': 'super-admin'}]

SESSION_PAYMENT_06 = [
    {'id': 'CreateRegister_06', 'type': 'create_register', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'CashierSession_02', 'type': 'create_register_session', 'user': 'automation', 'user_type': 'super-admin'}]

################################# Cashier Session  ###############################
CashierSession_04 = [
    {'id': 'CreateRegister_01', 'type': 'create_register', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'CashierSession_01', 'type': 'create_register_session', 'user': 'automation', 'user_type': 'super-admin'}]

CashierSession_05 = [
    {'id': 'CreateRegister_01', 'type': 'create_register', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'CashierSession_01', 'type': 'create_register_session', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'PatchCashierSession_01', 'type': 'patch_register_session', 'user': 'automation',
     'user_type': 'super-admin'}]

CashierSession_12 = [
    {'id': 'CreateRegister_01', 'type': 'create_register', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'CashierSession_01', 'type': 'create_register_session', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'PatchCashierSession_01', 'type': 'patch_register_session', 'user': 'automation',
     'user_type': 'super-admin'},
    {'id': 'CashierSession_01', 'type': 'create_register_session', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'PatchCashierSession_01', 'type': 'patch_register_session', 'user': 'automation',
     'user_type': 'super-admin'},
    {'id': 'CashierSession_01', 'type': 'create_register_session', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'PatchCashierSession_01', 'type': 'patch_register_session', 'user': 'automation',
     'user_type': 'super-admin'}]

################################# Patch Cashier Session  ###############################
PatchCashierSession_01 = [
    {'id': 'CreateRegister_01', 'type': 'create_register', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'CashierSession_01', 'type': 'create_register_session', 'user': 'automation', 'user_type': 'super-admin'}]

PatchCashierSession_02 = [
    {'id': 'CreateRegister_06', 'type': 'create_register', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'CashierSession_03', 'type': 'create_register_session', 'user': 'automation', 'user_type': 'super-admin'}]

PatchCashierSession_03 = [
    {'id': 'CreateRegister_06', 'type': 'create_register', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'CashierSession_03', 'type': 'create_register_session', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'PatchCashierSession_01', 'type': 'patch_register_session', 'user': 'automation',
     'user_type': 'super-admin'}]

PatchCashierSession_04 = [
    {'id': 'CreateRegister_06', 'type': 'create_register', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'CashierSession_03', 'type': 'create_register_session', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'PatchCashierSession_01', 'type': 'patch_register_session', 'user': 'automation',
     'user_type': 'super-admin'},
    {'id': 'CashierSession_03', 'type': 'create_register_session', 'user': 'automation', 'user_type': 'super-admin'}]

################################# Cashier Add Payment  ###############################
CashierAddPayment_01 = [
    {'id': 'CreateRegister_01', 'type': 'create_register', 'user': 'automation', 'user_type': 'super-admin'},
    {'id': 'CashierSession_01', 'type': 'create_register_session', 'user': 'automation', 'user_type': 'super-admin'}]

################################# Add expense #########################################

BookingWithExpense01 = [{'id': 'Booking_01', 'type': 'booking'},
                        {'id': 'Create_Expense_01', 'type': 'expense'}]

Checkin_Booking_Expense01 = [{'id': "Booking_01", 'type': 'booking'},
                             {'id': "checkinPost_01", 'type': 'check_in'},
                             {'id': "Create_Expense_01", 'type': 'expense'}]

Checkin_Booking_Expense02 = [{'id': "Booking_01", 'type': 'booking'},
                             {'id': "checkinPost_01", 'type': 'check_in'},
                             {'id': "Create_Expense_02", 'type': 'expense'}]

Checkin_Booking_Expense03 = [{'id': "Booking_11", 'type': 'booking'},
                             {'id': "checkinPost_01", 'type': 'check_in'},
                             {'id': "Create_Expense_09", 'type': 'expense'}]
Posted_Charges01 = [{'id': "Booking_01", 'type': 'booking'},
                    {'id': "checkinPost_01", 'type': 'check_in'},
                    {'id': "Create_Expense_01", 'type': 'expense'},
                    {'id': "Edit_Charge_01", 'type': 'edit_charge', 'charge_id': 2},
                    {'id': "Add_Allowance_02", 'type': 'add_allowance'}]

Posted_Charges02 = [{'id': "Booking_01", 'type': 'booking'},
                    {'id': "checkinPost_01", 'type': 'check_in'},
                    {'id': "Create_Expense_11", 'type': 'expense'},
                    {'id': "Edit_Charge_05", 'type': 'edit_charge', 'charge_id': 2, 'is_mock_rule_req': True}]

Pass_Allowance_On_Invoiced_Charge = [{'id': 'Booking_52_FullPayment_singleRoom', 'type': 'booking'},
                                     {'id': 'checkinBookingStatus_37', 'type': 'check_in'},
                                     {'id': 'AddPayment_03_preview', 'type': 'add_payment'},
                                     {'id': 'invoicePreview_45', 'type': 'preview_invoice'},
                                     {'id': 'checkoutAction_08', 'type': 'checkout'},
                                     {'id': 'InvoiceRegeneration_01', 'type': 'invoiceRegeneration'}]

Checkin_Booking01 = [{'id': "Booking_01", 'type': 'booking'},
                     {'id': "checkinPost_01", 'type': 'check_in'}]

Booking_With_Inclusion01 = [{'id': "Booking_02", 'type': 'booking', 'enable_rate_manager': True},
                            {'id': "Bulk_add_ons_04", 'type': 'add_bulk_addon_v2'}]

Booking_With_Inclusion02 = [{'id': "Booking_11", 'type': 'booking', 'enable_rate_manager': True},
                            {'id': "Bulk_add_ons_11", 'type': 'add_bulk_addon_v2'}]

Booking_with_non_linked_credit_expense_01 = [{'id': "Booking_114", 'type': 'booking'},
                                             {'id': "Create_Expense_16", 'type': 'expense'}]

Booking_with_non_linked_non_credit_expense_01 = [{'id': "Booking_114", 'type': 'booking'},
                                                 {'id': "Create_Expense_13", 'type': 'expense'}]

Booking_with_linked_credit_expense_01 = [{'id': "Booking_114", 'type': 'booking'},
                                         {'id': "Create_Expense_24", 'type': 'expense'}]

Booking_with_linked_non_credit_expense_01 = [{'id': "Booking_114", 'type': 'booking'},
                                             {'id': "Create_Expense_34", 'type': 'expense'}]

Booking_v2_with_rate_plan_and_inclusion = [{'id': "Booking_01", 'type': 'booking', 'enable_rate_manager': True,
                                            'is_booking_v2': True, 'is_inclusion_added': True}]

Booking_v2_with_two_guests_rate_plan_and_inclusion = [{'id': "Booking_76", 'type': 'booking',
                                                       'enable_rate_manager': True, 'is_booking_v2': True,
                                                       'is_inclusion_added': True}]

Future_booking_v2_with_rate_plan_and_inclusion = [{'id': "Booking_27", 'type': 'booking', 'enable_rate_manager': True,
                                                   'is_booking_v2': True, 'is_inclusion_added': False}]

B2BBooking_with_rate_plan_and_inclusion = [{'id': "Booking_11", 'type': 'booking', 'enable_rate_manager': True,
                                            'is_booking_v2': True, 'is_inclusion_added': True}]

################################# Action after Night Audit #########################################
ACTION_FOR_CHECKOUT = [{'id': 'invoicePreview_01', 'type': 'preview_invoice'},
                       {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment', 'is_mock_rule_req': True},
                       {'id': 'checkoutAction_04', 'type': 'checkout'}]

ACTION_FOR_CHECKOUT_02 = [{'id': 'invoicePreview_12', 'type': 'preview_invoice'},
                          {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment', 'is_mock_rule_req': True},
                          {'id': 'CheckoutV2_01', 'type': 'checkout_v2'}]

ACTION_FOR_REVERSE_CHECKOUT = [{'id': 'invoicePreview_01', 'type': 'preview_invoice'},
                               {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment', 'is_mock_rule_req': True},
                               {'id': 'checkoutAction_04', 'type': 'checkout'}, {'type': 'delete_booking_action'}]

ACTION_FOR_REVERSE_CHECKOUT_WITH_POSTED_CHARGE = [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                                                  {'id': 'invoicePreview_01', 'type': 'preview_invoice'},
                                                  {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment',
                                                   'is_mock_rule_req': True},
                                                  {'id': 'checkoutAction_04', 'type': 'checkout'},
                                                  {'type': 'delete_booking_action'}]

ACTION_FOR_CHECKOUT_ASSIGN_SAME_ROOM = [{'id': 'invoicePreview_01', 'type': 'preview_invoice'},
                                        {'id': 'AddPayment_04_ExtraAmount',
                                         'type': 'add_payment', 'is_mock_rule_req': True},
                                        {'id': 'checkoutAction_04', 'type': 'checkout'},
                                        {'id': "Booking_01", 'type': 'booking'},
                                        {'id': "UpdateRoomStay_09_AssignTodayCheckinBooking", 'type': 'room_update'}]

ACTION_FOR_CHECKOUT_CHECK_IN_SAME_ROOM = [{'id': 'invoicePreview_01', 'type': 'preview_invoice'},
                                          {'id': 'AddPayment_04_ExtraAmount',
                                           'type': 'add_payment', 'is_mock_rule_req': True},
                                          {'id': 'checkoutAction_04', 'type': 'checkout'},
                                          {'id': "Booking_01", 'type': 'booking'},
                                          {'id': "checkinPost_01", 'type': 'check_in'}]

INCREASE_CHECKOUT_DATE = [{'id': "UpdateRoomStay_80", 'type': 'room_update', 'resource_version': 2}]

DECREASE_CHECKOUT_DATE = [{'id': "UpdateRoomStay_81", 'type': 'room_update', 'resource_version': 2}]

SINGLE_BOOKING_ASSIGN_ROOM = [{'id': "Booking_01", 'type': 'booking'},
                              {'id': "UpdateRoomStay_09_AssignTodayCheckinBooking", 'type': 'room_update'}]

PART_CHECKOUT_BOOKING = [{'id': 'invoicePreview_65', 'type': 'preview_invoice'},
                         {'id': 'checkoutAction_35', 'type': 'checkout'}]

PART_CHECKOUT_BOOKING_V2 = [{'id': 'invoicePreviewCheckoutV2_01', 'type': 'preview_invoice'},
                            {'id': 'CheckoutV2_04', 'type': 'checkout_v2'}]

PART_CHECKOUT_BOOKING_WITH_POSTED_CHARGE = [{'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                                            {'id': 'invoicePreview_65', 'type': 'preview_invoice'},
                                            {'id': 'checkoutAction_35', 'type': 'checkout'}]

MULTIPLE_DAY_B2C_CHECKIN_BOOKING_01 = [{'id': "Booking_02", 'type': 'booking'},
                                       {'id': "checkinPost_11", 'type': 'check_in'}]

SINGLE_DAY_B2B_CHECKIN_BOOKING_01 = [{'id': "Booking_11", 'type': 'booking'},
                                     {'id': "checkinPost_01", 'type': 'check_in'}]

MULTIPLE_DAY_B2B_CHECKIN_BOOKING_01 = [{'id': "Booking_33", 'type': 'booking'},
                                       {'id': "checkinPost_08", 'type': 'check_in'}]

BOOKING_WITH_SPOT_CREDIT_FOLIO = [{'id': "booking_01", 'type': 'booking_v2'},
                                  {'id': 'SpotCredit_18', 'type': 'spot_credit'}]

SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE = [{'id': 'Booking_11', 'type': 'booking'},
                                                                    {'id': "checkinPost_01", 'type': 'check_in'},
                                                                    {'id': 'Create_Expense_01', 'type': 'expense'}]

SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_NON_LINKED_CREDIT_CHARGE = [{'id': 'Booking_11', 'type': 'booking'},
                                                                {'id': "checkinPost_01", 'type': 'check_in'},
                                                                {'id': 'Create_Expense_08', 'type': 'expense'}]

SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_NON_CREDIT_CHARGE = [{'id': 'Booking_11', 'type': 'booking'},
                                                                {'id': "checkinPost_01", 'type': 'check_in'},
                                                                {'id': 'Create_Expense_38', 'type': 'expense'}]

SINGLE_DAY_B2B_CHECKIN_BOOKING_WITH_LINKED_CREDIT_CHARGE = [{'id': 'Booking_11', 'type': 'booking'},
                                                            {'id': "checkinPost_01", 'type': 'check_in'},
                                                            {'id': 'Create_Expense_39', 'type': 'expense'}]

MULTIPLE_DAY_B2B_BOOKING_WITH_FUTURE_NON_CREDIT_NON_LINKED_EXPENSE = [{'id': "Booking_33", 'type': 'booking'},
                                                                      {'id': "checkinPost_08", 'type': 'check_in'},
                                                                      {'id': "Create_Expense_42", 'type': 'expense'}]

MULTIPLE_DAY_B2B_BOOKING_WITH_FUTURE_CREDIT_NON_LINKED_EXPENSE = [{'id': "Booking_33", 'type': 'booking'},
                                                                  {'id': "checkinPost_08", 'type': 'check_in'},
                                                                  {'id': "Create_Expense_43", 'type': 'expense'}]

MULTIPLE_DAY_B2B_BOOKING_WITH_FUTURE_NON_CREDIT_LINKED_EXPENSE = [{'id': "Booking_33", 'type': 'booking'},
                                                                  {'id': "checkinPost_08", 'type': 'check_in'},
                                                                  {'id': "Create_Expense_40", 'type': 'expense'}]

MULTIPLE_DAY_B2B_BOOKING_WITH_FUTURE_CREDIT_LINKED_EXPENSE = [{'id': "Booking_33", 'type': 'booking'},
                                                              {'id': "checkinPost_08", 'type': 'check_in'},
                                                              {'id': "Create_Expense_41", 'type': 'expense'}]

MULTIPLE_DAY_NON_CHECKED_IN_BOOKING_WITH_FUTURE_CREDIT_LINKED_EXPENSE = [{'id': "Booking_33", 'type': 'booking'},
                                                                         {'id': "Create_Expense_41", 'type': 'expense'}]

MULTIPLE_DAY_NON_CHECKED_IN_WITH_FUTURE_NON_CREDIT_LINKED_EXPENSE = [{'id': "Booking_33", 'type': 'booking'},
                                                                     {'id': "Create_Expense_40", 'type': 'expense'}]

SINGLE_DAY_NON_CHECKED_IN_BOOKING_WITH_LINKED_CREDIT_CHARGE = [{'id': 'Booking_11', 'type': 'booking'},
                                                               {'id': 'Create_Expense_39', 'type': 'expense'}]

MULTIPLE_DAY_NON_CHECKED_IN_BOOKING_WITH_FUTURE_CREDIT_NON_LINKED_EXPENSE = [{'id': "Booking_33", 'type': 'booking'},
                                                                             {'id': "Create_Expense_43",
                                                                              'type': 'expense'}]

MULTIPLE_DAY_NON_CHECKED_IN_BOOKING_WITH_FUTURE_NON_CREDIT_NON_LINKED_EXPENSE = [
    {'id': "Booking_33", 'type': 'booking'},
    {'id': "Create_Expense_42", 'type': 'expense'}]

SINGLE_DAY_NON_CHECKED_IN_BOOKING_WITH_NON_LINKED_NON_CREDIT_CHARGE = [{'id': 'Booking_11', 'type': 'booking'},
                                                                       {'id': 'Create_Expense_01', 'type': 'expense'}]

SINGLE_DAY_NON_CHECKED_IN_BOOKING_WITH_NON_LINKED_CREDIT_CHARGE = [{'id': 'Booking_11', 'type': 'booking'},
                                                                   {'id': 'Create_Expense_08', 'type': 'expense'}]

SINGLE_DAY_NON_CHECKED_IN_BOOKING_WITH_LINKED_NON_CREDIT_CHARGE = [{'id': 'Booking_11', 'type': 'booking'},
                                                                   {'id': 'Create_Expense_38', 'type': 'expense'}]

ADD_CONFIRMED_PAYMENT_01 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_01', 'type': 'add_payment_v2'}]
ADD_CONFIRMED_PAYMENT_02 = [{'id': 'Booking_114', 'type': 'booking'},
                            {'id': 'AddPaymentV2_01_old', 'type': 'add_payment_v2'}]
ADD_CONFIRMED_PAYMENT_03 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_01', 'type': 'add_payment_v2'},
                            {'id': 'AddPaymentV2_28', 'type': 'add_payment_v2'}]
ADD_CONFIRMED_PAYMENT_04 = [{'id': 'Booking_114', 'type': 'booking'},
                            {'id': 'AddPaymentV2_26', 'type': 'add_payment_v2'}]
ADD_CONFIRMED_PAYMENT_05 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2'}]
ADD_CONFIRMED_PAYMENT_06 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_55', 'type': 'add_payment_v2'}]
ADD_CONFIRMED_PAYMENT_07 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_63', 'type': 'add_payment_v2'}]
ADD_CONFIRMED_PAYMENT_08 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_71', 'type': 'add_payment_v2'}]
ADD_CONFIRMED_PAYMENT_09 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_81', 'type': 'add_payment_v2'}]
ADD_CONFIRMED_PAYMENT_10 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_89', 'type': 'add_payment_v2'}]
ADD_CONFIRMED_PAYMENT_11 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_98', 'type': 'add_payment_v2'}]
ADD_CONFIRMED_PAYMENT_12 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_106', 'type': 'add_payment_v2'}]
ADD_CONFIRMED_PAYMENT_13 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_112', 'type': 'add_payment_v2'}]
ADD_CONFIRMED_PAYMENT_14 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_115', 'type': 'add_payment_v2'}]
ADD_CONFIRMED_PAYMENT_15 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_125', 'type': 'add_payment_v2'}]
ADD_CONFIRMED_PAYMENT_16 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_133', 'type': 'add_payment_v2'}]
ADD_CONFIRMED_PAYMENT_17 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_18 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_42', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 2, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_19 = [{'id': 'booking_19_01_checkin_04', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_43', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 4, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_20 = [{'id': "booking_27", 'is_booking_v2': True, 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_44', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_21 = [{'id': "booking_48", 'is_booking_v2': True, 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_45', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 2, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_22 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_136', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_23 = [{'id': "booking_27", 'is_booking_v2': True, 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_44', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_145', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_24 = [{'id': 'booking_19_01_checkin_04', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_43', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 2, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_25 = [{'id': "booking_48", 'is_booking_v2': True, 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_45', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_26 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 2, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_27 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_46', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_28 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_47', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 2, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_29 = [{'id': 'booking_19_01_checkin_04', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_48', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 4, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_30 = [{'id': "booking_48", 'is_booking_v2': True, 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_49', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_31 = [{'id': "booking_48", 'is_booking_v2': True, 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_50', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 2, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_32 = [{'id': "booking_01", 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_168', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_33 = [{'id': "booking_01", 'type': 'booking_v2'},
                            {'id': 'AddPaymentCheckoutV2_01', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True, "payment_constraint": True}]
ADD_CONFIRMED_PAYMENT_34 = [{'id': "booking_01", 'type': 'booking_v2'},
                            {'id': 'AddPaymentCheckoutV2_10', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True, "payment_constraint": True}]
ADD_CONFIRMED_PAYMENT_35 = [{'id': "booking_01", 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_168', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_171', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_36 = [{'id': "booking_01", 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_179', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_37 = [{'id': "booking_01", 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_46', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_186', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_38 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_39 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'booking_01_different_ref', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_40 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentGetPaymentRefId_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_41 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_46_02', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_42 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41_50INR', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_43 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_178', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'EditPaymentV2_119', 'type': 'edit_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_44 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_46', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_41_02', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_142_100INR', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'EditPaymentV2_120', 'type': 'edit_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_45 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_46_02', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_182', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_46 = [{'id': "booking_01", 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_186', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_47 = [{'id': "booking_01", 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_179', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_179_02', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_48 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_46_02', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_182', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_186_02', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_49 = [{'id': "booking_01", 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'AddPaymentV2_191', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_50 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'EditPaymentV2_121', 'type': 'edit_payment_v2'}]
ADD_CONFIRMED_PAYMENT_51 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True},
                            {'id': 'EditPaymentV2_121', 'type': 'edit_payment_v2', 'payments_already_posted': True}]
ADD_CONFIRMED_PAYMENT_52 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2', "payor_billed_entity_id": 1,
                             "payment_matrix": True},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2', "payor_billed_entity_id": 1,
                             "payment_matrix": True}]
ADD_CONFIRMED_PAYMENT_53 = [{'id': 'booking_01', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2', "payor_billed_entity_id": 1,
                             "payment_matrix": True},
                            {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2', "payor_billed_entity_id": 1,
                             "payment_matrix": True},
                            {'id': 'EditPaymentV2_121', 'type': 'edit_payment_v2', 'payments_already_posted': True}]
ADD_CONFIRMED_PAYMENT_54 = [{'id': 'booking_01_more_booking_amount', 'type': 'booking_v2'},
                            {'id': 'AddPaymentV2_41_11k', 'type': 'add_payment_v2',
                             "payor_billed_entity_id": 1, "payment_matrix": True}]
EDIT_PAYMENT_01 = [{'id': 'booking_01', 'type': 'booking_v2'},
                   {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2', "payor_billed_entity_id": 1,
                    "payment_matrix": True}, {'id': 'EditPaymentV2_22', 'type': 'edit_payment_v2'}]
EDIT_PAYMENT_02 = [{'id': 'booking_01', 'type': 'booking_v2'},
                   {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2', "payor_billed_entity_id": 1,
                    "payment_matrix": True},
                   {'id': 'AddPaymentV2_41', 'type': 'add_payment_v2', "payor_billed_entity_id": 1,
                    "payment_matrix": True}, {'id': 'EditPaymentV2_22', 'type': 'edit_payment_v2'}]

PARTIAL_CHECKOUT_BOOKING = [{'id': 'invoicePreview_27', 'type': 'preview_invoice'},
                            {'id': 'checkoutAction_11', 'type': 'checkout'}]

PARTIAL_CHECKOUT_BOOKING_V2 = [{'id': 'invoicePreviewCheckoutV2_01', 'type': 'preview_invoice'},
                               {'id': 'CheckoutV2_02', 'type': 'checkout_v2'}]

POST_RATE_PLAN_CHARGE = [{'id': 'booking_01', 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                         {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '1'}]

BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_01 = [{'id': "booking_207", 'type': 'booking_v2',
                                                      'extras': {"has_slab_based_taxation": True}},
                                                     {'id': "EditCharge_40", 'type': 'update_expense', 'charge_id': 1,
                                                      'has_slab_based_taxation': True}]

BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_02 = [{'id': "booking_01", 'type': 'booking_v2',
                                                      'extras': {"has_slab_based_taxation": True}},
                                                     {'id': "EditCharge_40", 'type': 'update_expense', 'charge_id': 1,
                                                      'has_slab_based_taxation': True}]

BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_03 = [{'id': "booking_207", 'type': 'booking_v2'},
                                                     {'id': "EditCharge_40", 'type': 'update_expense', 'charge_id': 1}]

BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_04 = [{'id': "booking_207", 'type': 'booking_v2',
                                                      'extras': {"has_slab_based_taxation": True}},
                                                     {'id': "EditCharge_56", 'type': 'update_expense', 'charge_id': 1,
                                                      'has_slab_based_taxation': True}]

BOOKING_WITH_CHARGE_SPLITS_ON_RATE_PLAN_CHARGE_05 = [{'id': "booking_209", 'type': 'booking_v2',
                                                      'extras': {"has_slab_based_taxation": True}},
                                                     {'id': "EditCharge_57", 'type': 'update_expense', 'charge_id': 1,
                                                      'has_slab_based_taxation': True}]

BOOKING_WITH_CHARGE_SPLITS_CLUBBED_AND_SLAB_BASED = [{'id': "booking_205", 'type': 'booking_v2',
                                                      'extras': {"has_slab_based_taxation": True,
                                                                 "hotel_level_config": CLUBBED_TAX_CONFIG}},
                                                     {'id': "EditCharge_40", 'type': 'update_expense', 'charge_id': 1,
                                                      'has_slab_based_taxation': True,
                                                      'is_tax_clubbed': CLUBBED_TAX_CONFIG}]

BOOKING_WITH_CHARGE_SPLITS_CLUBBED_AND_SLAB_BASED_02 = [{'id': "booking_205", 'type': 'booking_v2',
                                                         'extras': {"has_slab_based_taxation": True,
                                                                    "hotel_level_config": CLUBBED_TAX_CONFIG}},
                                                        {'id': "EditCharge_58", 'type': 'update_expense',
                                                         'charge_id': 1,
                                                         'has_slab_based_taxation': True,
                                                         'is_tax_clubbed': CLUBBED_TAX_CONFIG}]

SINGLE_BOOKING_CHECK_IN_V2_SLAB_BASED_SPLIT_CHARGE = [{'id': "booking_207", 'type': 'booking_v2',
                                                       'extras': {"has_slab_based_taxation": True}},
                                                      {'id': "checkin_01", 'type': 'checkin_v2'},
                                                      {'id': "EditCharge_56", 'type': 'update_expense', 'charge_id': 1,
                                                       'has_slab_based_taxation': True}]

MULTIPLE_ROOM_BOOKING_CHARGE_SPLITS_V2_SLAB_BASED = [{'id': "booking_19", 'type': 'booking_v2',
                                                      'extras': {"has_slab_based_taxation": True}},
                                                     {'id': "EditCharge_40", 'type': 'update_expense', 'charge_id': 1,
                                                      'has_slab_based_taxation': True}]

MULTIPLE_ROOM_BOOKING_WITH_CHARGE_SPLITS_CLUBBED_AND_SLAB_BASED = [{'id': "booking_19", 'type': 'booking_v2',
                                                                    'extras': {"has_slab_based_taxation": True,
                                                                               "hotel_level_config": CLUBBED_TAX_CONFIG}},
                                                                   {'id': "EditCharge_40", 'type': 'update_expense',
                                                                    'charge_id': 1, 'has_slab_based_taxation': True,
                                                                    'is_tax_clubbed': CLUBBED_TAX_CONFIG}]

POSTED_NON_LINKED_CHARGE_FOR_CREATED_BOOKING = [{'id': 'booking_18', 'type': 'booking_v2'},
                                                {'id': 'Create_Expense_01', 'type': 'expense'},
                                                {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '2'}]

POSTED_NON_LINKED_CHARGE_FOR_CHECKEDIN_BOOKING = [{'id': 'booking_18', 'type': 'booking_v2'},
                                                  {'id': "checkin_01", 'type': 'checkin_v2'},
                                                  {'id': 'Create_Expense_51', 'type': 'expense'},
                                                  {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '6'}]

POSTED_LINKED_CHARGE_FOR_CREATED_BOOKING = [{'id': 'booking_171', 'type': 'booking_v2'},
                                            {'id': 'Create_Expense_38', 'type': 'expense'},
                                            {'id': "PostCharge_08", 'type': 'update_expense', 'charge_id': '6'}]

LINKED_CHARGE_FOR_CREATED_BOOKING = [{'id': 'booking_171', 'type': 'booking_v2'},
                                     {'id': 'Create_Expense_38', 'type': 'expense'}]

POSTED_LINKED_CHARGE_FOR_CHECKEDIN_BOOKING = [{'id': 'booking_171', 'type': 'booking_v2'},
                                              {'id': "checkin_01", 'type': 'checkin_v2'},
                                              {'id': 'Create_Expense_38', 'type': 'expense'},
                                              {'id': "PostCharge_08", 'type': 'update_expense', 'charge_id': '6'}]

POSTED_NON_LINKED_CREDIT_CHARGE_FOR_CREATED_BOOKING = [{'id': 'booking_171', 'type': 'booking_v2'},
                                                       {'id': 'Create_Expense_08', 'type': 'expense'},
                                                       {'id': "PostCharge_07", 'type': 'update_expense',
                                                        'charge_id': '2'}]

POSTED_NON_LINKED_CREDIT_CHARGE_FOR_CHECKEDIN_BOOKING = [{'id': 'booking_171', 'type': 'booking_v2'},
                                                         {'id': "checkin_01", 'type': 'checkin_v2'},
                                                         {'id': 'Create_Expense_52', 'type': 'expense'},
                                                         {'id': "PostCharge_07", 'type': 'update_expense',
                                                          'charge_id': '6'}]

POSTED_LINKED_CREDIT_CHARGE_FOR_CREATED_BOOKING = [{'id': 'booking_171', 'type': 'booking_v2'},
                                                   {'id': 'Create_Expense_39', 'type': 'expense'},
                                                   {'id': "PostCharge_09", 'type': 'update_expense', 'charge_id': '2'}]

POSTED_LINKED_CREDIT_CHARGE_FOR_CHECKEDIN_BOOKING = [{'id': 'booking_171', 'type': 'booking_v2'},
                                                     {'id': "checkin_01", 'type': 'checkin_v2'},
                                                     {'id': 'Create_Expense_39', 'type': 'expense'},
                                                     {'id': "PostCharge_09", 'type': 'update_expense',
                                                      'charge_id': '6'}]

CANCEL_CHARGE_FOR_CREATED_BOOKING = [{'id': 'booking_01', 'type': 'booking_v2'},
                                     {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '1'}]

CANCEL_CREATED_BOOKING = [{'id': "booking_01_different_ref", 'type': 'booking_v2'},
                          {'id': 'CancelAction_14', 'type': 'cancel'}]

POST_ROOMSTAY_CREDIT_CHARGE = [{'id': 'Booking_11', 'type': 'booking'},
                               {'id': "checkinPost_01", 'type': 'check_in'},
                               {'id': "PostCharge_09", 'type': 'update_expense', 'charge_id': '1'}]

POST_ROOMSTAY_NON_CREDIT_CHARGE = [{'id': 'Booking_01', 'type': 'booking'},
                                   {'id': "checkinPost_01", 'type': 'check_in'},
                                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'}]

POST_ROOMSTAY_NON_CREDIT_CHARGE_OTA_BOOKING = [{'id': "booking_212", 'type': 'booking_v2'},
                                               {'id': "checkin_01", 'type': 'checkin_v2'},
                                               {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'}]

POST_ROOM_STAY_V2_01_SLAB_BASED = [{'id': "booking_207", 'type': 'booking_v2',
                                    'extras': {"has_slab_based_taxation": True}},
                                   {'id': "checkin_01", 'type': 'checkin_v2'},
                                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'}]

POST_ROOM_STAY_V2_01_SPLIT_SLAB_BASED = [{'id': "booking_207", 'type': 'booking_v2',
                                          'extras': {"has_slab_based_taxation": True}},
                                         {'id': "checkin_01", 'type': 'checkin_v2'},
                                         {'id': "EditCharge_56", 'type': 'update_expense', 'charge_id': '1'},
                                         {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'}]

POSTED_NON_CREDIT_SPLIT_CHARGE = [{'id': 'Booking_11', 'type': 'booking'},
                                  {'id': "checkinPost_01", 'type': 'check_in'},
                                  {'id': "EditCharge_44", 'type': 'update_expense', 'charge_id': '1'},
                                  {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'}]

ADD_ALLOWANCE_NON_CREDIT_CHARGE = [{'id': 'Booking_01', 'type': 'booking'},
                                   {'id': "checkinPost_01", 'type': 'check_in'},
                                   {'id': "PostCharge_09", 'type': 'update_expense', 'charge_id': '1'},
                                   {'id': 'AddAllowance_02', 'type': 'add_allowance_v2', 'extras': [1, 1]}]

ADD_ALLOWANCE_NON_CREDIT_SPLIT_CHARGE = [{'id': 'Booking_01', 'type': 'booking'},
                                         {'id': "checkinPost_01", 'type': 'check_in'},
                                         {'id': "EditCharge_44", 'type': 'update_expense', 'charge_id': '1'},
                                         {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'},
                                         {'id': 'AddAllowance_02', 'type': 'add_allowance_v2', 'extras': [1, 1]},
                                         {'id': 'AddAllowance_02', 'type': 'add_allowance_v2', 'extras': [1, 2]}]

BOOKING_V2_WITH_BOOKED_ALLOWANCE = [{'id': "booking_01", 'type': 'booking_v2'},
                                    {'id': "checkin_01", 'type': 'checkin_v2'},
                                    {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                    {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [1, 1]}]

BOOKING_V2_WITH_3_DAYS_BOOKED_ALLOWANCE = [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
                                           {'id': "checkin_01", 'type': 'checkin_v2'},
                                           {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                           {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [1, 1]}]

BOOKING_V2_WITH_3_DAYS_BOOKED_CHARGE_AND_ALLOWANCE = [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
                                                      {'id': "checkin_01", 'type': 'checkin_v2'},
                                                      {'id': 'Create_Expense_01', 'type': 'expense'},
                                                      {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                                      {'id': "AddAllowance_02", 'type': 'add_allowance_v2',
                                                       'extras': [1, 1]}]

BOOKING_V2_WITH_3_DAYS_1_DAY_PAST_BOOKED_CHARGE_AND_ALLOWANCE = [{'id': "booking_153_invoice_preview_66",
                                                                  'type': 'booking_v2'},
                                                                 {'id': "checkin_01", 'type': 'checkin_v2'},
                                                                 {'id': "PostCharge_01", 'type': 'update_expense',
                                                                  'charge_id': 1},
                                                                 {'id': "AddAllowance_02", 'type': 'add_allowance_v2',
                                                                  'extras': [1, 1]}]

BOOKING_WITH_CANCEL_POSTED_BOOKED_CHARGE = [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
                                            {'id': "checkin_01", 'type': 'checkin_v2'},
                                            {'id': 'Create_Expense_01', 'type': 'expense'},
                                            {'id': 'Create_Expense_01', 'type': 'expense'},
                                            {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '6'},
                                            {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1}]

BOOKING_WITH_CANCELLED_CHARGE = [{'id': 'booking_01', 'type': 'booking_v2'},
                                 {'id': 'Create_Expense_01', 'type': 'expense'},
                                 {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '6'}]

BOOKING_WITH_CANCEL_POSTED_BOOKED_ALLOWANCE = [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
                                               {'id': "checkin_01", 'type': 'checkin_v2'},
                                               {'id': 'Create_Expense_01', 'type': 'expense'},
                                               {'id': 'Create_Expense_01', 'type': 'expense'},
                                               {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                               {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                                               {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 7},
                                               {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [1, 1]},
                                               {'id': "AddAllowance_21_expense_01", 'type': 'add_allowance_v2',
                                                'extras': [6, 1]},
                                               {'id': "UpdateAllowance_02", 'type': 'update_allowance_v2',
                                                'extras': [6, 1, 1, 'cancelled']},
                                               {'id': "AddAllowance_21_expense_01", 'type': 'add_allowance_v2',
                                                'extras': [7, 1]},
                                               {'id': "UpdateAllowance_01", 'type': 'update_allowance_v2',
                                                'extras': [1, 1, 1, 'consumed']}]

BOOKING_WITH_CANCEL_BOOKED_CHARGE_BOOKED_ALLOWANCE = [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
                                                      {'id': "checkin_01", 'type': 'checkin_v2'},
                                                      {'id': 'Create_Expense_01', 'type': 'expense'},
                                                      {'id': 'Create_Expense_01', 'type': 'expense'},
                                                      {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                                      {'id': "AddAllowance_02", 'type': 'add_allowance_v2',
                                                       'extras': [1, 1]},
                                                      {'id': "CancelCharge_01", 'type': 'update_expense',
                                                       'charge_id': '6'}]

BOOKING_WITH_CANCEL_BOOKED_ALLOWANCE = [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
                                        {'id': "checkin_01", 'type': 'checkin_v2'},
                                        {'id': 'Create_Expense_01', 'type': 'expense'},
                                        {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                        {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                                        {'id': "AddAllowance_02", 'type': 'add_allowance_v2', 'extras': [1, 1]},
                                        {'id': "AddAllowance_21_expense_01", 'type': 'add_allowance_v2',
                                         'extras': [6, 1]},
                                        {'id': "UpdateAllowance_02", 'type': 'update_allowance_v2',
                                         'extras': [1, 1, 1, 'cancelled']}]

BOOKING_WITH_CANCEL_BOOKED_ALLOWANCE_BOOKED_CHARGE = [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
                                                      {'id': "checkin_01", 'type': 'checkin_v2'},
                                                      {'id': 'Create_Expense_01', 'type': 'expense'},
                                                      {'id': 'Create_Expense_01', 'type': 'expense'},
                                                      {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                                      {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                                                      {'id': "AddAllowance_02", 'type': 'add_allowance_v2',
                                                       'extras': [1, 1]},
                                                      {'id': "AddAllowance_21_expense_01", 'type': 'add_allowance_v2',
                                                       'extras': [6, 1]},
                                                      {'id': "UpdateAllowance_02", 'type': 'update_allowance_v2',
                                                       'extras': [1, 1, 1, 'cancelled']}]

BOOKING_WITH_CANCEL_POSTED_BOOKED_CHARGE_BOOKED_ALLOWANCE = [{'id': "booking_153_invoice_preview_66",
                                                              'type': 'booking_v2'},
                                                             {'id': "checkin_01", 'type': 'checkin_v2'},
                                                             {'id': 'Create_Expense_01', 'type': 'expense'},
                                                             {'id': 'Create_Expense_01', 'type': 'expense'},
                                                             {'id': 'Create_Expense_01', 'type': 'expense'},
                                                             {'id': "CancelCharge_01", 'type': 'update_expense',
                                                              'charge_id': '6'},
                                                             {'id': "PostCharge_01", 'type': 'update_expense',
                                                              'charge_id': 1},
                                                             {'id': "PostCharge_01", 'type': 'update_expense',
                                                              'charge_id': 7},
                                                             {'id': "AddAllowance_21_expense_01",
                                                              'type': 'add_allowance_v2', 'extras': [7, 1]}]

BOOKING_WITH_CANCEL_POSTED_BOOKED_ALLOWANCE_BOOKED_CHARGE = [{'id': "booking_153_invoice_preview_66",
                                                              'type': 'booking_v2'},
                                                             {'id': "checkin_01", 'type': 'checkin_v2'},
                                                             {'id': 'Create_Expense_01', 'type': 'expense'},
                                                             {'id': 'Create_Expense_01', 'type': 'expense'},
                                                             {'id': 'Create_Expense_01', 'type': 'expense'},
                                                             {'id': "PostCharge_01", 'type': 'update_expense',
                                                              'charge_id': 1},
                                                             {'id': "PostCharge_01", 'type': 'update_expense',
                                                              'charge_id': 6},
                                                             {'id': "PostCharge_01", 'type': 'update_expense',
                                                              'charge_id': 7},
                                                             {'id': "AddAllowance_02", 'type': 'add_allowance_v2',
                                                              'extras': [1, 1]},
                                                             {'id': "AddAllowance_21_expense_01",
                                                              'type': 'add_allowance_v2', 'extras': [6, 1]},
                                                             {'id': "UpdateAllowance_02", 'type': 'update_allowance_v2',
                                                              'extras': [6, 1, 1, 'cancelled']},
                                                             {'id': "AddAllowance_21_expense_01",
                                                              'type': 'add_allowance_v2', 'extras': [7, 1]},
                                                             {'id': "UpdateAllowance_01", 'type': 'update_allowance_v2',
                                                              'extras': [1, 1, 1, 'consumed']}]

BOOKING_WITH_DIFFERENT_BE_AND_CHARGE = [{'id': "booking_two_room_two_guest_three_day_booking", 'type': 'booking_v2'},
                                        {'id': "checkin_two_room_two_guest_three_day_booking", 'type': 'checkin_v2'},
                                        {'id': 'Create_Expense_checkout_10', 'type': 'expense'},
                                        {'id': 'Create_Expense_checkout_10', 'type': 'expense'},
                                        {'id': 'Create_Expense_checkout_28', 'type': 'expense'},
                                        {'id': 'Create_Expense_checkout_28', 'type': 'expense'},
                                        {'id': 'Create_Expense_spot_credit_26', 'type': 'expense'},
                                        {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 15},
                                        {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '17'}]

MULTIPLE_ROOM_BOOKING_02_WITH_ALLOWANCE = [{'id': 'Booking_01', 'type': 'booking'},
                                           {'id': "checkinPost_01", 'type': 'check_in'},
                                           {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'},
                                           {'id': "AddAllowance_03", 'type': 'add_allowance_v2', 'extras': [1, 1]}]

ADD_MULTIPLE_ALLOWANCE_NON_CREDIT_CHARGE = [{'id': 'Booking_01', 'type': 'booking'},
                                            {'id': "checkinPost_01", 'type': 'check_in'},
                                            {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'},
                                            {'id': 'AddAllowance_02', 'type': 'add_allowance_v2', 'extras': [1, 1]},
                                            {'id': 'AddAllowance_18', 'type': 'add_allowance_v2', 'extras': [1, 1]}]

SINGLE_ALLOWANCE_ON_NON_CREDIT_SPLIT_CHARGE = [{'id': 'Booking_11', 'type': 'booking'},
                                               {'id': "checkinPost_01", 'type': 'check_in'},
                                               {'id': "EditCharge_44", 'type': 'update_expense', 'charge_id': '1'},
                                               {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'},
                                               {'id': 'AddAllowance_19', 'type': 'add_allowance_v2', 'extras': [1, 1]}]

BOOKING_WITH_EXTRA_CHARGE = [{'id': "booking_01", 'type': 'booking_v2'},
                             {'id': "checkin_01", 'type': 'checkin_v2'},
                             {'id': 'Multiple_Expense_95', 'type': 'create_expense_V3'},
                             {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                             {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                             {'id': 'invoicePreview_66', 'type': 'preview_invoice'}]

BOOKING_WITH_EXTRA_CHARGE_ALLOWANCE = [{'id': "booking_01", 'type': 'booking_v2'},
                                       {'id': "checkin_01", 'type': 'checkin_v2'},
                                       {'id': 'Multiple_Expense_96', 'type': 'create_expense_V3'},
                                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                       {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                                       {'id': 'AddAllowance_expense_96', 'type': 'add_allowance_v2', 'extras': [6, 1]},
                                       {'id': 'invoicePreview_template_01', 'type': 'preview_invoice'}]

BOOKING_WITH_EXTRA_CHARGE_CLUBBED_TAX = [{'id': "booking_01", 'type': 'booking_v2',
                                          'extras': {'hotel_level_config': CLUBBED_TAX_CONFIG}},
                                         {'id': "checkin_01", 'type': 'checkin_v2'},
                                         {'id': 'Multiple_Expense_95', 'type': 'create_expense_V3'},
                                         {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                         {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                                         {'id': 'invoicePreview_66', 'type': 'preview_invoice'}]

BOOKING_WITH_EXTRA_CHARGE_ALLOWANCE_CLUBBED_TAX = [{'id': "booking_01", 'type': 'booking_v2',
                                                    'extras': {'hotel_level_config': CLUBBED_TAX_CONFIG}},
                                                   {'id': "checkin_01", 'type': 'checkin_v2'},
                                                   {'id': 'Multiple_Expense_96', 'type': 'create_expense_V3'},
                                                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                                                   {'id': 'AddAllowance_expense_96', 'type': 'add_allowance_v2',
                                                    'extras': [6, 1], 'is_tax_clubbed': CLUBBED_TAX_CONFIG},
                                                   {'id': 'invoicePreview_template_01', 'type': 'preview_invoice'}]

BOOKING_WITH_EXTRA_CHARGE_SLAB_BASED_TAX = [{'id': "booking_207", 'type': 'booking_v2',
                                             'extras': {"has_slab_based_taxation": True}},
                                            {'id': "checkin_01", 'type': 'checkin_v2'},
                                            {'id': 'Multiple_Expense_95', 'type': 'create_expense_V3'},
                                            {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                            {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                                            {'id': 'invoicePreview_66', 'type': 'preview_invoice'}]

BOOKING_WITH_EXTRA_CHARGE_AND_ALLOWANCE_ON_ROOM = [{'id': "booking_01", 'type': 'booking_v2',
                                                    'extras': {'hotel_level_config': CLUBBED_TAX_CONFIG}},
                                                   {'id': "checkin_01", 'type': 'checkin_v2'},
                                                   {'id': 'Multiple_Expense_95', 'type': 'create_expense_V3'},
                                                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                                                   {'id': 'AddAllowance_16', 'type': 'add_allowance_v2',
                                                    'extras': [1, 1], 'is_tax_clubbed': CLUBBED_TAX_CONFIG},
                                                   {'id': 'invoicePreview_template_07', 'type': 'preview_invoice'}]

###################### Reissue Invoice ####################################

Modify_Locked_Invoice_01 = [{'id': 'Booking_01', 'type': 'booking'},
                            {'id': 'checkinPost_01', 'type': 'check_in'},
                            {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                            {'id': 'invoicePreview_01', 'type': 'preview_invoice'},
                            {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment', 'is_mock_rule_req': True},
                            {'id': 'checkoutAction_04', 'type': 'checkout'}]

Modify_Locked_Invoice_02 = [{'id': "booking_01_invoice_preview_83", 'type': 'booking_v2'},
                            {'id': "checkin_01", 'type': 'checkin_v2'},
                            {'id': 'Create_Expense_01', 'type': 'expense'},
                            {'id': 'Create_Expense_01', 'type': 'expense'},
                            {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '1'},
                            {'id': 'invoicePreview_modify_locked_invoice_31', 'type': 'preview_invoice'},
                            {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
                            {'id': 'AddPaymentV2_modify_locked_invoice_31', 'type': 'add_payment_v2'},
                            {'id': 'CheckoutV2_103', 'type': 'checkout_v2'}]

INVOICE_NEWLY_MODIFY_LOCKED_INVOICE_01 = [{'id': "booking_01", 'type': 'booking_v2'},
                                          {'id': "checkin_01", 'type': 'checkin_v2'},
                                          {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                          {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                                          {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
                                          {'id': "CheckoutV2_01", 'type': 'checkout_v2'}, {'type': 'get_bill_invoices'},
                                          {'id': 'Modify_Locked_Invoice_06', 'type': 'modify_locked_invoice'},
                                          {'id': 'InvoiceAccount_01', 'type': 'invoice_accounts'}]

INVOICE_NEWLY_MODIFY_LOCKED_INVOICE_02 = [{'id': "booking_01", 'type': 'booking_v2'},
                                          {'id': "checkin_01", 'type': 'checkin_v2'},
                                          {'id': 'Multiple_Expense_99', 'type': 'create_expense_V3'},
                                          {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                          {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                                          {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                                          {'id': "AddPaymentV2_checkout_102", 'type': 'add_payment_v2'},
                                          {'id': "CheckoutV2_01", 'type': 'checkout_v2'}, {'type': 'get_bill_invoices'},
                                          {'id': 'Modify_Locked_Invoice_For_24', 'type': 'modify_locked_invoice'},
                                          {'id': 'InvoiceAccount_01', 'type': 'invoice_accounts'}]

INVOICE_NEWLY_MODIFY_LOCKED_INVOICE_03 = [{'id': "booking_212", 'type': 'booking_v2'},
                                          {'id': "checkin_01", 'type': 'checkin_v2'},
                                          {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                          {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                                          {'id': "AddPaymentV2_checkout_117", 'type': 'add_payment_v2'},
                                          {'id': "CheckoutV2_01", 'type': 'checkout_v2'}, {'type': 'get_bill_invoices'},
                                          {'id': 'Modify_Locked_Invoice_06', 'type': 'modify_locked_invoice'},
                                          {'id': 'InvoiceAccount_01', 'type': 'invoice_accounts'}]

Credit_Shell_01 = [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                   {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                   {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
                   {'id': "CheckoutV2_01", 'type': 'checkout_v2'}, {'type': 'get_bill_invoices'},
                   {'id': 'Modify_Locked_Invoice_01', 'type': 'modify_locked_invoice'}]

Credit_Shell_02 = [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                   {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                   {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
                   {'id': "CheckoutV2_01", 'type': 'checkout_v2'}, {'type': 'get_bill_invoices'},
                   {'id': 'Modify_Locked_Invoice_06', 'type': 'modify_locked_invoice'}]

Credit_Shell_03 = [{'id': 'Booking_11', 'type': 'booking'}, {'id': 'checkinPost_01', 'type': 'check_in'},
                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                   {'id': 'invoicePreview_01', 'type': 'preview_invoice'},
                   {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment', 'is_mock_rule_req': True},
                   {'id': 'checkoutAction_04', 'type': 'checkout'}, {'type': 'get_bill_invoices'},
                   {'id': 'Modify_Locked_Invoice_01', 'type': 'modify_locked_invoice'}]

Credit_Shell_04 = [{'id': 'Booking_11', 'type': 'booking'}, {'id': 'checkinPost_01', 'type': 'check_in'},
                   {'id': 'Create_Expense_12', 'type': 'expense'},
                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                   {'id': 'invoicePreview_01', 'type': 'preview_invoice'},
                   {'id': 'AddPaymentV2_Redistribute_10', 'type': 'add_payment_v2'},
                   {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment', 'is_mock_rule_req': True},
                   {'id': 'checkoutAction_04', 'type': 'checkout'}, {'type': 'get_bill_invoices'},
                   {'id': 'Modify_Locked_Invoice_09', 'type': 'modify_locked_invoice'}]

Credit_Shell_05 = [{'id': 'Booking_01', 'type': 'booking'}, {'id': 'checkinPost_01', 'type': 'check_in'},
                   {'id': 'Create_Expense_12', 'type': 'expense'},
                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 2},
                   {'id': 'invoicePreview_01', 'type': 'preview_invoice'},
                   {'id': 'AddPaymentV2_Redistribute_10', 'type': 'add_payment_v2'},
                   {'id': 'AddPayment_04_ExtraAmount', 'type': 'add_payment', 'is_mock_rule_req': True},
                   {'id': 'checkoutAction_04', 'type': 'checkout'}, {'type': 'get_bill_invoices'},
                   {'id': 'Modify_Locked_Invoice_09', 'type': 'modify_locked_invoice'}]

Credit_Shell_06 = [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                   {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                   {'id': "AddPaymentCheckoutV2_14", 'type': 'add_payment_v2'},
                   {'id': "CheckoutV2_01", 'type': 'checkout_v2'}, {'type': 'get_bill_invoices'},
                   {'id': 'Modify_Locked_Invoice_01', 'type': 'modify_locked_invoice'}]

Credit_Shell_07 = [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                   {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                   {'id': "AddPaymentCreditShell_01", 'type': 'add_payment_v2'},
                   {'id': "CheckoutV2_01", 'type': 'checkout_v2'}, {'type': 'get_bill_invoices'},
                   {'id': 'Modify_Locked_Invoice_01', 'type': 'modify_locked_invoice'}]

Record_Payment_With_Credit_Shell = [{'id': 'booking_01_different_ref', 'type': 'booking_v2'},
                                    {'id': 'AddPayment_43', 'type': 'add_payment', 'is_mock_rule_req': True}]

Record_Multiple_Payment_With_Credit_Shell = [{'id': 'booking_01_different_ref', 'type': 'booking_v2'},
                                             {'id': 'AddPayment_43', 'type': 'add_payment', 'is_mock_rule_req': True},
                                             {'id': 'AddPayment_43', 'type': 'add_payment', 'is_mock_rule_req': True},
                                             {'id': 'AddPayment_43', 'type': 'add_payment', 'is_mock_rule_req': True}]

Record_Payment_With_Credit_Shell_On_Two_Different_booking = [{'id': 'booking_01_different_ref', 'type': 'booking_v2'},
                                                             {'id': 'AddPayment_43', 'type': 'add_payment',
                                                              'is_mock_rule_req': True},
                                                             {'id': 'Booking_01', 'type': 'booking'},
                                                             {'id': 'AddPayment_43', 'type': 'add_payment',
                                                              'is_mock_rule_req': True}]

Record_Payment_Greater_Than_Credit_Shell = [{'id': 'booking_01_different_ref', 'type': 'booking_v2'},
                                            {'id': 'AddPayment_44', 'type': 'add_payment', 'is_mock_rule_req': True}]

Record_Payment_With_Credit_Shell_Edit_Payment_Mode = [{'id': 'booking_01_different_ref', 'type': 'booking_v2'},
                                                      {'id': 'AddPayment_43', 'type': 'add_payment',
                                                       'is_mock_rule_req': True},
                                                      {'id': 'EditPaymentV2_17', 'type': 'edit_payment_v2'}]

Record_Payment_With_Credit_Shell_Increase_Amount_Paid = [{'id': 'booking_01_different_ref', 'type': 'booking_v2'},
                                                         {'id': 'AddPayment_43', 'type': 'add_payment',
                                                          'is_mock_rule_req': True},
                                                         {'id': 'EditPaymentV2_32', 'type': 'edit_payment_v2'}]

Record_Payment_With_Credit_Shell_Decrease_Amount_Paid = [{'id': 'booking_01_different_ref', 'type': 'booking_v2'},
                                                         {'id': 'AddPayment_43', 'type': 'add_payment',
                                                          'is_mock_rule_req': True},
                                                         {'id': 'EditPaymentV2_31', 'type': 'edit_payment_v2'}]

Record_Payment_With_Credit_Shell_Cancel_That_Payment = [{'id': 'booking_01_different_ref', 'type': 'booking_v2'},
                                                        {'id': 'AddPayment_43', 'type': 'add_payment',
                                                         'is_mock_rule_req': True},
                                                        {'id': 'EditPaymentV2_33', 'type': 'edit_payment_v2'}]

Record_Payment_With_Cash_Edit_Payment_Mode_To_CreditShell = [{'id': 'booking_01_different_ref', 'type': 'booking_v2'},
                                                             {'id': 'AddPayment_01', 'type': 'add_payment',
                                                              'is_mock_rule_req': True},
                                                             {'id': 'EditPaymentV2_34', 'type': 'edit_payment_v2'}]

Record_Payment_With_Credit_Shell_Edit_Payment_Mode_And_Amount = [{'id': 'booking_01_different_ref',
                                                                  'type': 'booking_v2'},
                                                                 {'id': 'AddPayment_43', 'type': 'add_payment',
                                                                  'is_mock_rule_req': True},
                                                                 {'id': 'EditPaymentV2_35', 'type': 'edit_payment_v2'}]

Record_Payment_With_Cash_Edit_Payment_Mode_CreditShell_AND_Amount = [{'id': 'booking_01_different_ref',
                                                                      'type': 'booking_v2'},
                                                                     {'id': 'AddPayment_01', 'type': 'add_payment',
                                                                      'is_mock_rule_req': True},
                                                                     {'id': 'EditPaymentV2_36',
                                                                      'type': 'edit_payment_v2'}]

CREATE_MULTIPLE_DAY_DNR_01 = [{'id': 'createDnr_02', 'type': 'create_multiple_dnr'}]
RESOLVE_MULTIPLE_DNRs = [{'id': 'createDnr_02', 'type': 'create_multiple_dnr'},
                         {'id': 'Resolve_bulk_dnr_04', 'type': 'resolve_multiple_dnr'}]
RESOLVE_DNR = [{'id': "createDnr_01", 'type': 'create_dnr', 'user_type': 'super-admin'},
               {'id': 'Resolve_dnr_01', 'type': 'resolve_dnr'}]
GET_MULTIPLE_DNRs = [{'id': 'createDnr_02', 'type': 'create_multiple_dnr'}]

UPLOAD_CREDIT_NOTE_TEMPLATE_01 = [{'id': "booking_01", 'type': 'booking_v2'},
                                  {'id': "checkin_01", 'type': 'checkin_v2'},
                                  {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                  {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                                  {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
                                  {'id': "CheckoutV2_01", 'type': 'checkout_v2'}, {'type': 'get_bill_invoices'},
                                  {'id': 'Modify_Locked_Invoice_01', 'type': 'modify_locked_invoice'}]

###################### New Checkout Flow ####################################
EARLY_CHECKOUT_1ST_DAY_3_DAY_BOOKING = [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
                                        {'id': "checkin_01", 'type': 'checkin_v2'},
                                        {'id': 'invoicePreview_abort_06', 'type': 'preview_invoice'},
                                        {'id': 'AddPaymentV2_Redistribute_15_1', 'type': 'add_payment_v2'},
                                        {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
                                        {'id': 'AddPaymentV2_checkout_91', 'type': 'add_payment_v2'}]

EARLY_CHECKOUT_1ST_DAY_3_DAY_OTA_BOOKING = [{'id': "ota_booking_3_days", 'type': 'booking_v2'},
                                            {'id': "checkin_01", 'type': 'checkin_v2'},
                                            {'id': 'invoicePreview_abort_06', 'type': 'preview_invoice'},
                                            {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
                                            {'id': 'AddPaymentV2_checkout_141', 'type': 'add_payment_v2'}]

EARLY_CHECKOUT_1ST_DAY_3_DAY_BOOKING_CHARGE_SPLIT = [{'id': "booking_153_checkout_138", 'type': 'booking_v2'},
                                                     {'id': "EditCharge_40", 'type': 'update_expense', 'charge_id': 1},
                                                     {'id': "checkin_01", 'type': 'checkin_v2'},
                                                     {'id': 'invoicePreview_abort_06', 'type': 'preview_invoice'},
                                                     {'id': 'AddPaymentV2_checkout_138_01', 'type': 'add_payment_v2'},
                                                     {'id': 'AddPaymentV2_checkout_138_02', 'type': 'add_payment_v2'}]

EARLY_CHECKOUT_CHARGE_SPLIT_SLAB_BASED = [{'id': "booking_153_checkout_138", 'type': 'booking_v2',
                                           'extras': {'has_slab_based_taxation': True}},
                                          {'id': "EditCharge_40", 'type': 'update_expense', 'charge_id': 1,
                                           'has_slab_based_taxation': True},
                                          {'id': "checkin_01", 'type': 'checkin_v2'},
                                          {'id': 'invoicePreview_abort_06', 'type': 'preview_invoice'},
                                          {'id': 'AddPaymentV2_checkout_139_01', 'type': 'add_payment_v2'},
                                          {'id': 'AddPaymentV2_checkout_139_02', 'type': 'add_payment_v2'}]

EARLY_CHECKOUT_CHARGE_SPLIT_SLAB_BASED_CLUBBED_CHARGE = [{'id': "booking_153_checkout_138", 'type': 'booking_v2',
                                                          'extras': {'has_slab_based_taxation': True,
                                                                     'hotel_level_config': CLUBBED_TAX_CONFIG}},
                                                         {'id': "EditCharge_40", 'type': 'update_expense',
                                                          'charge_id': 1,
                                                          'has_slab_based_taxation': True,
                                                          'is_tax_clubbed': CLUBBED_TAX_CONFIG},
                                                         {'id': "checkin_01", 'type': 'checkin_v2'},
                                                         {'id': 'invoicePreview_abort_06', 'type': 'preview_invoice'},
                                                         {'id': 'AddPaymentV2_checkout_140_01',
                                                          'type': 'add_payment_v2'},
                                                         {'id': 'AddPaymentV2_checkout_140_02',
                                                          'type': 'add_payment_v2'}]

EARLY_CHECKOUT_1ST_DAY_3_DAY_BOOKING_EXCESS_PAYMENT = [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
                                                       {'id': "checkin_01", 'type': 'checkin_v2'},
                                                       {'id': 'invoicePreview_abort_06', 'type': 'preview_invoice'},
                                                       {'id': 'AddPaymentV2_Redistribute_10', 'type': 'add_payment_v2'},
                                                       {'id': 'Redistribute_Payments_24',
                                                        'type': 'redistribute_payment'},
                                                       {'id': 'AddPaymentV2_abort_08', 'type': 'add_payment_v2'}]

EARLY_CHECKOUT_BOOKING_HAVING_EXPENSE_CHARGE_ONLY = [{'id': "booking_01_invoice_preview_83", 'type': 'booking_v2'},
                                                     {'id': "checkin_01", 'type': 'checkin_v2'},
                                                     {'id': 'Create_Expense_01', 'type': 'expense'},
                                                     {'id': "CancelCharge_01", 'type': 'update_expense',
                                                      'charge_id': '1'},
                                                     {'id': 'invoicePreview_83', 'type': 'preview_invoice'},
                                                     {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
                                                     {'id': 'AddPaymentV2_checkout_103', 'type': 'add_payment_v2'}]

EARLY_CHECKOUT_BOOKING_HAVING_CANCELLED_EXPENSE = [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
                                                   {'id': "checkin_01", 'type': 'checkin_v2'},
                                                   {'id': 'Create_Expense_01', 'type': 'expense'},
                                                   {'id': "CancelCharge_01", 'type': 'update_expense',
                                                    'charge_id': '6'},
                                                   {'id': 'invoicePreview_131', 'type': 'preview_invoice'},
                                                   {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
                                                   {'id': 'AddPaymentV2_checkout_97', 'type': 'add_payment_v2'}]

EARLY_CHECKOUT_BOOKING_WITH_MULTIPLE_ALLOWANCE = [{'id': "booking_01", 'type': 'booking_v2'},
                                                  {'id': "checkin_01", 'type': 'checkin_v2'},
                                                  {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                                  {'id': "AddAllowance_02", 'type': 'add_allowance_v2',
                                                   'extras': [1, 1]},
                                                  {'id': "AddAllowance_21_expense_01", 'type': 'add_allowance_v2',
                                                   'extras': [1, 1]},
                                                  {'id': 'invoicePreview_143', 'type': 'preview_invoice'},
                                                  {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
                                                  {'id': 'AddPaymentV2_checkout_119', 'type': 'add_payment_v2'}]

EARLY_CHECKOUT_BOOKING_WITH_CANCEL_AND_BOOKED_ALLOWANCE = [{'id': "booking_01", 'type': 'booking_v2'},
                                                           {'id': "checkin_01", 'type': 'checkin_v2'},
                                                           {'id': "PostCharge_01", 'type': 'update_expense',
                                                            'charge_id': 1},
                                                           {'id': "AddAllowance_02", 'type': 'add_allowance_v2',
                                                            'extras': [1, 1]},
                                                           {'id': "AddAllowance_21_expense_01",
                                                            'type': 'add_allowance_v2', 'extras': [1, 1]},
                                                           {'id': 'invoicePreview_144', 'type': 'preview_invoice'},
                                                           {'id': 'Redistribute_Payments_24',
                                                            'type': 'redistribute_payment'},
                                                           {'id': 'AddPaymentV2_checkout_120',
                                                            'type': 'add_payment_v2'}]

EARLY_CHECKOUT_BOOKING_WITH_TWO_ROOM = [{'id': "booking_08", 'type': 'booking_v2'},
                                        {'id': "checkin_preview_invoice_137", 'type': 'checkin_v2'},
                                        {'id': 'invoicePreview_137', 'type': 'preview_invoice'},
                                        {'id': 'Redistribute_Payments_Checkout_116', 'type': 'redistribute_payment'},
                                        {'id': 'AddPaymentV2_checkout_116', 'type': 'add_payment_v2'}]

EARLY_CHECKOUT_BOOKING_WITH_TWO_ROOM_CANCEL_ONE_ROOM_CHARGE = [{'id': "booking_08", 'type': 'booking_v2'},
                                                               {'id': "checkin_preview_invoice_137",
                                                                'type': 'checkin_v2'},
                                                               {'id': 'invoicePreview_139', 'type': 'preview_invoice'},
                                                               {'id': 'Redistribute_Payments_Checkout_116',
                                                                'type': 'redistribute_payment'},
                                                               {'id': 'AddPaymentV2_checkout_117',
                                                                'type': 'add_payment_v2'}]

EARLY_CHECKOUT_BOOKING_WITH_TWO_ROOM_CANCEL_BOTH_ROOM_CHARGE = [{'id': "booking_08", 'type': 'booking_v2'},
                                                                {'id': "checkin_preview_invoice_137",
                                                                 'type': 'checkin_v2'},
                                                                {'id': 'invoicePreview_140', 'type': 'preview_invoice'},
                                                                {'id': 'Redistribute_Payments_Checkout_116',
                                                                 'type': 'redistribute_payment'}]

SINGLE_ROOM_CHECKOUT_BOOKING_01 = [{'id': "booking_01", 'type': 'booking_v2'},
                                   {'id': "checkin_01", 'type': 'checkin_v2'},
                                   {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                   {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                                   {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'}]

CHECKOUT_1ST_DAY_3_DAY_BOOKING_WITH_CHECKOUT = [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
                                                {'id': "checkin_01", 'type': 'checkin_v2'},
                                                {'id': 'invoicePreview_abort_06', 'type': 'preview_invoice'},
                                                {'id': 'AddPaymentV2_Redistribute_15_1', 'type': 'add_payment_v2'},
                                                {'id': 'Redistribute_Payments_24', 'type': 'redistribute_payment'},
                                                {'id': 'AddPaymentV2_checkout_91', 'type': 'add_payment_v2'},
                                                {'id': "CheckoutV2_91", 'type': 'checkout_v2'}]

CHECKOUT_ONE_ROOM_1ST_DAY_IN_MULTI_ROOM_BOOKING = [{'id': "booking_310", 'type': 'booking_v2'},
                                                   {'id': "checkin_78", 'type': 'checkin_v2'},
                                                   {'id': "invoicePreviewCheckoutV2_01_cancel_room_01",
                                                    'type': 'preview_invoice'},
                                                   {'id': "CheckoutV2_158", 'type': 'checkout_v2'}]

CHECKIN_MULTI_ROOM_MULTI_DAY_BOOKING_V1 = [{'id': "booking_310", 'type': 'booking_v2'},
                                           {'id': "checkin_78", 'type': 'checkin_v2'}]

# -------------------Transfer Bulk Charges Before Test Actions------------------
POST_NON_LINKED_CHARGES_FOR_CHECKEDIN = [{'id': 'booking_18', 'type': 'booking_v2'},
                                         {'id': "checkin_01", 'type': 'checkin_v2'},
                                         {'id': 'Create_Expense_51', 'type': 'expense'},
                                         {'id': 'Create_Expense_51', 'type': 'expense'},
                                         {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '6'},
                                         {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '7'}]
POST_LINKED_CHARGES_FOR_2_ROOM_B2B_CHECKIN = [{'id': "booking_136_transfer_charge_09", 'type': 'booking_v2'},
                                              {'id': "checkin_25", 'type': 'checkin_v2'},
                                              {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '1'},
                                              {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '2'}]
TRANSFER_BULK_CHARGE_02 = [{'id': "booking_01", 'type': 'booking_v2'},
                           {'id': "checkin_01", 'type': 'checkin_v2'},
                           {'id': 'Create_Expense_51', 'type': 'expense'},
                           {'id': 'Create_Expense_51', 'type': 'expense'},
                           {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '6'},
                           {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '7'},
                           {'id': "booking_01_different_ref", 'type': 'booking_v2'},
                           {'id': "checkin_01", 'type': 'checkin_v2'}]
CONSUME_CREATED_CHARGE = [{'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '2'}]
TRANSFER_BULK_CHARGE_03 = WALK_IN_ONE_ROOM_BOOKING_CHECK_IN_V2 + CONSUME_CREATED_CHARGE + SINGLE_BOOKING_CHECK_IN_V2_02

TRANSFER_BULK_CHARGE_11 = [{'id': 'booking_01', 'type': 'booking_v2'},
                           {'id': 'Create_Expense_51', 'type': 'expense'},
                           {'id': 'Create_Expense_51', 'type': 'expense'},
                           {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '1'},
                           {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '6'},
                           {'id': "booking_01_different_ref", 'type': 'booking_v2'}]

TRANSFER_BULK_CHARGE_13 = [{'id': 'booking_18', 'type': 'booking_v2'},
                           {'id': "checkin_01", 'type': 'checkin_v2'},
                           {'id': 'Create_Expense_51', 'type': 'expense'},
                           {'id': 'Create_Expense_51', 'type': 'expense'},
                           {'id': "EditCharge_40_01", 'type': 'update_expense', 'charge_id': '6'},
                           {'id': "EditCharge_40_01", 'type': 'update_expense', 'charge_id': '7'},
                           {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '6'},
                           {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '7'}]
BEFORE_CHECKOUT_ACTION_01 = [{'id': "booking_01", 'type': 'booking_v2'},
                             {'id': "checkin_01", 'type': 'checkin_v2'},
                             {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                             {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                             {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'}]

TRANSFER_BULK_CHARGE_16 = [{'id': "booking_01", 'type': 'booking_v2'},
                           {'id': 'put_booking_01', 'type': 'edit_booking_v2'},
                           {'id': "checkin_01_01", 'type': 'checkin_v2'},
                           {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '6'},
                           {'id': "booking_01_different_ref", 'type': 'booking_v2'},
                           {'id': "checkin_01", 'type': 'checkin_v2'}]

TRANSFER_BULK_CHARGE_24 = [{'id': 'booking_154_invoice_preview_77', 'type': 'booking_v2'},
                           {'id': "checkin_01", 'type': 'checkin_v2'},
                           {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '1'},
                           {'id': "booking_154_duplicate", 'type': 'booking_v2'},
                           {'id': "checkin_01", 'type': 'checkin_v2'},
                           {'type': 'perform_night_audit'},
                           {'id': 'PostCharge_01', 'type': 'update_expense', 'bill_index': 0, 'charge_id': '2'}]

TRANSFER_BULK_CHARGE_25 = [{'id': 'booking_30_checkin_25', 'type': 'booking_v2'},
                           {'id': "checkin_25", 'type': 'checkin_v2'},
                           {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '1'},
                           {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '2'},
                           {'id': "booking_136_transfer_charge_09", 'type': 'booking_v2'},
                           {'id': "checkin_25", 'type': 'checkin_v2'}]

TRANSFER_BULK_CHARGE_26 = [{'id': 'booking_30_checkin_25', 'type': 'booking_v2'},
                           {'id': "checkin_25", 'type': 'checkin_v2'},
                           {'id': 'Create_Expense_51', 'type': 'expense'},
                           {'id': 'Create_Expense_51', 'type': 'expense'},
                           {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '10'},
                           {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '11'},
                           {'id': "booking_136_transfer_charge_09", 'type': 'booking_v2'},
                           {'id': "checkin_25", 'type': 'checkin_v2'}]

TRANSFER_BULK_CHARGE_28 = [{'id': 'booking_30_checkin_25', 'type': 'booking_v2'},
                           {'id': "checkin_25", 'type': 'checkin_v2'},
                           {'id': 'Create_Expense_51', 'type': 'expense'},
                           {'id': 'Create_Expense_51', 'type': 'expense'},
                           {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '1'},
                           {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '2'},
                           {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '10'},
                           {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '11'},
                           {'id': 'booking_30_transfer_charge_29', 'type': 'booking_v2'},
                           {'id': "checkin_25_transfer_charge_29", 'type': 'checkin_v2'}]

TRANSFER_BULK_CHARGE_29 = [{'id': 'booking_30_checkin_25', 'type': 'booking_v2'},
                           {'id': "checkin_25", 'type': 'checkin_v2'},
                           {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '1'},
                           {'id': 'PostCharge_01', 'type': 'update_expense', 'charge_id': '2'},
                           {'id': 'booking_30_transfer_charge_29', 'type': 'booking_v2'},
                           {'id': 'cancel_room_01', 'type': 'mark_cancel'},
                           {'id': "checkin_25_transfer_charge_30", 'type': 'checkin_v2'}]

BULK_CHARGE_01 = [{"old_charge_id": "1", "new_charge_id": "6", "consuming_guest_names": ['testing']},
                  {"old_charge_id": "2", "new_charge_id": "7"}, {"old_charge_id": "3", "new_charge_id": "8"},
                  {"old_charge_id": "4", "new_charge_id": "9"}, {"old_charge_id": "5", "new_charge_id": "10"}]

POS_CHARGE_01 = [{"old_charge_id": "1", "new_charge_id": "6", "consuming_guest_names": ['testing']},
                 {"old_charge_id": "2", "new_charge_id": "7"}, {"old_charge_id": "3", "new_charge_id": "8"},
                 {"old_charge_id": "4", "new_charge_id": "9"}, {"old_charge_id": "5", "new_charge_id": "10"},
                 {"old_charge_id": "6", "new_charge_id": "11"}]

BULK_CHARGE_01_16 = [{"old_charge_id": "6", "new_charge_id": "6", "consuming_guest_names": ['testing']},
                     {"old_charge_id": "7", "new_charge_id": "7"}, {"old_charge_id": "8", "new_charge_id": "8"},
                     {"old_charge_id": "9", "new_charge_id": "9"}, {"old_charge_id": "10", "new_charge_id": "10"}]

BULK_CHARGE_02 = [{"old_charge_id": "6", "new_charge_id": "6", "consuming_guest_names": ['testing']},
                  {"old_charge_id": "7", "new_charge_id": "7"}]

BULK_CHARGE_05 = BULK_CHARGE_01 + [{"old_charge_id": "6", "new_charge_id": "11"}]

BULK_CHARGE_06 = [{"old_charge_id": "6", "new_charge_id": "6", "consuming_guest_names": ['testing']}]

BULK_CHARGE_10 = [{"old_charge_id": "1", "new_charge_id": "6", "consuming_guest_names": ['testing'],
                   "billed_entity_account": {"account_number": 2, "billed_entity_id": 1}},
                  {"old_charge_id": "3", "new_charge_id": "7"},
                  {"old_charge_id": "4", "new_charge_id": "8"}, {"old_charge_id": "5", "new_charge_id": "9"},
                  {"old_charge_id": "6", "new_charge_id": "10"}, {"old_charge_id": "2", "new_charge_id": "11"},
                  {"old_charge_id": "7", "new_charge_id": "12"}, {"old_charge_id": "8", "new_charge_id": "13"},
                  {"old_charge_id": "9", "new_charge_id": "14"}]

BULK_CHARGE_11 = BULK_CHARGE_01 + [{"old_charge_id": "7", "new_charge_id": "11",
                                    "consuming_guest_names": ['testing']}]

BULK_CHARGE_13 = [{"old_charge_id": "6", "new_charge_id": "6", "consuming_guest_names": ['testing']},
                  {"old_charge_id": "7", "new_charge_id": "7"}]

BULK_CHARGE_16 = [{"old_charge_ids": ["1", "2", "3", "4", "5"], "new_charge_id": "6",
                   "consuming_guest_names": ['testing']}]

BULK_CHARGE_17 = [{"old_charge_ids": ["6"], "new_charge_id": "6", "consuming_guest_names": ['testing']},
                  {"old_charge_ids": ["7"], "new_charge_id": "7"}]

BULK_CHARGE_18 = [{"old_charge_ids": ["1", "2", "3", "4", "5"], "new_charge_id": "6",
                   "consuming_guest_names": ['testing']}, {"old_charge_ids": ["6"], "new_charge_id": "7"}]

BULK_CHARGE_19 = [{"old_charge_ids": ["6"], "new_charge_id": "6", "consuming_guest_names": ['testing']}]

BULK_CHARGE_20 = [{"old_charge_ids": ["1", "3", "4", "5", "6"], "new_charge_id": "6",
                   "consuming_guest_names": ['testing'], "billed_entity_account":
                       {"account_number": 2, "billed_entity_id": 1}},
                  {"old_charge_ids": ["2", "7", "8", "9"], "new_charge_id": "7"}]

BULK_CHARGE_23 = [{"old_charge_ids": ["6"], "new_charge_id": "6", "consuming_guest_names": ['testing']},
                  {"old_charge_ids": ["7"], "new_charge_id": "7"}]

BULK_CHARGE_24 = [{"old_charge_id": "1", "new_charge_id": "6", "consuming_guest_names": ['testing']},
                  {"old_charge_id": "4", "new_charge_id": "7"}, {"old_charge_id": "2", "new_charge_id": "8"},
                  {"old_charge_id": "5", "new_charge_id": "9"}]

BULK_CHARGE_25 = [{"old_charge_ids": ["1", "4"], "new_charge_id": "6", "consuming_guest_names": ['testing']},
                  {"old_charge_ids": ["2", "5"], "new_charge_id": "7"}]

BULK_CHARGE_26 = [{"old_charge_ids": ["1", "3", "4", "5", "6"], "new_charge_id": "10",
                   "consuming_guest_names": ['testing'], "billed_entity_account":
                       {"account_number": 1, "billed_entity_id": 1}},
                  {"old_charge_ids": ["2", "7", "8", "9"], "new_charge_id": "11"}]

BULK_CHARGE_27 = [{"old_charge_ids": ["10"], "new_charge_id": "10",
                   "consuming_guest_names": ['testing', 'checkin Test 02']},
                  {"old_charge_ids": ["11"], "new_charge_id": "11"}]

BULK_CHARGE_28 = [{"old_charge_ids": ["1", "3", "4", "5", "6"], "new_charge_id": "7",
                   "consuming_guest_names": ['testing'], "billed_entity_account":
                       {"account_number": 1, "billed_entity_id": 2}},
                  {"old_charge_ids": ["2", "7", "8", "9"], "new_charge_id": "8"}]

BULK_CHARGE_29 = [{"old_charge_ids": ["1", "3", "4", "5", "6"], "new_charge_id": "8",
                   "consuming_guest_names": ['checkin Test 02'],
                   "billed_entity_account": {"account_number": 1, "billed_entity_id": 3}},
                  {"old_charge_ids": ["2", "7", "8", "9"], "new_charge_id": "9"}]

BULK_CHARGE_31 = [{"old_charge_id": "1", "new_charge_id": "6", "consuming_guest_names": ['testing'],
                   "billed_entity_account": {"account_number": 1, "billed_entity_id": 2}},
                  {"old_charge_id": "2", "new_charge_id": "7"}, {"old_charge_id": "3", "new_charge_id": "8"},
                  {"old_charge_id": "4", "new_charge_id": "9"}, {"old_charge_id": "5", "new_charge_id": "10"}]

CHECKOUT_AFTER_EDIT_BOOKING = [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
                               {'id': 'Multiple_Expense_97_put_booking', 'type': 'create_expense_V3'},
                               {'id': "put_booking_01", 'type': 'put_booking_v2'},
                               {'id': "edit_booking_checkin_01", 'type': 'checkin_v2'},
                               {'id': 'Create_Expense_checkout_15', 'type': 'expense'},
                               {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                               {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 7}]

CHECKOUT_AFTER_CANCEL_CHARGE_EDIT_BOOKING = [{'id': "booking_153_invoice_preview_66", 'type': 'booking_v2'},
                                             {'id': 'Multiple_Expense_97_put_booking', 'type': 'create_expense_V3'},
                                             {'id': 'Multiple_Expense_97_put_booking', 'type': 'create_expense_V3'},
                                             {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '6'},
                                             {'id': "put_booking_01", 'type': 'put_booking_v2'},
                                             {'id': "edit_booking_checkin_01", 'type': 'checkin_v2'},
                                             {'id': 'Create_Expense_checkout_15', 'type': 'expense'},
                                             {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                                             {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 7},
                                             {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 8}]

SINGLE_DEFAULT_COMPANY_BOOKING_USING_V2 = [
    {'id': "booking_01_default_entity", 'type': 'booking_v2', 'is_booking_v2': True}]
SINGLE_DEFAULT_TA_USING_V2 = [
    {'id': "booking_06_default_entity", 'type': 'booking_v2', 'is_booking_v2': True}]

################################# No-show last room cases #########################################
CHECKOUT_ONE_ROOM_IN_TWO_ROOM_BOOKING = [{'id': "booking_30_no_show_01", 'type': 'booking_v2'},
                                         {'id': 'checkin_24_no_show_01', 'type': 'checkin_v2'},
                                         {'id': "invoicePreviewCheckoutV2_01_no_show_01", 'type': 'preview_invoice'},
                                         {'id': 'CheckoutV2_61_no_show_01', 'type': 'checkout_v2'}]

CHECKOUT_ALL_EXCEPT_ONE_IN_MULTIPLE_ROOM_BOOKING = [{'id': "booking_30_no_show_02", 'type': 'booking_v2'},
                                                    {'id': 'checkin_24_no_show_02', 'type': 'checkin_v2'},
                                                    {'id': "invoicePreviewCheckoutV2_01_no_show_02",
                                                     'type': 'preview_invoice'},
                                                    {'id': 'CheckoutV2_61_no_show_02', 'type': 'checkout_v2'}]

CHECKOUT_AND_CANCEL_IN_MULTIPLE_ROOM_BOOKING = [{'id': "booking_30_no_show_02", 'type': 'booking_v2'},
                                                {'id': 'checkin_24_no_show_01', 'type': 'checkin_v2'},
                                                {'id': 'cancel_room_05_cancel_room_03', 'type': 'mark_cancel'},
                                                {'id': "invoicePreviewCheckoutV2_01_no_show_01",
                                                 'type': 'preview_invoice'},
                                                {'id': 'CheckoutV2_61_no_show_01', 'type': 'checkout_v2'}]

CHECKOUT_AND_NOSHOW_REVERSE_NOSHOW_BOOKING = [{'id': "booking_30_no_show_01", 'type': 'booking_v2'},
                                              {'id': 'checkin_24_no_show_01', 'type': 'checkin_v2'},
                                              {'id': "invoicePreviewCheckoutV2_01_no_show_01",
                                               'type': 'preview_invoice'},
                                              {'id': 'CheckoutV2_61_no_show_01', 'type': 'checkout_v2'},
                                              {'id': 'MarkNoShow_135', 'type': "mark_no_show"},
                                              {'type': 'delete_booking_action'}]

################################# Cancel last room cases #########################################
CHECKOUT_ONE_ROOM_IN_TWO_ROOM_BOOKING_V2 = [{'id': "booking_30_cancel_room_01", 'type': 'booking_v2'},
                                            {'id': 'checkin_24_cancel_room_01', 'type': 'checkin_v2'},
                                            {'id': "invoicePreviewCheckoutV2_01_cancel_room_01",
                                             'type': 'preview_invoice'},
                                            {'id': 'CheckoutV2_61_cancel_room_01', 'type': 'checkout_v2'}]

CHECKOUT_ALL_EXCEPT_ONE_IN_MULTIPLE_ROOM_BOOKING_V2 = [{'id': "booking_30_cancel_room_02", 'type': 'booking_v2'},
                                                       {'id': 'checkin_24_cancel_room_02', 'type': 'checkin_v2'},
                                                       {'id': "invoicePreviewCheckoutV2_01_cancel_room_02",
                                                        'type': 'preview_invoice'},
                                                       {'id': 'CheckoutV2_61_cancel_room_02', 'type': 'checkout_v2'}]

CHECKIN_AND_PREVIEW_INVOICE_BOOKING = [{'id': "booking_30_no_show_02", 'type': 'booking_v2'},
                                       {'id': 'checkin_24_cancel_room_01', 'type': 'checkin_v2'},
                                       {'id': "invoicePreviewCheckoutV2_01_cancel_room_01", 'type': 'preview_invoice'}]

CHECKOUT_AND_NOSHOW_AFTER_NIGHT_AUDIT = [{'id': "MarkNoShow_137", 'type': 'mark_no_show'},
                                         {'id': 'CheckoutV2_61_cancel_room_01', 'type': 'checkout_v2'}]

CHECKOUT_REVERSE_CHECKOUT_AND_CHECKOUT_AGAIN = [{'id': "booking_30_cancel_room_01", 'type': 'booking_v2'},
                                                {'id': 'checkin_24_cancel_room_01', 'type': 'checkin_v2'},
                                                {'id': "invoicePreviewCheckoutV2_01_cancel_room_01",
                                                 'type': 'preview_invoice'},
                                                {'id': 'CheckoutV2_61_cancel_room_01', 'type': 'checkout_v2'},
                                                {'type': 'delete_booking_action'},
                                                {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                                                {'id': 'CheckoutV2_61_cancel_room_01', 'type': 'checkout_v2'}]

########################### Relocate booking to Treebo hotel cases ###################################
CREATE_TWO_BOOKINGS_MAKE_FULL_PAYMENT_FOR_ONE_AND_CANCEL = [{'id': "booking_01_different_ref", 'type': 'booking_v2'},
                                                            {'id': "booking_01", 'type': 'booking_v2'},
                                                            {'id': "AddPaymentV2_Redistribute_22",
                                                             'type': 'add_payment_v2'},
                                                            {'id': 'EditPaymentV2_39', 'type': 'edit_payment_v2'}]

CREATE_TWO_BOOKINGS_MAKE_PART_PAYMENT_FOR_ONE_AND_CANCEL = [{'id': "booking_01_different_ref", 'type': 'booking_v2'},
                                                            {'id': "booking_01", 'type': 'booking_v2'},
                                                            {'id': "AddPaymentV2_Redistribute_23",
                                                             'type': 'add_payment_v2'},
                                                            {'id': 'EditPaymentV2_40', 'type': 'edit_payment_v2'}]

CREATE_TWO_BOOKINGS_MAKE_MULTIPLE_PAYMENTS_FOR_ONE_V1 = [{'id': "booking_01_different_ref", 'type': 'booking_v2'},
                                                         {'id': "booking_01", 'type': 'booking_v2'},
                                                         {'id': "create_booking_payment_32", 'type': 'add_payment_v2'},
                                                         {'id': "create_booking_payment_33", 'type': 'add_payment_v2'},
                                                         {'id': "AddPaymentV2_Redistribute_23",
                                                          'type': 'add_payment_v2'},
                                                         {'id': 'EditPaymentV2_40', 'type': 'edit_payment_v2'}]

CREATE_TWO_BOOKINGS_MAKE_MULTIPLE_PAYMENTS_FOR_ONE_V2 = [{'id': "booking_01_different_ref", 'type': 'booking_v2'},
                                                         {'id': "booking_01", 'type': 'booking_v2'},
                                                         {'id': "create_booking_payment_34", 'type': 'add_payment_v2'},
                                                         {'id': "AddPaymentV2_Redistribute_23",
                                                          'type': 'add_payment_v2'},
                                                         {'id': 'EditPaymentV2_40', 'type': 'edit_payment_v2'}]

########################### Relocate booking to Non-Treebo hotel cases ###################################
CREATE_BOOKING_MAKE_FULL_PAYMENT_AND_CANCEL = [{'id': "booking_01", 'type': 'booking_v2'},
                                               {'id': "AddPaymentV2_Redistribute_22", 'type': 'add_payment_v2'},
                                               {'id': 'EditPaymentV2_39', 'type': 'edit_payment_v2'}]

CREATE_BOOKING_MAKE_PART_PAYMENT_AND_CANCEL = [{'id': "booking_01", 'type': 'booking_v2'},
                                               {'id': "AddPaymentV2_Redistribute_23", 'type': 'add_payment_v2'},
                                               {'id': 'EditPaymentV2_40', 'type': 'edit_payment_v2'}]

CREATE_BOOKING_MAKE_MULTIPLE_PAYMENTS_V1 = [{'id': "booking_01", 'type': 'booking_v2'},
                                            {'id': "create_booking_payment_32", 'type': 'add_payment_v2'},
                                            {'id': "create_booking_payment_33", 'type': 'add_payment_v2'},
                                            {'id': "AddPaymentV2_Redistribute_23", 'type': 'add_payment_v2'},
                                            {'id': 'EditPaymentV2_40', 'type': 'edit_payment_v2'}]

CREATE_BOOKING_MAKE_MULTIPLE_PAYMENTS_V2 = [{'id': "booking_01", 'type': 'booking_v2'},
                                            {'id': "create_booking_payment_34", 'type': 'add_payment_v2'},
                                            {'id': "AddPaymentV2_Redistribute_23", 'type': 'add_payment_v2'},
                                            {'id': 'EditPaymentV2_40', 'type': 'edit_payment_v2'}]

######################### charge_to field verify for bookings having add-ons cases ################################
CHECKOUT_BOOKING_HAVING_ADDONS = [{'id': "booking_302", 'type': 'booking_v2'},
                                  {'id': "AddOns_01", 'type': 'add_addon_v1'},
                                  {'id': "checkin_72", 'type': 'checkin_v2'},
                                  {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                  {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                                  {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                                  {'id': "AddPaymentCheckoutV2_16", 'type': 'add_payment_v2'}]

CHECKOUT_RECREATED_OR_EDITED_BOOKING = [{'id': 'booking_303', 'type': 'booking_v2'},
                                        {'id': 'put_booking_105_01', 'type': 'put_booking_v2'},
                                        {'id': "checkin_73", 'type': 'checkin_v2'},
                                        {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 5},
                                        {'id': "invoicePreviewCheckoutV2_10", 'type': 'preview_invoice'},
                                        {'id': "AddPaymentCheckoutV2_17", 'type': 'add_payment_v2'}]

CHECKOUT_RECREATED_BOOKING_HAVING_ADDONS = [{'id': 'booking_304', 'type': 'booking_v2'},
                                            {'id': "AddOns_01", 'type': 'add_addon_v1'},
                                            {'id': 'put_booking_105_01', 'type': 'put_booking_v2'},
                                            {'id': "checkin_73", 'type': 'checkin_v2'},
                                            {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 5},
                                            {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                                            {'id': "invoicePreviewCheckoutV2_10", 'type': 'preview_invoice'},
                                            {'id': "AddPaymentCheckoutV2_18", 'type': 'add_payment_v2'}]

CHECKOUT_REVERSE_CHECKEDOUT_AND_RECREATED_BOOKING_HAVING_ADDONS = [{'id': 'booking_304', 'type': 'booking_v2'},
                                            {'id': "AddOns_01", 'type': 'add_addon_v1'},
                                            {'id': 'put_booking_105_01', 'type': 'put_booking_v2'},
                                            {'id': "checkin_73", 'type': 'checkin_v2'},
                                            {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 5},
                                            {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6},
                                            {'id': "invoicePreviewCheckoutV2_10", 'type': 'preview_invoice'},
                                            {'id': "AddPaymentCheckoutV2_18", 'type': 'add_payment_v2'},
                                            {'id': "CheckoutV2_152", 'type': 'checkout_v2'},
                                            {'type': 'delete_booking_action'},
                                            {'id': "invoicePreviewCheckoutV2_10", 'type': 'preview_invoice'},
                                            ]

UPDATE_BOOKING_HAVING_ADDONS_EDIT_COMMENT = [{'id': "booking_28", 'type': 'booking_v2', 'is_booking_v2': True},
                                            {'id': "AddOns_01", 'type': 'add_addon_v1'}]

UPDATE_BOOKING_HAVING_ADDONS_EDIT_EMAIL_PHONE = [{'id': "booking_305", 'type': 'booking_v2', 'is_booking_v2': True},
                                                 {'id': "AddOns_01", 'type': 'add_addon_v1'}]

#################################### Auto+Manual funding cases ####################################
CHECKIN_CHECKOUT_TCP_BOOKING_V1 = [{'id': "checkin_74", 'type': 'checkin_v2'},
                                   {'id': "AddPaymentCheckoutV2_19", 'type': 'add_payment_v2'},
                                   {'id': "invoicePreviewCheckoutV2_11", 'type': 'preview_invoice'},
                                   {'id': 'CheckoutV2_154', 'type': 'checkout_v2'}]

CHECKIN_CHECKOUT_TCP_BOOKING_V2 = [{'id': "checkin_75", 'type': 'checkin_v2'},
                                   {'id': "AddPaymentCheckoutV2_20", 'type': 'add_payment_v2'},
                                   {'id': "invoicePreviewCheckoutV2_12", 'type': 'preview_invoice'},
                                   {'id': 'CheckoutV2_155', 'type': 'checkout_v2'}]

CHECKIN_CHECKOUT_TCP_BOOKING_V3 = [{'id': "checkin_76", 'type': 'checkin_v2'},
                                   {'id': "AddPaymentCheckoutV2_21", 'type': 'add_payment_v2'},
                                   {'id': "invoicePreviewCheckoutV2_13", 'type': 'preview_invoice'},
                                   {'id': 'CheckoutV2_156', 'type': 'checkout_v2'}]

CHECKIN_CHECKOUT_TCP_BOOKING_V4 = [{'id': "booking_306", 'type': 'booking_v2'},
                                   {'id': "checkin_74", 'type': 'checkin_v2'},
                                   {'id': "AddPaymentCheckoutV2_19", 'type': 'add_payment_v2'},
                                   {'id': "invoicePreviewCheckoutV2_11", 'type': 'preview_invoice'},
                                   {'id': 'CheckoutV2_154', 'type': 'checkout_v2'}]

ADD_PAYMENT_AND_CHECKOUT_V1 = [{'id': "AddPaymentCheckoutV2_21", 'type': 'add_payment_v2'},
                               {'id': "invoicePreviewCheckoutV2_14", 'type': 'preview_invoice'},
                               {'id': 'CheckoutV2_157', 'type': 'checkout_v2'}]

#################################### ECI-LCO Inventory Management Cases ####################################
CREATE_ECI_BLOCK_FOR_BOOKING_V1 = [{'id': "booking_312", 'type': 'booking_v2'},
                                   {'id': "Create_Expense_92", 'type': 'create_expense_V3'}]

CREATE_ECI_BLOCK_FOR_BOOKING_V2 = [{'id': "booking_315", 'type': 'booking_v2'},
                                   {'id': "Create_Expense_95", 'type': 'create_expense_V3'}]

CREATE_LCO_BLOCK_FOR_BOOKING = [{'id': "booking_313", 'type': 'booking_v2'},
                                  {'id': "Create_Expense_93", 'type': 'create_expense_V3'}]

CREATE_TEMP_BLOCK_FOR_BOOKING_V1 = [{'id': "booking_312", 'type': 'booking_v2'},
                                    {'id': "CreateInventoryBlock_01", 'type': 'create_inventory_block'}]

CREATE_TEMP_BLOCK_FOR_BOOKING_V2 = [{'id': "booking_312", 'type': 'booking_v2'},
                                    {'id': "CreateInventoryBlock_02", 'type': 'create_inventory_block'}]

CHECKIN_CHECKOUT_BOOKING_WITH_LCO_V1 = [{'id': "booking_313", 'type': 'booking_v2'},
                                        {'id': "Create_Expense_93", 'type': 'create_expense_V3'},
                                        {'id': "checkin_01", 'type': 'checkin_v2'},
                                        {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
                                        {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
                                        {'id': "AddPaymentCheckoutV2_01", 'type': 'add_payment_v2'},
                                        {'id': "CheckoutV2_02", 'type': 'checkout_v2'}]

CHECKIN_BOOKING_WITH_LCO_BLOCK = [{'id': "booking_314", 'type': 'booking_v2'},
                                  {'id': "Create_Expense_94", 'type': 'create_expense_V3'},
                                  {'id': "checkin_79", 'type': 'checkin_v2'}]

CONVERT_TEMP_TO_ECI_BLOCK_FOR_BOOKING = [{'id': "booking_315", 'type': 'booking_v2'},
                                         {'id': "CreateInventoryBlock_03", 'type': 'create_inventory_block'},
                                         {'id': "Create_Expense_98", 'type': 'create_expense_V3'}]

CONVERT_TEMP_TO_LCO_BLOCK_CHECKIN_BOOKING = [{'id': "booking_314", 'type': 'booking_v2'},
                                             {'id': "CreateInventoryBlock_04", 'type': 'create_inventory_block'},
                                             {'id': "Create_Expense_99", 'type': 'create_expense_V3'},
                                             {'id': "checkin_79", 'type': 'checkin_v2'}]

CREATE_ECI_BLOCK_THEN_EDIT_BOOKING = [{'id': "booking_312", 'type': 'booking_v2'},
                                      {'id': "Create_Expense_92", 'type': 'create_expense_V3'},
                                      {'id': "put_booking_22", 'type': 'edit_booking_v2'}]

CREATE_LCO_BLOCK_THEN_EDIT_BOOKING = [{'id': "booking_313", 'type': 'booking_v2'},
                                      {'id': "Create_Expense_93", 'type': 'create_expense_V3'},
                                      {'id': "put_booking_22", 'type': 'edit_booking_v2'}]

CREATE_ECI_BLOCK_THEN_CANCEL_ECI_CHARGE = [{'id': "booking_312", 'type': 'booking_v2'},
                                           {'id': "Create_Expense_92", 'type': 'create_expense_V3'},
                                           {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '2'}]

CREATE_LCO_BLOCK_THEN_CANCEL_LCO_CHARGE = [{'id': "booking_313", 'type': 'booking_v2'},
                                           {'id': "Create_Expense_93", 'type': 'create_expense_V3'},
                                           {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '6'}]
