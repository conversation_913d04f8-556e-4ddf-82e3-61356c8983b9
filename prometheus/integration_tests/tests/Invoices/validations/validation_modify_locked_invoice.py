import json

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_validations import BaseValidations
from prometheus.integration_tests.utilities.common_utils import assert_, return_date, query_execute, sanitize_test_data
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationModifyLockedInvoice(BaseValidations):
    def __init__(self, client_, test_case_id, response, hotel_id, booking_request, booking_id, user_type,
                 billed_entity_and_account=None):
        self.test_data = get_test_case_data(sheet_names.modify_locked_invoice_sheet_name, test_case_id)[0]
        self.test_case_id = test_case_id
        self.hotel_id = hotel_id
        self.response = response
        self.client = client_
        self.booking_request = booking_request
        self.booking_id = booking_id
        self.user_type = user_type
        self.billed_entity_and_account = billed_entity_and_account

    def validate_response(self):
        actual_accounts_summary = self.response['data']['accounts_used_for_modified_charges']
        expected_accounts_summary = json.loads(self.test_data['expected_accounts_summary'])
        sorted_expected_accounts_summary = sorted(expected_accounts_summary,
                                                  key=lambda i: (i['billed_entity_account']['billed_entity_id'],
                                                                 i['billed_entity_account']['account_number']))
        sorted_actual_accounts_summary = sorted(actual_accounts_summary,
                                                key=lambda i: (i['billed_entity_account']['billed_entity_id'],
                                                               i['billed_entity_account']['account_number']))
        for actual_account_summary, expected_account_summary in zip(sorted_actual_accounts_summary,
                                                                    sorted_expected_accounts_summary):
            self.validate_bill_summary(actual_account_summary['account_summary'],
                                       expected_account_summary['account_summary'], self.hotel_id)

        actual_credit_notes = self.response['data']['credit_notes']
        expected_credit_notes = json.loads(self.test_data['expected_credit_notes'])
        sorted_actual_credit_notes = sorted(actual_credit_notes, key=lambda i: i['pretax_amount'])
        sorted_expected_credit_notes = sorted(expected_credit_notes, key=lambda i: i['pretax_amount'])
        for actual_credit_note, expected_credit_note in zip(sorted_actual_credit_notes, sorted_expected_credit_notes):
            assert_(actual_credit_note['posttax_amount'], expected_credit_note['posttax_amount'])
            assert_(actual_credit_note['pretax_amount'], expected_credit_note['pretax_amount'])
            assert_(actual_credit_note['tax_amount'], expected_credit_note['tax_amount'])
            assert_(actual_credit_note['status'], expected_credit_note['status'])
            assert_(actual_credit_note['credit_note_date'], str(return_date(expected_credit_note['credit_note_date'])))
            if sanitize_test_data(self.test_data['comment']):
                actual_comment_added_in_credit_note = query_execute(db_queries.GET_CREDIT_NOTE_COMMENT.format(
                    credit_note_id=actual_credit_note['credit_note_id'])).fetchall()
                expected_comment = self.test_data['comment']
                assert_(str(actual_comment_added_in_credit_note[0][0]), expected_comment)

    def validate_room_stay_charge_id_map(self):
        actual_charge_ids = set()
        expected_room_stay_charge_ids_map = json.loads(self.test_data['expected_room_stay_charge_id_map'])
        for expected_room_stay_charge_id_map in expected_room_stay_charge_ids_map:
            get_response = self.booking_request.get_room_stay_details_v2(
                self.client, self.test_case_id, 200, sheet_names.modify_locked_invoice_sheet_name, self.user_type,
                int(expected_room_stay_charge_id_map['room_stay_id']))
            for date_wise_charge_id in get_response['data']['date_wise_charge_ids']:
                actual_charge_ids.add(str(date_wise_charge_id['charge_id']))
            assert_(sorted(list(actual_charge_ids)), sorted(expected_room_stay_charge_id_map['charge_ids']))

    def validate_expense(self):
        actual_expense_response = self.booking_request.get_all_expense_by_booking_id(self.client, self.booking_id,
                                                                                     200)['data']
        expected_expense_response = json.loads(self.test_data['expected_expenses'])
        sorted_actual_expense_response = sorted(actual_expense_response, key=lambda i: i['expense_id'])
        sorted_expected_expense_response = sorted(expected_expense_response, key=lambda i: i['expense_id'])
        for actual_expense, expected_expense in zip(sorted_actual_expense_response, sorted_expected_expense_response):
            assert_(actual_expense['added_by'], expected_expense['added_by'])
            assert_(actual_expense['assigned_to'], expected_expense['assigned_to'])
            assert_(actual_expense['comments'], expected_expense['comments'])
            assert_(actual_expense['expense_id'], expected_expense['expense_id'])
            assert_(actual_expense['expense_item_id'], expected_expense['expense_item_id'])
            assert_(actual_expense['room_stay_id'], expected_expense['room_stay_id'])
            assert_(actual_expense['sku_id'], expected_expense['sku_id'])
            assert_(actual_expense['status'], expected_expense['status'])
            assert_(actual_expense['via_addon'], expected_expense['via_addon'])

    def validate_commissions(self):
        if sanitize_test_data(self.test_data['expected_commissions']):
            self.validate_ta_commissions(json.loads(self.test_data['expected_commissions']))
        else:
            self.validate_ta_commissions()

    def validate_invoice_charge_contain_room_stay_id_or_not(self):
        get_count_of_invoice_charges_not_contain_room_stay_id = query_execute(
            db_queries.INVOICE_CHARGES_NOT_CONTAINS_ROOM_STAY_ID).fetchall()
        assert len(get_count_of_invoice_charges_not_contain_room_stay_id) is 0
        get_created_charge_count = query_execute(db_queries.CHARGE_WITH_CREATED_STATUS).fetchall()
        assert len(get_created_charge_count) is 0

    def validate_tax_from_invoice(self):
        get_tax_value_from_invoice = query_execute(db_queries.GET_TAX_FROM_INVOICE.format(
            billed_entity_id=self.billed_entity_and_account[0],
            billed_entity_account_number=self.billed_entity_and_account[1])).fetchall()
        expected_tax = self.test_data['expected_tax_details']
        assert_(str(get_tax_value_from_invoice), expected_tax)
