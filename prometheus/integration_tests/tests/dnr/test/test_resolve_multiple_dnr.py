import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *


class TestResolveMultipleDnrs(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_message",
        [
            ("Resolve_bulk_dnr_01", "", "Resolve bulk dnr, with empty array", 400, 'super-admin', "04010006",
             "[Dnr Ids] -> Send at least one dnr id for bulk resolve dnrs", "", "", ""),
            ("Resolve_bulk_dnr_02", "", "Resolve bulk dnr, with empty String values", 404, 'super-admin', "04010007",
             "Aggregate: DNRAggregate with id:  missing.", "", "", ""),
            ("Resolve_bulk_dnr_03", "", "Resolve bulk dnr, with null String values", 500, 'super-admin', "04010007",
             "'int' object has no attribute 'replace'", "", "", ""),
            ("Resolve_bulk_dnr_04", CREATE_MULTIPLE_DAY_DNR_01, "Resolve Valid DNR's", 200, 'super-admin', "", "", "",
             "", ""),
            ("Resolve_bulk_dnr_05", [CREATE_DNR_01], "Resolve Valid Single DNR", 200, 'super-admin', "", "", "", "",
             ""),
            ("Resolve_bulk_dnr_06", [CREATE_DNR_01], "for multiple DNR's some are valid and some are invalid", 404,
             'super-admin', "04010007", "Aggregate: DNRAggregate with id: invalid-DNR missing.", "", "", ""),
            ("Resolve_bulk_dnr_07", "", "for multiple DNR's all invalid DNRs", 404, 'super-admin',
             "04010007", "Aggregate: DNRAggregate with id: ", "", "", ""),
            ("Resolve_bulk_dnr_08", "", "resolve single invalid DNR", 404, 'super-admin', "04010007",
             "Aggregate: DNRAggregate with id: invalid-DNR missing.", "", "", ""),
            ("Resolve_bulk_dnr_09", RESOLVE_MULTIPLE_DNRs, "resolve multiple inactive DNR", 400, 'super-admin',
             "04010607", "DNR is already resolved", "", "", "")
        ])
    def test_resolve_bulk_dnr(self, client_, test_case_id, previous_actions, tc_description, status_code, user_type,
                              error_code, error_message, dev_message, error_payload, skip_message):

        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if ("MultiCurrency" in tc_description) else HOTEL_ID[0]
        self.dnr_request.dnr_ids = []
        self.dnr_request.allotment_ids = []
        self.dnr_request.room_ids = []
        if previous_actions:
            self.common_request_caller(client_, previous_actions)

        if test_case_id == 'Resolve_bulk_dnr_06' or test_case_id == 'Resolve_bulk_dnr_08':
            self.dnr_request.dnr_ids.append("invalid-DNR")

        if test_case_id == 'Resolve_bulk_dnr_07':
            self.dnr_request.dnr_ids.append("invalid-DNR-1")
            self.dnr_request.dnr_ids.append("invalid-DNR-2")
            self.dnr_request.dnr_ids.append("invalid-DNR-3")

        if test_case_id == 'Resolve_bulk_dnr_09':
            self.dnr_request.dnr_ids = self.dnr_request.inactive_dnr_ids

        response = self.dnr_request.bulk_resolve_dnr(client_, test_case_id, hotel_id, status_code,
                                                     self.dnr_request.dnr_ids)
        get_dnrs_response = self.dnr_request.get_multiple_dnr_request(client_, hotel_id, 200)

        if status_code in ERROR_CODES:

            # this code appends the DNR ids in order which they are appearing in error message
            # because order of dnr list in input is not equal to order of dnr list in output
            if test_case_id == 'Resolve_bulk_dnr_07':
                actual_error_message = response['errors'][0]['message']
                array = actual_error_message.split(' ')
                for index in range(array.index('id:') + 1, len(array)):
                    if index == len(array) - 1:
                        error_message = error_message + " " + array[index]
                        break
                    error_message = error_message + array[index]
            self.response_validation_negative_cases(response, error_code, error_message, None, None)

        elif status_code in SUCCESS_CODES:
            dnr_rooms_expected = response['data']
            self.validate_success_response(dnr_rooms_expected)
            self.assert_resolved_dnrs_in_getdnrs(response['data'], get_dnrs_response['data'])

    def validate_success_response(self, dnr_rooms_expected):
        sorted_response_by_dnr_id = sorted(dnr_rooms_expected, key=lambda i: (i['dnr_id']))
        for index, value in enumerate(sorted(self.dnr_request.dnr_ids)):
            assert sorted_response_by_dnr_id[index]['dnr_id'] == value
            assert dnr_rooms_expected[index]['status'] == 'inactive'
        assert len(self.dnr_request.dnr_ids) == len(dnr_rooms_expected)

    def assert_resolved_dnrs_in_getdnrs(self, marked_dnrs_response, get_dnrs_response):
        assert sorted(marked_dnrs_response, key=lambda i: int(i['room_id'])) == sorted(get_dnrs_response,
                                                                                       key=lambda i: int(i['room_id']))
