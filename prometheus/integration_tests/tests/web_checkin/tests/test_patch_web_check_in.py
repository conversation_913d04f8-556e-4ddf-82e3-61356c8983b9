import pytest

from prometheus.integration_tests.resources.db_queries import DELETE_ATTACHMENT
from prometheus.integration_tests.utilities.common_utils import query_execute
from prometheus.integration_tests.tests.web_checkin.validations.validation_patch_web_checkin import \
    ValidationPatchWebCheckIn
from prometheus.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *


class TestPatchWebCheckIn(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, error_message, "
        "dev_message, error_payload, skip_case",
        [
            ("PatchWebCheckin_01", "Single booking web-checkin", Patch_Web_check_in_01, 200, 'super_admin', "", "", "",
             "", ""),
            ("PatchWebCheckin_02", "Single booking web-checkin when booking is in reserved state", Patch_Web_check_in_02
             , 200, 'super_admin', "", "", "", "", ""),
            ("PatchWebCheckin_03", "Booking web-checkin with multiple rooms and guests", Patch_Web_check_in_03, 200,
             'super_admin', "", "", "", "", "Either attachments are not verified or rooms are not assigned"),
            ("PatchWebCheckin_04", "Booking web-checkin with multiple rooms and guests", Patch_Web_check_in_04, 200,
             'super_admin', "", "", "", "", "Either attachments are not verified or rooms are not assigned"),
            # doubt in PatchWebCheckin_05
            ("PatchWebCheckin_05", "Single booking web-checkin with web-checkin status as rejected",
             Patch_Web_check_in_02, 200, 'super_admin', "", "", "", "", "API can't mark web-checkin as rejected"),
            ("PatchWebCheckin_06", "Single booking web-checkin with no room allocation", Patch_Web_check_in_05, 400,
             'super_admin', "", "", "", "", ""),
            ("PatchWebCheckin_07", "Single booking web-checkin with no attachment verified", Patch_Web_check_in_06, 400,
             'super_admin', "", "", "", "", "Skipping for now"),
            ("PatchWebCheckin_08", "Single booking web-checkin with wrong guest_id", Patch_Web_check_in_07, 400,
             'super_admin', "", "", "", "", "Skipping for now"),

        ])
    @pytest.mark.regression
    def test_patch_web_check_in(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                                error_code, error_message, dev_message, error_payload, skip_case):

        query_execute(DELETE_ATTACHMENT)
        if skip_case:
            pytest.skip()

        if previous_actions:
            self.common_request_caller(client_, previous_actions)

        response = self.web_checkin_request.patch_web_checkin_request(client_, test_case_id, status_code,
                                                                      self.booking_request.booking_id)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, response, test_case_id, booking_request):
        validation = ValidationPatchWebCheckIn(client, response, test_case_id)
        validation.validate_response(booking_request)
