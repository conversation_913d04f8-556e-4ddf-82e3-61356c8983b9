import ast
import json
from random import random

from prometheus.integration_tests.config import common_config, sheet_names
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import (
    del_none,
    increment_date,
    return_date,
    sanitize_test_data,
)

rate_manager_data = None


def get_rack_manger_data():
    return rate_manager_data


class RateManagerClient:

    def __init__(self, test_case_id=None, sheet_name=None, hotel_id='0016932', is_put_booking_v2=False):
        self.test_case_id = test_case_id
        self.sheet_name = sheet_name
        self.hotel_id = hotel_id
        self.is_put_booking_v2 = is_put_booking_v2

    def set_rate_plan_data(self):
        booking_data_from_sheet = excel_utils.get_test_case_data(self.sheet_name, self.test_case_id)[0]
        room_stay_ids = booking_data_from_sheet['room_stay'].split(',')
        if self.is_put_booking_v2:
            put_booking_room_stays = []
            for room_stay in room_stay_ids:
                room_stay_id = room_stay.split('#')[0]
                put_booking_room_stays.append(room_stay_id)
            room_stay_ids = put_booking_room_stays
        rate_plans = []
        rate_id = 1
        if self.is_put_booking_v2:
            rate_id = 2
        for room_stay_id in room_stay_ids:
            room_stay_data_from_sheet = excel_utils.get_test_case_data(sheet_names.room_stays_sheet_name, room_stay_id)[
                0]
            rate_plan_id = room_stay_data_from_sheet['rate_plan_detail_id']
            rp_detail_data_from_sheet = \
                excel_utils.get_test_case_data(sheet_names.rate_plan_details_sheet_name, rate_plan_id)[0]
            rate_plan_reference_id = room_stay_data_from_sheet['rate_plan_reference_id']
            child_policy = {
                "child_allowed": bool(rp_detail_data_from_sheet['child_allowed']),
                "unit_of_charge": sanitize_test_data(rp_detail_data_from_sheet['unit_of_charge']),
                "charge_per_child": sanitize_test_data(rp_detail_data_from_sheet['charge_per_child'])
            }

            payment_policies = [{
                "advance_payment_percentage": sanitize_test_data(
                    rp_detail_data_from_sheet['advance_payment_percentage']),
                "unit_of_payment_percentage": sanitize_test_data(
                    rp_detail_data_from_sheet['unit_of_payment_percentage']),
                "days_before_checkin_to_make_payment": sanitize_test_data(
                    rp_detail_data_from_sheet['days_before_checkin_to_make_payment']),
                "occupancy_percentage": sanitize_test_data(
                    rp_detail_data_from_sheet['occupancy_percentage']),
            }]
            cancellation_policies = None
            if sanitize_test_data(rp_detail_data_from_sheet['cancellation_policy']):
                cancellation_policies = json.loads(rp_detail_data_from_sheet['cancellation_policy'])

            reward_policy = None
            if sanitize_test_data(rp_detail_data_from_sheet['reward_policy']):
                reward_policy = json.loads(rp_detail_data_from_sheet['reward_policy'])
            policies = {
                "cancellation_policies": cancellation_policies,
                "child_policy": child_policy,
                "payment_policy": payment_policies,
                "reward_policy": reward_policy
            }
            restrictions = None
            if sanitize_test_data(rp_detail_data_from_sheet['restrictions']):
                restrictions = json.loads(rp_detail_data_from_sheet['restrictions'])

            room_type_occupancy_mappings = [{"adult_count": 0, "room_type_id": "*"}]

            channel_mapping = [{
                "active": True,
                "channel": "*",
                "channel_rate_plan_code": "ALL1",
                "sub_channel": "*"}]

            rate_plan_dict = {
                'channel_mapping': channel_mapping,
                'description': "Rate Plan For Automation Purpose",
                'is_active': True,
                'is_flexi': bool(sanitize_test_data(rp_detail_data_from_sheet['is_flexi'])),
                'name': "automation" + rate_plan_reference_id,
                'non_room_night_inclusion_rates': None,
                'package_id': "PKG-" + self.hotel_id + "-" + rate_plan_reference_id,
                'policies': policies,
                'property_id': self.hotel_id,
                'rate_plan_id': rate_plan_reference_id,
                'room_type_occupancy_mappings': room_type_occupancy_mappings,
                'short_code': "automation" + rate_plan_reference_id,
                'package_data': {
                    'inclusions': [],
                    'package_id': "PKG-" + self.hotel_id + "-" + rate_plan_reference_id,
                    'package_name': "Auto-PKG " + rate_plan_reference_id,
                    'property_id': self.hotel_id
                },
                'rate_plan_id_for_validation': rate_id
            }

            if restrictions:
                rate_plan_dict['restrictions'] = restrictions
            # Default values for Print Rate is True and Suppress Rate is false
            rate_plan_dict['print_rate'] = True
            rate_plan_dict['suppress_rate'] = False
            if rp_detail_data_from_sheet['print_rate'] is not None:
                print_rate = bool(rp_detail_data_from_sheet['print_rate'])
                rate_plan_dict['print_rate'] = print_rate
            if rp_detail_data_from_sheet['suppress_rate'] is not None:
                suppress_rate = bool(rp_detail_data_from_sheet['suppress_rate'])
                rate_plan_dict['suppress_rate'] = suppress_rate


            rate_id += rate_id
            rate_plans.append(rate_plan_dict)
        global rate_manager_data
        rate_manager_data = rate_plans
