from prometheus.integration_tests.builders.common_request_builder import *
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import (
    del_none,
    sanitize_test_data,
)


class GetRefundDetailsForCancellationPolicy:
    def __init__(self, sheet_name, test_case_id, booking_id):
        self.sheet_name = sheet_name
        self.test_case_id = test_case_id
        self.booking_id = booking_id

    def get_refund_details_for_cancellation_policy_data(self):
        get_refund_details_for_cancellation_policy_data_from_sheet = excel_utils.get_test_case_data(
            self.sheet_name, self.test_case_id)[0]
        cancellation_charge = sanitize_test_data(
            get_refund_details_for_cancellation_policy_data_from_sheet['cancellation_charge'])
        cancellation_policy = sanitize_test_data(
            get_refund_details_for_cancellation_policy_data_from_sheet['cancellation_policy'])
        return json.dumps(del_none({
            'data': {
                'cancellation_charge': cancellation_charge,
                'cancellation_policy': cancellation_policy
            }
        }))
