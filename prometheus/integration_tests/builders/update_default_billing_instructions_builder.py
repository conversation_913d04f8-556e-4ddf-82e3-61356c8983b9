from prometheus.integration_tests.builders.common_request_builder import *
from prometheus.integration_tests.builders.common_request_builder import booking_repo
from prometheus.integration_tests.utilities import excel_utils


class UpdateDefaultBillingInstructions:
    def __init__(self, sheet_name, test_case_id, booking_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = Data(test_data).__dict__
        self.resource_version = booking_repo().load(booking_id).booking.version


class Data(object):
    def __init__(self, test_data):
        self.default_billed_entity_category = sanitize_test_data(test_data['default_billed_entity_category'])
        self.default_billed_entity_category_for_extras = sanitize_test_data(
            test_data['default_billed_entity_category_for_extras'])
        self.default_payment_instruction = sanitize_test_data(test_data['default_payment_instruction'])
        self.default_payment_instruction_for_extras = sanitize_test_data(
            test_data['default_payment_instruction_for_extras'])
        self.update_existing_charges = sanitize_test_data(test_data['update_existing_charges'])
