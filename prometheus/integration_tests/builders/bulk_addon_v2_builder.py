from prometheus.integration_tests.builders import add_on_v2_builder
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.builders.common_request_builder import booking_repo


class BulkAddOnV2(object):
    def __init__(self, booking_id, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        add_ons_data = excel_utils.get_test_case_data(sheet_names.add_ons_sheet_name,
                                                      test_data['Add_ons_ID'])

        self.data = AddOn(add_ons_data).__dict__
        self.resource_version = booking_repo().load(booking_id).booking.version


class AddOn(object):
    def __init__(self, add_ons_data):
        self.addons = []
        for add_on_data in add_ons_data:
            add_on_payload_data = add_on_v2_builder.Data(add_on_data).__dict__
            self.addons.append(add_on_payload_data)
