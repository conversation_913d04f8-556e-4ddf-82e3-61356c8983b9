from prometheus.integration_tests.builders.common_request_builder import *
from prometheus.integration_tests.config.common_config import ROOM_TYPE_MAP
from prometheus.integration_tests.config.sheet_names import add_prices_sheet_name
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import sanitize_test_data, get_room_type_id
from prometheus.integration_tests.requests.inventory_requests import InventoryRequests


class AllocateRoom(object):
    def __init__(self, sheet_name, test_case_id, booking_id, client, hotel_id, enable_rate_plan, is_inclusion_added):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = Data(test_data, client, hotel_id, enable_rate_plan, is_inclusion_added).__dict__
        self.resource_version = booking_repo().load(booking_id).booking.version


class Data(object):
    def __init__(self, test_data, client, hotel_id, enable_rate_plan, is_inclusion_added):
        if sanitize_test_data(test_data['prices']) and test_data['prices'] != 'NULL':
            self.prices = []
            price_data = excel_utils.get_test_case_data(add_prices_sheet_name, test_data['prices'])
            for price in price_data:
                self.prices.append(Prices(price, enable_rate_plan).__dict__)
        else:
            self.prices = sanitize_test_data(test_data['prices'])

        if sanitize_test_data(test_data['rate_plan_inclusions']) and is_inclusion_added:
            self.rate_plan_inclusions = []
            rate_plan_inclusions_data = excel_utils.get_test_case_data(sheet_names.
                                                                       rate_plan_inclusions_v2_booking_sheet_name,
                                                                       test_data['rate_plan_inclusions'])
            for rate_plan_inclusion_data in rate_plan_inclusions_data:
                self.rate_plan_inclusions.append(RatePlanInclusion(rate_plan_inclusion_data).__dict__)

        if sanitize_test_data(test_data['room_id']) or test_data['room_id'] == 'EMPTY':
            room_id = sanitize_test_data(test_data['room_id'])
        else:
            room_id = str(InventoryRequests().get_available_room_ids(
                client, test_data['checkin_date'], test_data['checkout_date'],
                get_room_type_id(test_data['room_type_id']), 200, True, hotel_id)[0])

        if room_id != 'DELETE':
            if room_id and room_id.isdigit():
                self.room_allocation = {'room_id': int(room_id)}
            else:
                self.room_allocation = {'room_id': room_id}

        if sanitize_test_data(test_data['room_type_id']) and test_data['room_type_id'] not in ('INVALID', 'NULL'):
            room_type_id = ROOM_TYPE_MAP[test_data['room_type_id'].lower()]
        else:
            room_type_id = sanitize_test_data(test_data['room_type_id'])

        if test_data['price_change_required'] != 'DELETE':
            self.room_type = {'room_type_id': room_type_id,
                              'price_change_required': sanitize_test_data(test_data['price_change_required'])}

    @staticmethod
    def get_room_stay_id(sheet_name, test_case_id):
        data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        if sanitize_test_data(data['room_stay_id']) and data['room_stay_id'] != 'INVALID':
            return int(data['room_stay_id'])
        return data['room_stay_id']

    @staticmethod
    def get_booking_id(sheet_name, test_case_id):
        data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        return data['Booking_id_for_invalid_case']
