import json

from prometheus.integration_tests.builders.common_request_builder import get_resource_version
from prometheus.integration_tests.utilities.common_utils import del_none


class UpdateTACommission:
    def __init__(self, booking_id, commission_value, commission_type='percent', commission_tax=None):
        self.booking_id = booking_id
        self.commission_value = commission_value
        self.commission_type = commission_type
        self.commission_tax = commission_tax

    def update_ta_commission_request(self):
        data = {
            'commission_type': self.commission_type,
            'commission_value': self.commission_value
        }

        # Add commission_tax if provided
        if self.commission_tax:
            data['commission_tax'] = self.commission_tax

        resource_version = get_resource_version(self.booking_id)
        return json.dumps(del_none({'data': data, 'resource_version': resource_version}))
