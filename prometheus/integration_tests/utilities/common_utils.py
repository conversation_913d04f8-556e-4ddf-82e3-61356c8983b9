import json
import re
import time
from datetime import timedelta, date, datetime
from decimal import Decimal

from treebo_commons.utils.dateutils import get_timezone

from prometheus.infrastructure.database import db_engine
from prometheus.integration_tests.config import common_config
from prometheus.integration_tests.config.common_config import ROOM_TYPE_MAP, HOTEL_CURRENCY_MAP, HOTEL_ID
from prometheus.integration_tests.resources.db_queries import UPDATE_INVENTORY_COUNT
from prometheus.integration_tests.utilities import excel_utils


# get year short version
def get_current_year_short():
    return time.strftime("%y", time.localtime())


# returns the date_time for the given day with timezone
def increment_date(days, timezone=None, seconds=None):
    if timezone is None:
        return return_date(days, seconds).strftime(common_config.DEFAULT)
    else:
        return return_date(days, seconds).strftime(timezone)


# returns only the date for the given day
def return_date(days, seconds=None):
    if seconds:
        return datetime.now(get_timezone()) + timedelta(days=int(days), seconds=int(seconds))
    else:
        return date.today() + timedelta(days=int(days))


# todo: with time completely replace sanitize_blank method with sanitize_test_data
# sanitize the values for blank fields from excel
def sanitize_blank(value):
    if value == '' or value == 'null' or value == {} or value == 'BLANK' or value == 'NULL' or value == 'emptyString':
        return None
    elif value == 'False':
        return False
    elif value == 'True':
        return True
    else:
        return value


# interpret the values for NULL/EMPTY/blank fields from excel
def sanitize_test_data(data):
    if data == '' or data == 'null' or data == 'emptyString':
        return None
    elif data == 'NULL':
        return 'NULL_KEY'
    elif data == 'EMPTY':
        return ''
    elif data == 'blank array':
        return []
    elif isinstance(data, bool) or data in ('False', 'false', 'FALSE', 'True', 'true', 'TRUE'):
        if str(data).lower() == 'false':
            return False
        else:
            return True
    elif isinstance(data, dict):
        if all(value is None for value in data.values()):
            return None
        elif all(value == 'NULL_KEY' or value is None for value in data.values()):
            return 'NULL_KEY'
        else:
            return data
    else:
        return data


# for deleting the null values and empty dict from object passed
def del_none(dict_object):
    for key, value in list(dict_object.items()):
        if isinstance(value, dict):
            del_none(value)
        elif isinstance(value, list):
            for val in value:
                if isinstance(val, dict):
                    del_none(val)
        if value == {} or value is None:
            del dict_object[key]
        if value == 'NULL_KEY':
            dict_object[key] = None
        elif value == '':
            dict_object[key] = ''
    return dict_object


# from room type name to room type id
def get_room_type_id(room_type):
    return ROOM_TYPE_MAP[room_type.lower()]


def assert_(actual_value, expected_value, failure_message=None):
    assert actual_value == expected_value, str(failure_message) + ". ACTUAL: " + str(
        actual_value) + " EXPECTED: " + str(expected_value)


def query_execute(query, commit_required=True):
    result = db_engine.get_scoped_session(None).execute(query)
    if commit_required:
        db_engine.get_scoped_session(None).commit()
    return result


def custom_serializer(obj):
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    elif isinstance(obj, Decimal):
        return str(obj)
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")


def query_execute_and_convert_to_json(query, commit_required=True):
    result = query_execute(query, commit_required)
    columns = result.keys()
    result_data = result.fetchall()
    data = []
    for row in result_data:
        data.append(dict(zip(columns, row)))
    # Convert the data to JSON using the custom serialization function
    json_data = json.loads(json.dumps(data, default=custom_serializer))
    return json_data


def convert_string_to_date(date_string):
    return datetime.strptime(date_string, '%Y-%m-%d').date()


def get_e_reg_card_config(enabled="false", required="false", level="room"):
    return {
        'config_name': "e_reg_card",
        'config_value': "{\"enabled\":" + enabled + "," + "\"required\":" + required + ", \"level\":\"" + level + "\"}",
        'value_type': 'json'
    }


def get_cashiering_config():
    return {
        "config_name": "cashiering_enabled",
        "config_value": "true",
        "value_type": "boolean"
    }


def get_club_with_room_rate_for_taxation():
    return {
        "config_name": "inclusion_config.club_with_room_rate_for_taxation",
        "config_value": True,
        "value_type": "boolean"
    }


def get_rate_manager_enabled():
    return [{
        "config_name": "rate_manager_enabled",
        "config_value": True,
        "value_type": "boolean"
    }]


def get_currency_and_amount(amount):
    return amount.split(' ')


def set_inventory_count(count, hotel_id):
    query_execute(UPDATE_INVENTORY_COUNT.format(count, hotel_id))


def convert_2d_list_to_1d_list(list):
    return [data for sublist in list for data in sublist]


def assert_amount_related_fields(actual_data, expected_data, hotel_id='0016932'):
    if hotel_id != HOTEL_ID[0]:
        expected_data = HOTEL_CURRENCY_MAP[hotel_id] + " " + expected_data
    assert_(actual_data, expected_data)


def get_room_stay_id(sheet_name, test_case_id):
    data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
    if sanitize_test_data(data['room_stay_id']) and data['room_stay_id'] != 'INVALID':
        return int(data['room_stay_id'])
    return data['room_stay_id']


def get_booking_id(sheet_name, test_case_id):
    data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
    return data['Booking_id_for_invalid_case']


def get_folio_number(sheet_name, test_case_id):
    data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
    if sanitize_test_data(data['folio_number']) and data['folio_number'] != 'INVALID':
        return int(data['folio_number'])
    return data['folio_number']


def exp_date(number_of_days):
    today = date.today()
    expiry_date = (today + timedelta(days=number_of_days)).strftime('%m/%Y')
    return expiry_date[0:3] + expiry_date[5:8]


def assert_gstin_regex(test_case_id, gstin_num, failure_message="Invalid GSTin"):
    pat = re.compile(r"([0-9]){2}([0-9A-Z]){10}([A-Z1-9]){1}[1-9A-JMNRSZ]{1}([A-Z0-9]){1}?")
    if test_case_id not in ("put_booking_46", "put_booking_51", "put_booking_71"):
        assert re.fullmatch(pat, gstin_num)
    else:
        str(failure_message)


def convert_to_decimal(value):
    if isinstance(value, str):
        match = re.search(r'([A-Z]{3}\s?)?(\d+(\.\d+)?)', value)
        if match:
            value = match.group(2)

    return Decimal(value)
