import logging

from kombu import Exchange, Producer
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from object_registry import register_instance
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.infrastructure.consumers.consumer_config import (
    ESDataSyncPublisherConfig,
)
from prometheus.infrastructure.messaging.queue_service import BaseQueueService
from ths_common.constants.tenant_settings_constants import TenantSettingName
from ths_common.exceptions import CRSException, ValidationException

logger = logging.getLogger(__name__)


class ESDataSyncEvent:
    def __init__(self, body, routing_key):
        self.body = body
        self.routing_key = routing_key


@register_instance()
class ESDataSyncJobPublisher(BaseQueueService):
    def _setup_entities(self):
        config = ESDataSyncPublisherConfig()
        exchange = Exchange(
            config.exchange_name, type=config.exchange_type, durable=True
        )
        self._tenant_wise_producers = dict()
        connection = self.tenant_wise_connection[
            TenantSettingName.TREEBO_TENANT_ID.value
        ]
        self._tenant_wise_producers[
            TenantSettingName.TREEBO_TENANT_ID.value
        ] = Producer(channel=connection.channel(), exchange=exchange)

    def publish(self, event: ESDataSyncEvent):
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        if tenant_id != TenantSettingName.TREEBO_TENANT_ID.value:
            raise ValidationException(ApplicationErrors.UN_SUPPORTED_ACTION_FOR_TENANT)
        self._initialize()
        payload = event.body
        routing_key = event.routing_key
        logger.debug('Publishing event %s', payload)

        if not self._tenant_wise_producers[tenant_id]:
            raise CRSException(
                description="RMQ Producer not configured for tenant_id: {0}".format(
                    tenant_id
                )
            )

        self._publish(self._tenant_wise_producers[tenant_id], payload, routing_key)
