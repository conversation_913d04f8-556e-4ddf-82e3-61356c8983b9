from marshmallow import Schema, fields


class ESBookingDataSchema(Schema):
    booking_id = fields.String(required=True)
    bill_id = fields.String(required=True)
    reference_number = fields.String(required=True)
    hotel_id = fields.String(required=True)
    group_name = fields.String(allow_none=True)
    company_legal_name = fields.String(allow_none=True)
    company_profile_id = fields.String(allow_none=True)
    travel_agent_legal_name = fields.String(allow_none=True)
    travel_agent_profile_id = fields.String(allow_none=True)
    status = fields.String(allow_none=True)
    channel_code = fields.String(allow_none=True)
    subchannel_code = fields.String(allow_none=True)
    application_code = fields.String(allow_none=True)
    customers = fields.List(fields.Dict())
    checkin_date = fields.LocalDateTime(required=True)
    checkout_date = fields.DateTime(required=True)
    created_at = fields.LocalDateTime()
    net_balance = fields.Float(allow_none=True)
