from object_registry import register_instance
from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from prometheus.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from ths_common.exceptions import DownstreamSystemFailure


@register_instance()
class PaymentServiceClient(BaseExternalClient):
    page_map = {
        'issue_refund': dict(
            type=BaseExternalClient.CallTypes.POST, url_regex="/api/paynow/v3/refund/"
        ),
        'issue_refund_via_payout_link': dict(
            type=BaseExternalClient.CallTypes.POST, url_regex="/api/paynow/payout-link/"
        ),
        'cancel_payout_link': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/api/paynow/payout-link/cancel/",
        ),
        'fetch_payout_link': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/api/paynow/payout-link/{payout_link_id}/",
        ),
        'issue_refund_via_treebo_wallet': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/api/paynow/v2/refund/",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_payment_service_url()

    @staticmethod
    def extract_error_message_from_payment_service(response):
        error_responses = response.errors or []
        error_response = (
            error_responses[0]
            if error_responses and isinstance(response.errors, list)
            else error_responses
        )
        error_message = error_response or {}
        if 'developer_message' in error_message:
            error_message = error_message['developer_message']
        elif 'message' in error_message:
            error_message = error_message['message']
        return error_message

    def cancel_payout_link(self, request_data):
        page_name = "cancel_payout_link"
        response = self.make_call(page_name, data=request_data)
        if not response.is_success():
            error_message = self.extract_error_message_from_payment_service(response)
            raise DownstreamSystemFailure(
                "Payment Service Errors: {} [cancel_payout_link API]".format(
                    error_message
                ),
                error_message,
            )
        return response.json_response['data']

    def fetch_payout_link(self, payout_link_id):
        page_name = "fetch_payout_link"
        url_params = dict(payout_link_id=payout_link_id)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            error_message = self.extract_error_message_from_payment_service(response)
            raise DownstreamSystemFailure(
                "Payment Service Response: {}".format(error_message),
                error_message,
            )
        return response.json_response['data']

    def issue_refund_via_payout_link(self, refund_dto):
        page_name = "issue_refund_via_payout_link"
        return self._issue_refund(page_name, refund_dto)

    def issue_refund_via_treebo_wallet(self, refund_dto):
        page_name = "issue_refund_via_treebo_wallet"
        return self._issue_refund(page_name, refund_dto)

    def issue_refund(self, refund_dto):
        page_name = "issue_refund"
        return self._issue_refund(page_name, refund_dto)

    def _issue_refund(self, page_name, refund_dto):
        try:
            response = self.make_call(page_name, data=refund_dto)
            if not response.is_success():
                error_message = self.extract_error_message_from_payment_service(
                    response
                )
                raise DownstreamSystemFailure(error_message)
            return response.json_response['data'], None
        except DownstreamSystemFailure as dsf:
            return None, dsf.message
