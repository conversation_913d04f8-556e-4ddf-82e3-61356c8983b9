class RolePrivilegesDTO:
    def __init__(self, privilege_code, module, attributes, description):
        self.privilege_code = privilege_code
        self.module = module
        self.attributes = attributes
        self.description = description

    @staticmethod
    def from_json(role_privilege_json):
        return (
            RolePrivilegesDTO(
                privilege_code=role_privilege_json.get('privilege_code'),
                module=role_privilege_json.get('module'),
                attributes=role_privilege_json.get('attributes'),
                description=role_privilege_json.get('description'),
            )
            if role_privilege_json
            else None
        )

    @staticmethod
    def array_to_dict(role_privilege_dtos):
        privilege_code_attributes_dict = dict()
        if not role_privilege_dtos:
            return privilege_code_attributes_dict
        for role_privilege_dto in role_privilege_dtos:
            privilege_code_attributes_dict[role_privilege_dto.privilege_code] = (
                role_privilege_dto.attributes
                if not role_privilege_dto.attributes
                else [attr for attr in role_privilege_dto.attributes]
            )
        return privilege_code_attributes_dict
