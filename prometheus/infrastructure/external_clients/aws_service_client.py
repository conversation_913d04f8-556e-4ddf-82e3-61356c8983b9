import json
import logging
import os
from urllib.parse import urlparse

import boto3 as boto3
from botocore.client import Config
from botocore.exceptions import ClientError

from object_registry import register_instance
from prometheus import crs_context
from prometheus.infrastructure.external_clients.core import constants
from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from ths_common.exceptions import DownstreamSystemFailure

logger = logging.getLogger(constants.EXTERNAL_CLIENTS_LOGGER_PREFIX + __name__)


@register_instance()
class AwsServiceClient(BaseExternalClient):
    """
    Amazon Web Services Client
    """

    def get_domain(self):
        # Stub to confirm to abstract implementation BaseExternalClient
        raise NotImplementedError("get_domain for AWS client is invalid")

    @staticmethod
    def get_region():
        return os.environ.get('AWS_REGION')

    @staticmethod
    def get_S3_region():
        return os.environ.get('AWS_S3_REGION')

    @staticmethod
    def get_s3_bucket_name():
        return os.environ.get('AWS_S3_BUCKET_NAME')

    @classmethod
    def get_night_audit_topic_arn(cls):
        region_name = cls.get_region()
        return f"arn:aws:sns:{region_name}:605536185498:superhero-nightaudits"

    @classmethod
    def get_client(cls, service_name, region_name=None):
        # Create AWS client for each service only once in the request cycle
        client = crs_context.aws_clients.get(service_name)
        if not client:
            if region_name is None:
                region_name = cls.get_S3_region()
            session = boto3.session.Session()
            client = session.client(
                service_name,
                region_name=region_name,
                config=Config(signature_version='s3v4'),
            )
            crs_context.aws_clients[service_name] = client
        return client

    @classmethod
    def get_s3_client(cls):
        return cls.get_client('s3')

    @classmethod
    def get_sns_client(cls):
        region_name = cls.get_region()
        return cls.get_client('sns', region_name)

    @classmethod
    def get_sqs_client(cls):
        return cls.get_client('sqs')

    @classmethod
    def upload_file_to_s3_and_get_presigned_url(
        cls, s3_directory, file_path, expires_in
    ):
        key_name = cls.upload_file_to_s3(file_path, s3_directory)
        return cls.get_presigned_url(key_name, expires_in)

    @classmethod
    def upload_file_to_s3(cls, file_path: str, s3_directory):
        s3_client = cls.get_s3_client()
        try:
            object_name = file_path.split('/')[-1]
            key_name = s3_directory + object_name
            s3_client.upload_file(file_path, cls.get_s3_bucket_name(), key_name)
        except FileNotFoundError:
            logger.exception("File not found at: {0}".format(file_path))
            raise
        except ClientError as e:
            logging.error(e)
            raise DownstreamSystemFailure(message="Failed to upload file to S3")
        except Exception:
            logger.exception("Unknown error occurred while upload file to S3")
            raise

        return key_name

    @classmethod
    def get_presigned_url(cls, key_name: str, link_expires_in=None):
        s3_client = cls.get_s3_client()
        try:
            return s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': cls.get_s3_bucket_name(), 'Key': key_name},
                ExpiresIn=link_expires_in,
            )
        except Exception as e:
            raise DownstreamSystemFailure(
                message="Unable to get signed url for file.",
                extra_payload={"exception": e},
            )

    @classmethod
    def get_presigned_url_from_s3_url(cls, s3_url: str, link_expires_in=None):
        try:
            url = urlparse(s3_url)
            url = url.path.lstrip("/")
        except Exception as e:
            raise DownstreamSystemFailure(
                message="Unable to parse url for s3 link: {}".format(s3_url),
                extra_payload={"exception": e},
            )
        return cls.get_presigned_url(url, link_expires_in)

    @classmethod
    def publish_message_to_sns(cls, topic_arn, message, attributes=None):
        sns_client = cls.get_sns_client()
        try:
            message_response = sns_client.publish(
                TopicArn=topic_arn,
                Message=message,
                MessageAttributes=attributes,
            )
            logger.debug(message_response)
        except Exception as e:
            raise DownstreamSystemFailure(
                message="Unable to publish message to SNS.",
                extra_payload={"exception": e},
            )

    @classmethod
    def publish_message_to_sqs_in_batch(cls, queue_url, messages):
        sqs_client = cls.get_sqs_client()
        try:
            message_response = sqs_client.send_message_batch(
                QueueUrl=queue_url,
                Entries=messages,
            )
            logger.debug(message_response)
        except Exception as e:
            raise DownstreamSystemFailure(
                message="Unable to publish message to SQS.",
                extra_payload={"exception": e},
            )

    @classmethod
    def publish_message_to_sqs(cls, queue_url, message, delay_seconds=0):
        sqs_client = cls.get_sqs_client()
        try:
            message_response = sqs_client.send_message(
                QueueUrl=queue_url,
                MessageBody=json.dumps(message),
                DelaySeconds=delay_seconds,
            )
            logger.debug(message_response)
        except Exception as e:
            raise DownstreamSystemFailure(
                message="Unable to publish message to SQS.",
                extra_payload={"exception": e},
            )
