from object_registry import register_instance
from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from prometheus.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)


@register_instance()
class PosCriticalTaskClient(BaseExternalClient):
    page_map = {
        'get_pending_critical_tasks': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/pos/v1/critical-tasks?seller_id={seller_id}",
        ),
        'get_pos_orders_details': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/pos/v1/orders?order_ids={pos_order_ids}",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_pos_service_url()

    def has_pending_critical_tasks(self, seller_id):
        page_name = "get_pending_critical_tasks"
        url_params = dict(seller_id=seller_id)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise Exception(
                "POS Critical Task API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        response_data = response.data
        if not response_data.get('critical_tasks'):
            return False
        else:
            for critical_task in response_data.get('critical_tasks'):
                if critical_task.get('count') > 0:
                    return True
            return False

    def get_pos_orders_details(self, pos_order_ids):
        page_name = "get_pos_orders_details"
        url_params = dict(pos_order_ids=','.join(pos_order_ids))
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise Exception(
                "POS Get Order details API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response
