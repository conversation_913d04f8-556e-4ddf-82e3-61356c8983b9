from prometheus.core.base_domain_event import BaseDomainEvent
from prometheus.domain.billing.domain_events.schema.billing import (
    CreditNoteGeneratedEventSchema,
)
from ths_common.constants.domain_event_constants import DomainEvent


class CreditNoteGeneratedEvent(BaseDomainEvent):
    def __init__(self, invoice_id_to_cn_id_mappings_data):
        self.invoice_id_to_cn_id_mappings_data = invoice_id_to_cn_id_mappings_data

    def serialize(self):
        serialized = (
            CreditNoteGeneratedEventSchema(many=True)
            .dump(self.invoice_id_to_cn_id_mappings_data)
            .data
        )
        return serialized

    def update_mapping(self, **kwargs):
        return

    def event_type(self):
        return DomainEvent.CREDIT_NOTE_GENERATED
