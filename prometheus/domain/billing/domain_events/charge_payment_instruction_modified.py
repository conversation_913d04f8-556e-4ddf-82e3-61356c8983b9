"""
Charge Payment Instructions Modified Domain Event
"""

import logging

from prometheus.core.base_domain_event import MergeableDomainEvent
from prometheus.domain.billing.domain_events.schema.billing import (
    ChargePaymentInstructionsModifiedEventSchema,
)
from prometheus.domain.billing.dto.charge_modified_event_data import (
    ChargeModifiedEventData,
)
from ths_common.constants.domain_event_constants import DomainEvent
from ths_common.exceptions import DomainEventMergerException

logger = logging.getLogger(__name__)


class ChargePaymentInstructionsModifiedEvent(MergeableDomainEvent):
    def __init__(self, charge, old_value, new_value):
        self.charge = charge
        self.charge_event_data = None
        self.details = [{"old_value": old_value, "new_value": new_value}]

    def serialize(self):
        serialized = (
            ChargePaymentInstructionsModifiedEventSchema()
            .dump(self.charge_event_data)
            .data
        )
        return serialized

    def update_mapping(self, **kwargs):
        folio_name_map = kwargs.get('folio_name_map')
        sku_category_map = kwargs.get('sku_category_map')

        self.charge_event_data = ChargeModifiedEventData(
            charge_id=self.charge.charge_id,
            charge_type=self.charge.type,
            applicable_date=self.charge.applicable_date,
            item_name=self.charge.item.name,
            status=self.charge.status,
            sku_category_id=self.charge.item.sku_category_id,
            posttax_amount=self.charge.posttax_amount,
            room_number=self.charge.item.details.get('room_no'),
            room_stay_id=self.charge.item.details.get('room_stay_id'),
            billed_entity_accounts=[
                cs.billed_entity_account
                for cs in self.charge.charge_splits
                if cs.billed_entity_account
            ],
        )

        for index, detail in enumerate(self.details):
            self.charge_event_data.details.append(
                {
                    "old_value": ' | '.join(
                        [
                            str(payment_instruction)
                            for payment_instruction in detail["old_value"]
                        ]
                    ),
                    "new_value": ' | '.join(
                        [
                            str(payment_instruction)
                            for payment_instruction in detail["new_value"]
                        ]
                    ),
                }
            )

        self.charge_event_data.set_sku_category(sku_category_map)
        self.charge_event_data.set_billed_entity_names(folio_name_map=folio_name_map)

    def event_type(self):
        return DomainEvent.CHARGE_PAYMENT_INSTRUCTION_MODIFIED

    def merge(self, mergeable_domain_event):
        if not isinstance(
            mergeable_domain_event, ChargePaymentInstructionsModifiedEvent
        ):
            raise DomainEventMergerException()

        self.details.extend(mergeable_domain_event.details)

    def can_merge(self, mergeable_domain_event):
        return (
            isinstance(mergeable_domain_event, ChargePaymentInstructionsModifiedEvent)
            and self.charge.charge_id == mergeable_domain_event.charge.charge_id
        )
