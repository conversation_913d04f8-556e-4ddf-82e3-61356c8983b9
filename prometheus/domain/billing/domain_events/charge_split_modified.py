# coding=utf-8
"""
Charge Split Modified Domain Event
"""

import logging

from prometheus.core.base_domain_event import BaseDomainEvent
from prometheus.domain.billing.domain_events.schema.billing import (
    ChargeSplitModifiedEventSchema,
)
from prometheus.domain.billing.dto.charge_split_event_data import (
    ChargeSplitEventData,
    ChargeSplitModifiedEventData,
)
from ths_common.constants.domain_event_constants import DomainEvent

logger = logging.getLogger(__name__)


class ChargeSplitModifiedEvent(BaseDomainEvent):
    def __init__(
        self,
        charge,
        old_splits: [ChargeSplitEventData],
        new_splits: [ChargeSplitEventData],
    ):
        self.charge = charge
        self.charge_event_data = None
        self.old_splits = old_splits
        self.new_splits = new_splits

    def serialize(self):
        serialized = ChargeSplitModifiedEventSchema().dump(self.charge_event_data).data
        return serialized

    def update_mapping(self, **kwargs):
        folio_name_map = kwargs.get('folio_name_map')
        sku_category_map = kwargs.get('sku_category_map')
        self.charge_event_data = ChargeSplitModifiedEventData(
            status=self.charge.status,
            sku_category_id=self.charge.item.sku_category_id,
            room_number=self.charge.item.details.get('room_no'),
            applicable_date=self.charge.applicable_date,
            charge_id=self.charge.charge_id,
            item_name=self.charge.item.name,
            old_splits=self.old_splits,
            new_splits=self.new_splits,
        )
        self.charge_event_data.set_sku_category(sku_category_map)
        for cs in self.charge_event_data.old_splits:
            cs.set_billed_entity_name(folio_name_map=folio_name_map)
        for cs in self.charge_event_data.new_splits:
            cs.set_billed_entity_name(folio_name_map=folio_name_map)
        return

    def event_type(self):
        return DomainEvent.CHARGE_SPLIT_MODIFIED
