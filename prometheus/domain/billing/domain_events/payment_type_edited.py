from prometheus.core.base_domain_event import BaseDomainEvent
from prometheus.domain.billing.domain_events.schema.billing import (
    PaymentTypeEditedEventSchema,
)
from prometheus.domain.billing.dto.payment_type_edited_data import (
    PaymentTypeEditedEventData,
)
from ths_common.constants.domain_event_constants import DomainEvent


class PaymentTypeEditedEvent(BaseDomainEvent):
    def __init__(self, payment, old_value, new_value):
        self.payment = payment
        self.payment_event_data = None
        self.details = {"old_value": old_value, "new_value": new_value}

    def serialize(self):
        serialized = PaymentTypeEditedEventSchema().dump(self.payment_event_data).data
        return serialized

    def update_mapping(self, **kwargs):
        folio_name_map = kwargs.get('folio_name_map')
        billed_entity_accounts = [
            ps.billed_entity_account for ps in self.payment.payment_splits
        ]
        self.payment_event_data = PaymentTypeEditedEventData(
            payment_id=self.payment.payment_id,
            payment_mode=self.payment.payment_mode,
            amount=self.payment.amount,
            payment_reference_id=self.payment.payment_ref_id,
        )
        self.payment_event_data.set_billed_entity_names_using_folio(
            folio_name_map, billed_entity_accounts
        )
        self.payment_event_data.set_details(self.details)

    def event_type(self):
        return DomainEvent.PAYMENT_TYPE_EDITED
