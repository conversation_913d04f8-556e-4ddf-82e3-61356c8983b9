from typing import List

from prometheus.core.base_domain_event import BaseDomainEvent
from prometheus.domain.billing.domain_events.schema.billing import (
    BillCreatedEventSchema,
)
from prometheus.domain.billing.dto.payment_event_data import PaymentEventData
from ths_common.constants.domain_event_constants import DomainEvent


class BillCreatedEvent(BaseDomainEvent):
    def __init__(self, bill_amount, payments: List[PaymentEventData]):
        self.payments = payments
        self.bill_amount = bill_amount

    def serialize(self):
        serialized = BillCreatedEventSchema().dump(self).data
        return serialized

    def update_mapping(self, **kwargs):
        return

    def event_type(self):
        return DomainEvent.BILL_CREATED
