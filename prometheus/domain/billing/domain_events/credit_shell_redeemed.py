from prometheus.core.base_domain_event import BaseDomainEvent
from prometheus.domain.billing.domain_events.schema.billing import (
    CreditShellEventSchema,
)
from prometheus.domain.billing.dto.credit_shell_data import CreditShellRedeemedEventData
from prometheus.domain.billing.entities.billed_entity import BilledEntity
from prometheus.domain.billing.entities.credit_shell_refund import CreditShellRefund
from ths_common.constants.domain_event_constants import DomainEvent


class CreditShellRedeemedEvent(BaseDomainEvent):
    def __init__(
        self,
        credit_shell_refund_data: CreditShellRefund,
        billed_entity_data: BilledEntity,
    ):
        self.credit_shell_refund_data = credit_shell_refund_data
        self.billed_entity_data = billed_entity_data
        self.credit_shell_redeemed_event_data = None

    def serialize(self):
        serialized = (
            CreditShellEventSchema().dump(self.credit_shell_redeemed_event_data).data
        )
        return serialized

    def update_mapping(self, **kwargs):
        self.credit_shell_redeemed_event_data = CreditShellRedeemedEventData(
            bill_id=self.credit_shell_refund_data.bill_id,
            booking_id=self.credit_shell_refund_data.booking_id,
            amount=self.credit_shell_refund_data.amount,
            credit_shell_id=self.credit_shell_refund_data.credit_shell_id,
            credit_shell_refund_id=self.credit_shell_refund_data.credit_shell_refund_id,
            payment_mode=self.credit_shell_refund_data.payment_mode,
            remarks=self.credit_shell_refund_data.remarks,
            owner_name=self.billed_entity_data.name.first_name,
        )

    def event_type(self):
        return DomainEvent.CREDIT_SHELL_REDEEMED
