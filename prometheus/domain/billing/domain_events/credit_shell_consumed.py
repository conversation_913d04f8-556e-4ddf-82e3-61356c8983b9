from prometheus.core.base_domain_event import MergeableDomainEvent
from prometheus.domain.billing.domain_events.schema.billing import (
    CreditShellEventSchema,
)
from prometheus.domain.billing.dto.credit_shell_data import CreditShellConsumedEventData
from prometheus.domain.billing.entities.billed_entity import BilledEntity
from prometheus.domain.billing.entities.credit_shell import CreditShell
from prometheus.domain.billing.entities.folio import Folio
from ths_common.constants.domain_event_constants import DomainEvent


class CreditShellConsumedEvent(MergeableDomainEvent):
    def __init__(
        self,
        credit_shell_data: CreditShell,
        net_payable,
        folio_data: Folio,
        billed_entity_data: BilledEntity,
    ):
        self.credit_shell_data = [credit_shell_data]
        self.folio_data = [folio_data]
        self.billed_entity_data = [billed_entity_data]
        self.net_payable = [net_payable]
        self.credit_shell_used_event_data = []

    def serialize(self):
        serialized = (
            CreditShellEventSchema(many=True)
            .dump(self.credit_shell_used_event_data)
            .data
        )
        return serialized

    def update_mapping(self, **kwargs):
        for credit_shell_detail, billed_entity_detail, folio_detail, net_payable in zip(
            self.credit_shell_data,
            self.billed_entity_data,
            self.folio_data,
            self.net_payable,
        ):
            self.credit_shell_used_event_data.append(
                CreditShellConsumedEventData(
                    bill_id=credit_shell_detail.bill_id,
                    credit_shell_id=credit_shell_detail.credit_shell_id,
                    total_credit=credit_shell_detail.total_credit,
                    remaining_credit=credit_shell_detail.remaining_credit,
                    used_credit=net_payable,
                    folio_number=folio_detail.folio_number,
                    owner_name=billed_entity_detail.name.first_name,
                )
            )

    def event_type(self):
        return DomainEvent.CREDIT_SHELL_CONSUMED

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, CreditShellConsumedEvent)

    def merge(self, mergeable_domain_event):
        self.credit_shell_data.extend(mergeable_domain_event.credit_shell_data)
        self.folio_data.extend(mergeable_domain_event.folio_data)
        self.billed_entity_data.extend(mergeable_domain_event.billed_entity_data)
        self.billed_entity_data.extend(mergeable_domain_event.billed_entity_data)
        self.net_payable.extend(mergeable_domain_event.net_payable)
