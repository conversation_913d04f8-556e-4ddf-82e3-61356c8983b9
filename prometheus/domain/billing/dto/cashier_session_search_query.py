class CashierSessionSearchQuery:
    def __init__(
        self,
        start_datetime=None,
        end_datetime=None,
        cashier_session_ids=None,
        transaction_type=None,
        status=None,
        sort_by=None,
        limit=20,
        offset=0,
        include_payments=False,
        cash_register_ids=None,
        vendor_id=None,
    ):
        '''

        :param start_datetime:
        :param end_datetime:
        :param cashier_session_id:
        :param transaction_type:
        :param status:
        :param sort_by:
        :param limit:
        :param offset:
        :param include_payments:
        '''
        self.start_datetime = start_datetime
        self.end_datetime = end_datetime
        self.cashier_session_ids = cashier_session_ids
        self.transaction_type = transaction_type
        self.sort_by = sort_by
        self.limit = limit
        self.offset = offset
        self.include_payments = include_payments
        self.status = status
        self.cash_register_ids = cash_register_ids
        self.vendor_id = vendor_id
