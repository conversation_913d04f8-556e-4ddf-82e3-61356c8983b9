from prometheus.domain.billing.dto.billed_entity_mixin import BilledEntityMixin


class PaymentModeEditedEventData(BilledEntityMixin):
    def __init__(self, payment_id, payment_type, payment_reference_id, amount):
        self.payment_id = payment_id
        self.payment_type = payment_type
        self.payment_reference_id = payment_reference_id
        self.amount = amount
        self.billed_entity_names = None
        self.details = None

    def set_details(self, details):
        self.details = details
