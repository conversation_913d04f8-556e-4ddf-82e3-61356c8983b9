class PaymentSplitEventData(object):
    def __init__(self, payment_split_id, billed_entity_account, amount):
        self.payment_split_id = payment_split_id
        self.billed_entity_account = billed_entity_account
        self.amount = amount
        self.billed_entity_name = None

    def set_billed_entity_name(self, billed_entity_map=None, folio_name_map=None):
        if billed_entity_map:
            self.billed_entity_name = billed_entity_map.get(
                self.billed_entity_account.billed_entity_id
            ).get_name_with_account_number(self.billed_entity_account.account_number)
        if folio_name_map:
            self.billed_entity_name = folio_name_map.get(self.billed_entity_account)
