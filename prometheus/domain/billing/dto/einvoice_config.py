class HotelSideCleartaxAccountModel:
    PER_HOTEL_MODEL = "hotel"
    DISTRIBUTED_MODEL = "distributed"


class EinvoiceConfig:
    def __init__(self, **kwargs):
        self.hotel_side_clear_tax_account_model = kwargs.get(
            "hotel_side_clear_tax_account_model",
            HotelSideCleartaxAccountModel.DISTRIBUTED_MODEL,
        )
        self._is_reseller_sell_side_enabled = kwargs.get(
            "reseller_sell_side_invoice_enabled", False
        )
        self._is_marketplace_sell_side_enabled = kwargs.get(
            "marketplace_sell_side_invoice_enabled", False
        )
        self._black_listed_gstins = kwargs.get("black_listed_gstins") or []

    def is_hotel_wise_clear_tax_account_configured(self):
        return (
            self.hotel_side_clear_tax_account_model
            == HotelSideCleartaxAccountModel.PER_HOTEL_MODEL
        )

    def is_enabled_for_marketplace_invoice(self):
        return self._is_marketplace_sell_side_enabled

    def is_enabled_for_reseller_invoice(self):
        return self._is_reseller_sell_side_enabled

    def is_gstin_black_listed(self, gstin):
        return gstin in self._black_listed_gstins
