from prometheus.domain.billing.dto.billed_entity_mixin import BilledEntityMixin


class PaymentAmountEditedEventData(BilledEntityMixin):
    def __init__(self, payment_id, payment_mode, payment_type, payment_reference_id):
        self.payment_id = payment_id
        self.payment_mode = payment_mode
        self.payment_type = payment_type
        self.payment_reference_id = payment_reference_id
        self.billed_entity_names = None
        self.details = None

    def set_details(self, details):
        self.details = details
