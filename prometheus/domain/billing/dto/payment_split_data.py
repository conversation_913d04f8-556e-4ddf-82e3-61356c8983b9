from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO


class PaymentSplitData(object):
    """
    PaymentSplit Data
    """

    def __init__(self, billed_entity_account: BilledEntityAccountVO, amount):
        self.billed_entity_account = billed_entity_account
        self.amount = amount

    @staticmethod
    def from_dict(data):
        return PaymentSplitData(
            billed_entity_account=data['billed_entity_account'], amount=data['amount']
        )

    def add_split(self, payment_split_dto):
        assert payment_split_dto.billed_entity_account == self.billed_entity_account, (
            "Can only add splits from same " "billed entity account"
        )
        self.amount += payment_split_dto.amount
