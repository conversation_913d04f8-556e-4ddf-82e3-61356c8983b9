from typing import List

from object_registry import locate_instance, register_instance
from prometheus import crs_context
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.billing.exceptions import InvoiceNumberMaxLengthBreached
from prometheus.domain.billing.models import (
    BlockedInvoiceNumberModel,
    InvoiceSequenceModel,
)
from prometheus.domain.catalog.models import HotelModel
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.billing_constants import (
    InvoiceSeriesGenerationStrategy,
    IssuedByType,
)
from ths_common.constants.tenant_settings_constants import TenantSettingName
from ths_common.constants.user_constants import UserType
from ths_common.exceptions import AuthorizationError
from ths_common.utils.dateutils import get_current_fiscal_year


class BaseInvoiceNumberGenerationStrategy(object):
    @staticmethod
    def generate_invoice_number(prefix, sequence_number):
        raise NotImplementedError

    @staticmethod
    def get_default_prefix(vendor_id, issued_by_type: IssuedByType, business_date=None):
        if not business_date:
            business_date = crs_context.current_business_date
        year = get_current_fiscal_year(short_format=True, business_date=business_date)
        if issued_by_type == IssuedByType.RESELLER:
            prefix = f"R{vendor_id}{year}"
        else:
            prefix = f"{vendor_id}{year}"
        return prefix


class CRSInvoiceNumberGenerationStrategy(BaseInvoiceNumberGenerationStrategy):
    @staticmethod
    def generate_invoice_number(prefix, sequence_number):
        tenant_settings = locate_instance(TenantSettings)
        max_inv_num_length = tenant_settings.get_setting_value(
            TenantSettingName.INVOICE_NUM_MAX_LENGTH.value
        )
        max_invoice_number_length = max_inv_num_length or 16
        number_of_special_characters = 1
        remaining_length_after_prefix = (
            max_invoice_number_length - number_of_special_characters - len(prefix)
        )
        invoice_number = (
            f"{prefix}-{str(sequence_number).rjust(remaining_length_after_prefix, '0')}"
        )
        if len(invoice_number) > max_invoice_number_length:
            raise InvoiceNumberMaxLengthBreached(
                max_length=max_invoice_number_length,
                current_prefix=prefix,
                sequence_number=sequence_number,
            )
        return invoice_number


class HxInvoiceNumberGenerationStrategy(BaseInvoiceNumberGenerationStrategy):
    @staticmethod
    def generate_invoice_number(prefix, sequence_number):
        max_invoice_number_length = 16
        invoice_number = f"{prefix}{sequence_number}"
        if len(invoice_number) > max_invoice_number_length:
            raise InvoiceNumberMaxLengthBreached(
                max_length=max_invoice_number_length,
                current_prefix=prefix,
                sequence_number=sequence_number,
            )
        return invoice_number


class CRSInvoiceNumberGenerationStrategyV2(BaseInvoiceNumberGenerationStrategy):
    @staticmethod
    def generate_invoice_number(prefix, sequence_number):
        tenant_settings = locate_instance(TenantSettings)
        max_inv_num_length = tenant_settings.get_setting_value(
            TenantSettingName.INVOICE_NUM_MAX_LENGTH.value
        )
        max_invoice_number_length = max_inv_num_length or 16
        remaining_length_after_prefix = max_invoice_number_length - len(prefix)
        invoice_number = (
            f"{prefix}{str(sequence_number).rjust(remaining_length_after_prefix, '0')}"
        )
        if len(invoice_number) > max_invoice_number_length:
            raise InvoiceNumberMaxLengthBreached(
                max_length=max_invoice_number_length,
                current_prefix=prefix,
                sequence_number=sequence_number,
            )
        return invoice_number


sequence_strategy_map = {
    InvoiceSeriesGenerationStrategy.CRS_STRATEGY.value: CRSInvoiceNumberGenerationStrategy,
    InvoiceSeriesGenerationStrategy.CRS_STRATEGY_V2.value: CRSInvoiceNumberGenerationStrategyV2,
    InvoiceSeriesGenerationStrategy.HX_STRATEGY.value: HxInvoiceNumberGenerationStrategy,
}


@register_instance()
class InvoiceSequenceRepository(BaseRepository):
    DEFAULT_SEQUENCE_STRATEGY = InvoiceSeriesGenerationStrategy.CRS_STRATEGY

    def from_aggregate(self, aggregate=None):
        pass

    def to_aggregate(self, **kwargs):
        pass

    @classmethod
    def get_generation_strategy(cls):
        tenant_settings = locate_instance(TenantSettings)
        return (
            tenant_settings.get_setting_value(
                TenantSettingName.INVOICE_NUMBER_GENERATION_STRATEGY.value
            )
            or cls.DEFAULT_SEQUENCE_STRATEGY.value
        )

    @classmethod
    def get_default_prefix(
        cls,
        vendor_id,
        issued_by_type: IssuedByType,
        business_date=None,
        sequence_generation_strategy=None,
    ):
        if not sequence_generation_strategy:
            sequence_generation_strategy = cls.get_generation_strategy()
        sequence_generation_strategy_cls = sequence_strategy_map.get(
            sequence_generation_strategy
        )
        return sequence_generation_strategy_cls.get_default_prefix(
            vendor_id, issued_by_type, business_date
        )

    def get_current_invoice_series_details(self, issued_by_type, vendor_id, gstin):
        sequence = self.get_for_update(
            InvoiceSequenceModel,
            issued_by_type=issued_by_type.value,
            vendor_id=vendor_id,
            gstin=gstin,
        )
        if not sequence:
            sequence = self._build_new_sequence(gstin, issued_by_type, vendor_id)
        return (
            sequence.prefix,
            InvoiceSeriesGenerationStrategy(sequence.sequence_generation_strategy),
            sequence.last_sequence_number,
        )

    def _build_new_sequence(
        self, gstin, issued_by_type: IssuedByType, vendor_id, sequence_number=0
    ):
        sequence_generation_strategy = self.get_generation_strategy()
        # noinspection PyArgumentList
        return InvoiceSequenceModel(
            issued_by_type=issued_by_type.value,
            vendor_id=vendor_id,
            gstin=gstin,
            prefix=self.get_default_prefix(
                vendor_id,
                issued_by_type,
                sequence_generation_strategy=sequence_generation_strategy,
            ),
            last_sequence_number=sequence_number,
            sequence_generation_strategy=sequence_generation_strategy,
        )

    def get_next_invoice_number(self, issued_by_type, vendor_id, gstin):
        sequence = self.get_for_update(
            InvoiceSequenceModel,
            issued_by_type=issued_by_type.value,
            vendor_id=vendor_id,
            gstin=gstin,
        )
        if not sequence:
            locked_hotel = self.get_for_update(
                HotelModel, hotel_id=vendor_id, nowait=False
            )
            sequence = self.get_for_update(
                InvoiceSequenceModel,
                issued_by_type=issued_by_type.value,
                vendor_id=vendor_id,
                gstin=gstin,
                nowait=False,
            )
            if not sequence:
                sequence_number = 1
                new_sequence = self._build_new_sequence(
                    gstin,
                    issued_by_type,
                    vendor_id,
                    sequence_number=sequence_number,
                )
                current_sequence = self._save(new_sequence)
            else:
                sequence_number = sequence.last_sequence_number + 1
                sequence.last_sequence_number = sequence_number
                self._save(sequence)
                current_sequence = sequence
        else:
            sequence_number = sequence.last_sequence_number + 1
            sequence.last_sequence_number = sequence_number
            self._save(sequence)
            current_sequence = sequence

        sequence_strategy_cls = sequence_strategy_map.get(
            current_sequence.sequence_generation_strategy
        )
        invoice_number = sequence_strategy_cls.generate_invoice_number(
            current_sequence.prefix, sequence_number
        )

        self.flush_session()
        return invoice_number

    def reset_invoice_sequence(
        self,
        user_type: UserType,
        issued_by_type: IssuedByType,
        vendor_id,
        prefix,
        sequence_number,
        sequence_generation_strategy,
        gstin,
    ):
        if user_type not in (
            UserType.SUPER_ADMIN.value,
            UserType.CRS_MIGRATION_USER.value,
        ):
            raise AuthorizationError("You're not authorized to perform this operation")

        sequence = self.get_for_update(
            InvoiceSequenceModel,
            issued_by_type=issued_by_type.value,
            vendor_id=vendor_id,
            gstin=gstin,
        )

        if not sequence_generation_strategy:
            sequence_generation_strategy = InvoiceSeriesGenerationStrategy()

        if not sequence:
            locked_hotel = self.get_for_update(HotelModel, hotel_id=vendor_id)
            sequence = self.get_for_update(
                InvoiceSequenceModel,
                issued_by_type=issued_by_type.value,
                vendor_id=vendor_id,
                gstin=gstin,
            )
            if not sequence:
                new_sequence = self._build_new_sequence(
                    gstin,
                    issued_by_type,
                    vendor_id,
                    sequence_number=sequence_number,
                )
                self._save(new_sequence)
                return

        # Sequence found. Reset values in it.
        sequence.prefix = prefix
        sequence.last_sequence_number = sequence_number
        sequence.sequence_generation_strategy = sequence_generation_strategy.value

        self._save(sequence)
        self.flush_session()

    def block_invoice_sequence(
        self,
        user_type: UserType,
        issued_by_type,
        vendor_id,
        issued_by_gstin,
        reason,
        hotel_gstin=None,
        block_hotel_invoice_sequence=False,
    ):
        hotel_invoice_number = None
        if user_type not in UserType.SUPER_ADMIN.value:
            raise AuthorizationError("You're not authorized to perform this operation")
        if issued_by_type == IssuedByType.RESELLER and block_hotel_invoice_sequence:
            assert (
                hotel_gstin
            ), "If the issuer type is resseller, hotel_gstin is mandatory to block the credit number"
            hotel_invoice_number = self.get_next_invoice_number(
                IssuedByType.HOTEL, vendor_id, hotel_gstin
            )

        blocked_invoice_number = self.get_next_invoice_number(
            issued_by_type, vendor_id, issued_by_gstin
        )
        # noinspection PyArgumentList
        blocked_invoice_number_model = BlockedInvoiceNumberModel(
            issued_by_type=issued_by_type.value,
            vendor_id=vendor_id,
            gstin=issued_by_gstin,
            invoice_number=blocked_invoice_number,
            hotel_invoice_number=hotel_invoice_number,
            reason=reason,
        )
        self._save(blocked_invoice_number_model)
        self.flush_session()
        return blocked_invoice_number, hotel_invoice_number

    def load_all(self, vendor_ids) -> List[InvoiceSequenceModel]:
        return (
            self.session()
            .query(InvoiceSequenceModel)
            .filter(InvoiceSequenceModel.deleted.is_(False))
            .filter(
                InvoiceSequenceModel.vendor_id.in_(vendor_ids),
            )
        )

    def bulk_update(self, models: List[InvoiceSequenceModel]):
        self._bulk_update_mappings(
            InvoiceSequenceModel, [model.mapping_dict() for model in models]
        )
