from typing import List

from prometheus.domain.billing.entities.billed_entity import BilledEntity
from prometheus.domain.billing.models import BilledEntityModel
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    BilledEntityStatus,
)
from ths_common.constants.booking_constants import Salutation
from ths_common.value_objects import Name


class BilledEntityDBAdaptor(object):
    @staticmethod
    def to_db_models(bill_id, billed_entities: List[BilledEntity]):
        billed_entity_models = []
        for billed_entity in billed_entities:
            model = BilledEntityModel(
                billed_entity_id=billed_entity.billed_entity_id,
                bill_id=bill_id,
                first_name=billed_entity.name.first_name,
                last_name=billed_entity.name.last_name,
                salutation=billed_entity.name.salutation.value
                if billed_entity.name.salutation
                else None,
                category=billed_entity.category.value,
                secondary_category=billed_entity.secondary_category.value
                if billed_entity.secondary_category
                else None,
                deleted=billed_entity.deleted,
                status=billed_entity.status.value,
            )
            billed_entity_models.append(model)
        return billed_entity_models

    @staticmethod
    def to_entities(billed_entity_models: List[BilledEntityModel], account_map):
        billed_entities = []
        for billed_entity_model in billed_entity_models:
            billed_entity = BilledEntity(
                billed_entity_id=billed_entity_model.billed_entity_id,
                name=Name(
                    first_name=billed_entity_model.first_name,
                    last_name=billed_entity_model.last_name,
                    salutation=(
                        Salutation(billed_entity_model.salutation)
                        if billed_entity_model.salutation
                        else None
                    ),
                ),
                accounts=account_map.get(billed_entity_model.billed_entity_id),
                category=BilledEntityCategory(billed_entity_model.category),
                secondary_category=BilledEntityCategory(
                    billed_entity_model.secondary_category
                )
                if billed_entity_model.secondary_category
                else None,
                deleted=billed_entity_model.deleted,
                status=BilledEntityStatus(
                    billed_entity_model.status or BilledEntityStatus.ACTIVE.value
                ),
                dirty=False,
                new=False,
            )
            billed_entities.append(billed_entity)
        return billed_entities
