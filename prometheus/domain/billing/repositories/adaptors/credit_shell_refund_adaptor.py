from prometheus.domain.billing.entities.credit_shell_refund import CreditShellRefund
from prometheus.domain.billing.models import CreditShellRefundModel


class CreditShellRefundAdaptor(object):
    @staticmethod
    def to_db_model(credit_shell_refund: CreditShellRefund):
        return CreditShellRefundModel(
            amount=credit_shell_refund.amount.amount,
            bill_id=credit_shell_refund.bill_id,
            booking_id=credit_shell_refund.booking_id,
            credit_shell_id=credit_shell_refund.credit_shell_id,
            credit_shell_refund_id=credit_shell_refund.credit_shell_refund_id,
            paid_by=credit_shell_refund.paid_by,
            paid_to=credit_shell_refund.paid_to,
            payment_mode=credit_shell_refund.payment_mode,
            payout_details=credit_shell_refund.payout_details,
            payment_ref_id=credit_shell_refund.payment_ref_id,
            remarks=credit_shell_refund.remarks,
        )

    @staticmethod
    def to_entity(credit_shell_refund_model: CreditShellRefundModel):
        return CreditShellRefund(
            amount=credit_shell_refund_model.amount,
            bill_id=credit_shell_refund_model.bill_id,
            booking_id=credit_shell_refund_model.booking_id,
            credit_shell_id=credit_shell_refund_model.credit_shell_id,
            credit_shell_refund_id=credit_shell_refund_model.credit_shell_refund_id,
            paid_by=credit_shell_refund_model.paid_by,
            paid_to=credit_shell_refund_model.paid_to,
            payment_mode=credit_shell_refund_model.payment_mode,
            payout_details=credit_shell_refund_model.payout_details,
            payment_ref_id=credit_shell_refund_model.payment_ref_id,
            remarks=credit_shell_refund_model.remarks,
            hotel_id=None,
            new=False,
            dirty=False,
        )
