from collections import defaultdict

from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.entities.charge_split import ChargeSplit
from prometheus.domain.billing.models import ChargeSplitModel
from ths_common.constants.billing_constants import ChargeBillToTypes, ChargeTypes


class RawChargeSplitDBAdapter(object):
    columns = (
        ChargeSplitModel.charge_split_id,
        ChargeSplitModel.charge_id,
        ChargeSplitModel.charge_to,
        ChargeSplitModel.tax,
        ChargeSplitModel.pre_tax,
        ChargeSplitModel.post_tax,
        ChargeSplitModel.invoice_id,
        ChargeSplitModel.charge_type,
        ChargeSplitModel.bill_to_type,
        ChargeSplitModel.billed_entity_id,
        ChargeSplitModel.billed_entity_account_number,
        ChargeSplitModel.payment_id,
        ChargeSplitModel.percentage,
    )

    @staticmethod
    def to_entities(
        charge_split_rows, allowances_group_by_charge_and_split, base_currency
    ):
        base_currency = (
            base_currency
            if isinstance(base_currency, CurrencyType)
            else CurrencyType(base_currency)
        )
        charge_splits_map = defaultdict(list)
        for charge_split_row in charge_split_rows:
            charge_id = charge_split_row[1]
            charge_split_id = charge_split_row[0]
            charge_split = ChargeSplit(
                charge_split_id=charge_split_id,
                charge_to=charge_split_row[2],
                pre_tax=Money(charge_split_row[4], base_currency),
                tax=Money(charge_split_row[3], base_currency),
                post_tax=Money(charge_split_row[5], base_currency),
                tax_details=None,
                percentage=charge_split_row[12],
                invoice_id=charge_split_row[6],
                deleted=False,
                billed_entity_account=BilledEntityAccountVO(
                    billed_entity_id=charge_split_row[9],
                    account_number=charge_split_row[10],
                ),
                allowances=allowances_group_by_charge_and_split.get(
                    (charge_id, charge_split_id)
                ),
                charge_type=ChargeTypes(charge_split_row[7])
                if charge_split_row[7]
                else None,
                bill_to_type=ChargeBillToTypes(charge_split_row[8])
                if charge_split_row[8]
                else None,
                payment_id=charge_split_row[11],
                dirty=False,
                new=False,
            )
            charge_splits_map[charge_id].append(charge_split)

        return charge_splits_map
