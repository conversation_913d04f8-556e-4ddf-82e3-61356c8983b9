from datetime import date, datetime
from typing import List, Optional

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.errors import BillingErrors
from ths_common.constants.billing_constants import (
    ChargeBillToTypes,
    ChargeTypes,
    InvoiceStatus,
    IssuedByType,
    IssuedToType,
)
from ths_common.exceptions import ValidationException
from ths_common.value_objects import (
    InvoiceBillToInfo,
    InvoiceIssuedByInfo,
    TaxDetail,
    VendorDetails,
)


class Invoice(object):
    def __init__(
        self,
        bill_id: str,
        invoice_id: str,
        invoice_number: str,
        vendor_id: str,
        vendor_details: VendorDetails,
        invoice_date: date,
        invoice_due_date: date,
        parent_info: dict,
        bill_to: InvoiceBillToInfo,
        status: InvoiceStatus,
        generated_by: str,
        generation_channel: str,
        pretax_amount: Money,
        tax_amount: Money,
        posttax_amount: Money,
        invoice_url: str,
        bill_to_type: ChargeBillToTypes,
        user_info_map: dict,
        allowed_charge_types: [ChargeTypes],
        allowed_charge_to_ids: [str],
        version: int,
        tax_details_breakup: dict,
        deleted=False,
        issued_to_type: IssuedToType = None,
        issued_by_type: IssuedByType = None,
        hotel_invoice_id: str = None,
        issued_by: InvoiceIssuedByInfo = None,
        created_at=None,
        modified_at=None,
        irn: str = None,
        qr_code: str = None,
        signed_invoice: str = None,
        irp_ack_number: str = None,
        irp_ack_date: datetime.date = None,
        is_einvoice: bool = False,
        signed_url: str = None,
        signed_url_expiry_time: datetime = None,
        base_currency=None,
        billed_entity_account: BilledEntityAccountVO = None,
        is_downloaded: Optional[bool] = None,
        is_reissue_allowed: bool = True,
        is_spot_credit: bool = False,
    ):
        self.created_at = created_at if created_at else dateutils.current_datetime()
        self.modified_at = modified_at if modified_at else dateutils.current_datetime()
        self.bill_id = bill_id
        self._invoice_id = invoice_id
        self._invoice_number = invoice_number
        self.parent_info = parent_info
        self.bill_to = bill_to
        self.billed_entity_account = billed_entity_account
        self.bill_to_type = bill_to_type
        self.allowed_charge_types = allowed_charge_types
        self.allowed_charge_to_ids = allowed_charge_to_ids
        self._status = status
        self.generated_by = generated_by
        self.generation_channel = generation_channel
        self.base_currency = base_currency
        self.pretax_amount = (
            pretax_amount
            if isinstance(pretax_amount, Money)
            else Money(pretax_amount, self.base_currency)
        )
        self.tax_amount = (
            tax_amount
            if isinstance(tax_amount, Money)
            else Money(tax_amount, self.base_currency)
        )
        self.posttax_amount = (
            posttax_amount
            if isinstance(posttax_amount, Money)
            else Money(posttax_amount, self.base_currency)
        )
        self.invoice_due_date = invoice_due_date
        self.invoice_date = invoice_date
        self._vendor_details = vendor_details
        self._user_info_map = user_info_map
        self._version = version
        self._deleted = deleted
        self.vendor_id = vendor_id
        self.invoice_url = invoice_url
        self.tax_details_breakup = tax_details_breakup
        if self.tax_details_breakup:
            self.tax_details = [
                TaxDetail(type, None, Money(amount, self.base_currency))
                for type, amount in self.tax_details_breakup.items()
            ]
        else:
            self.tax_details = []
        self.issued_to_type = issued_to_type
        self.issued_by_type = issued_by_type
        self.issued_by = issued_by
        self.hotel_invoice_id = hotel_invoice_id
        self.irn = irn
        self.qr_code = qr_code
        self.signed_invoice = signed_invoice
        self.irp_ack_number = irp_ack_number
        self.irp_ack_date = irp_ack_date
        self.is_einvoice = is_einvoice

        self.signed_url = signed_url
        self.signed_url_expiry_time = signed_url_expiry_time

        self.is_downloaded = is_downloaded
        self.is_reissue_allowed = is_reissue_allowed
        self.is_spot_credit = is_spot_credit

    @property
    def invoice_id(self):
        return self._invoice_id

    def reset_issued_by(self):
        issued_by = InvoiceIssuedByInfo(
            gst_details=self.vendor_details.gst_details,
            phone=self.vendor_details.phone,
            email=self.vendor_details.email,
            url=self.vendor_details.url,
            legal_signature=self.vendor_details.legal_signature,
            bank_details=self.issued_by.bank_details if self.issued_by else None,
        )
        self.issued_by = issued_by

    def set_bill_to(self, value, override=False):
        # check if the invoice is locked and there should not be any update in the gstin details at this time
        if not override:
            if not self._is_edit_allowed():
                raise ValidationException(
                    BillingErrors.INVOICE_EDIT_NOT_ALLOWED,
                    description="Invoice_id={} failed to edit as it is in status={}".format(
                        self.invoice_id, self.status.value
                    ),
                )
            if self.bill_to_type == ChargeBillToTypes.COMPANY:
                raise ValidationException(BillingErrors.BILL_TO_UPDATE_FAILED)
        if self.is_gst_filed and self.update_in_gst(
            value.gstin_num, self.bill_to.gstin_num
        ):
            return
        self.bill_to = value

    def add_charge_type_to_allowed_charge_types(self, charge_types: List[ChargeTypes]):
        for charge_type in charge_types:
            if charge_type not in self.allowed_charge_types:
                self.allowed_charge_types.append(charge_type)

    def set_hotel_invoice(self, hotel_invoice_id):
        if self.issued_by_type != IssuedByType.RESELLER:
            raise ValidationException(
                BillingErrors.REQUESTED_HOTEL_INVOICE_FOR_NON_RESELLER_INVOICE
            )
        if self.hotel_invoice_id:
            raise ValidationException(BillingErrors.HOTEL_INVOICE_ALREADY_EXISTS)
        self.hotel_invoice_id = hotel_invoice_id

    @property
    def user_info_map(self):
        return self._user_info_map

    def set_user_info_map(self, value):
        self._user_info_map = value

    @property
    def version(self):
        return self._version

    def increment_version(self):
        self._version = self._version + 1

    @property
    def deleted(self):
        return self._deleted

    def delete(self):
        self._deleted = True

    @property
    def status(self):
        return self._status

    @property
    def is_gst_filed(self):
        return self._status == InvoiceStatus.SENT_FOR_GST_FILING

    @property
    def is_cancelled(self):
        return self._status == InvoiceStatus.CANCELLED

    @property
    def is_locked(self):
        return self._status == InvoiceStatus.LOCKED

    @status.setter
    def status(self, value):
        if value not in InvoiceStatus.all_options():
            raise ValidationException(BillingErrors.INVOICE_STATUS_TYPE_ERROR)
        self._status = value

    def confirm(self):
        if self._status == InvoiceStatus.PREVIEW:
            self._status = InvoiceStatus.GENERATED
        else:
            self._status = InvoiceStatus.REGENERATED

    def cancel(self):
        if self._status in [
            InvoiceStatus.PREVIEW,
            InvoiceStatus.GENERATED,
            InvoiceStatus.REGENERATED,
        ]:
            self._status = InvoiceStatus.CANCELLED

    @property
    def vendor_details(self):
        return self._vendor_details

    @vendor_details.setter
    def vendor_details(self, value):
        self._vendor_details = value

    def update_vendor_details(self, value):
        if self.is_gst_filed and self.update_in_gst(
            value.gst_details.gstin_num, self.vendor_details.gst_details.gstin_num
        ):
            return
        self.vendor_details = value

    def update_in_gst(self, new_gst, old_gst):
        return new_gst != old_gst

    @property
    def invoice_number(self):
        return self._invoice_number

    def get_users(self, charge_to_ids):
        return [
            self.user_info_map[charge_to_id]
            for charge_to_id in charge_to_ids
            if self.user_info_map.get(charge_to_id)
        ]

    def set_invoice_number(self, invoice_number, override=False):
        if self._invoice_number and not override:
            return ValidationException(BillingErrors.INVOICE_NO_REASSIGNMENT_ERROR)
        self._invoice_number = invoice_number

    def update_amount(
        self, pretax_amount, tax_amount, posttax_amount, tax_details_breakup
    ):
        pretax_amount = (
            pretax_amount
            if isinstance(pretax_amount, Money)
            else Money(pretax_amount, self.base_currency)
        )
        tax_amount = (
            tax_amount
            if isinstance(tax_amount, Money)
            else Money(tax_amount, self.base_currency)
        )
        posttax_amount = (
            posttax_amount
            if isinstance(posttax_amount, Money)
            else Money(posttax_amount, self.base_currency)
        )
        if posttax_amount != pretax_amount + tax_amount:
            raise ValidationException(
                BillingErrors.ROUND_OFF_ADJUSTMENT_ERROR_IN_INVOICE
            )

        self.pretax_amount = pretax_amount
        self.tax_amount = tax_amount
        self.posttax_amount = posttax_amount
        self.tax_details_breakup = tax_details_breakup
        self.tax_details = [
            TaxDetail(type, None, amount)
            for type, amount in self.tax_details_breakup.items()
        ]

    def _is_edit_allowed(self):
        return self.status in [InvoiceStatus.PREVIEW]

    def update_lock_and_gst_filing_status(self):
        invoice_lock_date = dateutils.day_in_next_month(self.invoice_date, day=5)
        gst_filing_date = dateutils.day_in_next_month(self.invoice_date, day=10)
        updated = False
        if dateutils.current_date() >= gst_filing_date and self.status not in [
            InvoiceStatus.SENT_FOR_GST_FILING,
            InvoiceStatus.CANCELLED,
        ]:
            self.status = InvoiceStatus.SENT_FOR_GST_FILING
            updated = True
        elif dateutils.current_date() >= invoice_lock_date and self.status not in [
            InvoiceStatus.LOCKED,
            InvoiceStatus.CANCELLED,
            InvoiceStatus.SENT_FOR_GST_FILING,
        ]:
            self.status = InvoiceStatus.LOCKED
            updated = True

        return updated

    def mark_as_spot_credit(self):
        self.is_spot_credit = True

    def requires_locking(self):
        invoice_lock_date = dateutils.day_in_next_month(self.invoice_date, day=5)
        gst_filing_date = dateutils.day_in_next_month(self.invoice_date, day=10)
        if dateutils.current_date() >= gst_filing_date and self.status not in [
            InvoiceStatus.SENT_FOR_GST_FILING,
            InvoiceStatus.CANCELLED,
        ]:
            return True
        elif dateutils.current_date() >= invoice_lock_date and self.status not in [
            InvoiceStatus.LOCKED,
            InvoiceStatus.CANCELLED,
            InvoiceStatus.SENT_FOR_GST_FILING,
        ]:
            return True

        return False

    def is_editable(self):
        return self.status in [InvoiceStatus.PREVIEW, InvoiceStatus.CANCELLED]
