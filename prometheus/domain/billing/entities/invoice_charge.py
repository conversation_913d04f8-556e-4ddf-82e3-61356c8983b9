from datetime import datetime
from typing import List

from treebo_commons.money import Money

from prometheus.domain.billing.errors import BillingErrors
from prometheus.domain.billing.exceptions import BillingError
from ths_common.base_entity import EntityChangeTracker
from ths_common.constants.billing_constants import (
    ChargeBillToTypes,
    ChargeStatus,
    ChargeTypes,
)
from ths_common.exceptions import ValidationException
from ths_common.value_objects import ChargeItem, TaxDetail


class InvoiceCharge(EntityChangeTracker):
    def __init__(
        self,
        invoice_charge_id: int,
        charge_id: int,
        charge_split_ids: [],
        pretax_amount: Money,
        tax_amount: Money,
        tax_details: List[TaxDetail],
        posttax_amount: Money,
        charge_type: ChargeTypes,
        bill_to_type: ChargeBillToTypes,
        charge_status: ChargeStatus,
        recorded_time: datetime,
        applicable_date: datetime,
        comment: str,
        created_by: str,
        charge_item: ChargeItem,
        charge_to_ids: str,
        deleted=False,
        credit_note_generated_amount=None,
        dirty=True,
        new=True,
    ):
        super().__init__(dirty=dirty, new=new)
        if charge_type not in ChargeTypes:
            raise ValidationException(
                description="Charge::Invalid charge type {} in InvoiceCharge".format(
                    charge_type
                )
            )

        if charge_status not in ChargeStatus:
            raise ValidationException(
                description="Charge::Invalid charge status {} in InvoiceCharge".format(
                    charge_status
                )
            )

        if bill_to_type not in ChargeBillToTypes:
            raise ValidationException(
                description="Charge::Invalid bill to type {} for InvoiceCharge".format(
                    bill_to_type
                )
            )

        self.invoice_charge_id = invoice_charge_id
        self._charge_id = charge_id
        self._charge_split_ids = charge_split_ids
        self._pretax_amount = pretax_amount
        self._tax_details = tax_details if tax_details else []
        self._tax_amount = tax_amount
        self._posttax_amount = posttax_amount
        self._charge_type = charge_type
        self._bill_to_type = bill_to_type
        self._charge_status = charge_status
        self._recorded_time = recorded_time
        self._applicable_date = applicable_date
        self._comment = comment
        self._created_by = created_by
        self._charge_item = charge_item
        self._charge_to_ids = charge_to_ids
        self._deleted = deleted
        self._credit_note_generated_amount = credit_note_generated_amount

    @property
    def charge_id(self):
        return self._charge_id

    @charge_id.setter
    def charge_id(self, value):
        self._charge_id = value
        self.mark_dirty()

    @property
    def charge_split_ids(self):
        return self._charge_split_ids

    @charge_split_ids.setter
    def charge_split_ids(self, value):
        self._charge_split_ids = value
        self.mark_dirty()

    @property
    def pretax_amount(self):
        return self._pretax_amount

    @pretax_amount.setter
    def pretax_amount(self, value):
        self._pretax_amount = value
        self.mark_dirty()

    @property
    def tax_details(self):
        return self._tax_details

    @tax_details.setter
    def tax_details(self, value):
        self._tax_details = value
        self.mark_dirty()

    @property
    def tax_amount(self):
        return self._tax_amount

    @tax_amount.setter
    def tax_amount(self, value):
        self._tax_amount = value
        self.mark_dirty()

    @property
    def posttax_amount(self):
        return self._posttax_amount

    @posttax_amount.setter
    def posttax_amount(self, value):
        self._posttax_amount = value
        self.mark_dirty()

    @property
    def charge_type(self):
        return self._charge_type

    @charge_type.setter
    def charge_type(self, value):
        self._charge_type = value
        self.mark_dirty()

    @property
    def bill_to_type(self):
        return self._bill_to_type

    @bill_to_type.setter
    def bill_to_type(self, value):
        self._bill_to_type = value
        self.mark_dirty()

    @property
    def charge_status(self):
        return self._charge_status

    @charge_status.setter
    def charge_status(self, value):
        self._charge_status = value
        self.mark_dirty()

    @property
    def recorded_time(self):
        return self._recorded_time

    @recorded_time.setter
    def recorded_time(self, value):
        self._recorded_time = value
        self.mark_dirty()

    @property
    def applicable_date(self):
        return self._applicable_date

    @applicable_date.setter
    def applicable_date(self, value):
        self._applicable_date = value
        self.mark_dirty()

    @property
    def comment(self):
        return self._comment

    @comment.setter
    def comment(self, value):
        self._comment = value
        self.mark_dirty()

    @property
    def created_by(self):
        return self._created_by

    @created_by.setter
    def created_by(self, value):
        self._created_by = value
        self.mark_dirty()

    @property
    def charge_item(self):
        return self._charge_item

    @charge_item.setter
    def charge_item(self, value):
        self._charge_item = value
        self.mark_dirty()

    @property
    def charge_to_ids(self):
        return self._charge_to_ids

    @charge_to_ids.setter
    def charge_to_ids(self, value):
        self._charge_to_ids = value
        self.mark_dirty()

    @property
    def credit_note_generated_amount(self):
        return self._credit_note_generated_amount

    @credit_note_generated_amount.setter
    def credit_note_generated_amount(self, value):
        self._credit_note_generated_amount = value
        self.mark_dirty()

    @property
    def deleted(self):
        return self._deleted

    @deleted.setter
    def deleted(self, value):
        self._deleted = value
        self.mark_dirty()

    def max_allowed_credit_amount(self):
        return self.posttax_amount - self.credit_note_generated_amount

    def credit_note_generated(self, credit_note_generated_amount):
        if credit_note_generated_amount > self.max_allowed_credit_amount():
            raise BillingError(
                error=BillingErrors.CREDIT_NOTE_AMOUNT_MORE_THAN_INVOICE_CHARGE_AMOUNT
            )
        self.credit_note_generated_amount = (
            self.credit_note_generated_amount + credit_note_generated_amount
        )

    def reverse_credit_note_amount(self, credit_note_amount):
        self.credit_note_generated_amount = (
            self.credit_note_generated_amount - credit_note_amount
        )
        if self.credit_note_generated_amount < 0:
            raise BillingError(error=BillingErrors.EXTRA_CREDIT_NOTE_AMOUNT_REVERSED)

    def delete(self):
        self.deleted = True
