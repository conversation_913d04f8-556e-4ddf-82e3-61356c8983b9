import re
from curses.ascii import isdigit
from decimal import Decimal
from typing import List

from prometheus import crs_context
from prometheus.domain.billing.entities.credit_note_line_item import CreditNoteLineItem
from prometheus.domain.billing.errors import BillingErrors
from prometheus.domain.billing.exceptions import (
    EInvoicingError,
    EInvoicingValidationError,
)
from ths_common.constants.billing_constants import TaxTypes
from ths_common.utils.common_utils import extract_state_code_from_gstin
from ths_common.value_objects import InvoiceBillToInfo, InvoiceIssuedByInfo


class EInvoiceSerializable:
    def as_dict(self):
        return vars(self)


class EInvoiceGSTInfo(EInvoiceSerializable):
    MAX_LINE_LENGTH = 60

    def __init__(self, gst_details, issuer=None):
        if isinstance(gst_details, InvoiceBillToInfo):
            self.legal_name = gst_details.name
            self.is_sez = gst_details.is_sez
            self.has_lut = gst_details.has_lut
        else:
            self.legal_name = gst_details.legal_name
            self.is_sez = None
            self.has_lut = None

        self.gstin = gst_details.gstin_num
        self.phone_number = ""
        self.email_id = ""
        self.place_of_supply = None

        if isinstance(issuer, (InvoiceIssuedByInfo, InvoiceBillToInfo)):
            self.phone_number = issuer.phone.number if issuer.phone else None
            self.email_id = issuer.email

        address = gst_details.address
        if not address:
            raise EInvoicingError(BillingErrors.MISSING_ADDRESS)
        self._split_and_distribute_address(address)

        if self._is_cleartax_sandbox_gstin_for_non_treebo_tenant():
            self.state_code = self.gstin[0:2]
        else:
            self.state_code = extract_state_code_from_gstin(self.gstin)

        if not self.state_code:
            raise EInvoicingError(BillingErrors.INVALID_GSTIN)

    def _split_and_distribute_address(self, address):
        if address.field1 and address.city and address.pincode:
            address_field = address.field1 + (address.field2 or "")
            self.location = address.city[:50]
            self.pincode = address.pincode
        else:
            address_parts = []
            if address.field1:
                address_parts.append(address.field1)
            if address.field2:
                address_parts.append(address.field2)
            if address.city:
                address_parts.append(address.city)
            address_line = ", ".join(address_parts)
            address_splits = address_line.replace('-', ',').split(",")
            if len(address_splits) == 1:
                address_splits = address_line.split()
            pincode = (
                address_splits[-1].strip()
                if address_splits[-1].strip().isdigit()
                else None
            )
            location = (
                ",".join(address_splits[-3:-1])
                if pincode
                else ",".join(address_splits[-2:])
            )
            address_field = (
                ",".join(address_splits[:-3])
                if pincode
                else ",".join((address_splits[:-2]))
            )
            self.pincode = address.pincode or pincode or ""
            self.location = address.city or location or ""
        self.address_field1 = address_field
        self.address_field2 = None
        if len(address_field) > 50:
            self.address_field1 = address_field[:50]
            self.address_field2 = address_field[50:100]
            self.address_field2 = (
                self.address_field2 if len(self.address_field2) > 3 else None
            )

    def _is_cleartax_sandbox_gstin_for_non_treebo_tenant(self):
        """
        Adding this check to enable einvoicing demos in tenants other than treebo using clear tax sandbox
        gstin which does not comply with official gstin regex
        :return:
        """
        return self.gstin == '29AAFCD5862R000' and not crs_context.is_treebo_tenant()


class EInvoiceItem(EInvoiceSerializable):
    def __init__(self, charge, invoice_map=None):
        if isinstance(charge, CreditNoteLineItem):
            assert invoice_map
            invoice = invoice_map.get(charge.invoice_id)
            _charge = invoice.get_invoice_charge(charge.invoice_charge_id)
            self.product_name = _charge.charge_item.name
            self.hsn_code = _charge.charge_item.item_code.value
            self.serial_number = charge.credit_note_line_item_id
        else:
            self.product_name = charge.charge_item.name
            self.hsn_code = charge.charge_item.item_code.value
            self.serial_number = charge.invoice_charge_id

        self.is_service = "Y"

        self.quantity = 1
        self.unit_of_measure = "UNT"

        self.unit_price = charge.pretax_amount.amount
        self.assessable_amount = charge.pretax_amount.amount
        self.total_amount = charge.pretax_amount.amount * self.quantity
        self.discount = Decimal(0)
        self.other_charge = Decimal(0)
        self.total_item_value = charge.posttax_amount.amount

        self.cgst = Decimal(0)
        self.sgst = Decimal(0)
        self.igst = Decimal(0)
        self.cess = Decimal(0)
        self.state_cess = Decimal(0)
        self.cess_non_advalorem = Decimal(0)

        self.cgst_amount = Decimal(0)
        self.sgst_amount = Decimal(0)
        self.igst_amount = Decimal(0)
        self.cess_amount = Decimal(0)
        self.state_cess_amount = Decimal(0)
        self.cess_non_advalorem_amount = Decimal(0)

        for td in charge.tax_details:
            if td.tax_type == TaxTypes.CGST:
                self.cgst += td.percentage
                self.cgst_amount += td.amount.amount
            elif td.tax_type == TaxTypes.SGST:
                self.sgst += td.percentage
                self.sgst_amount += td.amount.amount
            elif td.tax_type == TaxTypes.IGST:
                self.igst += td.percentage
                self.igst_amount += td.amount.amount
            elif td.tax_type == TaxTypes.KERALA_FLOOD_CESS:
                self.state_cess += td.percentage
                self.state_cess_amount += td.amount.amount
            else:
                self.cess += td.percentage
                self.cess_amount += td.amount.amount
        self.gst = self.igst if self.igst else sum([self.cgst, self.sgst])


class EInvoiceTotal(EInvoiceSerializable):
    def __init__(self, einvoice_items: List[EInvoiceItem]):
        self.assessable_amount = Decimal(0)
        self.other_charge = Decimal(0)
        self.discount = Decimal(0)
        self.final_invoice_value = Decimal(0)

        self.cgst = Decimal(0)
        self.igst = Decimal(0)
        self.sgst = Decimal(0)
        self.cess = Decimal(0)
        self.state_cess = Decimal(0)
        self.cess_non_advalorem = Decimal(0)

        for item in einvoice_items:
            self.assessable_amount += item.assessable_amount
            self.discount += item.discount
            self.other_charge += item.other_charge
            self.final_invoice_value += item.total_item_value

            self.cgst += item.cgst_amount
            self.sgst += item.sgst_amount
            self.cess += item.cess_amount
            self.igst += item.igst_amount
            self.state_cess += item.state_cess_amount
            self.cess_non_advalorem += item.cess_non_advalorem_amount


class EInvoiceDTO:
    def __init__(self, invoice_or_creditnote):
        from prometheus.domain.billing.aggregates.credit_note_aggregate import (
            CreditNoteAggregate,
        )
        from prometheus.domain.billing.aggregates.invoice_aggregate import (
            InvoiceAggregate,
        )
        from prometheus.domain.billing.repositories import InvoiceRepository

        self.invoice_repository = InvoiceRepository()

        self.tax_schema = "GST"
        self.version = '1.1'
        self._entity = invoice_or_creditnote

        if isinstance(self._entity, InvoiceAggregate):
            issued_by = self._entity.invoice.issued_by
            issued_to = self._entity.invoice.bill_to
            self.document_number = self._entity.invoice.invoice_number.lstrip('0')
            self.document_type = "INV"
            self.document_date = self._entity.invoice.invoice_date
            self._line_items = [EInvoiceItem(c) for c in self._entity.invoice_charges]

        elif isinstance(self._entity, CreditNoteAggregate):
            issued_by = self._entity.credit_note.issued_by
            issued_to = self._entity.credit_note.issued_to
            self.document_number = self._entity.credit_note.credit_note_number.lstrip(
                '0'
            )
            self.document_type = "CRN"
            self.document_date = self._entity.credit_note.credit_note_date
            invoices, _ = self._entity.get_attached_invoices_with_credit_note_amount()
            invoices = self.invoice_repository.load_all(invoices)
            invoice_map = {i.invoice_id: i for i in invoices}
            self.original_invoice_number = ",".join(
                [i.invoice_number for i in invoices]
            )
            self._line_items = [
                EInvoiceItem(c, invoice_map)
                for c in self._entity.credit_note_line_items
            ]
        else:
            raise RuntimeError("Unknown type for Einvoice")

        self._issued_by = EInvoiceGSTInfo(
            gst_details=issued_by.gst_details, issuer=issued_by
        )
        self._issued_to = EInvoiceGSTInfo(gst_details=issued_to, issuer=issued_to)
        self._total = EInvoiceTotal(self._line_items)

        self._issued_to.place_of_supply = self._issued_by.state_code

    @property
    def transaction_details(self):
        return dict(supply_type=self.supply_type, tax_schema=self.tax_schema)

    @property
    def document_details(self):
        return dict(
            document_type=self.document_type,
            document_number=self.document_number,
            document_date=self.document_date,
        )

    @property
    def supplier_information(self):
        return self._issued_by.as_dict()

    @property
    def buyer_information(self):
        return self._issued_to.as_dict()

    @property
    def total_value_details(self):
        return self._total

    @property
    def item_details(self):
        return self._line_items

    @property
    def supply_type(self):
        if self._issued_to.is_sez and not self._issued_to.has_lut:
            return 'SEZWP'
        if self._issued_to.is_sez and self._issued_to.has_lut:
            return 'SEZWOP'
        return 'B2B'


class IrpDetailsDTO:
    def __init__(self, irn, qr_code, ack_no, acked_on, signed_invoice):
        self.irn = irn
        self.qr_code = qr_code
        self.ack_no = ack_no
        self.acked_on = acked_on
        self.signed_invoice = signed_invoice

    @classmethod
    def dummy(cls):
        import datetime
        import random
        import string
        import uuid

        uid = f"DUMMY-{uuid.uuid4()}"
        dummy_string = ''.join(
            random.choices(string.ascii_uppercase + string.digits, k=40)
        )
        return cls(
            irn=None,
            qr_code=None,
            acked_on=datetime.datetime.today(),
            ack_no=None,
            signed_invoice=None,
        )
