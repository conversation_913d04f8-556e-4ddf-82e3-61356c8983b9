from decimal import Decimal
from typing import List, <PERSON><PERSON>

from treebo_commons.money import Money

from object_registry import register_instance
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.domain_events.spot_credit_provided import (
    SpotCreditProvidedEvent,
)
from prometheus.domain.billing.dto.spot_credit_event_data import SpotCreditEventData
from prometheus.domain.billing.entities.billed_entity import (
    BilledEntityAccountVO,
    BillingInstructionVO,
)
from prometheus.domain.billing.entities.charge import Charge
from prometheus.domain.billing.entities.charge_split import ChargeSplit
from prometheus.domain.billing.exceptions import SpotCreditIssueError
from prometheus.domain.domain_events.domain_event_registry import register_event
from ths_common.constants.billing_constants import (
    ChargeTypes,
    PaymentInstruction,
    SpotCreditSettlementType,
)


@register_instance()
class SpotCreditService:
    @staticmethod
    def _get_inclusion_charges_with_effective_total(bill_aggregate, charge, account):
        charge_splits: List[Tuple[int, ChargeSplit]] = []
        effective_charge_split_amount = Money(currency=charge.posttax_amount.currency)
        if charge.inclusion_charge_ids:
            inclusion_charges = bill_aggregate.get_active_charges(
                charge.inclusion_charge_ids
            )
            for inclusion_charge in inclusion_charges:
                splits_in_current_account = (
                    inclusion_charge.get_charge_splits_by_account(
                        account, ChargeTypes.NON_CREDIT, exclude_invoiced=True
                    )
                )
                if len(splits_in_current_account) == 0:
                    continue
                # there can be only one split for given account inside a charge
                inclusion_charge_split = splits_in_current_account[0]
                charge_splits.append(
                    (inclusion_charge.charge_id, inclusion_charge_split)
                )
                effective_charge_split_amount += (
                    inclusion_charge_split.get_posttax_amount_post_allowance_in_same_account()
                )
        return charge_splits, effective_charge_split_amount

    @staticmethod
    def _group_charges_for_spot_credit(bill_aggregate, account: BilledEntityAccountVO):
        splittable_charge: List[Tuple[Charge, ChargeSplit]] = []
        non_splittable_charge: List[Tuple[Charge, ChargeSplit]] = []
        for charge_id, charge_splits in bill_aggregate.get_charge_splits_of_account(
            account, ChargeTypes.NON_CREDIT, exclude_invoiced=True
        ).items():
            if len(charge_splits) == 0:
                continue
            charge = bill_aggregate.get_charge(charge_id)
            charge_split_of_account = charge_splits[
                0
            ]  # there can be only one split for given account inside a charge
            if charge.is_split_allowed():
                splittable_charge.append((charge, charge_split_of_account))
            elif not charge.is_inclusion_charge:
                non_splittable_charge.append((charge, charge_split_of_account))
        non_splittable_charge = sorted(
            non_splittable_charge,
            key=lambda s: s[1].get_posttax_amount_post_allowance_in_same_account(),
            reverse=True,
        )
        splittable_charge = sorted(
            splittable_charge,
            key=lambda s: s[1].get_posttax_amount_post_allowance_in_same_account(),
        )
        return non_splittable_charge, splittable_charge

    def _settle_account_by_spot_credit(
        self,
        bill_aggregate,
        from_account: BilledEntityAccountVO,
        settle_to_account: BilledEntityAccountVO,
        settlement_type: SpotCreditSettlementType,
    ):
        bill_summary = bill_aggregate.get_account_summary(from_account)
        currency = bill_summary.balance_to_clear_before_checkout.currency
        available_credit = bill_summary.credit_summary.total_credit
        if bill_summary.balance_to_clear_before_checkout == Money(currency=currency):
            raise SpotCreditIssueError(
                description="Net payable on this account {} is zero".format(
                    from_account
                )
            )
        if bill_summary.balance_to_clear_before_checkout < Money(currency=currency):
            raise SpotCreditIssueError(
                description="The account {} has surplus payments".format(from_account)
            )
        total_spot_credit_amount = Money(
            currency=bill_summary.balance_to_clear_before_checkout.currency
        )
        (
            non_splittable_charge,
            splittable_charge_list,
        ) = self._group_charges_for_spot_credit(bill_aggregate, from_account)
        affected_charges = []
        for charge, charge_split in non_splittable_charge:
            effective_charge_split_amount = (
                charge_split.get_posttax_amount_post_allowance_in_same_account()
            )
            splits_to_handle: List[Tuple[int, ChargeSplit]] = [
                (charge.charge_id, charge_split)
            ]
            (
                inclusion_splits,
                inclusion_splits_sum,
            ) = self._get_inclusion_charges_with_effective_total(
                bill_aggregate, charge, from_account
            )
            if len(inclusion_splits) > 0:
                effective_charge_split_amount += inclusion_splits_sum
                splits_to_handle.extend(inclusion_splits)
            if effective_charge_split_amount <= available_credit:
                available_credit -= effective_charge_split_amount
            else:
                for charge_split_map in splits_to_handle:
                    charge_id, charge_split = charge_split_map
                    bill_aggregate.convert_charge_split_to_spot_credit(
                        charge_id, charge_split.charge_split_id, settle_to_account
                    )
                    affected_charges.append(charge_id)
                total_spot_credit_amount += effective_charge_split_amount

        for charge, charge_split in splittable_charge_list:
            effective_charge_split_amount = (
                charge_split.get_posttax_amount_post_allowance_in_same_account()
            )
            if effective_charge_split_amount <= available_credit:
                available_credit -= effective_charge_split_amount
            elif available_credit > 0:
                amount_need_to_be_transferred = (
                    effective_charge_split_amount - available_credit
                )
                total_spot_credit_amount += amount_need_to_be_transferred
                bill_aggregate.create_spot_credit_from_existing_charge_split(
                    charge.charge_id,
                    charge_split.charge_split_id,
                    amount_need_to_be_transferred,
                    settle_to_account,
                )
                available_credit -= available_credit
                affected_charges.append(charge.charge_id)
            else:
                bill_aggregate.convert_charge_split_to_spot_credit(
                    charge.charge_id, charge_split.charge_split_id, settle_to_account
                )
                total_spot_credit_amount += effective_charge_split_amount
                affected_charges.append(charge.charge_id)
        if available_credit.amount > Decimal('0'):
            raise SpotCreditIssueError(
                description="Existing payment on this account {} can't be distributed".format(
                    from_account
                )
            )
        register_event(
            SpotCreditProvidedEvent(
                SpotCreditEventData(
                    source_billed_entity_account=from_account,
                    unpaid_charges=total_spot_credit_amount,
                    settlement_type=settlement_type.value,
                    destination_billed_entity_account=settle_to_account,
                )
            )
        )
        return affected_charges

    def _merge_charge_splits_if_eligible(
        self, bill_aggregate: BillAggregate, charge_ids
    ):
        # once spot credit issued there can be charges with more than one splits having same account.
        # this post runner will correct those charges
        for charge_id in charge_ids:
            charge: Charge = bill_aggregate.get_charge(charge_id)
            if len(charge.charge_splits) > 1:
                accounts = {
                    charge_split.billed_entity_account
                    for charge_split in charge.charge_splits
                }
                if len(accounts) == 1:
                    billed_entity_account = accounts.pop()
                    self._update_billing_instruction(
                        bill_aggregate, billed_entity_account, charge_id
                    )

    @staticmethod
    def _update_billing_instruction(bill_aggregate, billed_entity_account, charge_id):
        billing_instructions = [
            (
                BillingInstructionVO(
                    billed_entity_account,
                    PaymentInstruction.PAY_AFTER_CHECKOUT,
                    Decimal('100'),
                    is_spot_credit=True,
                )
            )
        ]
        bill_aggregate.update_billing_instructions(charge_id, billing_instructions)

    def settle_by_spot_credit(
        self,
        bill_aggregate,
        settle_to,
        account_to_settle,
        settlement_type: SpotCreditSettlementType,
    ):
        settle_to_credit_account = (
            bill_aggregate.get_billed_entity_account_for_new_assignment(
                settle_to, ChargeTypes.CREDIT
            )
        )
        affected_charge_ids = []
        for bea in account_to_settle:
            charges_converted = self._settle_account_by_spot_credit(
                bill_aggregate, bea, settle_to_credit_account, settlement_type
            )
            affected_charge_ids.extend(charges_converted)
        self._merge_charge_splits_if_eligible(bill_aggregate, set(affected_charge_ids))
        return bill_aggregate.get_folio(billed_entity_account=settle_to_credit_account)
