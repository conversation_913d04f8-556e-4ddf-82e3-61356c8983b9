import abc
import datetime
import logging
from typing import List, Sequence, Union

import pytz
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.domain.billing.aggregates.credit_note_aggregate import (
    CreditNoteAggregate,
)
from prometheus.domain.billing.aggregates.invoice_aggregate import InvoiceAggregate
from prometheus.domain.billing.entities.charge import Charge
from prometheus.domain.billing.entities.einvoice import EInvoiceDTO, IrpDetailsDTO
from prometheus.domain.billing.exceptions import (
    EInvoicingException,
    EInvoicingValidationError,
)
from prometheus.domain.billing.repositories import (
    CreditNoteRepository,
    InvoiceRepository,
)
from prometheus.domain.booking.repositories.booking_repository import IST
from prometheus.infrastructure.external_clients.cleartax_client import ClearTaxClient
from ths_common.constants.billing_constants import ChargeTypes, IssuedByType
from ths_common.constants.tenant_settings_constants import (
    EInvoicingConfig,
    TenantSettingName,
)
from ths_common.utils.common_utils import extract_state_code_from_gstin

logger = logging.getLogger(__name__)


class EInvoicingApplicability:
    def __init__(self, is_einvoicing_applicable):
        self.is_applicable = is_einvoicing_applicable

    def __bool__(self):
        return self.is_applicable

    def should_submit_to_irp(self):
        if self.is_applicable:
            return True
        return False

    @classmethod
    def from_data(
        cls,
        bill_to,
        issued_by,
        issued_by_type,
        einvoicing_config,
        invoice_charges: List[Charge] = None,
        creditnote_line_items=None,
        creditnote_invoices=None,
    ):
        data = {
            'bill_to': bill_to,
            'issued_by': issued_by,
            'issued_by_type': issued_by_type,
            'invoice_charges': invoice_charges,
            'creditnote_line_items': creditnote_line_items,
            'einvoicing_config': einvoicing_config,
            'creditnote_invoices': creditnote_invoices,
        }
        einvoicing_applicable = (
            einvoice_live_date_validator(data)
            and gstin_applicablity_validator(data)
            and config_validator(data)
            and creditnote_invoices_validator(data)
        )
        return cls(einvoicing_applicable)


class EInvoiceValidator(metaclass=abc.ABCMeta):
    def __init__(self, field_names: Sequence[str], _raise=True):
        self.field_names = (
            [field_names] if isinstance(field_names, str) else field_names
        )
        self.message = f"{self.__class__} failed"
        self._raise = _raise

    @abc.abstractmethod
    def validate(self, *args, **kwargs):
        pass

    def __call__(self, data, _raise=None):
        data = {fn: data.get(fn) for fn in self.field_names}
        _raise = _raise if _raise is not None else self._raise
        if self.validate(**data):
            return True
        if _raise:
            raise EInvoicingValidationError(description=self.message)
        return False


class EInvoiceLiveDateValidator(EInvoiceValidator):
    tz = pytz.timezone(IST)
    EINVOICE_LIVE_DATE = datetime.date(2021, 1, 1)

    def __init__(self, field_names=None, _raise=False):
        if field_names is None:
            field_names = ['invoice_charges', 'creditnote_line_items']
        super().__init__(field_names, _raise)

    def validate(self, invoice_charges, creditnote_line_items):
        either_invoice_or_cn_charge_present = bool(invoice_charges) ^ bool(
            creditnote_line_items
        )
        if not either_invoice_or_cn_charge_present:
            raise RuntimeError(
                "Either invoice charge or credit note line items are required"
            )
        if dateutils.current_date() < self.EINVOICE_LIVE_DATE:
            return False
        return True


class GstinApplicabilityValidator(EInvoiceValidator):
    def __init__(self, field_names='bill_to', _raise=False):
        super().__init__(field_names, _raise)

    def validate(self, bill_to):
        if bool(bill_to.gstin_num):
            return True


class ResellerApplicabilityValidator(EInvoiceValidator):
    def __init__(self, field_names='issued_by_type', _raise=False):
        super().__init__(field_names, _raise)

    def validate(self, issued_by_type):
        if issued_by_type == IssuedByType.RESELLER:
            return True


# Not being used currently
class ManagedPropertyApplicabilityValidator(EInvoiceValidator):
    def __init__(self, field_names='issued_by', _raise=False):
        super().__init__(field_names, _raise)

    def validate(self, issued_by):
        if 'ruptub' in (issued_by.gst_details.legal_name or '').lower():
            return True


class BTCChargesApplicabilityValidator(EInvoiceValidator):
    def __init__(self, field_names='invoice_charges', _raise=False):
        super().__init__(field_names, _raise)

    def validate(self, invoice_charges):
        if not invoice_charges:
            return False

        btc_charges = [
            c for c in invoice_charges if c.charge_type == ChargeTypes.CREDIT
        ]
        if btc_charges and len(btc_charges) == len(invoice_charges):
            return True


# Not being used currently
class GstinValidator(EInvoiceValidator):
    def __init__(self, field_names='gstin', _raise=False):
        super().__init__(field_names, _raise)

    def validate(self, gstin):
        if len(gstin) == 15:
            return True


# Not being used currently
class PINCodeSateCodeValidator(EInvoiceValidator):
    pincode_state_code_map = {
        10: "",
        11: "7",
        12: "6",
        13: "6",
        14: "3",
        15: "3",
        16: "3",
        17: "2",
        18: "1",
        19: "1",
        20: "9",
        21: "9",
        22: "9",
        23: "9",
        24: "9",
        25: "9",
        26: "9",
        27: "9",
        28: "9",
        29: "",
        30: "8",
        31: "8",
        32: "8",
        33: "8",
        34: "8",
        35: "",
        36: "24",
        37: "24",
        38: "24",
        39: ["24", "25", "26"],
        40: ["27", "30"],
        41: "27",
        42: "27",
        43: "27",
        44: "27",
        45: "23",
        46: "23",
        47: "23",
        48: "23",
        49: "22",
        50: "36",
        51: "37",
        52: "37",
        53: "37",
        54: "",
        55: "",
        56: "29",
        57: "29",
        58: "29",
        59: "29",
        60: ["33", "34"],
        61: "33",
        62: "33",
        63: "33",
        64: "33",
        65: "33",
        66: "33",
        67: "32",
        68: ["32", "31"],
        69: "32",
        70: "19",
        71: "19",
        72: "19",
        73: ["19", "11"],
        74: ["19", "35"],
        75: "21",
        76: "21",
        77: "21",
        78: "18",
        79: ["12", "17", "14", "15", "13", "16"],
        80: ["10", "20"],
        81: ["10", "20"],
        82: ["10", "20"],
        83: ["10", "20"],
        84: ["10", "20"],
        85: ["10", "20"],
        86: "",
        87: "",
        88: "",
        89: "",
        90: "",
    }

    def __init__(self, field_names='bill_to', _raise=False):
        super().__init__(field_names, _raise)

    def validate(self, bill_to):
        if not bill_to.address:
            return False

        pincode = bill_to.address.pincode
        state_code = extract_state_code_from_gstin(bill_to.gstin_num)
        if not len(pincode) == 6:
            return False

        applicable_state_codes = self.pincode_state_code_map.get(int(pincode[:2]))
        if isinstance(applicable_state_codes, str):
            return state_code == applicable_state_codes
        return state_code in applicable_state_codes


class CreditnoteInvoicesValidator(EInvoiceValidator):
    def __init__(self, field_names="creditnote_invoices", _raise=False):
        super().__init__(field_names, _raise)

    def validate(self, creditnote_invoices):
        if not creditnote_invoices:
            return True
        invoice_irn = [i.invoice.irn for i in creditnote_invoices]
        if None in invoice_irn:
            return False
        return True


class ConfigValidator(EInvoiceValidator):
    def __init__(self, field_names=None, _raise=False):
        if field_names is None:
            field_names = ["einvoicing_config", "issued_by", "issued_by_type"]
        super().__init__(field_names, _raise)

    def validate(self, einvoicing_config, issued_by, issued_by_type):
        gstin = issued_by.gst_details.gstin_num
        if not einvoicing_config:
            return False
        if einvoicing_config.is_gstin_black_listed(gstin):
            return False
        if issued_by_type == IssuedByType.RESELLER:
            return einvoicing_config.is_enabled_for_reseller_invoice()
        elif issued_by_type == IssuedByType.HOTEL:
            return einvoicing_config.is_enabled_for_marketplace_invoice()
        return False


def validate(validators: Sequence[EInvoiceValidator], data, _raise=False):
    validations = [validator(data, _raise) for validator in validators]
    return all(validations)


einvoice_live_date_validator = EInvoiceLiveDateValidator()
gstin_applicablity_validator = GstinApplicabilityValidator()
managed_property_applicability_validator = ManagedPropertyApplicabilityValidator()
btc_charges_applicability_validator = BTCChargesApplicabilityValidator()
pincode_state_code_validator = PINCodeSateCodeValidator()
config_validator = ConfigValidator()
creditnote_invoices_validator = CreditnoteInvoicesValidator()


@register_instance(
    dependencies=[
        ClearTaxClient,
        InvoiceRepository,
        CreditNoteRepository,
        TenantSettings,
        JobSchedulerService,
    ]
)
class EInvoicingService(object):
    def __init__(
        self,
        client,
        invoice_repository,
        credit_note_repository,
        tenant_settings,
        job_scheduler_service,
    ):
        self.client = client
        self.invoice_repository: InvoiceRepository = invoice_repository
        self.credit_note_repository: CreditNoteRepository = credit_note_repository
        self.tenant_settings = tenant_settings
        self.job_scheduler_service = job_scheduler_service

    def is_einvoicing_applicable(self, aggregate) -> EInvoicingApplicability:
        if isinstance(aggregate, InvoiceAggregate):
            return self._is_einvoicing_applicable_for_invoice(aggregate)
        elif isinstance(aggregate, CreditNoteAggregate):
            return self._is_einvoicing_applicable_for_creditnote(aggregate)
        else:
            raise RuntimeError("unknown entity for EInvoice applicability check")

    def _is_einvoicing_applicable_for_invoice(
        self, invoice_aggregate: InvoiceAggregate
    ):
        bill_to = invoice_aggregate.invoice.bill_to
        issued_by = invoice_aggregate.invoice.issued_by
        issued_by_type = invoice_aggregate.invoice.issued_by_type
        charges = invoice_aggregate.invoice_charges
        hotel_id = (
            None
            if issued_by_type == IssuedByType.SELLER
            else invoice_aggregate.invoice.vendor_id
        )
        einvoicing_config = self.tenant_settings.get_einvoicing_config(hotel_id)
        return EInvoicingApplicability.from_data(
            bill_to,
            issued_by,
            issued_by_type,
            einvoicing_config,
            invoice_charges=charges,
        )

    def _is_einvoicing_applicable_for_creditnote(
        self, credit_note_aggregate: CreditNoteAggregate
    ):
        bill_to = credit_note_aggregate.credit_note.issued_to
        issued_by = credit_note_aggregate.credit_note.issued_by
        issued_by_type = credit_note_aggregate.credit_note.issued_by_type
        line_items = credit_note_aggregate.credit_note_line_items
        hotel_id = (
            None
            if issued_by_type == IssuedByType.SELLER
            else credit_note_aggregate.credit_note.vendor_id
        )
        einvoicing_config = self.tenant_settings.get_einvoicing_config(hotel_id)
        (
            invoices,
            _,
        ) = credit_note_aggregate.get_attached_invoices_with_credit_note_amount()
        creditnote_invoices = self.invoice_repository.load_all(invoices)
        return EInvoicingApplicability.from_data(
            bill_to,
            issued_by,
            issued_by_type,
            einvoicing_config,
            creditnote_line_items=line_items,
            creditnote_invoices=creditnote_invoices,
        )

    def generate_irn(
        self, aggregate: Union[InvoiceAggregate, CreditNoteAggregate]
    ) -> IrpDetailsDTO:
        einvoice = EInvoiceDTO(aggregate)
        try:
            irp_response: IrpDetailsDTO = self.client.generate_irn(
                aggregate.issued_by_type,
                aggregate.vendor_id,
                einvoice,
            )
        except EInvoicingException as e:
            logger.exception(e)
            self._trigger_failure_report(aggregate, e)
            raise
        except Exception as e:
            raise EInvoicingException(
                description=f"{self.client} failed due to  {e}"
            ) from e
        return irp_response

    def _trigger_failure_report(self, aggregate, e):
        if e.error_code == 'EINV-0316':
            failures = [e.extra_payload.get("client_response", {}).get("error_message")]
        elif e.error_code == 'EINV-0317':
            failures = e.extra_payload.get("errors")
        else:
            failures = [str(e)]
        send_to = self._get_reporting_email()
        if not send_to:
            return
        if isinstance(aggregate, InvoiceAggregate):
            self.job_scheduler_service.schedule_einvoice_failure_report(
                send_to=send_to, invoice_id=aggregate.invoice_id, failures=failures
            )
        if isinstance(aggregate, CreditNoteAggregate):
            self.job_scheduler_service.schedule_einvoice_failure_report(
                send_to=send_to,
                credit_note_id=aggregate.credit_note.credit_note_id,
                failures=failures,
            )

    def get_einvoices_without_irn(self, invoice_ids=None, bill_ids=None):
        if not invoice_ids or bill_ids:
            return None
        return self.invoice_repository.get_einvoices_without_irn(
            invoice_ids=invoice_ids, bill_ids=bill_ids
        )

    def get_ecredit_notes_without_irn(self, credit_note_ids=None, bill_ids=None):
        if not credit_note_ids or bill_ids:
            return None
        return self.credit_note_repository.get_ecredit_notes_without_irn(
            credit_note_ids=credit_note_ids, bill_ids=bill_ids
        )

    def set_invoice_irp_details(
        self, invoice_aggregate, irp_details, invoice_number=None
    ):
        invoice_aggregate.set_irp_details(irp_details)
        if invoice_number:
            invoice_aggregate.invoice.set_invoice_number(invoice_number)

    def set_credit_note_irp_details(
        self, credit_note_aggregate, irp_details, credit_note_number=None
    ):
        credit_note_aggregate.set_irp_details(irp_details)
        if credit_note_number:
            credit_note_aggregate.credit_note.credit_note_number = credit_note_number

    def _get_reporting_email(self):
        einvoicing_config = self.tenant_settings.get_setting_value(
            TenantSettingName.E_INVOICING.value
        )
        return (
            einvoicing_config.get(EInvoicingConfig.REPORTING_EMAIL.value)
            if einvoicing_config
            else None
        )
