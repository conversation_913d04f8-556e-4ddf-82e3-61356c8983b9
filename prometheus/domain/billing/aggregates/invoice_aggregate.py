# coding=utf-8
"""
Invoice aggregate
"""
import logging
from collections import defaultdict
from typing import List

from treebo_commons.money import Money

from prometheus import crs_context
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.domain_events.invoice_bill_to_updated import (
    InvoiceBillToUpdatedEvent,
)
from prometheus.domain.billing.domain_events.invoice_cancelled import (
    InvoiceCancelledEvent,
)
from prometheus.domain.billing.domain_events.invoice_generated import (
    InvoiceGeneratedEvent,
)
from prometheus.domain.billing.domain_events.invoice_regenerated import (
    InvoiceRegeneratedEvent,
)
from prometheus.domain.billing.dto.invoice_charge_date import InvoiceChargeData
from prometheus.domain.billing.dto.invoice_event_data import InvoiceEventData
from prometheus.domain.billing.dto.stay_charge_item_event_data import (
    StayChargeItemEventData,
)
from prometheus.domain.billing.entities.einvoice import IrpDetailsDTO
from prometheus.domain.billing.entities.invoice import Invoice
from prometheus.domain.billing.entities.invoice_charge import InvoiceCharge
from prometheus.domain.billing.errors import BillingErrors
from prometheus.domain.billing.exceptions import BillingError
from prometheus.domain.domain_events.domain_event_registry import register_event
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.access_entity_facts import AccessEntityFacts
from ths_common.constants.billing_constants import (
    ChargeBillToTypes,
    ChargeTypes,
    InvoiceStatus,
    IssuedByType,
    IssuedToType,
)
from ths_common.constants.catalog_constants import SkuCategory
from ths_common.exceptions import ValidationException
from ths_common.value_objects import InvoiceBillToInfo, RoomChargeItemDetails

logger = logging.getLogger(__name__)


class InvoiceAggregate(object):
    """
    Invoice Aggregate
    Root entities: Invoice
    Non-root entities: Charge
    """

    def __init__(self, invoice: Invoice, invoice_charges: List[InvoiceCharge]):
        self._invoice = invoice
        self._invoice_charges = invoice_charges
        self.payment_splits = []

        RuleEngine.action_allowed(
            action='access_entity',
            facts=AccessEntityFacts(
                user_data=crs_context.user_data,
                entity_vendor_id=invoice.vendor_id,
                entity_type="invoice",
            ),
            fail_on_error=True,
        )
        self.net_payable = None
        self.hotel_invoice_number = None

    @property
    def invoice_url(self):
        return self.invoice.invoice_url

    @property
    def bill_id(self):
        return self.invoice.bill_id

    def update_invoice_url(self, invoice_url):
        self.invoice.invoice_url = invoice_url

    def populate_actual_check_in_checkout_dates(
        self, check_in_date=None, checkout_date=None
    ):
        if check_in_date:
            self.invoice.parent_info["actual_checkin_date"] = str(check_in_date)
        if checkout_date:
            self.invoice.parent_info["actual_checkout_date"] = str(checkout_date)

    def credit_note_generated(self, invoice_charge_id, credit_note_generated_amount):
        if not self._can_generate_credit_note():
            raise BillingError(
                error=BillingErrors.CREDIT_NOTE_GENERATION_ON_NOT_GENERATED_INVOICES
            )
        invoice_charge = self.get_invoice_charge(invoice_charge_id)
        invoice_charge.credit_note_generated(credit_note_generated_amount)

    def _can_generate_credit_note(self):
        return self._invoice.status not in [
            InvoiceStatus.PREVIEW,
            InvoiceStatus.CANCELLED,
        ]

    def reverse_credit_note_amount(self, invoice_charge_id, credit_note_amount):
        invoice_charge = self.get_invoice_charge(invoice_charge_id)
        invoice_charge.reverse_credit_note_amount(credit_note_amount)

    def is_reseller_issued_invoice(self):
        return self.invoice.issued_by_type == IssuedByType.RESELLER

    def reset_issuer_details(self, issued_by_type, issued_by):
        self.invoice.issued_by_type = issued_by_type
        self.invoice.issued_by = issued_by

    def update_issued_to_details(self, issued_to_type):
        self.invoice.issued_to_type = issued_to_type

    def increment_version(self):
        self.invoice.increment_version()

    def lock(self):
        self.invoice.status = InvoiceStatus.LOCKED

    @property
    def invoice(self):
        return self._invoice

    @property
    def vendor_id(self):
        return self._invoice.vendor_id

    @property
    def issued_by_type(self):
        return self._invoice.issued_by_type

    @property
    def invoice_charges(self):
        if not self._invoice_charges:
            return []
        return [
            invoice_charge
            for invoice_charge in self._invoice_charges
            if not invoice_charge.deleted
        ]

    @property
    def allowed_charge_to_ids(self):
        return self.invoice.allowed_charge_to_ids

    @property
    def billed_entity_id(self):
        return self.invoice.billed_entity_account.billed_entity_id

    @property
    def allowed_charge_types(self):
        return self.invoice.allowed_charge_types

    @property
    def allowed_bill_to_ids(self):
        if self.invoice.bill_to_type == ChargeBillToTypes.GUEST:
            return list(self.invoice.user_info_map)
        else:
            return [self.invoice.bill_to.customer_id] if self.invoice.bill_to else None

    def get_charge_split_map(self):
        charge_map = defaultdict(lambda: set())
        for invoice_charge in self.invoice_charges:
            for charge_split_id in invoice_charge.charge_split_ids:
                charge_map[invoice_charge.charge_id].add(charge_split_id)
        return charge_map

    def get_invoice_charge(self, invoice_charge_id):
        for invoice_charge in self.invoice_charges:
            if invoice_charge.invoice_charge_id == invoice_charge_id:
                return invoice_charge
        return None

    def delete_all_charges(self, except_ids=None):
        for invoice_charge in self.invoice_charges:
            if except_ids and invoice_charge.charge_id in except_ids:
                continue
            invoice_charge.delete()

    def filter_invoice_charge(self, charge_id):
        invoice_charge = [
            invoice_charge
            for invoice_charge in self.invoice_charges
            if invoice_charge.charge_id == charge_id
        ]
        return invoice_charge[0] if invoice_charge else None

    def filter_and_get_charges(self, charge_ids):
        charges = [
            invoice_charge
            for invoice_charge in self.invoice_charges
            if invoice_charge.charge_id in charge_ids
        ]
        return charges

    def mark_as_spot_credit_invoice(self):
        self.invoice.mark_as_spot_credit()

    def bill_to_update(self, new_bill_to: InvoiceBillToInfo):
        old_bill_to = self.invoice.bill_to
        self.invoice.set_bill_to(new_bill_to)
        register_event(
            InvoiceBillToUpdatedEvent(
                invoice_id=self.invoice.invoice_id,
                old_bill_to=old_bill_to,
                new_bill_to=new_bill_to,
            )
        )

    def confirm(self, invoice_number):
        # Attach invoice number
        self.invoice.set_invoice_number(invoice_number)

        # mark the invoice generated
        self.invoice.confirm()
        self.invoice.generated_by = (
            crs_context.user_data.user if crs_context.user_data else None
        )

        # Raise Domain Event
        if self.invoice.status == InvoiceStatus.GENERATED:
            stay_charge_item_event_data = list(
                set(
                    [
                        StayChargeItemEventData(
                            RoomChargeItemDetails.from_dict(
                                invoice_charge.charge_item.details
                            )
                        )
                        for invoice_charge in self.invoice_charges
                        if invoice_charge.charge_item.sku_category_id
                        == SkuCategory.STAY.value
                    ]
                )
            )

            register_event(
                InvoiceGeneratedEvent(
                    InvoiceEventData(
                        invoice_id=self.invoice.invoice_id,
                        invoice_number=invoice_number,
                        invoice_amount=self.invoice.posttax_amount,
                        stay_charge_item_details=stay_charge_item_event_data,
                        status=self.invoice.status.value,
                    )
                )
            )

    @property
    def status(self):
        return self.invoice.status

    @property
    def invoice_id(self):
        return self.invoice.invoice_id

    @property
    def invoice_number(self):
        return self.invoice.invoice_number

    @property
    def bill_to(self):
        return self.invoice.bill_to

    def get_non_credit_payable(self):
        # TODO: Verify if this requires change
        return Money(
            sum(
                [
                    invoice_charge.posttax_amount
                    for invoice_charge in self.invoice_charges
                    if invoice_charge.charge_type == ChargeTypes.NON_CREDIT
                ]
            ),
            self.invoice.base_currency,
        )

    def get_credit_payable(self):
        return Money(
            sum(
                [
                    invoice_charge.posttax_amount
                    for invoice_charge in self.invoice_charges
                    if invoice_charge.charge_type == ChargeTypes.CREDIT
                ]
            ),
            self.invoice.base_currency,
        )

    def overide_invoice_charges(self, invoice_charge_data: [InvoiceChargeData]):
        """
        Override the invoice charges and create new set of invoice charges
        Args:
            invoice_charge_data: a list of invoice charge datas

        Returns: the set of new invoice charges created

        """
        next_invoice_charge_id = (
            max(
                [
                    invoice_charge.invoice_charge_id
                    for invoice_charge in self._invoice_charges
                ]
            )
            if self._invoice_charges
            else 0
        )

        total_tax_details = defaultdict(lambda: Money('0', self.invoice.base_currency))
        pretax_amount = 0
        tax_amount = 0
        posttax_amount = 0
        invoice_charges = []
        if self.invoice.is_editable():
            self.delete_all_charges()
        else:
            charge_ids_to_invoiced = [
                charge_data.charge_id for charge_data in invoice_charge_data
            ]
            existing_charge_ids = [
                invoice_charge.charge_id for invoice_charge in self.invoice_charges
            ]
            if set(charge_ids_to_invoiced) != set(existing_charge_ids):
                raise BillingError(error=BillingErrors.INVOICE_IN_INVALID_STATE)

        for charge_data in invoice_charge_data:
            existing_invoice_charge = self.filter_invoice_charge(charge_data.charge_id)
            if existing_invoice_charge:
                existing_invoice_charge.charge_type = charge_data.charge_type
            else:
                # create invoice charge
                next_invoice_charge_id += 1
                invoice_charge = InvoiceCharge(
                    invoice_charge_id=next_invoice_charge_id,
                    charge_id=charge_data.charge_id,
                    charge_split_ids=charge_data.charge_split_ids,
                    pretax_amount=charge_data.pretax_amount,
                    tax_amount=charge_data.tax_amount,
                    tax_details=charge_data.tax_details,
                    posttax_amount=charge_data.posttax_amount,
                    charge_type=charge_data.charge_type,
                    bill_to_type=charge_data.bill_to_type,
                    charge_status=charge_data.charge_status,
                    recorded_time=charge_data.recorded_time,
                    applicable_date=charge_data.applicable_date,
                    comment=charge_data.comment,
                    created_by=charge_data.created_by,
                    charge_item=charge_data.charge_item,
                    charge_to_ids=charge_data.charge_to_ids,
                    credit_note_generated_amount=Money(0, self.invoice.base_currency),
                )

                for tax_detail in invoice_charge.tax_details:
                    total_tax_details[tax_detail.tax_type] += tax_detail.amount
                pretax_amount += invoice_charge.pretax_amount
                tax_amount += invoice_charge.tax_amount
                posttax_amount += invoice_charge.posttax_amount

                invoice_charges.append(invoice_charge)
            logger.info(
                "Tax details for invoice charge for bill_id=%s, charge_id=%s is %s",
                self.bill_id,
                charge_data.charge_id,
                charge_data.tax_details,
            )

        self._invoice_charges.extend(invoice_charges)

        # update amounts
        if self.invoice.is_editable():
            self.invoice.update_amount(
                pretax_amount, tax_amount, posttax_amount, total_tax_details
            )
        logger.info(
            "Tax details for invoice with invoice_id=%s is %s",
            self.invoice.invoice_id,
            self.invoice.tax_details,
        )

        # update status
        if self.invoice.status in (InvoiceStatus.GENERATED, InvoiceStatus.REGENERATED):
            # TODO: Fix this code. Status update should ideally not happen in this method
            self._invoice.status = InvoiceStatus.REGENERATED
            register_event(
                InvoiceRegeneratedEvent(
                    InvoiceEventData(
                        invoice_id=self.invoice.invoice_id,
                        invoice_number=self.invoice.invoice_number,
                        invoice_amount=self.invoice.posttax_amount,
                        status=self.invoice.status.value,
                    )
                )
            )

        if self.invoice.status in (
            InvoiceStatus.LOCKED,
            InvoiceStatus.SENT_FOR_GST_FILING,
        ):
            register_event(
                InvoiceRegeneratedEvent(
                    InvoiceEventData(
                        invoice_id=self.invoice.invoice_id,
                        invoice_number=self.invoice.invoice_number,
                        invoice_amount=self.invoice.posttax_amount,
                        status=self.invoice.status.value,
                    )
                )
            )

        return invoice_charges

    def get_assigned_users(self):
        charge_to_ids = []
        for invoice_charge in self.invoice_charges:
            charge_to_ids.extend(invoice_charge.charge_to_ids)
        return set(charge_to_ids)

    def is_void(self):
        return self.invoice.is_cancelled or not self.invoice_charges

    def cancel(self):
        assert self.invoice.status in [
            InvoiceStatus.PREVIEW,
            InvoiceStatus.CANCELLED,
            InvoiceStatus.GENERATED,
            InvoiceStatus.REGENERATED,
        ]
        if self.invoice.status == InvoiceStatus.CANCELLED:
            return
        previous_status = self.invoice.status
        self.invoice.status = InvoiceStatus.CANCELLED
        if previous_status != InvoiceStatus.PREVIEW:
            register_event(
                InvoiceCancelledEvent(
                    InvoiceEventData(
                        self.invoice_id,
                        self.invoice.invoice_number,
                        self.invoice.posttax_amount,
                        status=self.invoice.status.value,
                    )
                )
            )

    def set_irp_details(self, irp_details: IrpDetailsDTO):
        self.invoice.irn = irp_details.irn
        self.invoice.qr_code = irp_details.qr_code
        self.invoice.irp_ack_number = irp_details.ack_no
        self.invoice.irp_ack_date = irp_details.acked_on
        self.invoice.signed_invoice = irp_details.signed_invoice
        self.invoice.status = InvoiceStatus.LOCKED

    def has_invoice_credit_charge(self):
        for invoice_charge in self.invoice_charges:
            if invoice_charge.charge_type == ChargeTypes.NON_CREDIT:
                return True
        return False

    @property
    def signed_url(self):
        return self.invoice.signed_url

    def set_signed_url(self, url, expiration):
        self.invoice.signed_url = url
        self.invoice.signed_url_expiry_time = expiration

    def set_hotel_invoice_number(self, hotel_invoice_number):
        self.hotel_invoice_number = hotel_invoice_number

    @property
    def credit_note_amount(self):
        return (
            sum(
                [
                    invoice_charge.credit_note_generated_amount
                    for invoice_charge in self.invoice_charges
                ]
            )
            if self.invoice_charges
            else Money(0, self.invoice.base_currency)
        )

    @property
    def can_refresh(self):
        return self._invoice.status == InvoiceStatus.GENERATED

    @property
    def hotel_invoice_id(self):
        return self.invoice.hotel_invoice_id

    def set_is_downloaded(self, is_downloaded):
        if not self.invoice.is_downloaded:
            self.invoice.is_downloaded = is_downloaded

    def set_hotel_invoice(self, hotel_invoice_id):
        self.invoice.set_hotel_invoice(hotel_invoice_id)

    def update_hsn_details(self, grouped_sku_categories: dict):
        # TODO: grouped_sku_categories receives SKUCategory entity -> which is different domain.
        #  Try to get rid of that dependency and instead pass some DTO, or only item_code,
        #  which is used here
        for invoice_charge in self.invoice_charges:
            sku_category = grouped_sku_categories.get(
                invoice_charge.charge_item.sku_category_id
            )
            if sku_category and sku_category.item_code:
                invoice_charge.charge_item.item_code = sku_category.item_code
            else:
                extra_payload = dict(
                    sku_category_id=invoice_charge.charge_item.sku_category_id
                )
                # TODO: Replace with BillingErrors
                raise ValidationException(
                    ApplicationErrors.CHARGE_ITEM_HSN_CODE_NOT_FOUND,
                    extra_payload=extra_payload,
                )

    def update_invoice_charge_item_details(self, charge_id, room_stay_id):
        charge = self.get_invoice_charge(charge_id)
        charge.charge_item.details['room_stay_id'] = room_stay_id
        charge.mark_dirty()

    def is_customer_invoice(self):
        return self.invoice.issued_to_type == IssuedToType.CUSTOMER

    def is_pos_seller_invoice(self):
        return self.invoice.issued_by_type == IssuedByType.SELLER

    def get_linked_room_stay_ids(self):
        return [
            invoice_charge.charge_item.details['room_stay_id']
            for invoice_charge in self.invoice_charges
        ]
