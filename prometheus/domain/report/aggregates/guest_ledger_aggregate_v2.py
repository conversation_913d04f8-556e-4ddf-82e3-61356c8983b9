from prometheus.domain.report.entities.guest_ledger_v2 import GuestLedgerV2


class GuestLedgerAggregateV2(object):
    def __init__(self, guest_ledger: GuestLedgerV2):
        self.guest_ledger = guest_ledger

    @property
    def booking_id(self):
        return self.guest_ledger.booking_id

    @property
    def opening_balance_in_base_currency(self):
        return self.guest_ledger.opening_balance_in_base_currency

    @property
    def closing_balance_in_base_currency(self):
        return self.guest_ledger.closing_balance_in_base_currency

    @property
    def total_payments(self):
        return self.guest_ledger.total_payments

    @property
    def total_charges(self):
        return self.guest_ledger.total_charges

    def check_invariance(self):
        pass
