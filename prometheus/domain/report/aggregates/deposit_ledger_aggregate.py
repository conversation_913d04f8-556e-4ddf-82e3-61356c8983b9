from prometheus.domain.report.entities.deposit_ledger import DepositLedger


class DepositLedgerAggregate(object):
    def __init__(self, deposit_ledger: DepositLedger):
        self.deposit_ledger = deposit_ledger

    @property
    def opening_balance_in_base_currency(self):
        return self.deposit_ledger.opening_balance_in_base_currency

    @property
    def closing_balance_in_base_currency(self):
        return self.deposit_ledger.closing_balance_in_base_currency

    @property
    def total_payments(self):
        return self.deposit_ledger.total_payments

    @property
    def total_charges(self):
        return self.deposit_ledger.total_charges

    def check_invariance(self):
        pass
