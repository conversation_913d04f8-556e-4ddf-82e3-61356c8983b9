from sqlalchemy import (
    DECIMAL,
    JSON,
    Column,
    Date,
    Index,
    Integer,
    String,
    UniqueConstraint,
)
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from shared_kernel.infrastructure.database.orm_base import DeleteMixin, TimeStampMixin


class GuestLedgerReportModel(Base, TimeStampMixin, DeleteMixin):
    """
    Guest Ledger Report
    """

    __tablename__ = "guest_ledger_report"

    id = Column(Integer, primary_key=True)
    report_date = Column(Date)
    booking_id = Column(String)
    opening_balance_in_base_currency = Column(DECIMAL(precision=15, scale=4))
    total_payments = Column(DECIMAL(precision=15, scale=4))
    total_charges = Column(DECIMAL(precision=15, scale=4))
    closing_balance_in_base_currency = Column(DECIMAL(precision=15, scale=4))
    deposit_transferred_at_checkin = Column(DECIMAL(precision=15, scale=4))
    base_currency = Column(String)
    payment_components = Column(JSON)
    revenue_components = Column(JSON)
    non_revenue_components = Column(JSON)
    charges_transferred_to_ar = Column(DECIMAL(precision=15, scale=4))
    hotel_id = Column(String)

    __table_args__ = (
        Index('ix_glr_report_date', 'report_date'),
        Index('ix_glr_booking_id', 'booking_id'),
        UniqueConstraint(
            'report_date', 'booking_id', name='ix_glr_booking_id_report_date_uniq'
        ),
    )


class ARLedgerReportModel(Base, TimeStampMixin, DeleteMixin):
    """
    Account Receivable Report
    """

    __tablename__ = "ar_ledger_report"

    id = Column(Integer, primary_key=True)
    report_date = Column(Date)
    opening_balance_in_base_currency = Column(DECIMAL(precision=15, scale=4))
    total_payments = Column(DECIMAL(precision=15, scale=4))
    total_charges = Column(DECIMAL(precision=15, scale=4))
    closing_balance_in_base_currency = Column(DECIMAL(precision=15, scale=4))
    hotel_id = Column(String)
    base_currency = Column(String)
    payment_components = Column(JSON)
    revenue_components = Column(JSON)
    non_revenue_components = Column(JSON)

    __table_args__ = (
        Index('ix_arlr_report_date', 'report_date'),
        UniqueConstraint(
            'hotel_id', 'report_date', name='ix_arlr_report_date_hotel_id_uniq'
        ),
    )


class DepositLedgerReportModel(Base, TimeStampMixin, DeleteMixin):
    """
    Deposit Ledger Report
    """

    __tablename__ = "deposit_ledger_report"

    id = Column(Integer, primary_key=True)
    report_date = Column(Date)
    opening_balance_in_base_currency = Column(DECIMAL(precision=15, scale=4))
    total_payments = Column(DECIMAL(precision=15, scale=4))
    total_charges = Column(DECIMAL(precision=15, scale=4))
    deposit_transferred_at_checkin = Column(DECIMAL(precision=15, scale=4))
    closing_balance_in_base_currency = Column(DECIMAL(precision=15, scale=4))
    hotel_id = Column(String)
    base_currency = Column(String)
    payment_components = Column(JSON)
    revenue_components = Column(JSON)
    non_revenue_components = Column(JSON)

    __table_args__ = (
        Index('ix_dlr_report_date', 'report_date'),
        UniqueConstraint(
            'hotel_id', 'report_date', name='ix_dlr_report_date_hotel_id_uniq'
        ),
    )


class GuestLedgerReportModelV2(Base, TimeStampMixin, DeleteMixin):
    """
    Guest Ledger Report
    """

    __tablename__ = "guest_ledger_report_v2"

    id = Column(Integer, primary_key=True)
    business_date = Column(Date)
    booking_id = Column(String)
    opening_balance_in_base_currency = Column(DECIMAL(precision=15, scale=4))
    total_payments = Column(DECIMAL(precision=15, scale=4))
    total_charges = Column(DECIMAL(precision=15, scale=4))
    closing_balance_in_base_currency = Column(DECIMAL(precision=15, scale=4))
    deposit_transferred_at_checkin = Column(DECIMAL(precision=15, scale=4))
    base_currency = Column(String)
    payment_components = Column(JSON)
    revenue_components = Column(JSON)
    non_revenue_components = Column(JSON)
    charges_transferred_to_ar = Column(DECIMAL(precision=15, scale=4))
    hotel_id = Column(String)

    __table_args__ = (
        Index('ix_glrv2_business_date', 'business_date'),
        UniqueConstraint(
            'hotel_id', 'business_date', name='ix_glrv2_business_date_hotel_id_uniq'
        ),
    )


class ARLedgerReportModelV2(Base, TimeStampMixin, DeleteMixin):
    """
    Account Receivable Report
    """

    __tablename__ = "ar_ledger_report_v2"

    id = Column(Integer, primary_key=True)
    business_date = Column(Date)
    opening_balance_in_base_currency = Column(DECIMAL(precision=15, scale=4))
    total_payments = Column(DECIMAL(precision=15, scale=4))
    total_charges = Column(DECIMAL(precision=15, scale=4))
    closing_balance_in_base_currency = Column(DECIMAL(precision=15, scale=4))
    hotel_id = Column(String)
    base_currency = Column(String)
    payment_components = Column(JSON)
    revenue_components = Column(JSON)
    non_revenue_components = Column(JSON)

    __table_args__ = (
        Index('ix_arlrv2_business_date', 'business_date'),
        UniqueConstraint(
            'hotel_id', 'business_date', name='ix_arlrv2_business_date_hotel_id_uniq'
        ),
    )


class DepositLedgerReportModelV2(Base, TimeStampMixin, DeleteMixin):
    """
    Deposit Ledger Report
    """

    __tablename__ = "deposit_ledger_report_v2"

    id = Column(Integer, primary_key=True)
    business_date = Column(Date)
    opening_balance_in_base_currency = Column(DECIMAL(precision=15, scale=4))
    total_payments = Column(DECIMAL(precision=15, scale=4))
    total_charges = Column(DECIMAL(precision=15, scale=4))
    deposit_transferred_at_checkin = Column(DECIMAL(precision=15, scale=4))
    closing_balance_in_base_currency = Column(DECIMAL(precision=15, scale=4))
    hotel_id = Column(String)
    base_currency = Column(String)
    payment_components = Column(JSON)
    revenue_components = Column(JSON)
    non_revenue_components = Column(JSON)

    __table_args__ = (
        Index('ix_dlrv2_business_date', 'business_date'),
        UniqueConstraint(
            'hotel_id', 'business_date', name='ix_dlrv2_business_date_hotel_id_uniq'
        ),
    )


class FlashManagerReportModel(Base, TimeStampMixin):
    "Flash Manager Report"
    __tablename__ = "flash_manager_report"
    id = Column(Integer, primary_key=True)
    report_date = Column(Date)
    data = Column(JSON)
    hotel_id = Column(String)

    __table_args__ = (Index('idx_report_date_hotel_id', report_date, hotel_id),)
