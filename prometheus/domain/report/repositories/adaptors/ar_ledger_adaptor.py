from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from prometheus.domain.report.entities.ar_ledger import ARLedger
from prometheus.domain.report.models import ARLedgerReportModel


class ARLedgerDBAdapter(object):
    @staticmethod
    def to_db_model(ar_ledger):
        # noinspection PyArgumentList
        return ARLedgerReportModel(
            report_date=ar_ledger.report_date,
            opening_balance_in_base_currency=ar_ledger.opening_balance_in_base_currency.amount,
            total_payments=ar_ledger.total_payments.amount
            if ar_ledger.total_payments
            else 0,
            total_charges=ar_ledger.total_charges.amount
            if ar_ledger.total_charges
            else 0,
            closing_balance_in_base_currency=ar_ledger.closing_balance_in_base_currency.amount,
            hotel_id=ar_ledger.hotel_id,
            base_currency=ar_ledger.base_currency.value,
            payment_components=ar_ledger.payment_components,
            revenue_components=ar_ledger.revenue_components,
            non_revenue_components=ar_ledger.non_revenue_components,
            deleted=ar_ledger.deleted,
        )

    @staticmethod
    def to_entity(ar_ledger_model):
        base_currency = CurrencyType(ar_ledger_model.base_currency)
        return ARLedger(
            report_date=ar_ledger_model.report_date,
            opening_balance_in_base_currency=Money(
                ar_ledger_model.opening_balance_in_base_currency, base_currency
            ),
            total_payments=Money(ar_ledger_model.total_payments, base_currency),
            total_charges=Money(ar_ledger_model.total_charges, base_currency),
            closing_balance_in_base_currency=Money(
                ar_ledger_model.closing_balance_in_base_currency, base_currency
            ),
            payment_components=ar_ledger_model.payment_components,
            revenue_components=ar_ledger_model.revenue_components,
            non_revenue_components=ar_ledger_model.non_revenue_components,
            base_currency=base_currency,
            hotel_id=ar_ledger_model.hotel_id,
            deleted=ar_ledger_model.deleted,
        )
