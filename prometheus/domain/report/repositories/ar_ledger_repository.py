# coding=utf-8
"""
AR ledger repository
"""
from sqlalchemy import func

from object_registry import register_instance
from prometheus.domain.report.aggregates.ar_ledger_aggregate import ARLedgerAggregate
from prometheus.domain.report.models import ARLedgerReportModel
from prometheus.domain.report.repositories.adaptors.ar_ledger_adaptor import (
    ARLedgerDBAdapter,
)
from prometheus.infrastructure.database.base_repository import BaseRepository


@register_instance()
class ARLedgerRepository(BaseRepository):
    """
    AR Ledger repository
    """

    def save(self, ar_ledger_aggregate):
        """
        Saves the ar_ledger_aggregate in DB

        :param ar_ledger_aggregate:
        """
        ar_ledger_aggregate.check_invariance()
        ar_ledger_model = ARLedgerDBAdapter.to_db_model(ar_ledger_aggregate.ar_ledger)

        self._save(ar_ledger_model)
        self.flush_session()

    def to_aggregate(self, ar_ledger_model):
        ar_ledger = (
            ARLedgerDBAdapter.to_entity(ar_ledger_model) if ar_ledger_model else None
        )
        ar_ledger_aggregate = ARLedgerAggregate(ar_ledger=ar_ledger)
        return ar_ledger_aggregate

    def load_for_date_range(self, start_date, end_date, hotel_id):
        if not start_date and end_date:
            return []
        q = self.query(ARLedgerReportModel)
        q = q.filter(func.Date(ARLedgerReportModel.report_date) >= start_date)
        q = q.filter(func.Date(ARLedgerReportModel.report_date) <= end_date)
        if hotel_id:
            q = q.filter(ARLedgerReportModel.hotel_id == hotel_id)
        ar_ledger_models = q.all()
        return [
            self.to_aggregate(ar_ledger_model=ar_ledger_model)
            for ar_ledger_model in ar_ledger_models
        ]
