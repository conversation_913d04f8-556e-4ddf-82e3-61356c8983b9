# coding=utf-8
"""
Deposit ledger factory
"""
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from prometheus.domain.report.aggregates.deposit_ledger_aggregate_v2 import (
    DepositLedgerAggregateV2,
)
from prometheus.domain.report.entities.deposit_ledger_V2 import DepositLedgerV2


class DepositLedgerFactoryV2:
    @classmethod
    def create_new_deposit_ledger(
        cls,
        opening_balance_in_base_currency,
        closing_balance_in_base_currency,
        total_payments,
        total_charges,
        deposit_transferred_at_checkin,
        hotel_id,
        payment_components,
        revenue_components,
        non_revenue_components,
        business_date=dateutils.current_datetime(),
        base_currency=CurrencyType.INR,
    ):
        deposit_ledger = DepositLedgerV2(
            business_date=business_date,
            opening_balance_in_base_currency=opening_balance_in_base_currency,
            closing_balance_in_base_currency=closing_balance_in_base_currency,
            total_payments=total_payments,
            total_charges=total_charges,
            deposit_transferred_at_checkin=deposit_transferred_at_checkin,
            hotel_id=hotel_id,
            base_currency=base_currency,
            payment_components=payment_components,
            revenue_components=revenue_components,
            non_revenue_components=non_revenue_components,
        )
        return DepositLedgerAggregateV2(deposit_ledger=deposit_ledger)
