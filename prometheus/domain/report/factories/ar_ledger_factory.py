# coding=utf-8
"""
AR ledger factory
"""
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from prometheus.domain.report.aggregates.ar_ledger_aggregate import ARLedgerAggregate
from prometheus.domain.report.entities.ar_ledger import ARLedger


class ARLedgerFactory:
    @classmethod
    def create_new_ar_ledger(
        cls,
        opening_balance_in_base_currency,
        closing_balance_in_base_currency,
        total_payments,
        total_charges,
        hotel_id,
        payment_components,
        revenue_components,
        non_revenue_components,
        report_date=dateutils.current_datetime(),
        base_currency=CurrencyType.INR,
    ):
        ar_ledger = ARLedger(
            report_date=report_date,
            opening_balance_in_base_currency=opening_balance_in_base_currency,
            closing_balance_in_base_currency=closing_balance_in_base_currency,
            total_payments=total_payments,
            total_charges=total_charges,
            payment_components=payment_components,
            revenue_components=revenue_components,
            non_revenue_components=non_revenue_components,
            hotel_id=hotel_id,
            base_currency=base_currency,
        )
        return ARLedgerAggregate(ar_ledger=ar_ledger)
