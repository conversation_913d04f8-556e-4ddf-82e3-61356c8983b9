import logging

from flask.cli import with_appcontext
from sentry_sdk.integrations.serverless import serverless_function

from prometheus.common.decorators import consumer_middleware
from prometheus.domain.report.services.erp_service import ERPService
from prometheus.infrastructure.consumers.base_consumer import BaseRMQConsumer
from prometheus.infrastructure.consumers.consumer_config import (
    InterfaceExchangeConsumerConfig,
)

logger = logging.getLogger(__name__)


class ERPServiceConsumer(BaseRMQConsumer):
    def __init__(
        self,
        hotel_repo,
        sku_category_repo,
        bill_repo,
        booking_repo,
        invoice_repo,
        credit_note_repo,
        tenant_id,
    ):
        super().__init__(InterfaceExchangeConsumerConfig(tenant_id))
        self.hotel_repo = hotel_repo
        self.sku_category_repo = sku_category_repo
        self.bill_repo = bill_repo
        self.booking_repo = booking_repo
        self.invoice_repo = invoice_repo
        self.credit_note_repo = credit_note_repo
        logger.info(
            f"Listening to RMQ on host: {self.connection} from queue: {self.queue}"
        )

    @serverless_function
    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        logger.info(f"Processing erp event {body}")
        try:
            payload = body.get('body')
            logger.info(f"Received erp crs Request with body: {payload}")
            if body.get('event_type') == 'crs-erp-data':
                hotel_id = (payload.get('hotel_id'),)
                business_date = payload.get('business_date')
                module = payload.get('module_name')
                is_erp_data_on_post_checkout = payload.get(
                    'is_erp_data_on_post_checkout'
                )
                if module == 'crs':
                    erp_service = ERPService(
                        self.bill_repo,
                        self.booking_repo,
                        self.hotel_repo,
                        self.sku_category_repo,
                        self.invoice_repo,
                        self.credit_note_repo,
                    )
                    logger.info("Preparing erp data for crs")
                    if is_erp_data_on_post_checkout:
                        erp_service.prepare_and_publish_erp_details_post_checkout(
                            hotel_id,
                            business_date,
                            body.get('event_id'),
                            body.get('batch_id'),
                        )
                    else:
                        erp_service.prepare_and_publish_erp_details_pre_checkout(
                            hotel_id,
                            business_date,
                            body.get('event_id'),
                            body.get('batch_id'),
                        )
        except Exception as exc:
            logger.exception(f"Exception occurred while processing erp event: {body}")
            raise

        logger.info("erp message process complete. Message acknowledged")
        message.ack()
