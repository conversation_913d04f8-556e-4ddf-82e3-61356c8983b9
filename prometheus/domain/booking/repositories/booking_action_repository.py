import json
import logging
from json import J<PERSON><PERSON>ncoder

import jsonpickle

from object_registry import register_instance
from prometheus.domain.booking.aggregates.booking_action_aggregate import (
    BookingActionAggregate,
)
from prometheus.domain.booking.entities.booking_action import BookingAction
from prometheus.domain.booking.models import BookingActionModel
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.booking_constants import (
    ActionStatus,
    BookingActions,
    BookingStatus,
)
from ths_common.value_objects import (
    ActionReversalSideEffects,
    BookingActionSideEffects,
    PriceData,
)

logger = logging.getLogger(__name__)


class BookingActionPayloadEncoder(JSONEncoder):
    def default(self, obj):
        if isinstance(obj, PriceData):
            return obj.to_json()
        return JSONEncoder.default(self, obj)


@register_instance()
class BookingActionRepository(BaseRepository):
    """
    Booking repository
    """

    def from_aggregate(self, aggregate=None):
        booking_action = aggregate.booking_action
        payload = booking_action.payload
        payload = json.loads(jsonpickle.encode(payload, unpicklable=True))
        side_effects = booking_action.side_effects.to_json()
        reversal_side_effects = booking_action.reversal_side_effects.to_json()
        is_reversible = booking_action.is_reversible
        previous_state = (
            booking_action.previous_state.value
            if booking_action.previous_state
            else None
        )
        booking_action_model = BookingActionModel(
            action_id=booking_action.action_id,
            action_type=booking_action.action_type.value,
            created_by=booking_action.created_by,
            booking_id=booking_action.booking_id,
            status=booking_action.status.value,
            side_effects=side_effects,
            reversal_side_effects=reversal_side_effects,
            payload=payload,
            deleted=booking_action.deleted,
            version=booking_action.version,
            is_reversible=is_reversible,
            previous_state=previous_state,
            business_date=booking_action.business_date,
        )
        return booking_action_model

    def to_aggregate(self, **kwargs):
        booking_action_model = kwargs.get('booking_action_model')
        payload = booking_action_model.payload
        payload = jsonpickle.decode(json.dumps(payload), keys=True, classes=[PriceData])
        side_effects = BookingActionSideEffects.from_json(
            booking_action_model.side_effects
        )
        reversal_side_effects = ActionReversalSideEffects.from_json(
            booking_action_model.reversal_side_effects
        )
        booking_action = BookingAction(
            action_id=booking_action_model.action_id,
            action_type=BookingActions(booking_action_model.action_type),
            booking_id=booking_action_model.booking_id,
            payload=payload,
            created_by=booking_action_model.created_by,
            status=ActionStatus(booking_action_model.status),
            side_effects=side_effects,
            reversal_side_effects=reversal_side_effects,
            deleted=booking_action_model.deleted,
            version=booking_action_model.version,
            previous_state=BookingStatus(booking_action_model.previous_state)
            if booking_action_model.previous_state
            else None,
            dirty=False,
            new=False,
            created_at=booking_action_model.created_at,
            business_date=booking_action_model.business_date,
        )
        return BookingActionAggregate(booking_action=booking_action)

    def save(self, booking_action_aggregate):
        booking_action_model = self.from_aggregate(aggregate=booking_action_aggregate)
        self._save(booking_action_model)
        self.flush_session()

    def update(self, booking_action_aggregate):
        booking_action_aggregate.booking_action.version += 1
        booking_action_model = self.from_aggregate(aggregate=booking_action_aggregate)
        self._update(booking_action_model)
        self.flush_session()

    def load(self, action_id):
        booking_action_model = self.get(BookingActionModel, action_id=action_id)
        booking_action_aggregate = self.to_aggregate(
            booking_action_model=booking_action_model
        )
        return booking_action_aggregate

    def load_all_actions_for_booking(self, booking_id):
        booking_action_models = self.filter(
            BookingActionModel, BookingActionModel.booking_id == booking_id
        ).order_by(BookingActionModel.created_at.desc())
        return [
            self.to_aggregate(booking_action_model=model)
            for model in booking_action_models
        ]

    def load_latest_reversible_action(self, booking_id):
        booking_action_aggregate = None
        booking_action_model = (
            self.filter(BookingActionModel, BookingActionModel.booking_id == booking_id)
            .filter(BookingActionModel.is_reversible)
            .order_by(BookingActionModel.created_at.desc())
            .first()
        )
        if booking_action_model:
            booking_action_aggregate = self.to_aggregate(
                booking_action_model=booking_action_model
            )
        return booking_action_aggregate

    def is_latest_reversible_action(self, booking_id, action_id):
        latest_reversible_action_id = (
            self.filter(
                BookingActionModel.action_id,
                BookingActionModel.booking_id == booking_id,
            )
            .filter(BookingActionModel.is_reversible)
            .order_by(BookingActionModel.created_at.desc())
            .first()
        )
        return (
            bool(latest_reversible_action_id)
            and latest_reversible_action_id[0] == action_id
        )
