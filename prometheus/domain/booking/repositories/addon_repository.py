import logging

import sqlalchemy
from treebo_commons.money import Money

from object_registry import register_instance
from prometheus import crs_context
from prometheus.domain.booking.aggregates.addon_aggregate import AddonAggregate
from prometheus.domain.booking.entities.addon import Addon as AddonEntity
from prometheus.domain.booking.errors import BookingErrors
from prometheus.domain.booking.exceptions import AddonNotFoundError
from prometheus.domain.booking.models import AddonModel
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.billing_constants import ChargeBillToTypes, ChargeTypes
from ths_common.constants.booking_constants import (
    AddonRelativeDate,
    AddonStatus,
    ExpenseAddedBy,
)
from ths_common.exceptions import (
    AggregateNotFound,
    OutdatedVersion,
    ValidationException,
)

logger = logging.getLogger(__name__)


@register_instance()
class AddonRepository(BaseRepository):
    def to_aggregate(self, addon_model):
        base_currency = crs_context.hotel_context.base_currency
        pretax_price = (
            Money(addon_model.pretax_price, base_currency)
            if addon_model.pretax_price
            else None
        )
        posttax_price = (
            Money(addon_model.posttax_price, base_currency)
            if addon_model.posttax_price
            else None
        )
        addon_entity = AddonEntity(
            addon_id=addon_model.addon_id,
            booking_id=addon_model.booking_id,
            room_stay_id=addon_model.room_stay_id,
            expense_item_id=addon_model.expense_item_id,
            sku_id=addon_model.sku_id,
            name=addon_model.name,
            pretax_price=pretax_price,
            posttax_price=posttax_price,
            quantity=addon_model.quantity,
            charge_checkin=addon_model.charge_checkin,
            charge_checkout=addon_model.charge_checkout,
            charge_other_days=addon_model.charge_other_days,
            start_relative=AddonRelativeDate(addon_model.start_relative)
            if addon_model.start_relative
            else None,
            end_relative=AddonRelativeDate(addon_model.end_relative)
            if addon_model.end_relative
            else None,
            start_date=addon_model.start_date,
            end_date=addon_model.end_date,
            linked=addon_model.linked,
            charge_type=ChargeTypes(addon_model.charge_type)
            if addon_model.charge_type
            else None,
            bill_to_type=ChargeBillToTypes(addon_model.bill_to_type)
            if addon_model.bill_to_type
            else None,
            added_by=ExpenseAddedBy(addon_model.added_by),
            status=AddonStatus(addon_model.status),
            deleted=addon_model.deleted,
            version=addon_model.version,
            dirty=False,
            new=False,
            is_rate_plan_addon=addon_model.rate_plan_addon,
        )
        # addon_entity.set_addon_dates_v1(addon_entity.charge)

        return AddonAggregate(addon_entity, expenses=addon_model.expenses)

    def from_aggregate(self, aggregate=None):
        addon = aggregate.addon
        pretax_price = addon.pretax_price.amount if addon.pretax_price else None
        posttax_price = addon.posttax_price.amount if addon.posttax_price else None
        addon_model = AddonModel(
            addon_id=addon.addon_id,
            booking_id=addon.booking_id,
            room_stay_id=addon.room_stay_id,
            expense_item_id=addon.expense_item_id,
            sku_id=addon.sku_id,
            name=addon.name,
            pretax_price=pretax_price,
            posttax_price=posttax_price,
            quantity=addon.quantity,
            charge_checkin=addon.charge_checkin,
            charge_checkout=addon.charge_checkout,
            charge_other_days=addon.charge_other_days,
            start_relative=AddonRelativeDate(addon.start_relative).value
            if addon.start_relative
            else None,
            end_relative=AddonRelativeDate(addon.end_relative).value
            if addon.end_relative
            else None,
            start_date=addon.start_date,
            end_date=addon.end_date,
            charge_type=addon.charge_type.value if addon.charge_type else None,
            bill_to_type=addon.bill_to_type.value if addon.bill_to_type else None,
            added_by=addon.added_by.value,
            status=addon.status.value,
            deleted=addon.deleted,
            expenses=aggregate.expenses,
            version=addon.version,
            linked=addon.linked,
            rate_plan_addon=addon.is_rate_plan_addon,
        )

        return addon_model

    def save(self, addon_aggregate):
        addon_model = self.from_aggregate(addon_aggregate)
        self._save(addon_model)
        self.flush_session()
        self.mark_clean(addon_aggregate)

    def save_all(self, addon_aggregates):
        addon_models = [
            self.from_aggregate(addon_aggregate) for addon_aggregate in addon_aggregates
        ]
        self._save_all(addon_models)
        self.flush_session()
        for addon_aggregate in addon_aggregates:
            self.mark_clean(addon_aggregate)

    def get_addons_for_booking(
        self, booking_id, include_linked=False, include_only_rate_plan_addon=None
    ):
        query = (
            self.session()
            .query(AddonModel)
            .filter(AddonModel.booking_id == booking_id, AddonModel.deleted == False)
        )
        if not include_linked:
            query = query.filter(AddonModel.linked == False)
        if include_only_rate_plan_addon is not None:
            query = query.filter(
                AddonModel.rate_plan_addon == include_only_rate_plan_addon
            )
        addon_models = query.all()
        addon_aggregates = [
            self.to_aggregate(addon_model) for addon_model in addon_models
        ]
        return addon_aggregates

    def get_addons_for_room_stay(self, booking_id, room_stay_id, linked=None):
        query = (
            self.session()
            .query(AddonModel)
            .filter(
                AddonModel.booking_id == booking_id,
                AddonModel.room_stay_id == room_stay_id,
                AddonModel.deleted == False,
            )
        )
        if linked is not None:
            query = query.filter(AddonModel.linked == linked)

        addon_models = query.all()

        addon_aggregates = [
            self.to_aggregate(addon_model) for addon_model in addon_models
        ]

        return addon_aggregates

    def get_addons_for_room_stays(self, booking_id, room_stay_ids):
        addon_models = (
            self.session()
            .query(AddonModel)
            .filter(
                AddonModel.booking_id == booking_id,
                AddonModel.room_stay_id.in_(room_stay_ids),
                AddonModel.deleted == False,
            )
            .all()
        )

        addon_aggregates = [
            self.to_aggregate(addon_model) for addon_model in addon_models
        ]

        return addon_aggregates

    def load(self, addon_id, booking_id=None, include_deleted=True):
        try:
            q = self.filter(AddonModel, AddonModel.addon_id == addon_id)
            if not include_deleted:
                q = q.filter(AddonModel.deleted == False)
            addon_model = q.one()
        except sqlalchemy.orm.exc.NoResultFound:
            raise AddonNotFoundError()
        if booking_id is not None and addon_model.booking_id != booking_id:
            raise ValidationException(BookingErrors.ADDON_DOES_NOT_BELONG_TO_BOOKING)

        return self.to_aggregate(addon_model)

    def load_for_update(
        self, addon_id, booking_id=None, version=None, include_deleted=True
    ):
        if include_deleted:
            addon_model = self.get_for_update(AddonModel, addon_id=addon_id)
        else:
            addon_model = self.get_for_update(
                AddonModel, addon_id=addon_id, deleted=False
            )
        if not addon_model:
            raise AggregateNotFound("AddOn", addon_id)

        if booking_id is not None and addon_model.booking_id != booking_id:
            raise ValidationException(BookingErrors.ADDON_DOES_NOT_BELONG_TO_BOOKING)

        if version is not None and version != addon_model.version:
            raise OutdatedVersion(
                "AddOn", requested_version=version, current_version=addon_model.version
            )

        return self.to_aggregate(addon_model)

    def mark_clean(self, addon_aggregate):
        addon_aggregate.addon.mark_clean()

    def update(self, addon_aggregate):
        if not addon_aggregate.addon.is_dirty():
            return
        addon_aggregate.increment_version()
        addon_model = self.from_aggregate(addon_aggregate)
        if addon_aggregate.addon.is_new():
            self._bulk_insert_mappings(AddonModel, [addon_model.mapping_dict()])
        else:
            self._bulk_update_mappings(AddonModel, [addon_model.mapping_dict()])
        self.flush_session()
        self.mark_clean(addon_aggregate)

    def update_all(self, addon_aggregates):
        created_addon_models = []
        updated_addon_models = []
        for addon_aggregate in addon_aggregates:
            if not addon_aggregate.addon.is_dirty():
                continue
            addon_aggregate.increment_version()
            addon_model = self.from_aggregate(addon_aggregate)
            if addon_aggregate.addon.is_new():
                created_addon_models.append(addon_model)
            else:
                updated_addon_models.append(addon_model)
        self._bulk_update_mappings(
            AddonModel, [addon.mapping_dict() for addon in updated_addon_models]
        )
        self._bulk_insert_mappings(
            AddonModel, [addon.mapping_dict() for addon in created_addon_models]
        )
        self.flush_session()
        for addon_aggregate in addon_aggregates:
            self.mark_clean(addon_aggregate)

    def load_bulk_for_update(
        self,
        charge_checkin=None,
        charge_checkout=None,
        charge_other_days=None,
        offset=None,
        limit=None,
    ):
        q = self.filter(AddonModel, for_update=True)
        if charge_checkin is not None:
            q = q.filter(AddonModel.charge_checkin == charge_checkin)
        if charge_checkout is not None:
            q = q.filter(AddonModel.charge_checkout == charge_checkout)
        if charge_other_days is not None:
            q = q.filter(AddonModel.charge_other_days == charge_other_days)

        q = q.filter(AddonModel.deleted == False)
        q = q.filter(AddonModel.added_by != None)
        q = q.order_by(AddonModel.created_at)

        if limit is not None and offset is not None:
            q = q.limit(limit).offset(offset)

        addon_models = q.all()

        addons = [self.to_aggregate(addon_model) for addon_model in addon_models]

        return addons

    def load_linked_addons(self, booking_ids):
        addon_models = (
            self.session()
            .query(AddonModel)
            .filter(
                AddonModel.booking_id.in_(booking_ids),
                AddonModel.deleted == False,
                AddonModel.linked == True,
            )
            .all()
        )
        addon_aggregates = [
            self.to_aggregate(addon_model) for addon_model in addon_models
        ]
        return addon_aggregates
