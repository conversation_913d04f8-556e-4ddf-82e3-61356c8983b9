# coding=utf-8
from treebo_commons.utils import dateutils

from ths_common.base_entity import EntityChangeTracker
from ths_common.constants.booking_constants import (
    AttachmentFileType,
    AttachmentGroup,
    AttachmentStatus,
)


class Attachment(EntityChangeTracker):
    def __init__(
        self,
        attachment_id,
        booking_id,
        original_url,
        display_name,
        file_type: AttachmentFileType,
        attachment_group: AttachmentGroup,
        source,
        uploaded_by,
        status=None,
        rejection_reason=None,
        signed_url=None,
        deleted=False,
    ):
        super().__init__()
        self.attachment_id = attachment_id
        self.booking_id = booking_id
        self._signed_url = signed_url
        self.original_url = original_url
        self.display_name = display_name
        self.file_type = file_type
        self.attachment_group = attachment_group
        self.source = source
        self.uploaded_by = uploaded_by
        self.status = status if status else AttachmentStatus.PENDING_VERIFICATION
        self.rejection_reason = rejection_reason
        self.deleted = deleted

    @property
    def signed_url(self):
        return self._signed_url

    @signed_url.setter
    def signed_url(self, value):
        self._signed_url = value
