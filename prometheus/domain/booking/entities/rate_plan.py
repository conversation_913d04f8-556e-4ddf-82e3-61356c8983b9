# coding=utf-8
from math import ceil

from treebo_commons.utils import dateutils

from prometheus.domain.booking.dtos.rate_plan_dtos import (
    RatePlanNonRoomNightInclusion,
    RatePlanPackage,
    RatePlanPolicies,
    RatePlanRestrictions,
)
from prometheus.domain.booking.errors import BookingErrors
from prometheus.domain.booking.exceptions import RatePlanValidationError
from ths_common.base_entity import EntityChangeTracker


class RatePlan(EntityChangeTracker):
    def __init__(
        self,
        rate_plan_reference_id,
        rate_plan_id,
        rate_plan_code,
        name,
        package: RatePlanPackage = None,
        policies: RatePlanPolicies = None,
        restrictions: RatePlanRestrictions = None,
        non_room_night_inclusions: [RatePlanNonRoomNightInclusion] = None,
        is_flexi=False,
        print_rate=True,
        suppress_rate=False,
        commission_details=None,
        deleted=False,
        new=True,
        dirty=True,
    ):
        super().__init__(new=new, dirty=dirty)
        self.rate_plan_reference_id = rate_plan_reference_id
        self.rate_plan_id = rate_plan_id
        self.rate_plan_code = rate_plan_code
        self.name = name
        self.package = package
        self.policies = policies
        self.restrictions = restrictions
        self.non_room_night_inclusions = non_room_night_inclusions
        self._deleted = deleted
        self.is_flexi = is_flexi
        self.print_rate = print_rate
        self.suppress_rate = suppress_rate
        self.commission_details = commission_details

    @property
    def is_active(self):
        return not self.deleted

    @property
    def deleted(self):
        return self._deleted

    def delete(self):
        self._deleted = True
        self.dirty = True

    def get_cancellation_details(self, checkin_date, cancellation_datetime):
        cancellation_range_lower_bound = [
            cp.cancellation_duration_before_checkin_start
            for cp in self.policies.cancellation_policies
        ]
        cancellation_type = None
        cancellation_charge_value = None
        for cancellation_policy in self.policies.cancellation_policies:
            if (
                cancellation_policy.cancellation_duration_before_checkin_start is None
                and cancellation_policy.cancellation_duration_before_checkin_end
            ):
                cancellation_charge_value = 100
            elif (
                cancellation_policy.cancellation_duration_before_checkin_end is None
                and cancellation_policy.cancellation_duration_before_checkin_start
            ):
                cancellation_charge_value = 0
            elif (
                cancellation_policy.cancellation_duration_before_checkin_start is None
                and cancellation_policy.cancellation_duration_before_checkin_end is None
            ):
                cancellation_type = cancellation_policy.cancellation_charge_unit
                cancellation_charge_value = 100
            elif dateutils.to_date(cancellation_datetime) < dateutils.to_date(
                checkin_date
            ):
                time_delta_to_checkin = checkin_date - cancellation_datetime
                ceil_hours_left_to_check_in = ceil(
                    time_delta_to_checkin.total_seconds() / 3600
                )
                if ceil_hours_left_to_check_in in range(
                    cancellation_policy.cancellation_duration_before_checkin_start,
                    cancellation_policy.cancellation_duration_before_checkin_end + 1,
                ):
                    cancellation_type, cancellation_charge_value = (
                        cancellation_policy.cancellation_charge_unit,
                        cancellation_policy.cancellation_charge_value,
                    )
                    break
        """
         - In case cancellation information is given after check_in, we will consider the policy closet to the check_in
         date.
         - In case cancellation information is given at a date beyond the cancellation policy range, cancellation charge
         must be zero.
        """
        if not cancellation_type and not cancellation_charge_value:
            if dateutils.to_date(cancellation_datetime) < dateutils.to_date(
                checkin_date
            ):
                cancellation_charge_value = 0
            else:
                for cancellation_policy in self.policies.cancellation_policies:
                    if (
                        cancellation_policy.cancellation_duration_before_checkin_start
                        == min(cancellation_range_lower_bound)
                    ):
                        cancellation_type, cancellation_charge_value = (
                            cancellation_policy.cancellation_charge_unit,
                            cancellation_policy.cancellation_charge_value,
                        )
                        break
        return cancellation_type, cancellation_charge_value

    def does_restrict(self, length_of_stay, creation_window, strict=True):
        if not self.restrictions:
            return False
        if (
            self.restrictions.minimum_los is not None
            and length_of_stay < self.restrictions.minimum_los
        ):
            if strict:
                raise RatePlanValidationError(
                    error=BookingErrors.ROOM_STAY_LENGTH_LESS_THAN_MINIMUM_LOS
                )
            return True
        if (
            self.restrictions.minimum_abw is not None
            and creation_window < self.restrictions.minimum_abw
        ):
            if strict:
                raise RatePlanValidationError(
                    error=BookingErrors.ROOM_STAY_BOOKING_WINDOW_LESS_THAN_MINIMUM_ABW
                )
            return True
        if (
            self.restrictions.maximum_abw is not None
            and creation_window > self.restrictions.maximum_abw
        ):
            if strict:
                raise RatePlanValidationError(
                    error=BookingErrors.ROOM_STAY_BOOKING_WINDOW_GREATER_THAN_MAXIMUM_ABW
                )
            return True
        if (
            self.restrictions.maximum_los is not None
            and length_of_stay > self.restrictions.maximum_los
        ):
            if strict:
                raise RatePlanValidationError(
                    error=BookingErrors.ROOM_STAY_LENGTH_MORE_THAN_MAXIMUM_LOS
                )
            return True
        return False

    def allow_child(self):
        return self.policies.child_policy.child_allowed
