from ths_common.base_entity import EntityChangeTracker


class ERegCard(EntityChangeTracker):
    def __init__(
        self,
        e_reg_card_id,
        booking_id,
        room_stay_id,
        ups_user_id,
        travel_details,
        id_proof,
        employment_details,
        status,
        room_number=None,
        is_primary=False,
        dirty=True,
        new=True,
        deleted=False,
    ):
        super().__init__(dirty=dirty, new=new)
        self.booking_id = booking_id
        self.e_reg_card_id = e_reg_card_id
        self.room_stay_id = room_stay_id
        self.ups_user_id = ups_user_id
        self.is_primary = is_primary
        self.room_number = room_number
        # Travel details value object
        self.travel_details = travel_details
        # IDProof value object
        self.id_proof = id_proof
        # Employment details value object
        self.employment_details = employment_details
        self.status = status
        self.deleted = deleted
