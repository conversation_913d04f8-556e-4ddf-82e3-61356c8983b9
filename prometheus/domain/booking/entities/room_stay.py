import datetime
import logging
from typing import List

from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import date_range

from prometheus import crs_context
from prometheus.domain.booking.domain_events.disallow_charge_addition import (
    DisallowChargeAdditionChangedEvent,
)
from prometheus.domain.booking.domain_events.guest_lifecycle_action_performed import (
    GuestLifecycleEvent,
)
from prometheus.domain.booking.domain_events.guest_stay_noshow_reversed import (
    GuestStayNoShowReversedEvent,
)
from prometheus.domain.booking.domain_events.rate_plan_changed import (
    RoomPlanChangedEvent,
)
from prometheus.domain.booking.domain_events.room_allocation import RoomFreedEvent
from prometheus.domain.booking.domain_events.room_changed import RoomChangedEvent
from prometheus.domain.booking.domain_events.room_stay_cancel_reversed import (
    RoomStayCancelReversedEvent,
)
from prometheus.domain.booking.dtos.disallow_charge_addition_data import DCAEventData
from prometheus.domain.booking.dtos.guest_checkin_data import GuestCheckinData
from prometheus.domain.booking.dtos.guest_checkout_data import GuestCheckoutRequestData
from prometheus.domain.booking.dtos.guest_stay_data import GuestStayData
from prometheus.domain.booking.dtos.guest_stay_event_data import GuestStayEventData
from prometheus.domain.booking.dtos.noshow_event_data import NoShowEventData
from prometheus.domain.booking.dtos.room_event_data import RoomEventData
from prometheus.domain.booking.dtos.room_night_commission_dto import (
    RoomNightTACommissionDto,
)
from prometheus.domain.booking.dtos.room_stay_event_data import RoomStayEventData
from prometheus.domain.booking.entities.guest_stay import GuestStay
from prometheus.domain.booking.entities.room_allocation import RoomAllocation
from prometheus.domain.booking.entities.room_night_commission import (
    RoomNightTACommission,
)
from prometheus.domain.booking.errors import BookingErrors
from prometheus.domain.booking.exceptions import (
    BookingUpdateError,
    InvalidActionError,
    InvalidStateError,
    InvalidStayDatesError,
    PriceError,
)
from prometheus.domain.booking.state_machines.room_stay_state_machine import (
    RoomStayStateMachine,
)
from prometheus.domain.domain_events.domain_event_registry import register_event
from ths_common.base_entity import EntityChangeTracker
from ths_common.constants.booking_constants import (
    AgeGroup,
    BookingActions,
    BookingStatus,
    RoomStayType,
    TACommissionStatus,
)
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import (
    InternalServerException,
    InvalidOperationError,
    ResourceNotFound,
    ValidationException,
)
from ths_common.value_objects import (
    DateRange,
    GuestStaySideEffect,
    Occupancy,
    RoomRatePlan,
    RoomRent,
    RoomStayConfig,
    RoomStaySideEffect,
)

logger = logging.getLogger(__name__)


class RoomStay(EntityChangeTracker):
    CHARGE_COMPONENT_NAME = "RoomRent"
    states = BookingStatus.valid_room_stay_status()

    __slots__ = (
        'created_at',
        'modified_at',
        'room_stay_id',
        'room_type_id',
        'type',
        'status',
        'checkin_date',
        'checkout_date',
        'disallow_charge_addition',
        'actual_checkin_date',
        'actual_checkout_date',
        '_room_allocation',
        '_room_allocation_history',
        '_guest_stays',
        'charge_id_map',
        'room_id',
        'room_number',
        'deleted',
        'charge_ids',
        'room_rents',
        'is_overflow',
        'allowed_actions',
        'state_machine',
        'room_rate_plans',
        'cancellation_date',
        'cancellation_date_time',
        'room_rate_plans_ids',
        'checkin_business_date',
        'checkout_business_date',
        'actual_checkout_business_date',
        'date_wise_occupancies',
        '_room_night_ta_commissions',
        'room_night_ta_commission_map',
        'extra_information',
        'actual_checkin_calendar_date',
        'actual_checkout_calendar_date',
        'discounts',
    )

    def __init__(
        self,
        room_stay_id,
        room_type_id,
        type,
        status,
        checkin_date,
        checkout_date,
        guest_stays,
        charge_id_map,
        disallow_charge_addition=False,
        actual_checkin_date=None,
        actual_checkout_date=None,
        room_allocations=None,
        deleted=False,
        created_at=None,
        modified_at=None,
        room_rents: List[RoomRent] = None,
        room_rate_plans: [RoomRatePlan] = None,
        cancellation_date=None,
        cancellation_date_time=None,
        checkin_business_date=None,
        checkout_business_date=None,
        actual_checkout_business_date=None,
        room_night_ta_commissions: List[RoomNightTACommission] = None,
        extra_information=None,
        discounts=None,
        dirty=True,
        new=True,
        actual_checkin_calendar_date=None,
        actual_checkout_calendar_date=None,
    ):
        super().__init__(dirty=dirty, new=new)

        if type is not None and type not in RoomStayType:
            raise ValueError("RoomStay::Invalid room stay type {}".format(type))
        if status.value not in RoomStay.states:
            raise InvalidStateError(
                description="Invalid state: {0} for Room Stay".format(status.value)
            )

        self.created_at = created_at if created_at else dateutils.current_datetime()
        self.modified_at = modified_at if modified_at else dateutils.current_datetime()
        self.room_stay_id = room_stay_id
        self.room_type_id = room_type_id
        self.type = type
        self.status = status
        self.checkin_date = checkin_date
        self.checkout_date = checkout_date
        self.disallow_charge_addition = disallow_charge_addition
        self.actual_checkin_date = actual_checkin_date
        self.actual_checkout_date = actual_checkout_date
        # Validate that the below list has at max single element
        active_room_allocations = (
            [ra for ra in room_allocations if ra.is_current]
            if room_allocations
            else None
        )
        if active_room_allocations and len(active_room_allocations) > 1:
            raise InternalServerException(
                description="RoomStay in invalid state. Got more than 1 active room_allocations"
            )
        self._room_allocation = (
            active_room_allocations[0] if active_room_allocations else None
        )
        if self._room_allocation and self._room_allocation.deleted:
            raise InternalServerException(
                description="RoomStay in invalid state. deleted room allocation is the current room allocation"
            )
        self._room_allocation_history = (
            [ra for ra in room_allocations if not ra.is_current]
            if room_allocations
            else list()
        )
        self._guest_stays = guest_stays if guest_stays else list()
        self.charge_id_map = charge_id_map if charge_id_map else dict()
        self.room_id = self._room_allocation.room_id if self._room_allocation else None
        self.room_number = (
            self._room_allocation.room_no if self._room_allocation else None
        )
        self.deleted = deleted
        self.charge_ids = list(self.charge_id_map.values())
        self.room_rents = room_rents
        if self.room_rents is None:
            self.room_rents = []

        self.state_machine = RoomStayStateMachine(
            self, RoomStay.states, self.status.value
        )
        # NOTE: This value will be populated from booking_aggregate
        self.allowed_actions = []
        self.is_overflow = False
        self.room_rate_plans = room_rate_plans
        self.cancellation_date = cancellation_date
        self.cancellation_date_time = cancellation_date_time
        self.room_rate_plans_ids = (
            set(rate_plan.rate_plan_id for rate_plan in room_rate_plans)
            if room_rate_plans
            else set()
        )
        self._room_night_ta_commissions = (
            room_night_ta_commissions if room_night_ta_commissions else []
        )
        self.room_night_ta_commission_map = {
            rn_ta_cn.ta_commission_id: rn_ta_cn
            for rn_ta_cn in self._room_night_ta_commissions
        }
        self.checkin_business_date = checkin_business_date
        if not self.checkin_business_date:
            self.checkin_business_date = dateutils.to_date(checkin_date)

        self.checkout_business_date = checkout_business_date
        if not checkout_business_date:
            self.checkout_business_date = dateutils.to_date(checkout_date)

        self.actual_checkout_business_date = actual_checkout_business_date
        self.date_wise_occupancies = self._get_date_wise_occupancies()
        self.extra_information = extra_information
        self.actual_checkin_calendar_date = actual_checkin_calendar_date
        self.actual_checkout_calendar_date = actual_checkout_calendar_date
        self.discounts = discounts if discounts else []

    def is_active(self):
        return not (self.deleted or self.is_cancelled() or self.is_noshow())

    def mark_overflow(self, overflow):
        self.is_overflow = overflow
        self.mark_dirty()

    def is_eligible_to_be_marked_overflow_on_date(self, stay_date):
        return (
            not self.is_overflow
            and self.is_active()
            and self.status == BookingStatus.RESERVED
            and self.checkin_business_date <= stay_date < self.checkout_business_date
        )

    @property
    def external_room_stay_id(self):
        if not self.extra_information:
            return None
        return self.extra_information.get("external_room_stay_id")

    def update_external_room_stay_id(self, external_room_stay_id):
        if not self.extra_information:
            self.extra_information = dict()
        self.extra_information["external_room_stay_id"] = external_room_stay_id
        self.mark_dirty()

    @property
    def room_allocation(self):
        return self._room_allocation

    @room_allocation.setter
    def room_allocation(self, room_allocation):
        self._room_allocation = room_allocation
        self.mark_dirty()

    @property
    def next_room_allocation_id(self):
        if self._room_allocation_history:
            return (
                max(ra.room_allocation_id for ra in self._room_allocation_history) + 1
            )
        else:
            return 1

    @property
    def room_allocation_history(self):
        return [
            room_allocation
            for room_allocation in self._room_allocation_history
            if not room_allocation.deleted
        ]

    @property
    def stay_start(self):
        hotel_context = crs_context.get_hotel_context()
        if not self.actual_checkin_date:
            return dateutils.to_date(self.checkin_date)
        else:
            return hotel_context.hotel_checkin_date(self.actual_checkin_date)

    @property
    def actual_stay_start_date(self):
        if not self.actual_checkin_date:
            return dateutils.to_date(self.checkin_date)
        else:
            return dateutils.to_date(self.actual_checkin_date)

    @property
    def actual_stay_end_date(self):
        if not self.actual_checkout_date:
            return dateutils.to_date(self.checkout_date)
        else:
            return dateutils.to_date(self.actual_checkout_date)

    @property
    def stay_end(self):
        hotel_context = crs_context.get_hotel_context()
        if not self.actual_checkout_date:
            checkout_date = dateutils.to_date(self.checkout_date)
            return (
                dateutils.add(checkout_date, 1)
                if checkout_date == self.stay_start
                else checkout_date
            )
        else:
            checkout_date = hotel_context.hotel_checkout_date(self.actual_checkout_date)
            return (
                dateutils.add(checkout_date, 1)
                if checkout_date == self.stay_start
                else checkout_date
            )

    @property
    def guest_stays(self):
        return [g for g in self._guest_stays if not g.deleted]

    @property
    def room_night_ta_commissions(self):
        return [
            rn_ta_cm
            for rn_ta_cm in self._room_night_ta_commissions
            if not rn_ta_cm.deleted
        ]

    def get_all_active_room_night_ta_commissions(self, include_allowance=False):
        return [
            rn_ta_cm
            for rn_ta_cm in self._room_night_ta_commissions
            if not rn_ta_cm.deleted
            and rn_ta_cm.is_active
            and (include_allowance or not rn_ta_cm.is_allowance)
        ]

    @room_night_ta_commissions.setter
    def room_night_ta_commissions(self, value):
        self._room_night_ta_commissions = value
        self.mark_dirty()

    def all_room_allocations(self, include_deleted=False):
        all_room_allocations = []
        if include_deleted:
            if self._room_allocation:
                all_room_allocations.append(self._room_allocation)
            if self._room_allocation_history:
                all_room_allocations.extend(self._room_allocation_history)
        else:
            if self._room_allocation and not self._room_allocation.deleted:
                all_room_allocations.append(self._room_allocation)
            if self.room_allocation_history:
                all_room_allocations.extend(self.room_allocation_history)
        return all_room_allocations

    @guest_stays.setter
    def guest_stays(self, value):
        self._guest_stays = value
        self.mark_dirty()

    @property
    def date_wise_charge_ids(self):
        date_wise_charge_ids = list()
        for date_, charge_id in self.charge_id_map.items():
            date_wise_charge_ids.append(dict(charge_date=date_, charge_id=charge_id))
        return date_wise_charge_ids

    def get_charge_for_date(self, date_):
        return self.charge_id_map.get(dateutils.date_to_ymd_str(date_))

    def contains_charge(self, charge_id):
        return charge_id in self.charge_ids

    def _get_date_wise_occupancies(self):
        date_wise_occupancies = dict()
        for d in date_range(
            dateutils.to_date(self.checkin_date), dateutils.to_date(self.checkout_date)
        ):
            active_guest_stays = self._get_active_guest_stays_on_date(d)

            adult = 0
            child = 0
            for gs in active_guest_stays:
                if gs.age_group == AgeGroup.ADULT:
                    adult += 1
                if gs.age_group == AgeGroup.CHILD:
                    child += 1
            date_wise_occupancies[d] = Occupancy(adult=adult, child=child)
        return date_wise_occupancies

    @property
    def max_occupancy(self):
        date_wise_occupancies = list()
        for d in date_range(
            dateutils.to_date(self.checkin_date), dateutils.to_date(self.checkout_date)
        ):
            active_guest_stays = self._get_active_guest_stays_on_date(d)
            adult = 0
            child = 0
            for gs in active_guest_stays:
                if gs.age_group == AgeGroup.ADULT:
                    adult += 1
                if gs.age_group == AgeGroup.CHILD:
                    child += 1
            total_occupancy = Occupancy(adult=adult, child=child).total()
            date_wise_occupancies.append(total_occupancy)
        return max(date_wise_occupancies)

    @property
    def room_stay_config(self):
        return RoomStayConfig(
            self.room_type_id,
            self.checkin_date,
            self.checkout_date,
            date_wise_occupancies=self.date_wise_occupancies,
        )

    @property
    def charge_component_name(self):
        return RoomStay.CHARGE_COMPONENT_NAME

    @property
    def length_of_stay(self):
        return (self.checkout_date - self.checkin_date).days + 1

    @property
    def creation_window(self):
        return (self.checkin_date - self.created_at).days + 1

    def is_future_room_stay(self):
        hotel_context = crs_context.get_hotel_context()
        return dateutils.to_date(self.checkin_date) > hotel_context.current_date()

    def has_current_business_date_checkin(self):
        hotel_context = crs_context.get_hotel_context()
        return dateutils.to_date(self.checkin_date) == hotel_context.current_date()

    def has_current_time_crossed_midnight_of_checkin_datetime(self):
        current_time = dateutils.current_datetime()
        return current_time > dateutils.datetime_at_midnight(
            dateutils.add(self.checkin_date, days=1)
        )

    def _get_active_guest_stays_on_date(self, date_):
        return [
            gs
            for gs in self.guest_stays
            if gs.status != BookingStatus.CANCELLED
            and not gs.deleted
            and date_
            in dateutils.date_range(
                dateutils.to_date(gs.checkin_date), dateutils.to_date(gs.checkout_date)
            )
        ]

    def adult_guest_stays(self):
        return [
            gs
            for gs in self.guest_stays
            if gs.age_group == AgeGroup.ADULT and gs.status != BookingStatus.CANCELLED
        ]

    def child_guest_stays(self):
        return [
            ch
            for ch in self.guest_stays
            if ch.age_group == AgeGroup.CHILD and ch.status != BookingStatus.CANCELLED
        ]

    def get_current_guest_allocations(self):
        return [gs.get_allocated_guest() for gs in self.active_guest_stays()]

    def get_current_checked_in_guest_allocations(self):
        return [
            gs.get_allocated_guest()
            for gs in self.active_guest_stays()
            if gs.is_checked_in()
        ]

    def get_all_guest_allocations(self):
        all_guest_allocations = []
        for gs in self.active_guest_stays():
            if not gs.is_checkin_performed():
                continue
            if gs.guest_allocation_history:
                all_guest_allocations.extend(gs.guest_allocation_history)
            if gs.guest_allocation:
                all_guest_allocations.append(gs.guest_allocation)

        return all_guest_allocations

    def get_guest_stay_wise_allocations(self, guest_id):
        all_guest_allocations = dict()
        for gs in self.active_guest_stays():
            guest_allocations = []
            if not gs.is_checkin_performed():
                continue
            if gs.guest_allocation_history:
                for ga in gs.guest_allocation_history:
                    if ga.guest_id == guest_id:
                        guest_allocations.append(ga)

            if gs.guest_allocation and gs.guest_allocation.guest_id == guest_id:
                guest_allocations.append(gs.guest_allocation)

            if guest_allocations:
                all_guest_allocations[gs] = guest_allocations
        return all_guest_allocations

    def active_guest_stays(self):
        return [
            gs
            for gs in self.guest_stays
            if gs.status not in (BookingStatus.CANCELLED, BookingStatus.NOSHOW)
        ]

    def all_guest_stays_except_cancelled(self):
        return [gs for gs in self.guest_stays if gs.status != BookingStatus.CANCELLED]

    def checked_in_guest_ids(self):
        return set(
            [
                gs.guest_id
                for gs in self.guest_stays
                if gs.status == BookingStatus.CHECKED_IN
            ]
        )

    def checked_in_guest_ids_on_date(self, date, age_group=None):
        age_groups = [age_group] if age_group else AgeGroup.all_options()
        return [
            gs.guest_id
            for gs in self.guest_stays
            if gs.guest_id
            and gs.age_group in age_groups
            and gs.stay_start <= date <= gs.stay_end
            and gs.status in (BookingStatus.CHECKED_IN, BookingStatus.CHECKED_OUT)
        ]

    def get_all_current_guest_ids(self, date, age_group=None):
        age_groups = [age_group] if age_group else AgeGroup.all_options()
        return [
            gs.guest_id
            for gs in self.guest_stays
            if gs.guest_id
            and gs.age_group in age_groups
            and gs.stay_start <= date <= gs.stay_end
            and gs.status
            in (
                BookingStatus.RESERVED,
                BookingStatus.CHECKED_IN,
                BookingStatus.CHECKED_OUT,
            )
        ]

    def delete(self):
        # TODO: Handle the last room stay deletion
        self.deleted = True
        for gs in self.guest_stays:
            gs.delete()
        for ta_c in self.room_night_ta_commissions:
            ta_c.delete()
        self.mark_dirty()

    def mark_noshow(self, cancellation_date=None):
        privileges = crs_context.privileges_as_dict
        if self.is_noshow():
            raise InvalidOperationError(
                error=BookingErrors.ROOM_STAY_ALREADY_MARKED_NO_SHOW,
                description="Roomstay already marked no-show.",
            )
        if (
            privileges
            and PrivilegeCode.CAN_CANCEL_WALKIN_BOOKING_BEFORE_TIME in privileges
        ):
            if not self._can_mark_room_stay_noshow(
                privileges[PrivilegeCode.CAN_CANCEL_WALKIN_BOOKING_BEFORE_TIME][0]
            ):
                # should not reach here if triggerd from via pms (can reach here from api using postman)
                raise InvalidActionError(
                    error=BookingErrors.NOSHOW_NOT_ALLOWED,
                    extra_payload=dict(
                        entity="RoomStay", room_stay_id=self.room_stay_id
                    ),
                )
        elif not self.has_current_time_crossed_midnight_of_checkin_datetime():
            raise InvalidActionError(
                error=BookingErrors.NOSHOW_BEFORE_CHECKIN_MIDNIGHT,
                extra_payload=dict(entity="RoomStay", room_stay_id=self.room_stay_id),
            )

        self.noshow()
        self.cancellation_date = cancellation_date
        self.cancellation_date_time = dateutils.today()
        room_stay_side_effect = RoomStaySideEffect(self.room_stay_id)
        for gs in self.active_guest_stays():
            marked_noshow = gs.mark_noshow()
            if marked_noshow:
                room_stay_side_effect.add_guest_stay(
                    GuestStaySideEffect(gs.guest_stay_id)
                )
        return room_stay_side_effect

    def undo_mark_noshow(self, guest_stay_datas):
        if not self.is_noshow():
            raise InvalidOperationError(
                error=BookingErrors.ROOM_STAY_NOT_IN_NOSHOW_STATE,
                description="Room stay should be in no-show state to undo it.",
            )
        self.undo_noshow()
        self.cancellation_date = None
        self.cancellation_date_time = None
        for guest_stay_data in guest_stay_datas:
            gs = self.get_guest_stay(guest_stay_data.guest_stay_id)
            gs.undo_noshow()

    def mark_cancel(self, cancellation_date=None):
        self._fail_if_cannot_cancel()
        if self.is_cancelled():
            raise InvalidOperationError(
                error=BookingErrors.ROOM_STAY_ALREADY_CANCELLED,
                description="Roomstay already cancelled.",
            )
        self.cancel()
        self.cancellation_date = cancellation_date
        self.cancellation_date_time = dateutils.today()
        room_stay_side_effect = RoomStaySideEffect(self.room_stay_id)
        for gs in self.active_guest_stays():
            marked = gs.mark_cancel()
            if marked:
                room_stay_side_effect.add_guest_stay(
                    GuestStaySideEffect(gs.guest_stay_id)
                )
        return room_stay_side_effect

    def undo_mark_cancel(self, guest_stay_datas, raise_event=False):
        if not self.is_cancelled():
            raise InvalidOperationError(
                error=BookingErrors.ROOM_STAY_NOT_IN_CANCELLED_STATE,
                description="Room stay should be in canceled state to undo it.",
            )
        self.undo_cancel()
        self.cancellation_date = None
        self.cancellation_date_time = None
        guest_stay_event_data = []
        for guest_stay_data in guest_stay_datas:
            gs = self.get_guest_stay(guest_stay_data.guest_stay_id)
            guest_stay_event_data.append(
                GuestStayEventData(
                    guest_stay_id=gs.guest_stay_id,
                    guest_id=gs.guest_id,
                    room_stay_id=self.room_stay_id,
                    room_number=self.room_allocation.room_no
                    if self.room_allocation
                    else None,
                    room_type_id=self.room_type_id,
                )
            )
            gs.undo_cancel()

        if raise_event:
            register_event(
                RoomStayCancelReversedEvent(guest_stay_event_data=guest_stay_event_data)
            )

    def _fail_if_change_in_room_type(self):
        current_room_type_id = self.room_allocation.room_type_id
        for room_allocation_from_history in self.room_allocation_history:
            if current_room_type_id != room_allocation_from_history.room_type_id:
                raise InvalidActionError(
                    error=BookingErrors.ROOM_TYPE_CHANGE_AFTER_CHECKIN,
                    extra_payload=dict(
                        room_stay_id=self.room_stay_id,
                        current_room_type_id=current_room_type_id,
                        changed_room_type_id=room_allocation_from_history.room_type_id,
                    ),
                )

    def _fail_if_inactive_room_stay(self):
        if not self.is_active():
            raise InvalidOperationError(
                error=BookingErrors.ROOM_STAY_INACTIVE,
                description="RoomStay is cancelled or marked noshow",
            )

    def _fail_if_checked_out_room_stay(self):
        if self.is_checked_out():
            raise InvalidOperationError(error=BookingErrors.CHECKED_OUT_ROOM_STAY)

    def _fail_if_cannot_cancel(self):
        if self.is_checked_in() or self.is_part_checked_in():
            raise InvalidActionError(error=BookingErrors.CHECKED_IN_ROOM_REMOVAL_ERROR)

    def add_guest_stay(
        self,
        guest_stay_data: GuestStayData,
        override_checkin_time=None,
        override_checkout_time=None,
    ):
        self._fail_if_inactive_room_stay()
        self._fail_if_checked_out_room_stay()
        guest_stays = self._guest_stays
        guest_stay_id = max([gs.guest_stay_id for gs in guest_stays], default=0)
        guest_stay_id += 1

        status = BookingStatus.RESERVED

        if override_checkin_time and guest_stay_data.checkin_date:
            guest_stay_data.override_checkin_time(override_checkin_time)

        if override_checkout_time and guest_stay_data.checkout_date:
            guest_stay_data.override_checkout_time(override_checkout_time)

        if (
            guest_stay_data.checkin_date < self.checkin_date
            or guest_stay_data.checkout_date > self.checkout_date
        ):
            raise InvalidStayDatesError(
                description="GuestStay checkin and checkout date should be within room stay checkin, checkout dates"
            )

        age_group = guest_stay_data.age_group
        guest_stay = GuestStay(
            guest_stay_id,
            status,
            age_group,
            guest_stay_data.checkin_date,
            guest_stay_data.checkout_date,
            actual_checkin_date=None,
            actual_checkout_date=None,
            checkin_business_date=dateutils.to_date(guest_stay_data.checkin_date),
            checkout_business_date=dateutils.to_date(guest_stay_data.checkout_date),
        )

        if guest_stay_data.guest_allocation_data:
            guest_stay.allocate_guest(guest_stay_data.guest_allocation_data)
        self._guest_stays.append(guest_stay)
        self._trigger_state_transition()
        self.date_wise_occupancies = self._get_date_wise_occupancies()
        self.mark_dirty()
        return guest_stay

    def cancel_guest_stay(self, guest_stay_id):
        self._fail_if_inactive_room_stay()
        if self._is_last_guest_stay(guest_stay_id):
            raise InvalidActionError(error=BookingErrors.LAST_GUEST_REMOVAL_ERROR)

        guest_stay = self.get_guest_stay(guest_stay_id)
        if guest_stay.is_cancelled():
            raise InvalidActionError(
                error=BookingErrors.GUEST_STAY_ALREADY_CANCELLED,
                extra_payload=dict(guest_stay_id=guest_stay_id),
            )
        guest_stay.mark_cancel()
        self._trigger_state_transition()
        self.date_wise_occupancies = self._get_date_wise_occupancies()
        self.mark_dirty()
        return guest_stay

    def mark_guest_stay_noshow(self, guest_stay_id):
        self._fail_if_inactive_room_stay()
        if self._is_last_guest_stay(guest_stay_id):
            raise InvalidActionError(error=BookingErrors.LAST_GUEST_NO_SHOW_ERROR)

        guest_stay = self.get_guest_stay(guest_stay_id)
        if guest_stay.is_noshow():
            raise InvalidActionError(
                error=BookingErrors.GUEST_STAY_ALREADY_MARKED_NO_SHOW,
                extra_payload=dict(guest_stay_id=guest_stay_id),
            )
        guest_stay.mark_noshow()
        self._trigger_state_transition()
        self.mark_dirty()
        return guest_stay

    def undo_mark_guest_stay_noshow(self, guest_stay_id):
        guest_stay = self.get_guest_stay(guest_stay_id)
        if not guest_stay.is_noshow():
            raise InvalidActionError(
                error=BookingErrors.GUEST_STAY_NOT_IN_NO_SHOW_STATE,
                extra_payload=dict(guest_stay_id=guest_stay_id),
            )
        noshow_event_data = NoShowEventData(
            room_stay_id=self.room_stay_id,
            room_number=self.room_allocation.room_no if self.room_allocation else None,
            guest_stay_id=guest_stay_id,
            guest_id=guest_stay.guest_id,
            room_type_id=self.room_type_id,
        )
        register_event(
            GuestStayNoShowReversedEvent(noshow_event_data=noshow_event_data)
        )
        guest_stay.undo_noshow()
        self._trigger_state_transition()
        self.mark_dirty()

    def get_guest_stay(self, guest_stay_id):
        for gs in self.guest_stays:
            if gs.guest_stay_id == guest_stay_id:
                return gs
        raise ResourceNotFound(
            "GuestStay",
            description="RoomStay:GuestStay not found in {}:{}".format(
                self.room_stay_id, guest_stay_id
            ),
        )

    def get_guest_stays(self, guest_stay_ids):
        return [self.get_guest_stay(guest_stay_id) for guest_stay_id in guest_stay_ids]

    def checkin_guests(
        self, guest_checkin_data: List[GuestCheckinData], room_allocation_data=None
    ):
        is_first_checkin = self.is_reserved()
        hotel_context = crs_context.get_hotel_context()

        self._fail_if_inactive_room_stay()
        if not self.room_allocation and not room_allocation_data:
            raise InvalidActionError(error=BookingErrors.BOOKING_ERROR)

        room_stay_side_effect = RoomStaySideEffect(room_stay_id=self.room_stay_id)

        for guest_checkin in guest_checkin_data:
            guest_stay = self.get_guest_stay(guest_checkin.guest_stay_id)
            if guest_stay.is_checked_in():
                raise InvalidActionError(
                    error=BookingErrors.GUEST_STAY_ALREADY_CHECKED_IN,
                    extra_payload=dict(
                        room_stay_id=self.room_stay_id,
                        guest_stay_id=guest_stay.guest_stay_id,
                    ),
                )
            actual_checkin_date = guest_checkin.checkin_date
            (
                min_actual_checkin,
                max_actual_checkin,
            ) = guest_stay.get_allowed_checkin_window()
            logger.debug(
                "Guest Stay allowed checkin window: [%s, %s]. Provided actual checkin date: %s",
                min_actual_checkin,
                max_actual_checkin,
                actual_checkin_date,
            )

            if (
                actual_checkin_date < min_actual_checkin
                or actual_checkin_date > max_actual_checkin
            ):
                raise InvalidActionError(
                    error=BookingErrors.GUEST_STAY_CHECK_IN_OPERATION_ERROR,
                    description="Actual checkin date should be between allowed checkin window",
                    extra_payload=dict(
                        room_stay_id=self.room_stay_id,
                        guest_stay_id=guest_stay.guest_stay_id,
                        min_allowed_checkin_date=dateutils.isoformat_datetime(
                            min_actual_checkin
                        ),
                        max_allowed_checkin_date=dateutils.isoformat_datetime(
                            max_actual_checkin
                        ),
                        actual_checkin_date=dateutils.isoformat_datetime(
                            guest_checkin.checkin_date
                        ),
                    ),
                )

            if not guest_stay.has_current_business_date_checkin():
                raise InvalidActionError(
                    error=BookingErrors.FUTURE_CHECKIN_NOT_ALLOWED,
                    description="Cannot perform checkin operation on a future or past guest stay.",
                    extra_payload=dict(
                        room_stay_id=self.room_stay_id,
                        guest_stay_id=guest_stay.guest_stay_id,
                        checkin_date=dateutils.isoformat_datetime(
                            guest_stay.checkin_date
                        ),
                        business_date=hotel_context.current_date(),
                    ),
                )

            checked_in, guest_stay_side_effect = guest_stay.perform_checkin(
                guest_id=guest_checkin.guest_id, checkin_date=guest_checkin.checkin_date
            )
            if not checked_in:
                raise InvalidActionError(
                    error=BookingErrors.GUEST_STAY_CHECK_IN_OPERATION_ERROR,
                    extra_payload=dict(
                        room_stay_id=self.room_stay_id,
                        guest_stay_id=guest_stay.guest_stay_id,
                    ),
                )
            room_stay_side_effect.add_guest_stay(guest_stay_side_effect)

        if not self.actual_checkin_date:
            self.actual_checkin_date = min(
                guest_checkin.checkin_date for guest_checkin in guest_checkin_data
            )

        new_room_allocation, previous_room_allocation = None, None
        if room_allocation_data:
            # TODO: Room allocation should not be changed on already checked in room in this method
            min_guest_stay_checkin_date = min(
                guest_checkin.checkin_date for guest_checkin in guest_checkin_data
            )

            if min_guest_stay_checkin_date != room_allocation_data.checkin_date:
                raise InvalidActionError(
                    error=BookingErrors.INVALID_ROOM_ALLOCATION_CHECKIN_DATE,
                    description="Room allocation checkin date should the minimum of all "
                    "guest_stays checkin_date "
                    "provided",
                    extra_payload=dict(
                        room_stay_id=self.room_stay_id,
                        min_guest_stay_checkin_date=min_guest_stay_checkin_date,
                        room_allocation_checkin_date=room_allocation_data.checkin_date,
                    ),
                )
            new_room_allocation, previous_room_allocation = self.allocate_room(
                room=room_allocation_data.room,
                room_id=room_allocation_data.room_id,
                checkin_date=room_allocation_data.checkin_date,
            )

        self.actual_checkin_date = min(
            gs.actual_checkin_date for gs in self.guest_stays if gs.actual_checkin_date
        )
        self.actual_checkin_calendar_date = dateutils.current_datetime()
        if is_first_checkin:
            self.room_allocation.update_checkin_date(
                checkin_date=self.actual_checkin_date
            )

        if room_allocation_data and new_room_allocation:
            self.room_allocation.update_checkin_date(
                checkin_date=self.actual_checkin_date
            )

        self._trigger_state_transition()
        self.mark_dirty()
        return self.room_allocation, previous_room_allocation, room_stay_side_effect

    def undo_checkin_guests(self, guest_checkin_data: List[GuestStaySideEffect]):
        self._fail_if_change_in_room_type()

        checkin_reverted_guest_allocations = []
        room_number = self.room_allocation.room_no if self.room_allocation else None
        for guest_checkin in guest_checkin_data:
            guest_stay = self.get_guest_stay(guest_checkin.guest_stay_id)
            if not guest_stay.is_checked_in():
                raise InvalidActionError(
                    error=BookingErrors.GUEST_STAY_NOT_CHECKED_IN,
                    extra_payload=dict(
                        room_stay_id=self.room_stay_id,
                        guest_stay_id=guest_stay.guest_stay_id,
                    ),
                )
            (
                undo_checked_in,
                checkin_reverted_guest_allocation,
            ) = guest_stay.undo_checkin_action(guest_checkin)
            if not undo_checked_in:
                raise InvalidActionError(
                    error=BookingErrors.GUEST_STAY_UNDO_CHECK_IN_OPERATION_ERROR,
                    extra_payload=dict(
                        room_stay_id=self.room_stay_id,
                        guest_stay_id=guest_stay.guest_stay_id,
                    ),
                )
            guest_stay_data = GuestStayEventData(
                guest_stay_id=guest_stay.guest_stay_id,
                guest_id=checkin_reverted_guest_allocation.guest_id,
                checkin_date=guest_stay.actual_checkin_date,
                checkout_date=guest_stay.actual_checkout_date,
            )

            room_stay_data = RoomStayEventData(
                self.room_stay_id,
                self.room_type_id,
                self.actual_checkin_date,
                self.actual_checkout_date,
                [guest_stay_data],
                room_number=room_number,
            )
            register_event(
                GuestLifecycleEvent(
                    guest_data=guest_stay_data,
                    room_stay_data=room_stay_data,
                    action_type=BookingActions.UNDO_CHECKIN,
                )
            )

            checkin_reverted_guest_allocations.append(checkin_reverted_guest_allocation)
        actual_checkin_dates = [
            gs.actual_checkin_date for gs in self.guest_stays if gs.actual_checkin_date
        ]
        if not actual_checkin_dates:
            self.actual_checkin_date = None
            self.actual_checkin_calendar_date = None
        else:
            self.actual_checkin_date = min(actual_checkin_dates)
        self._trigger_state_transition()

        deleted_current_room_allocation = None
        if self.status == BookingStatus.RESERVED:
            self._delete_old_room_allocations()
            deleted_current_room_allocation = self._delete_current_room_allocation()
            room_event_data = RoomEventData(
                room_stay_id=deleted_current_room_allocation.room_stay_id,
                room_type_id=deleted_current_room_allocation.room_type_id,
                room_number=deleted_current_room_allocation.room_no,
            )
            register_event(RoomFreedEvent([room_event_data]))

        return deleted_current_room_allocation, checkin_reverted_guest_allocations

    def _delete_old_room_allocations(self):
        deleted_room_allocations = []
        for room_allocation in self.room_allocation_history:
            room_allocation.mark_deleted()
            deleted_room_allocations.append(room_allocation)
        return deleted_room_allocations

    def delete_room_allocations(self):
        deleted_room_allocations = []
        if self.status == BookingStatus.RESERVED:
            deleted_current_room_allocation = self._delete_current_room_allocation()
            deleted_room_allocations.append(deleted_current_room_allocation)
            for room_allocation in self.room_allocation_history:
                room_allocation.mark_deleted()
                deleted_room_allocations.append(room_allocation)
            room_event_data = RoomEventData(
                room_stay_id=deleted_current_room_allocation.room_stay_id,
                room_type_id=deleted_current_room_allocation.room_type_id,
                room_number=deleted_current_room_allocation.room_no,
            )
            register_event(RoomFreedEvent([room_event_data]))
        self.mark_dirty()
        return deleted_room_allocations

    def _delete_current_room_allocation(self):
        current_room_allocation = self.room_allocation
        current_room_allocation.set_is_current(False)
        current_room_allocation.mark_deleted()
        self._room_allocation_history.append(current_room_allocation)
        self.room_allocation = None
        self.mark_dirty()
        return current_room_allocation

    def checkout_guests(
        self,
        guest_checkout_request_data: [GuestCheckoutRequestData],
        checkout_datetime: datetime,
    ):
        # TODO: Adding fix for same date checkin and checkout.
        if dateutils.localize_datetime(
            checkout_datetime
        ) <= dateutils.localize_datetime(self.actual_checkin_date):
            checkout_datetime = dateutils.add(
                dateutils.datetime_at_given_time(
                    self.actual_checkin_date, dateutils.current_time()
                ),
                minutes=1,
            )

        for request_data in guest_checkout_request_data:
            guest_stay = self.get_guest_stay(request_data.guest_stay_id)
            if guest_stay.guest_id != request_data.guest_id:
                raise ValidationException(
                    BookingErrors.INVALID_GUEST_SELECTION,
                    description="Guest selected: {} for room_stay_id: {} should belong to the "
                    "room "
                    "and should be in checked-in state".format(
                        [request_data.guest_id], self.room_stay_id
                    ),
                )
            guest_stay.perform_checkout(
                checkout_datetime=request_data.checkout_datetime
            )
        self._trigger_state_transition()
        if self.is_checked_out():
            self.actual_checkout_date = checkout_datetime
            self.actual_checkout_business_date = (
                crs_context.get_hotel_context().current_date()
            )
            self.room_allocation.update_checkout_date(self.actual_checkout_date)
            self.actual_checkout_calendar_date = dateutils.current_datetime()

        self.date_wise_occupancies = self._get_date_wise_occupancies()
        self.mark_dirty()

    def undo_checkout_guests(
        self, guest_checkout_request_data: [GuestCheckoutRequestData]
    ):
        for request_data in guest_checkout_request_data:
            guest_stay = self.get_guest_stay(request_data.guest_stay_id)
            if guest_stay.guest_id != request_data.guest_id:
                raise ValidationException(
                    BookingErrors.INVALID_GUEST_SELECTION,
                    description="Guest selected: {} for room_stay_id: {} should belong to the "
                    "room".format([request_data.guest_id], self.room_stay_id),
                )

            guest_stay_data = GuestStayEventData(
                guest_stay_id=guest_stay.guest_stay_id,
                guest_id=guest_stay.guest_id,
                checkin_date=guest_stay.actual_checkin_date,
                checkout_date=guest_stay.actual_checkout_date,
            )

            room_stay_data = RoomStayEventData(
                self.room_stay_id,
                self.room_type_id,
                self.actual_checkin_date,
                self.actual_checkout_date,
                [guest_stay_data],
                room_number=self.room_allocation.room_no,
            )
            register_event(
                GuestLifecycleEvent(
                    guest_data=guest_stay_data,
                    room_stay_data=room_stay_data,
                    action_type=BookingActions.UNDO_CHECKOUT,
                )
            )

            guest_stay.undo_checkout_action()
        self.room_allocation.update_checkout_date(None)
        self.actual_checkout_date = None
        self.actual_checkout_business_date = None
        self.actual_checkout_calendar_date = None
        self.date_wise_occupancies = self._get_date_wise_occupancies()
        self._trigger_state_transition()
        self.mark_dirty()

    def allocate_room(self, room, room_id, checkin_date, assigned_by=None):
        self._fail_if_inactive_room_stay()
        (
            min_room_allocation_checkin,
            max_room_allocation_checkin,
        ) = self.get_allowed_room_allocation_checkin_window()

        if (
            checkin_date < min_room_allocation_checkin
            or checkin_date > max_room_allocation_checkin
        ):
            raise InvalidActionError(
                error=BookingErrors.INVALID_ROOM_ALLOCATION_CHECKIN_DATE,
                description="Room allocation checkin date should be within allowed duration",
                extra_payload=dict(
                    room_stay_id=self.room_stay_id,
                    min_room_allocation_checkin=dateutils.isoformat_datetime(
                        min_room_allocation_checkin
                    ),
                    max_room_allocation_checkin_date=dateutils.isoformat_datetime(
                        max_room_allocation_checkin
                    ),
                    room_allocation_checkin_date=dateutils.isoformat_datetime(
                        checkin_date
                    ),
                ),
            )
        if room.room_type_id != self.room_type_id:
            raise InvalidActionError(
                error=BookingErrors.ROOM_TYPE_MISMATCH_IN_ROOM_ALLOCATION,
                extra_payload=dict(
                    room_stay_id=self.room_stay_id,
                    room_type_id=self.room_type_id,
                    room_allocation_room_type_id=room.room_type_id,
                ),
            )
        previous_room_allocation = self.room_allocation
        if previous_room_allocation:
            if previous_room_allocation.room_id == room_id:
                return None, None
            previous_room_allocation.update_checkout_date(checkin_date)
            previous_room_allocation.set_is_current(False)
            if not self.is_checkin_performed():
                # The room is not checked in yet. This is pre-checkin room change. So allocation will always get
                # overridden
                previous_room_allocation.mark_overridden()

            if (
                previous_room_allocation.checkin_business_date
                == previous_room_allocation.checkout_business_date
            ):
                # Post checkin same day room change
                previous_room_allocation.mark_overridden()
            self._room_allocation_history.append(previous_room_allocation)

        self.room_allocation = RoomAllocation(
            self.next_room_allocation_id,
            self.room_stay_id,
            room_id,
            self.room_type_id,
            assigned_by,
            checkin_date,
            None,
            room_no=room.room_number,
            is_current=True,
        )

        register_event(
            RoomChangedEvent(
                room_stay_id=self.room_stay_id,
                new_room_number=room.room_number,
                new_room_type_id=room.room_type_id,
                old_room_number=previous_room_allocation.room_no
                if previous_room_allocation
                else None,
                old_room_type_id=previous_room_allocation.room_type_id
                if previous_room_allocation
                else None,
            )
        )
        self.mark_dirty()
        return self.room_allocation, previous_room_allocation

    def get_allowed_room_allocation_checkin_window(self):
        hotel_context = crs_context.get_hotel_context()
        if self.room_allocation:
            min_room_allocation_checkin = self.room_allocation.checkin_date
        else:
            min_room_allocation_checkin = dateutils.datetime_at_given_time(
                self.checkin_date, hotel_context.switch_over_time
            )
        max_room_allocation_checkin = dateutils.datetime_at_given_time(
            self.checkout_date, hotel_context.free_late_checkout_time
        )
        return min_room_allocation_checkin, max_room_allocation_checkin

    def update_charge_id_map(self, charge_id_map, charges_to_delete=None):
        if charges_to_delete:
            self._remove_charge_mappings(charges_to_delete)
        for date_string, charge_id in charge_id_map.items():
            dt = dateutils.to_date(dateutils.ymd_str_to_date(date_string))
            if dt < dateutils.to_date(self.checkin_date) or dt >= dateutils.to_date(
                self.checkout_date
            ):
                raise PriceError(
                    extra_payload=dict(
                        price_date=date_string,
                        checkin_date=dateutils.isoformat_datetime(self.checkin_date),
                        checkout_date=dateutils.isoformat_datetime(self.checkout_date),
                    )
                )
        self.charge_id_map = {**self.charge_id_map, **charge_id_map}
        self.charge_ids = list(self.charge_id_map.values())
        self.mark_dirty()

    def update_room_rents(self, room_rents):
        if not self.room_rents:
            self.room_rents = room_rents
            return

        date_wise_room_rents = {
            room_rent.applicable_date: room_rent for room_rent in self.room_rents
        }
        for room_rent in room_rents:
            date_wise_room_rents[room_rent.applicable_date] = room_rent

        self.room_rents = list(date_wise_room_rents.values())
        self.mark_dirty()

    def _remove_charge_mappings(self, charge_ids):
        new_charge_id_map = {
            date: charge_id
            for date, charge_id in self.charge_id_map.items()
            if charge_id not in charge_ids
        }
        new_room_rents = [
            room_rent
            for room_rent in self.room_rents
            if dateutils.date_to_ymd_str(room_rent.applicable_date) in new_charge_id_map
        ]

        self.room_rents = new_room_rents
        self.charge_id_map = new_charge_id_map
        self.charge_ids = list(self.charge_id_map.values())
        self.mark_dirty()

    def _is_last_guest_stay(self, guest_stay_id):
        adult_guest_stays = self.adult_guest_stays()
        adult_guest_stays = [
            gs for gs in adult_guest_stays if gs.status != BookingStatus.NOSHOW
        ]
        return (
            len(adult_guest_stays) == 1
            and adult_guest_stays[0].guest_stay_id == guest_stay_id
        )

    def refresh_room_stay_checkin_checkout_dates(self):
        valid_guest_stays = [
            gs
            for gs in self._guest_stays
            if not gs.deleted and gs.status != BookingStatus.CANCELLED
        ]
        room_stay_checkin_date = min(r.checkin_date for r in valid_guest_stays)
        room_stay_checkout_date = max(r.checkout_date for r in valid_guest_stays)
        self.checkin_date = room_stay_checkin_date
        self.checkout_date = room_stay_checkout_date
        self.mark_dirty()

    def get_charges_for_stay_dates(self, stay_dates):
        charges = []
        if not stay_dates:
            return charges
        for d, charge_id in self.charge_id_map.items():
            if dateutils.ymd_str_to_date(d) in stay_dates:
                charges.append(charge_id)
        return charges

    def get_room_rents_for_stay_dates(self, stay_dates):
        room_rents = []
        if not stay_dates:
            return room_rents

        for room_rent in self.room_rents:
            if room_rent.applicable_date in stay_dates:
                room_rents.append(room_rent)
        return room_rents

    def change_stay_dates(self, checkin_date, checkout_date):
        self._fail_if_inactive_room_stay()
        deleted_room_allocations = self._delete_allocation_history_out_of_stay_boundary(
            checkin_date, checkout_date
        )

        if checkin_date != self.checkin_date:
            if self.is_checkin_performed():
                # Domain Constraint
                raise BookingUpdateError(
                    error=BookingErrors.CHECKED_IN_ROOM_STAY_CHECKIN_DATE_CHANGE,
                    description="RoomStay has already been checked in. Cannot change room stay checkin date",
                )
            self._update_room_stay_checkin_date(checkin_date)
            if self.room_allocation:
                self.room_allocation.set_is_current(False)
            for room_allocation in self.all_room_allocations():
                room_allocation.mark_deleted()
                deleted_room_allocations.append(room_allocation)
        if checkout_date != self.checkout_date:
            if self.status == BookingStatus.CHECKED_OUT:
                # Domain Constraint
                raise BookingUpdateError(
                    error=BookingErrors.CHECKED_OUT_ROOM_STAY_CHECKOUT_DATE_CHANGE,
                    description="RoomStay has already been checked out. Cannot change room stay checkout date",
                )
            self._update_room_stay_checkout_date(checkout_date)

        logger.debug(
            "Room Stay duration changed to checkin_date: %s, checkout_date: %s",
            self.checkin_date,
            self.checkout_date,
        )
        self._trigger_state_transition()
        self.date_wise_occupancies = self._get_date_wise_occupancies()
        self.mark_dirty()
        return deleted_room_allocations

    def replace_stay_dates(self, checkin_date, checkout_date):
        if not self.is_active():
            return

        self.checkin_date = checkin_date
        self.checkout_date = checkout_date

        self.date_wise_occupancies = self._get_date_wise_occupancies()
        self.mark_dirty()
        return

    def _update_room_stay_checkin_date(self, checkin_date):
        if not self._is_all_guest_stays_checkin_date_same_as_room_stay_checkin_date():
            raise BookingUpdateError(
                error=BookingErrors.ROOM_STAY_DURATION_CHANGE_ERROR,
                description="Some guest stays have checkin date different from room stay checkin date. "
                "Cannot change room stay checkin date in such scenario.",
            )
        self.checkin_date = checkin_date
        for gs in self.all_guest_stays_except_cancelled():
            gs.checkin_date = checkin_date
            if gs.guest_allocation and gs.guest_allocation.checkin_date:
                self._update_guest_allocation_checkin_date(gs, checkin_date)
        self.mark_dirty()

    def _is_all_guest_stays_checkin_date_same_as_room_stay_checkin_date(self):
        guest_stay_checkin_dates = set(
            gs.checkin_date for gs in self.active_guest_stays()
        )
        return (
            len(guest_stay_checkin_dates) == 1
            and guest_stay_checkin_dates.pop() == self.checkin_date
        )

    def _update_room_stay_checkout_date(self, checkout_date):
        if not self._is_all_guest_stays_checkout_date_same_as_room_stay_checkout_date():
            raise BookingUpdateError(
                error=BookingErrors.ROOM_STAY_DURATION_CHANGE_ERROR,
                description="Some guest stays have checkout date different from room stay checkout date. "
                "Cannot change room stay checkout date in such scenario.",
            )
        self.checkout_date = checkout_date
        for gs in self.all_guest_stays_except_cancelled():
            gs.checkout_date = checkout_date
        self.mark_dirty()

    def _is_all_guest_stays_checkout_date_same_as_room_stay_checkout_date(self):
        guest_stay_checkout_dates = set(
            gs.checkout_date for gs in self.active_guest_stays()
        )
        return (
            len(guest_stay_checkout_dates) == 1
            and guest_stay_checkout_dates.pop() == self.checkout_date
        )

    def update_room_type(self, room_type_id, room_allocation_data=None):
        self._fail_if_inactive_room_stay()
        if self.room_type_id != room_type_id:
            if (
                self.room_allocation
                and room_type_id != self.room_allocation.room_type_id
            ):
                if not room_allocation_data:
                    raise BookingUpdateError(
                        error=BookingErrors.ROOM_TYPE_MISMATCH_IN_ROOM_ALLOCATION,
                        description="Please provide a new room allocation for room type change",
                        extra_payload=dict(
                            room_stay_id=self.room_stay_id,
                            room_type_id=self.room_type_id,
                            room_allocation_room_type_id=self.room_allocation.room_type_id,
                        ),
                    )
            self.room_type_id = room_type_id

        new_room_allocation, previous_room_allocation = None, None
        if room_allocation_data:
            new_room_allocation, previous_room_allocation = self.allocate_room(
                room=room_allocation_data.room,
                room_id=room_allocation_data.room_id,
                checkin_date=room_allocation_data.checkin_date,
            )
        self.mark_dirty()
        return new_room_allocation, previous_room_allocation

    def update_disallow_charge_addition(self, disallow_charge_addition):
        self.disallow_charge_addition = disallow_charge_addition
        dca_event_data = DCAEventData(
            room_id=self.room_id,
            room_number=self.room_number,
            room_type_id=self.room_type_id,
            room_stay_id=self.room_stay_id,
            disallow_charge_addition=disallow_charge_addition,
        )
        register_event(DisallowChargeAdditionChangedEvent([dca_event_data]))
        self.mark_dirty()

    def update_room_rate_plans(self, date_wise_rate_plan: dict):
        if not all(list(date_wise_rate_plan.values())):
            raise RuntimeError(
                f"Rate plan ids not found for all dates: {','.join([str(d) for d in date_wise_rate_plan.keys()])}"
            )
        self.room_rate_plans = [
            room_rp
            for room_rp in self.room_rate_plans
            if dateutils.to_date(self.checkin_date)
            <= room_rp.stay_date
            < dateutils.to_date(self.checkout_date)
        ]
        date_wise_room_rate_plan = {
            room_rp.stay_date: room_rp for room_rp in self.room_rate_plans
        }
        stay_date_range = [
            dateutils.to_date(date)
            for date in DateRange(
                start_date=self.checkin_date,
                end_date=dateutils.subtract(self.checkout_date, 1),
            ).as_list()
        ]
        for date in stay_date_range:
            existing_rate_plan_id = date_wise_room_rate_plan.get(date)
            new_rate_plan_id = date_wise_rate_plan.get(date)
            if not existing_rate_plan_id:
                self.room_rate_plans.append(
                    RoomRatePlan(
                        stay_date=date, rate_plan_id=date_wise_rate_plan.get(date)
                    )
                )
            elif (
                new_rate_plan_id
                and existing_rate_plan_id.rate_plan_id != new_rate_plan_id
            ):
                raise RuntimeError(
                    f"Cannot update rate_plan_id for existing dates in update stay dates flow. Date: {str(date)}"
                )

    # ========================== State Machine and Allowed Actions related codes ==============================

    def on_state_transition(self, status):
        self.status = status
        if self.status == BookingStatus.CHECKED_OUT:
            self.room_allocation.update_checkout_date(self.actual_checkout_date)
        self.mark_dirty()

    def _trigger_state_transition(self):
        guest_stays = self.active_guest_stays()
        self.state_machine.trigger_state_transition(guest_stays)

    def refresh_allowed_actions(self, is_soft_booking=False):
        for gs in self.guest_stays:
            gs.refresh_allowed_actions(is_soft_booking)

        self.allowed_actions = self.derive_room_stay_allowed_actions(is_soft_booking)

    def derive_room_stay_allowed_actions(self, is_soft_booking):
        allowed_actions = []
        if not self.guest_stay_wise_allowed_actions():
            return allowed_actions

        if self.is_noshow_allowed_for_room_stay(is_soft_booking):
            allowed_actions.append(BookingActions.NOSHOW.value)

        if self.is_cancellation_allowed_for_room_stay():
            allowed_actions.append(BookingActions.CANCEL.value)

        if self.is_checkin_allowed_for_room_stay(is_soft_booking):
            allowed_actions.append(BookingActions.CHECKIN.value)

        if self.is_checkout_allowed_for_room_stay():
            allowed_actions.append(BookingActions.CHECKOUT.value)

        return allowed_actions

    def is_noshow_allowed_for_room_stay(self, is_soft_booking):
        return (
            all(
                BookingActions.NOSHOW.value in actions
                for actions in self.guest_stay_wise_allowed_actions()
            )
            and not any(
                gs.status == BookingStatus.CHECKED_OUT for gs in self.guest_stays
            )
            and not is_soft_booking
        )

    def is_cancellation_allowed_for_room_stay(self):
        return all(
            BookingActions.CANCEL.value in actions
            for actions in self.guest_stay_wise_allowed_actions()
        ) and not any(gs.status == BookingStatus.CHECKED_OUT for gs in self.guest_stays)

    def is_checkin_allowed_for_room_stay(self, is_soft_booking):
        return (
            any(
                BookingActions.CHECKIN.value in actions
                for actions in self.guest_stay_wise_allowed_actions()
            )
            and all(
                BookingActions.CHECKIN.value in actions
                or BookingActions.CHECKOUT.value in actions
                for actions in self.guest_stay_wise_allowed_actions()
            )
            and not is_soft_booking
        )

    def is_checkout_allowed_for_room_stay(self):
        return all(
            BookingActions.CHECKOUT.value in actions
            for actions in self.guest_stay_wise_allowed_actions()
        )

    def guest_stay_wise_allowed_actions(self):
        return [gs.allowed_actions for gs in self.guest_stays if gs.allowed_actions]

    def is_checkin_performed(self):
        return self.status in {
            BookingStatus.PART_CHECKIN,
            BookingStatus.PART_CHECKOUT,
            BookingStatus.CHECKED_IN,
            BookingStatus.CHECKED_OUT,
        }

    def is_fully_checked_in(self):
        return not any(gs.is_reserved() for gs in self.active_guest_stays())

    def noshow(self):
        return self.state_machine.noshow()

    def undo_noshow(self):
        return self.state_machine.undo_noshow()

    def cancel(self):
        return self.state_machine.cancel()

    def undo_cancel(self):
        return self.state_machine.undo_cancel()

    def checkin(self):
        return self.state_machine.checkin()

    def is_checked_out(self):
        return self.state_machine.is_checked_out()

    def is_checked_in(self):
        return self.state_machine.is_checked_in()

    def is_part_checked_in(self):
        return self.state_machine.is_part_checked_in()

    def is_cancelled(self):
        return self.state_machine.is_cancelled()

    def is_noshow(self):
        return self.state_machine.is_noshow()

    def is_reserved(self):
        return self.state_machine.is_reserved()

    def is_part_checked_out(self):
        return self.state_machine.is_part_checked_out()

    def is_occupied(self):
        return self.status in {
            BookingStatus.CHECKED_IN,
            BookingStatus.PART_CHECKIN,
            BookingStatus.PART_CHECKOUT,
        }

    def update_room_rate_plan(
        self, rate_plan_details, existing_rate_plan_dict, applicable_date=None
    ):
        if applicable_date:
            for room_rate_plan in self.room_rate_plans:
                if room_rate_plan.stay_date == applicable_date:
                    existing_rate_plan_id = room_rate_plan.rate_plan_id
                    room_rate_plan.rate_plan_id = rate_plan_details.rate_plan_id
                    self._register_rate_plan_changed_event(
                        rate_plan_details,
                        existing_rate_plan_id,
                        existing_rate_plan_dict,
                    )
                    self.mark_dirty()
        else:
            for room_rate_plan in self.room_rate_plans:
                existing_rate_plan_id = room_rate_plan.rate_plan_id
                room_rate_plan.rate_plan_id = rate_plan_details.rate_plan_id
                self._register_rate_plan_changed_event(
                    rate_plan_details, existing_rate_plan_id, existing_rate_plan_dict
                )
            self.mark_dirty()

    @staticmethod
    def _update_guest_allocation_checkin_date(guest_stay, checkin_date):
        guest_stay.guest_allocation.update_checkin_date(checkin_date)
        for guest_allocation in guest_stay.guest_allocation_history:
            guest_allocation.mark_deleted()

    def _delete_allocation_history_out_of_stay_boundary(
        self, checkin_date, checkout_date
    ):
        deleted_room_allocations = set()
        for room_allocation in self.room_allocation_history:
            if (
                room_allocation.checkout_date
                and room_allocation.checkout_date < checkin_date
            ):
                room_allocation.mark_deleted()
                deleted_room_allocations.add(room_allocation)
            if (
                room_allocation.checkin_date
                and room_allocation.checkin_date > checkout_date
            ):
                room_allocation.mark_deleted()
                deleted_room_allocations.add(room_allocation)
        for gs in self.guest_stays:
            for guest_allocation in gs.guest_allocation_history:
                if (
                    guest_allocation.checkout_date
                    and guest_allocation.checkout_date < checkin_date
                ):
                    guest_allocation.mark_deleted()
                if (
                    guest_allocation.checkin_date
                    and guest_allocation.checkin_date > checkout_date
                ):
                    guest_allocation.mark_deleted()
        return list(deleted_room_allocations)

    def _register_rate_plan_changed_event(
        self, rate_plan_details, existing_rate_plan_id, existing_rate_plan_dict
    ):
        if not rate_plan_details.rate_plan_id == existing_rate_plan_id:
            register_event(
                RoomPlanChangedEvent(
                    room_stay_id=self.room_stay_id,
                    room_type_id=self.room_type_id,
                    new_rate_plan_id=rate_plan_details.rate_plan_id,
                    old_rate_plan_id=existing_rate_plan_id,
                    new_rate_plan_name=rate_plan_details.name,
                    old_rate_plan_name=[
                        rp.name
                        for rp in existing_rate_plan_dict
                        if rp.rate_plan_id == existing_rate_plan_id
                    ][0],
                )
            )

    def get_ta_commission(self, room_night_ta_commission_id):
        room_night_ta_commission = self.room_night_ta_commission_map.get(
            room_night_ta_commission_id
        )
        if not room_night_ta_commission or room_night_ta_commission.deleted:
            raise ResourceNotFound(
                "RoomNightTaCommission",
                description="RoomStay:RoomNightTaCommission not found in {}:{}".format(
                    self.room_stay_id, room_night_ta_commission_id
                ),
            )
        return room_night_ta_commission

    def update_room_night_ta_commission(
        self,
        room_night_ta_commission_id,
        room_night_ta_commission_data: RoomNightTACommissionDto,
    ):
        ta_commission: RoomNightTACommission = self.get_ta_commission(
            room_night_ta_commission_id
        )
        ta_commission.update(room_night_ta_commission_data)

    def add_room_night_ta_commission(
        self,
        room_night_ta_commission_data: RoomNightTACommissionDto,
    ):
        self._fail_if_inactive_room_stay()
        ta_commission_id = max(
            [cm.ta_commission_id for cm in self._room_night_ta_commissions], default=0
        )
        ta_commission_id += 1
        ta_commission = RoomNightTACommission(
            ta_commission_id=ta_commission_id,
            pretax_amount=room_night_ta_commission_data.pretax_amount,
            posttax_amount=room_night_ta_commission_data.posttax_amount,
            room_night_price=room_night_ta_commission_data.room_night_price,
            status=room_night_ta_commission_data.status,
            applicable_on=room_night_ta_commission_data.applicable_on,
            is_allowance=room_night_ta_commission_data.is_allowance,
            reissued_on=room_night_ta_commission_data.reissued_on,
            locked_on=room_night_ta_commission_data.locked_on,
            linked_ta_commission_id=room_night_ta_commission_data.linked_ta_commission_id,
        )
        self._room_night_ta_commissions.append(ta_commission)
        self.room_night_ta_commission_map[ta_commission_id] = ta_commission
        return ta_commission

    def get_total_commission_amount(self):
        room_night_ta_commission = self.get_all_active_room_night_ta_commissions()
        base_currency = (
            crs_context.hotel_context.base_currency
            if (crs_context.hotel_context and crs_context.hotel_context.base_currency)
            else CurrencyType.INR
        )
        return (
            sum(rn_cm.posttax_amount for rn_cm in room_night_ta_commission)
            if room_night_ta_commission
            else Money(amount='0', currency=base_currency)
        )

    def get_total_room_rent_to_which_ta_commission_is_applied(self):
        room_night_ta_commission = self.get_all_active_room_night_ta_commissions()
        base_currency = (
            crs_context.hotel_context.base_currency
            if (crs_context.hotel_context and crs_context.hotel_context.base_currency)
            else CurrencyType.INR
        )
        return (
            sum(rn_cm.room_night_price for rn_cm in room_night_ta_commission)
            if room_night_ta_commission
            else Money(amount='0', currency=base_currency)
        )

    def nullify_locked_ta_commission_via_reissue(self, room_night_ta_commission_id):
        ta_commission: RoomNightTACommission = self.get_ta_commission(
            room_night_ta_commission_id
        )
        assert (
            ta_commission.status == TACommissionStatus.LOCKED
        ), "Reissue allowed only for locked entries"
        ta_commission.update_status(TACommissionStatus.NULLIFIED_BY_REISSUE)
        room_night_ta_commission_allowance_dto = (
            RoomNightTACommissionDto.create_allowance_entry_from_commission(
                ta_commission
            )
        )
        return self.add_room_night_ta_commission(room_night_ta_commission_allowance_dto)

    def cancel_all_ta_commission(self, commission_ids=None):
        if commission_ids is not None:
            ta_commissions_to_cancel = [
                self.get_ta_commission(cm_id) for cm_id in commission_ids
            ]
        else:
            ta_commissions_to_cancel = self.get_all_active_room_night_ta_commissions()
        for ta_commission in ta_commissions_to_cancel:
            if ta_commission.status == TACommissionStatus.CREATED:
                ta_commission.update_status(TACommissionStatus.CANCELLED)
            elif ta_commission.status == TACommissionStatus.LOCKED:
                self.nullify_locked_ta_commission_via_reissue(
                    ta_commission.ta_commission_id
                )

    def all_room_night_ta_commissions(self, include_deleted=True):
        return [
            rn_ta_cm
            for rn_ta_cm in self._room_night_ta_commissions
            if rn_ta_cm.deleted or include_deleted
        ]

    def lock_ta_commission(self, room_night_ta_commission_id=None):
        if room_night_ta_commission_id:
            ta_commission: RoomNightTACommission = self.get_ta_commission(
                room_night_ta_commission_id
            )
            ta_commission.lock()
            return
        for ta_commission in self.get_all_active_room_night_ta_commissions():
            if ta_commission.status == TACommissionStatus.CREATED:
                ta_commission.lock()

    def _can_mark_room_stay_noshow(self, cancellation_time):
        checkin_date = self.checkin_date.date()
        current_datetime = dateutils.current_datetime()

        if checkin_date < current_datetime.date():
            return True
        elif checkin_date > current_datetime.date():
            return False
        return current_datetime.time().hour >= int(cancellation_time)
