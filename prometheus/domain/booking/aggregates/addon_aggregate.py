import warnings

from prometheus.domain.billing.dto import ChargeData
from prometheus.domain.booking.entities.addon import Addon


class AddonAggregate(object):
    def __init__(self, addon: Addon, expenses=None):
        self.addon = addon
        self.expense_ids = [] if expenses is None else expenses

    def increment_version(self):
        self.addon.version += 1

    def link_expenses(self, expenses):
        self.expense_ids.extend(expenses)
        self.addon.mark_dirty()

    def clear_expenses(self):
        self.expense_ids = []
        self.addon.mark_dirty()

    def mark_deleted(self):
        self.addon.deleted = True
        self.addon.mark_dirty()

    @property
    def deleted(self):
        return self.addon.deleted

    @property
    def expenses(self):
        warnings.warn(
            "AddonAggregate.expenses will be deprecated. Please use AddonAggregate.expense_ids"
        )
        return self.expense_ids

    @expenses.setter
    def expenses(self, expense_ids):
        warnings.warn(
            "AddonAggregate.expenses will be deprecated. Please use AddonAggregate.expense_ids"
        )
        self.expense_ids = expense_ids
        self.addon.mark_dirty()

    @property
    def quantity(self):
        return self.addon.quantity

    @property
    def booking_id(self):
        return self.addon.booking_id

    @property
    def room_stay_id(self):
        return self.addon.room_stay_id

    @property
    def linked(self):
        return self.addon.linked

    @property
    def start_date(self):
        return self.addon.start_date

    @property
    def end_date(self):
        return self.addon.end_date

    @property
    def is_rate_plan_addon(self):
        return self.addon.is_rate_plan_addon

    def populate_data_from_rate_plan_inclusion_charge(
        self, inclusion_charge: ChargeData
    ):
        self.addon.bill_to_type = inclusion_charge.bill_to_type
        self.addon.charge_type = inclusion_charge.type
        self.addon.posttax_price = inclusion_charge.posttax_amount
        self.addon.pretax_price = inclusion_charge.pretax_amount
