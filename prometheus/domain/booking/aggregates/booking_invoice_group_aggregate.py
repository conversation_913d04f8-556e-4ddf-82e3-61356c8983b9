from prometheus.domain.booking.entities import BookingInvoiceGroup


class BookingInvoiceGroupAggregate(object):
    def __init__(self, booking_invoice_group: BookingInvoiceGroup):
        self.booking_invoice_group = booking_invoice_group

    def increment_version(self):
        self.booking_invoice_group.increment_version()

    def add_invoice_id(self, invoice_id):
        self.booking_invoice_group.invoice_ids.append(invoice_id)

    @property
    def booking_id(self):
        return self.booking_invoice_group.booking_id

    @property
    def booking_invoice_group_id(self):
        return self.booking_invoice_group.booking_invoice_group_id

    @property
    def is_last_checkout(self):
        return self.booking_invoice_group.is_last_checkout

    @property
    def is_advanced(self):
        return self.booking_invoice_group.is_advanced

    @property
    def room_wise_invoice_request(self):
        return self.booking_invoice_group.room_wise_invoice_request

    @property
    def request_datetime(self):
        return self.booking_invoice_group.request_datetime

    @property
    def invoice_ids(self):
        return self.booking_invoice_group.invoice_ids

    @property
    def status(self):
        return self.booking_invoice_group.status

    @property
    def newly_added_charge_ids(self):
        return self.booking_invoice_group.newly_added_charge_ids

    def update_status(self, status):
        self.booking_invoice_group.status = status
        self.booking_invoice_group.mark_dirty()
