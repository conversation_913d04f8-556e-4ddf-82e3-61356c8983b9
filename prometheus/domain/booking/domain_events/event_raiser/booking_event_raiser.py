from prometheus.domain.booking.domain_events import (
    BookingCancellationReversedEvent,
    BookingCancelledEvent,
    BookingConfirmedEvent,
    BookingDetailsUpdated,
    BookingMarkedNoShowEvent,
    BookingNoShowReversedEvent,
    BookingReCreatedEvent,
    CustomerUpdatedEvent,
    GuestAssignedEvent,
    GuestLifecycleEvent,
    GuestStayAddedEvent,
    GuestStayCancelledEvent,
    GuestStayMarkedNoShowEvent,
    RoomStayAddedEvent,
    RoomStayCancelledEvent,
    RoomStayDatesChangedEvent,
    RoomStayMarkedNoShowEvent,
    RoomStayNoShowReversedEvent,
    RoomStayOccupancyChangedEvent,
    RoomStayRoomTypeChangedEvent,
)
from prometheus.domain.booking.domain_events.ta_commission.events import (
    TACommissionAddedEvent,
    TACommissionUpdatedEvent,
)
from prometheus.domain.booking.dtos import (
    GuestStayEventData,
    NoShowEventData,
    RoomStayEventData,
)
from prometheus.domain.domain_events.domain_event_registry import register_event
from ths_common.value_objects import DateRange, Occupancy


class BookingEventRaiser(object):
    def __init__(self, booking_aggregate):
        self.booking_aggregate = booking_aggregate

    def raise_booking_recreated_domain_event(self):
        room_stay_event_data = []
        for room_stay in self.booking_aggregate.room_stays:
            guest_stay_event_data = []
            for guest_stay in room_stay.guest_stays:
                guest_id = (
                    guest_stay.guest_allocation.guest_id
                    if guest_stay.guest_allocation
                    else None
                )
                customer = (
                    self.booking_aggregate.get_customer(guest_id) if guest_id else None
                )
                guest_stay_event_data.append(
                    GuestStayEventData(
                        guest_stay_id=guest_stay.guest_stay_id,
                        guest_name=customer.first_name if customer else None,
                        guest_id=guest_id,
                        checkin_date=guest_stay.checkin_date,
                        checkout_date=guest_stay.checkout_date,
                    )
                )

            room_stay_event_data.append(
                RoomStayEventData(
                    room_stay.room_stay_id,
                    room_stay.room_type_id,
                    room_stay.checkin_date,
                    room_stay.checkout_date,
                    guest_stay_event_data,
                )
            )
        register_event(
            BookingReCreatedEvent(
                booking_id=self.booking_aggregate.booking.booking_id,
                reference_number=self.booking_aggregate.booking.reference_number,
                room_stay_event_data=room_stay_event_data,
            )
        )

    def raise_booking_cancelled_event(self, cancellation_reason):
        register_event(
            BookingCancelledEvent(
                booking_id=self.booking_aggregate.booking.booking_id,
                reason=cancellation_reason,
            )
        )

    def raise_booking_cancellation_reversed_event(self):
        register_event(
            BookingCancellationReversedEvent(
                booking_id=self.booking_aggregate.booking_id
            )
        )

    def raise_booking_marked_noshow_event(self):
        register_event(
            BookingMarkedNoShowEvent(
                booking_id=self.booking_aggregate.booking.booking_id
            )
        )

    def raise_booking_noshow_reversed_event(self):
        register_event(
            BookingNoShowReversedEvent(self.booking_aggregate.booking.booking_id)
        )

    def raise_booking_confirmed_event(self):
        register_event(
            BookingConfirmedEvent(booking_id=self.booking_aggregate.booking.booking_id)
        )

    def raise_guest_assigned_to_guest_stay_event(
        self, room_stay_id, guest_stay, customer
    ):
        register_event(
            GuestAssignedEvent(
                booking_id=self.booking_aggregate.booking.booking_id,
                room_stay_id=room_stay_id,
                guest_stay_id=guest_stay.guest_stay_id,
                guest_id=customer.customer_id,
            )
        )

    def raise_room_stay_cancelled_domain_event(
        self, guest_stays_cancelled_ids, room_stay, room_stay_id
    ):
        guest_stay_event_data = []
        for guest_stay_id in guest_stays_cancelled_ids:
            guest_stay = room_stay.get_guest_stay(guest_stay_id)
            guest_id = (
                guest_stay.guest_allocation.guest_id
                if guest_stay.guest_allocation
                else None
            )
            customer = (
                self.booking_aggregate.get_customer(guest_id) if guest_id else None
            )
            guest_stay_event_data.append(
                GuestStayEventData(
                    room_stay_id=room_stay_id,
                    room_type_id=room_stay.room_type_id,
                    guest_stay_id=guest_stay.guest_stay_id,
                    guest_id=guest_id,
                    guest_name=customer.first_name if customer else None,
                    checkin_date=guest_stay.checkin_date,
                    checkout_date=guest_stay.checkout_date,
                )
            )
        if guest_stay_event_data:
            register_event(RoomStayCancelledEvent(guest_stays=guest_stay_event_data))

    def raise_room_stay_marked_noshow_domain_event(
        self, room_stay, room_stay_id, room_stay_side_effect
    ):
        guest_stay_ids = [
            rsse.guest_stay_id for rsse in room_stay_side_effect.guest_stays
        ]
        guest_stays = room_stay.get_guest_stays(guest_stay_ids)
        noshow_event_data = []
        for guest_stay in guest_stays:
            guest_id = (
                guest_stay.guest_allocation.guest_id
                if guest_stay.guest_allocation
                else None
            )
            customer = (
                self.booking_aggregate.get_customer(guest_id) if guest_id else None
            )
            noshow_event_data.append(
                NoShowEventData(
                    room_stay_id=room_stay_id,
                    room_number=room_stay.room_allocation.room_no
                    if room_stay.room_allocation
                    else None,
                    guest_stay_id=guest_stay.guest_stay_id,
                    guest_name=customer.first_name if customer else None,
                    guest_id=guest_id,
                )
            )
        register_event(
            RoomStayMarkedNoShowEvent(
                booking_id=self.booking_aggregate.booking.booking_id,
                noshow_event_data=noshow_event_data,
            )
        )

    def raise_room_stay_noshow_reversed_domain_event(
        self, guest_stays_marked_noshow, room_stay
    ):
        guest_stay_event_data = []
        for guest_stay_side_effect in guest_stays_marked_noshow:
            guest_stay = self.booking_aggregate.get_guest_stay(
                room_stay.room_stay_id, guest_stay_side_effect.guest_stay_id
            )
            customer = (
                self.booking_aggregate.get_customer(guest_stay.guest_id)
                if guest_stay.guest_id
                else None
            )
            room_no = (
                room_stay.room_allocation.room_no
                if room_stay and room_stay.room_allocation
                else None
            )
            guest_stay_event_data.append(
                GuestStayEventData(
                    guest_stay_id=guest_stay.guest_stay_id,
                    guest_name=customer.first_name if customer else None,
                    guest_id=guest_stay.guest_id,
                    room_stay_id=room_stay.room_stay_id,
                    room_number=room_no,
                    room_type_id=room_stay.room_type_id,
                )
            )
        register_event(
            RoomStayNoShowReversedEvent(
                booking_id=self.booking_aggregate.booking.booking_id,
                guest_stays=guest_stay_event_data,
            )
        )

    def raise_guest_stay_added_domain_event(
        self, guest_stay, old_occupancies, room_stay, room_stay_id
    ):
        guest_id = (
            guest_stay.guest_allocation.guest_id
            if guest_stay.guest_allocation
            else None
        )
        customer = self.booking_aggregate.get_customer(guest_id) if guest_id else None
        room_no = (
            room_stay.room_allocation.room_no
            if room_stay and room_stay.room_allocation
            else None
        )
        register_event(
            GuestStayAddedEvent(
                guest_stays=[
                    GuestStayEventData(
                        room_stay_id=room_stay_id,
                        room_number=room_no,
                        room_type_id=room_stay.room_type_id,
                        guest_stay_id=guest_stay.guest_stay_id,
                        guest_id=guest_id,
                        guest_name=customer.first_name if customer else None,
                        checkin_date=guest_stay.checkin_date,
                        checkout_date=guest_stay.checkout_date,
                    )
                ]
            )
        )
        new_occupancy = room_stay.date_wise_occupancies
        occupancy_changes = []
        for date, occupancy in new_occupancy.items():
            old_occupancy = old_occupancies.get(date, Occupancy(0, 0))
            if old_occupancy != occupancy:
                occupancy_changes.append(
                    dict(
                        date=date,
                        old_occupancy=old_occupancy.total(),
                        new_occupancy=occupancy.total(),
                    )
                )
        register_event(
            RoomStayOccupancyChangedEvent(
                room_stay_id=room_stay_id, occupancy_changes=occupancy_changes
            )
        )

    def raise_room_stay_dates_changed_domain_event(
        self, old_stay_dates, room_stay, room_stay_id
    ):
        new_stay_dates = DateRange(
            start_date=room_stay.checkin_date, end_date=room_stay.checkout_date
        )
        room_type_id = room_stay.room_type_id
        room_number = (
            room_stay.room_allocation.room_no if room_stay.room_allocation else None
        )
        register_event(
            RoomStayDatesChangedEvent(
                room_stay_id=room_stay_id,
                room_number=room_number,
                room_type_id=room_type_id,
                old_stay_dates=old_stay_dates,
                new_stay_dates=new_stay_dates,
            )
        )

    def raise_room_stay_room_type_changed_domain_event(
        self, old_room_number, old_room_type_id, room_stay, room_stay_id
    ):
        new_room_type_id = room_stay.room_type_id
        new_room_number = (
            room_stay.room_allocation.room_no if room_stay.room_allocation else None
        )
        if new_room_type_id != old_room_type_id:
            register_event(
                RoomStayRoomTypeChangedEvent(
                    room_stay_id=room_stay_id,
                    old_room_number=old_room_number,
                    old_room_type_id=old_room_type_id,
                    new_room_number=new_room_number,
                    new_room_type_id=new_room_type_id,
                )
            )

    def raise_guest_stay_cancelled_domain_event(
        self, guest_stays_cancelled, old_occupancies, room_stay, room_stay_id
    ):
        guest_stay_event_data = []
        room_no = (
            room_stay.room_allocation.room_no
            if room_stay and room_stay.room_allocation
            else None
        )
        for guest_stay in guest_stays_cancelled:
            guest_id = (
                guest_stay.guest_allocation.guest_id
                if guest_stay.guest_allocation
                else None
            )
            customer = (
                self.booking_aggregate.get_customer(guest_id) if guest_id else None
            )
            guest_stay_event_data.append(
                GuestStayEventData(
                    room_stay_id=room_stay_id,
                    room_number=room_no,
                    room_type_id=room_stay.room_type_id,
                    guest_stay_id=guest_stay.guest_stay_id,
                    guest_id=guest_id,
                    guest_name=customer.first_name if customer else None,
                    checkin_date=guest_stay.checkin_date,
                    checkout_date=guest_stay.checkout_date,
                )
            )
        register_event(GuestStayCancelledEvent(guest_stays=guest_stay_event_data))
        new_occupancy = room_stay.date_wise_occupancies
        occupancy_changes = []
        for date, occupancy in new_occupancy.items():
            old_occupancy = old_occupancies.get(date, Occupancy(0, 0))
            if old_occupancy != occupancy:
                occupancy_changes.append(
                    dict(
                        date=date,
                        old_occupancy=old_occupancy.total(),
                        new_occupancy=occupancy.total(),
                    )
                )
        register_event(
            RoomStayOccupancyChangedEvent(
                room_stay_id=room_stay_id, occupancy_changes=occupancy_changes
            )
        )

    def raise_guest_stay_marked_noshow_domain_event(
        self, guest_stay, guest_stay_id, room_stay, room_stay_id
    ):
        guest_id = (
            guest_stay.guest_allocation.guest_id
            if guest_stay.guest_allocation
            else None
        )
        customer = self.booking_aggregate.get_customer(guest_id) if guest_id else None
        noshow_event_data = NoShowEventData(
            room_stay_id=room_stay_id,
            room_number=room_stay.room_allocation.room_no
            if room_stay.room_allocation
            else None,
            guest_stay_id=guest_stay_id,
            guest_name=customer.first_name if customer else None,
            guest_id=guest_id,
        )
        register_event(
            GuestStayMarkedNoShowEvent(
                booking_id=self.booking_aggregate.booking.booking_id,
                noshow_event_data=noshow_event_data,
            )
        )

    def register_booking_lifecycle_action_event_on_guest(
        self, room_stay, guests_action_data, action_type
    ):
        for guest_action_data in guests_action_data:
            guest_stay = room_stay.get_guest_stay(guest_action_data.guest_stay_id)
            customer = (
                self.booking_aggregate.get_customer(guest_stay.guest_id)
                if guest_stay.guest_id
                else None
            )
            guest_stay_data = GuestStayEventData(
                guest_stay_id=guest_stay.guest_stay_id,
                guest_name=customer.first_name if customer else None,
                guest_id=guest_stay.guest_id,
                checkin_date=guest_stay.actual_checkin_date,
                checkout_date=guest_stay.actual_checkout_date,
            )
            room_number = (
                room_stay.room_allocation.room_no if room_stay.room_allocation else None
            )
            room_stay_data = RoomStayEventData(
                room_stay.room_stay_id,
                room_stay.room_type_id,
                room_stay.actual_checkin_date,
                room_stay.actual_checkout_date,
                [guest_stay_data],
                room_number=room_number,
            )
            register_event(
                GuestLifecycleEvent(
                    guest_data=guest_stay_data,
                    room_stay_data=room_stay_data,
                    action_type=action_type,
                )
            )

    def raise_room_stay_added_domain_event(self, room_stay):
        guest_stay_event_data = []
        for guest_stay in room_stay.guest_stays:
            guest_id = (
                guest_stay.guest_allocation.guest_id
                if guest_stay.guest_allocation
                else None
            )
            customer = (
                self.booking_aggregate.get_customer(guest_id) if guest_id else None
            )
            guest_stay_event_data.append(
                GuestStayEventData(
                    guest_stay_id=guest_stay.guest_stay_id,
                    guest_name=customer.first_name if customer else None,
                    guest_id=guest_id,
                    checkin_date=guest_stay.checkin_date,
                    checkout_date=guest_stay.checkout_date,
                )
            )
        room_stay_event_data = [
            RoomStayEventData(
                room_stay.room_stay_id,
                room_stay.room_type_id,
                room_stay.checkin_date,
                room_stay.checkout_date,
                guest_stay_event_data,
            )
        ]
        register_event(
            RoomStayAddedEvent(
                booking_id=self.booking_aggregate.booking.booking_id,
                room_stays=room_stay_event_data,
            )
        )

    def register_booking_details_updated_event(self, attribute, old_value, new_value):
        register_event(
            BookingDetailsUpdated(
                booking_id=self.booking_aggregate.booking.booking_id,
                attribute=attribute,
                old_value=str(old_value) if old_value is not None else None,
                new_value=str(new_value) if new_value is not None else None,
            )
        )

    def register_ta_commission_added_event(self):
        register_event(
            TACommissionAddedEvent(
                booking_id=self.booking_aggregate.booking.booking_id,
                reference_number=self.booking_aggregate.booking.reference_number,
                commission_amount=self.booking_aggregate.get_total_commission_amount(),
            )
        )

    def register_ta_commission_updated_event(self, old_amount):
        new_commission_amount = self.booking_aggregate.get_total_commission_amount()
        if old_amount != new_commission_amount:
            register_event(
                TACommissionUpdatedEvent(
                    booking_id=self.booking_aggregate.booking.booking_id,
                    reference_number=self.booking_aggregate.booking.reference_number,
                    old_commission_amount=old_amount,
                    new_commission_amount=new_commission_amount,
                )
            )

    def register_customer_details_updated_event(
        self,
        room_stay,
        room_no,
        room_type_id,
        guest_stay_id,
        attr,
        old_value,
        new_value,
    ):
        register_event(
            CustomerUpdatedEvent(
                room_stay_id=room_stay.room_stay_id if room_stay else None,
                room_number=room_no,
                room_type_id=room_type_id,
                guest_stay_id=guest_stay_id,
                attribute=attr,
                old_value=str(old_value) if old_value is not None else None,
                new_value=str(new_value) if new_value is not None else None,
            )
        )
