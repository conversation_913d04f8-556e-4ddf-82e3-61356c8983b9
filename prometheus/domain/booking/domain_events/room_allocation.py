from typing import List

from prometheus.core.base_domain_event import MergeableDomain<PERSON>vent
from prometheus.domain.booking.domain_events.schema.booking import RoomFreedEventSchema
from prometheus.domain.booking.dtos.room_event_data import RoomEventData
from ths_common.constants.domain_event_constants import DomainEvent
from ths_common.exceptions import DomainEventMergerException


class RoomFreedEvent(MergeableDomainEvent):
    def __init__(self, rooms: List[RoomEventData]):
        self.rooms = rooms

    def serialize(self):
        serialized = RoomFreedEventSchema(many=True).dump(self.rooms).data
        return serialized

    def update_mapping(self, **kwargs):
        room_type_map = kwargs.get('room_type_map')
        for room in self.rooms:
            room.room_type_name = room_type_map[room.room_type_id].room_type.type

    def event_type(self):
        return DomainEvent.ROOM_FREED

    def merge(self, mergeable_domain_event):
        if not isinstance(mergeable_domain_event, RoomFreedEvent):
            raise DomainEventMergerException()

        self.rooms.extend(mergeable_domain_event.rooms)

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, RoomFreedEvent)
