from typing import List

from prometheus.core.base_domain_event import MergeableDomain<PERSON>vent
from prometheus.domain.booking.domain_events.schema.booking import (
    DisallowChargeAdditionEventSchema,
)
from prometheus.domain.booking.dtos.disallow_charge_addition_data import DCAEventData
from ths_common.constants.domain_event_constants import DomainEvent
from ths_common.exceptions import DomainEventMergerException


class DisallowChargeAdditionChangedEvent(MergeableDomainEvent):
    def __init__(self, dca_event_data: List[DCAEventData]):
        self.dca_event_data = dca_event_data

    def serialize(self):
        serialized = (
            DisallowChargeAdditionEventSchema(many=True).dump(self.dca_event_data).data
        )
        return serialized

    def update_mapping(self, **kwargs):
        room_type_map = kwargs.get('room_type_map')
        for event in self.dca_event_data:
            event.set_room_type(room_type_map)

    def event_type(self):
        return DomainEvent.DISALLOW_CHARGE_ADDITION_CHANGED

    def merge(self, mergeable_domain_event):
        if not isinstance(mergeable_domain_event, DisallowChargeAdditionChangedEvent):
            raise DomainEventMergerException()

        self.dca_event_data.extend(mergeable_domain_event.dca_event_data)

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, DisallowChargeAdditionChangedEvent)
