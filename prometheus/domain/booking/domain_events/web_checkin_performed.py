from prometheus.core.base_domain_event import BaseDomainEvent
from prometheus.domain.booking.domain_events.schema.booking import (
    WebCheckInModifiedEventSchema,
    WebCheckInPerformedEventSchema,
)
from ths_common.constants.domain_event_constants import DomainEvent


class WebCheckInPerformedEvent(BaseDomainEvent):
    def __init__(self, booking_id, web_checkin_id, status, guest_ids):
        self.booking_id = booking_id
        self.web_checkin_id = web_checkin_id
        self.status = status
        self.guest_ids = guest_ids

    def serialize(self):
        serialized = WebCheckInPerformedEventSchema().dump(self).data
        return serialized

    def update_mapping(self, **kwargs):
        return

    def event_type(self):
        return DomainEvent.WEB_CHECKIN_COMPLETED_BY_GUESTS


class WebCheckInModifiedEvent(BaseDomainEvent):
    def __init__(self, booking_id, web_checkin_id, status, guest_ids):
        self.booking_id = booking_id
        self.web_checkin_id = web_checkin_id
        self.status = status
        self.guest_ids = guest_ids

    def serialize(self):
        serialized = WebCheckInModifiedEventSchema().dump(self).data
        return serialized

    def update_mapping(self, **kwargs):
        return

    def event_type(self):
        return DomainEvent.WEB_CHECKIN_MODIFIED
