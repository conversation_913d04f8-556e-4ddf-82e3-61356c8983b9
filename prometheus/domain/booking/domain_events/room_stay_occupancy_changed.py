from prometheus.core.base_domain_event import BaseDomainEvent
from prometheus.domain.booking.domain_events.schema.booking import (
    RoomStayOccupancyChangedEventSchema,
)
from ths_common.constants.domain_event_constants import DomainEvent


class RoomStayOccupancyChangedEvent(BaseDomainEvent):
    def __init__(self, room_stay_id, occupancy_changes):
        self.room_stay_id = room_stay_id
        self.occupancy_changes = occupancy_changes

    def serialize(self):
        serialized = RoomStayOccupancyChangedEventSchema().dump(self).data
        return serialized

    def update_mapping(self, **kwargs):
        return

    def event_type(self):
        return DomainEvent.ROOM_STAY_OCCUPANCY_CHANGED
