from prometheus.core.base_domain_event import BaseDomain<PERSON>vent, MergeableDomainEvent
from prometheus.domain.booking.domain_events.schema.booking import (
    BookingCreatedEventSchema,
)
from prometheus.domain.booking.domain_events.ta_commission.schema import (
    TACommissionAddedEventSchema,
    TACommissionUpdatedEventSchema,
)
from ths_common.constants.domain_event_constants import DomainEvent
from ths_common.exceptions import DomainEventMergerException


class TACommissionAddedEvent(BaseDomainEvent):
    def __init__(self, booking_id, reference_number, commission_amount):
        self.booking_id = booking_id
        self.reference_number = reference_number
        self.commission_amount = commission_amount

    def serialize(self):
        serialized = TACommissionAddedEventSchema().dump(self).data
        return serialized

    def update_mapping(self, **kwargs):
        pass

    def event_type(self):
        return DomainEvent.TA_COMMISSION_ADDED


class TACommissionUpdatedEvent(MergeableDomainEvent):
    def __init__(
        self, booking_id, reference_number, old_commission_amount, new_commission_amount
    ):
        self.booking_id = booking_id
        self.reference_number = reference_number
        self.old_commission_amount = old_commission_amount
        self.new_commission_amount = new_commission_amount

    def serialize(self):
        serialized = TACommissionUpdatedEventSchema().dump(self).data
        return serialized

    def update_mapping(self, **kwargs):
        pass

    def event_type(self):
        return DomainEvent.TA_COMMISSION_UPDATED

    def merge(self, mergeable_domain_event):
        if not self.can_merge(mergeable_domain_event):
            raise DomainEventMergerException()
        self.new_commission_amount = mergeable_domain_event.new_commission_amount

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, TACommissionUpdatedEvent)
