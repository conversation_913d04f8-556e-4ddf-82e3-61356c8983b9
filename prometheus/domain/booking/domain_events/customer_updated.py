from prometheus.core.base_domain_event import MergeableDomain<PERSON>vent
from prometheus.domain.booking.domain_events.schema.booking import (
    CustomerUpdatedEventSchema,
)
from ths_common.constants.domain_event_constants import DomainEvent
from ths_common.exceptions import DomainEventMergerException


class CustomerUpdatedEvent(MergeableDomainEvent):
    def __init__(
        self,
        room_stay_id,
        room_number,
        guest_stay_id,
        room_type_id,
        attribute=None,
        old_value=None,
        new_value=None,
        details=None,
    ):
        self.room_stay_id = room_stay_id
        self.room_number = room_number
        self.guest_stay_id = guest_stay_id
        self.room_type_id = room_type_id
        self.room_type_name = None
        if not details:
            self.details = [
                {"attribute": attribute, "old_value": old_value, "new_value": new_value}
            ]
        else:
            self.details = details

    def serialize(self):
        serialized = CustomerUpdatedEventSchema().dump(self).data
        return serialized

    def update_mapping(self, **kwargs):
        room_type_map = kwargs.get('room_type_map')
        if self.room_type_id:
            self.room_type_name = room_type_map.get(self.room_type_id).room_type.type

    def event_type(self):
        return DomainEvent.GUEST_DETAILS_MODIFIED

    def merge(self, mergeable_domain_event):
        if not self.can_merge(mergeable_domain_event):
            raise DomainEventMergerException()

        self.details.extend(mergeable_domain_event.details)

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, CustomerUpdatedEvent)
