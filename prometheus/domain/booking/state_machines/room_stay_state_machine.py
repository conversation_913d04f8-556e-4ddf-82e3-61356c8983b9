from collections import Counter

from treebo_commons.utils import dateutils

from prometheus.core.globals import crs_context
from prometheus.domain.booking.errors import BookingErrors
from prometheus.domain.booking.exceptions import InvalidActionError, InvalidStateError
from prometheus.domain.booking.peek_machine import PeekMachine
from ths_common.constants.booking_constants import BookingActions, BookingStatus
from ths_common.constants.user_constants import PrivilegeCode


class RoomStayStateMachine(object):
    def __init__(self, model, states, initial_status):
        self.model = model
        assert hasattr(model, 'on_state_transition')
        self.machine = PeekMachine(
            model=self,
            states=states,
            initial=initial_status,
            auto_transitions=False,
            after_state_change='on_state_transition',
            name="RoomStay",
        )
        self.setup_state_transition()

    def on_state_transition(self):
        self.model.on_state_transition(BookingStatus(self.state))

    def setup_state_transition(self):
        transitions = [
            {
                'trigger': BookingActions.CHECKIN.value,
                'source': [
                    BookingStatus.RESERVED.value,
                    BookingStatus.PART_CHECKIN.value,
                    BookingStatus.PART_CHECKOUT.value,
                    BookingStatus.CHECKED_OUT.value,
                ],
                'dest': BookingStatus.CHECKED_IN.value,
                'unless': ['is_future_room_stay'],
            },
            {
                'trigger': BookingActions.CHECKOUT.value,
                'source': [
                    BookingStatus.CHECKED_IN.value,
                    BookingStatus.PART_CHECKOUT.value,
                ],
                'dest': BookingStatus.CHECKED_OUT.value,
            },
            {
                'trigger': BookingActions.CANCEL.value,
                'source': [BookingStatus.RESERVED.value],
                'dest': BookingStatus.CANCELLED.value,
            },
            {
                'trigger': BookingActions.NOSHOW.value,
                'source': [BookingStatus.RESERVED.value],
                'dest': BookingStatus.NOSHOW.value,
                'conditions': 'has_current_time_crossed_midnight_of_checkin_datetime',
            },
            {
                'trigger': 'part_checkin',  # Trigger internal to domain
                'source': [
                    BookingStatus.RESERVED.value,
                    BookingStatus.PART_CHECKIN.value,
                    BookingStatus.CHECKED_IN.value,
                    BookingStatus.PART_CHECKOUT.value,
                    BookingStatus.CHECKED_OUT.value,
                    BookingStatus.PART_CHECKIN,
                ],
                'dest': BookingStatus.PART_CHECKIN.value,
                'unless': ['is_future_room_stay'],
            },
            {
                'trigger': 'part_checkout',  # Trigger internal to domain
                'source': [
                    BookingStatus.CHECKED_IN.value,
                    BookingStatus.PART_CHECKIN.value,
                    BookingStatus.PART_CHECKOUT.value,
                    BookingStatus.CHECKED_OUT.value,
                ],
                'dest': BookingStatus.PART_CHECKOUT.value,
            },
            {
                'trigger': BookingActions.UNDO_NOSHOW.value,  # Trigger internal to domain
                'source': [BookingStatus.NOSHOW.value],
                'dest': BookingStatus.RESERVED.value,
            },
            {
                'trigger': BookingActions.UNDO_CANCEL.value,  # Trigger internal to domain
                'source': [BookingStatus.CANCELLED.value],
                'dest': BookingStatus.RESERVED.value,
            },
            {
                'trigger': BookingActions.UNDO_CHECKIN.value,  # Trigger internal to domain
                'source': [
                    BookingStatus.PART_CHECKIN.value,
                    BookingStatus.CHECKED_IN.value,
                ],
                'dest': BookingStatus.RESERVED.value,
            },
        ]
        self.machine.add_transitions(transitions)

    def is_future_room_stay(self):
        hotel_context = crs_context.get_hotel_context()
        return dateutils.to_date(self.model.checkin_date) > hotel_context.current_date()

    def has_current_time_crossed_midnight_of_checkin_datetime(self):
        current_datetime = dateutils.current_datetime()
        privileges = crs_context.privileges_as_dict
        if (
            privileges
            and PrivilegeCode.CAN_CANCEL_WALKIN_BOOKING_BEFORE_TIME in privileges
        ):
            checkin_date = self.model.checkin_date.date()
            current_datetime = dateutils.current_datetime()
            cancellation_time = privileges[
                PrivilegeCode.CAN_CANCEL_WALKIN_BOOKING_BEFORE_TIME
            ][0]
            if checkin_date < current_datetime.date():
                return True
            elif checkin_date > current_datetime.date():
                return False
            return current_datetime.time().hour >= int(cancellation_time)

        return current_datetime > dateutils.datetime_at_midnight(
            dateutils.add(self.model.checkin_date, days=1)
        )

    def trigger_state_transition(self, guest_stays):
        counter = Counter([rs.status for rs in guest_stays])

        if (
            counter.get(BookingStatus.CHECKED_OUT)
            and counter.get(BookingStatus.RESERVED)
            and not counter.get(BookingStatus.CHECKED_IN)
        ):
            raise InvalidStateError(
                description="Booking cannot be in part checked-out when no other guest is checked-in. "
                "Please mark no-show guests yet to be checked-in to allow complete checkout."
            )

        if counter.get(BookingStatus.RESERVED) == len(guest_stays):
            if self.is_checked_in() or self.is_part_checked_in():
                self.undo_checkin()
            elif self.is_noshow():
                self.undo_noshow()

        elif counter.get(BookingStatus.CHECKED_OUT, 0) == len(guest_stays):
            if not self.is_checked_out():
                self.checkout()

        elif counter.get(BookingStatus.CHECKED_IN, 0) == len(guest_stays):
            if not self.is_checked_in():
                success = self.checkin()
                if not success:
                    raise InvalidActionError(
                        error=BookingErrors.ROOM_STAY_CHECKIN_NOT_ALLOWED,
                        extra_payload=dict(room_stay_id=self.model.room_stay_id),
                    )

        elif 0 < counter.get(BookingStatus.CHECKED_OUT, 0) < len(guest_stays):
            self.part_checkout()

        elif 0 < counter.get(BookingStatus.CHECKED_IN, 0) < len(guest_stays):
            success = self.part_checkin()
            if not success:
                raise InvalidActionError(
                    error=BookingErrors.ROOM_STAY_CHECKIN_NOT_ALLOWED,
                    extra_payload=dict(room_stay_id=self.model.room_stay_id),
                )
