from treebo_commons.utils import dateutils


class BookingOwnerDetailsDto:
    def __init__(self, first_name, last_name, phone, email):
        self.name = f"{first_name} {last_name or ''}".strip()
        self.phone = phone
        self.email = email


class BookingDetailsDto:
    def __init__(self, booking_details):
        self.bill_id = booking_details.bill_id
        self.hotel_id = booking_details.hotel_id
        self.reference_number = booking_details.reference_number
        self.checkin_date = dateutils.to_date(booking_details.checkin_date)
        self.checkout_date = dateutils.to_date(booking_details.checkout_date)
        self.channel_code = booking_details.channel_code
        self.subchannel_code = booking_details.subchannel_code
        self.seller_model = booking_details.seller_model
        self.company_details = booking_details.company_details
        self.travel_agent_details = booking_details.travel_agent_details
        self.checkout_date_time = booking_details.checkout_date
        self.status = booking_details.status
        self.actual_checkout_date = booking_details.actual_checkout_date
        self.cancellation_datetime = booking_details.cancellation_datetime
        self.actual_checkout_calendar_date = (
            booking_details.actual_checkout_calendar_date
        )
        self.booking_owner = (
            BookingOwnerDetailsDto(
                booking_details.first_name,
                booking_details.last_name,
                booking_details.phone,
                booking_details.email,
            )
            if hasattr(booking_details, 'first_name')
            else None
        )
        self.booking_id = booking_details.booking_id
