class GuestStayEventData(object):
    def __init__(
        self,
        guest_stay_id,
        guest_name=None,
        guest_id=None,
        checkin_date=None,
        checkout_date=None,
        room_stay_id=None,
        room_number=None,
        room_type_id=None,
    ):
        self.guest_stay_id = guest_stay_id
        self.guest_id = guest_id
        self.guest_name = guest_name
        self.checkin_date = checkin_date
        self.checkout_date = checkout_date
        self.room_stay_id = room_stay_id
        self.room_number = room_number
        self.room_type_id = room_type_id
        self.room_type_name = None
