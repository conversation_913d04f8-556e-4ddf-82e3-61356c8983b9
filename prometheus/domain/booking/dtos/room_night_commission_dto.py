from treebo_commons.utils.dateutils import current_date

from ths_common.constants.booking_constants import TACommissionStatus


class RoomNightTACommissionDto(object):
    def __init__(
        self,
        applicable_on,
        room_night_price,
        pretax_amount,
        posttax_amount,
        status=None,
        ta_commission_id=None,
        reissued_on=None,
        locked_on=None,
        is_allowance=False,
        linked_ta_commission_id=None,
    ):
        self.applicable_on = applicable_on
        self.pretax_amount = pretax_amount
        self.posttax_amount = posttax_amount
        self.status = status or TACommissionStatus.CREATED
        self.room_night_price = room_night_price
        self.ta_commission_id = ta_commission_id
        self.reissued_on = reissued_on
        self.locked_on = locked_on
        self.is_allowance = is_allowance
        self.linked_ta_commission_id = linked_ta_commission_id

    @staticmethod
    def initialise_with_room_night_price(
        applicable_on,
        room_night_price,
        ta_commission_id=None,
        reissued_on=None,
        locked_on=None,
        status=None,
    ):
        return RoomNightTACommissionDto(
            applicable_on=applicable_on,
            room_night_price=room_night_price,
            pretax_amount=None,
            posttax_amount=None,
            ta_commission_id=ta_commission_id,
            reissued_on=reissued_on,
            locked_on=locked_on,
            status=status,
        )

    @staticmethod
    def create_allowance_entry_from_commission(domain_entity):
        return RoomNightTACommissionDto(
            applicable_on=domain_entity.applicable_on,
            room_night_price=domain_entity.room_night_price,
            pretax_amount=domain_entity.pretax_amount,
            posttax_amount=domain_entity.posttax_amount,
            is_allowance=True,
            reissued_on=current_date(),
            locked_on=current_date(),
            linked_ta_commission_id=domain_entity.ta_commission_id,
            status=TACommissionStatus.LOCKED,
        )
