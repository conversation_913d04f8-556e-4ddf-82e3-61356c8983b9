from datetime import datetime

from treebo_commons.utils.dateutils import current_datetime

from prometheus import crs_context
from prometheus.domain.booking.entities.addon import Addon
from ths_common.constants.booking_constants import ExpenseAddedBy, ExpenseStatus
from ths_common.constants.catalog_constants import SkuName
from ths_common.constants.funding_constants import FundingExpenseItem
from ths_common.value_objects import ExpenseServiceContext


class ExpenseDto:
    def __init__(
        self,
        room_stay_id: str,
        status: ExpenseStatus,
        comments: str,
        created_at: datetime,
        applicable_date: datetime,
        added_by=None,
        guests=None,
        via_addon=False,
        extra_information=None,
        sku_id=None,
        expense_item_id=None,
        sku_name=None,
        via_rate_plan=False,
        linked=False,
        pretax_amount=None,
        posttax_amount=None,
        sku_category_id=None,
        service_context: ExpenseServiceContext = None,
        **kwargs
    ):
        self.expense_item_id = expense_item_id
        self.room_stay_id = room_stay_id
        self.status = status
        self.comments = comments
        self.created_at = created_at
        self.applicable_date = applicable_date
        self.added_by = added_by
        self.guests = guests if guests is not None else []
        self.via_addon = via_addon
        self.extra_information = extra_information
        self.sku_id = sku_id
        self.sku_name = sku_name
        self.sku_category_id = sku_category_id
        self.via_rate_plan = via_rate_plan
        self.linked = linked
        self.pretax_amount = pretax_amount
        self.posttax_amount = posttax_amount
        self.service_context = service_context
        # NOTE: ChargeDto is set for inclusion expense dto creation from RoomStayData.create method
        self.charge_dto = None

    @classmethod
    def from_addon(cls, addon: Addon, applicable_date):
        return cls(
            expense_item_id=addon.expense_item_id,
            sku_id=addon.sku_id,
            room_stay_id=addon.room_stay_id,
            status=ExpenseStatus.CREATED,
            sku_name=addon.name,
            comments=addon.name,
            added_by=addon.added_by,
            created_at=datetime.now(),
            applicable_date=applicable_date,
            via_addon=True,
            via_rate_plan=addon.is_rate_plan_addon,
            linked=addon.linked,
        )

    @classmethod
    def from_existing_expense(cls, expense, applicable_date):
        return cls(
            expense_item_id=expense.expense_item_id,
            sku_id=expense.sku_id,
            room_stay_id=expense.room_stay_id,
            status=ExpenseStatus.CREATED,
            sku_name=expense.sku_name,
            comments=expense.sku_name,
            added_by=expense.added_by,
            created_at=datetime.now(),
            applicable_date=applicable_date,
            via_addon=expense.via_addon,
            via_rate_plan=expense.via_rate_plan,
            linked=expense.linked,
            service_context=expense.service_context,
        )

    @classmethod
    def from_room_stay_and_expense_item(
        cls, room_stay, guests, expense_item, applicable_date
    ):
        return cls(
            expense_item_id=expense_item.expense_item_id,
            sku_id=expense_item.sku_id,
            sku_name=expense_item.name,
            room_stay_id=room_stay.room_stay_id,
            status=ExpenseStatus.CONSUMED,
            added_by=ExpenseAddedBy.TREEBO
            if crs_context.is_treebo_tenant()
            else ExpenseAddedBy.HOTEL,
            created_at=datetime.now(),
            comments='',
            applicable_date=applicable_date,
            guests=guests,
        )

    @classmethod
    def from_dict(cls, expense_dict: dict):
        expense_dict['created_at'] = current_datetime()
        return cls(**expense_dict)

    # TODO: Change these below conditions for sku_id
    def is_cancellation_noshow_expense(self):
        return self.expense_item_id in ["no_show", "booking_cancellation"]

    def is_cancellation_expense(self):
        return self.expense_item_id in ["booking_cancellation"]

    def is_noshow_expense(self):
        return self.expense_item_id in ["no_show"]

    def is_transferred_expense(self):
        return self.sku_name == SkuName.TRANSFERRED_CHARGE.value

    def is_treebo_funding_expense(self):
        return self.expense_item_id in (
            FundingExpenseItem.TREEBO_AUTO_FUNDING.value,
            FundingExpenseItem.TREEBO_MANUAL_FUNDING.value,
        )

    @staticmethod
    def from_rate_plan_inclusion(
        rate_plan_inclusion, expense_item, applicable_date, room_stay
    ):
        return ExpenseDto.from_dict(
            dict(
                room_stay_id=room_stay.room_stay_id,
                status=ExpenseStatus.CREATED,
                comments='',
                applicable_date=applicable_date,
                sku_id=rate_plan_inclusion.sku_id,
                sku_name=expense_item.name,
                expense_item_id=expense_item.expense_item_id,
                sku_category_id=expense_item.sku_category_id,
                via_rate_plan=True,
                linked=False,
                pretax_amount=rate_plan_inclusion.pretax_amount,
                posttax_amount=rate_plan_inclusion.posttax_amount,
                via_addon=True,
            )
        )
