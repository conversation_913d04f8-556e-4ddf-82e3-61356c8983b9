from typing import List

from treebo_commons.utils import dateutils

from prometheus.core.globals import HotelDtoForContext
from ths_common.value_objects import (
    BookingSource,
    GSTDetails,
    LoyaltyProgramDetails,
    Name,
)


class HouseViewBookingOwnerDto:
    def __init__(
        self, booking_id, customer_id, gst_details: GSTDetails, is_primary, name: Name
    ):
        self.booking_id = booking_id
        self.customer_id = customer_id
        self.gst_details = gst_details
        self.is_primary = is_primary
        self.name = name

    @property
    def full_name(self):
        return self.name.full_name if self.name else None


class HouseViewCustomerDto:
    def __init__(
        self,
        booking_id,
        customer_id,
        gst_details: GSTDetails,
        dummy: bool,
        is_primary,
        name: Name,
        is_vip: bool,
        loyalty_program_details: LoyaltyProgramDetails,
    ):
        self.booking_id = booking_id
        self.customer_id = customer_id
        self.gst_details = gst_details
        self.dummy = dummy
        self.is_primary = is_primary
        self.name = name
        self.is_vip = is_vip
        self.loyalty_program_details = loyalty_program_details

    @property
    def full_name(self):
        return self.name.full_name if self.name else None


class HouseViewGuestStayDto:
    def __init__(self, booking_id, room_stay_id, guest_stay_id, guest_id, status):
        self.booking_id = booking_id
        self.room_stay_id = room_stay_id
        self.guest_stay_id = guest_stay_id
        self.guest_id = guest_id
        self.status = status


class HouseViewRoomAllocationDto:
    def __init__(
        self,
        booking_id,
        room_stay_id,
        room_allocation_id,
        room_id,
        room_type_id,
        checkin_date,
        checkout_date,
        overridden,
        is_current,
        hotel_dto_for_context,
    ):
        self.booking_id = booking_id
        self.room_stay_id = room_stay_id
        self.room_allocation_id = room_allocation_id
        self.room_id = room_id
        self.room_type_id = room_type_id
        self.checkin_date = checkin_date
        self.checkout_date = checkout_date
        self.overridden = overridden
        self.is_current = is_current

        self.hotel_dto_for_context = hotel_dto_for_context

        self.stay_start = (
            hotel_dto_for_context.hotel_checkin_date(self.checkin_date)
            if self.checkin_date
            else None
        )
        self.stay_end = None
        if self.checkout_date:
            if self.is_current:
                self.stay_end = hotel_dto_for_context.hotel_checkout_date(
                    self.checkout_date
                )
            else:
                self.stay_end = hotel_dto_for_context.hotel_checkin_date(
                    self.checkout_date
                )

        self.houseview_label = None

    def set_houseview_label(self, houseview_label):
        self.houseview_label = houseview_label


class HouseViewRoomStayDto:
    def __init__(
        self,
        booking_id,
        room_stay_id,
        checkin_date,
        checkout_date,
        actual_checkin_date,
        actual_checkout_date,
        room_type_id,
        status,
        guest_stays: List[HouseViewGuestStayDto],
        room_allocations: List[HouseViewRoomAllocationDto],
        hotel_dto_for_context,
        is_overflow=False,
    ):
        self.booking_id = booking_id
        self.room_stay_id = room_stay_id
        self.checkin_date = checkin_date
        self.checkout_date = checkout_date
        self.actual_checkin_date = actual_checkin_date
        self.actual_checkout_date = actual_checkout_date
        self.room_type_id = room_type_id
        self.status = status
        self.is_overflow = is_overflow
        self.guest_stays = guest_stays
        self.room_allocation_history = [
            allocation for allocation in room_allocations if not allocation.is_current
        ]
        self.room_allocation = next(
            (allocation for allocation in room_allocations if allocation.is_current),
            None,
        )
        self.room_id = self.room_allocation.room_id if self.room_allocation else None

        self.hotel_dto_for_context = hotel_dto_for_context

        self.stay_start = dateutils.to_date(self.checkin_date)
        self.stay_end = None
        if not self.actual_checkout_date:
            checkout_date = dateutils.to_date(self.checkout_date)
            self.stay_end = (
                dateutils.add(checkout_date, 1)
                if checkout_date == self.stay_start
                else checkout_date
            )
        else:
            checkout_date = hotel_dto_for_context.hotel_checkout_date(
                self.actual_checkout_date
            )
            self.stay_end = (
                dateutils.add(checkout_date, 1)
                if checkout_date == self.stay_start
                else checkout_date
            )

        self.houseview_label = None

    def set_room_allocation_houseview_labels(self, customers, houseview_label=None):
        customer_group_by_id = {cust.customer_id: cust for cust in customers}
        guests = [
            customer_group_by_id.get(guest_stay.guest_id)
            for guest_stay in self.guest_stays
        ]
        guest_name = next(
            (guest.full_name for guest in guests if not guest.dummy), None
        )
        houseview_label = guest_name or houseview_label

        if not (self.room_allocation or self.room_allocation_history):
            self.houseview_label = houseview_label

        else:
            if self.room_allocation:
                self.room_allocation.set_houseview_label(houseview_label)

            for room_allocation in self.room_allocation_history:
                room_allocation.set_houseview_label(houseview_label)


class HouseViewBookingDto:
    def __init__(
        self,
        booking_id,
        reference_number,
        bill_id,
        checkin_date,
        checkout_date,
        actual_checkin_date,
        actual_checkout_date,
        status,
        group_name,
        source: BookingSource,
        comments,
        extra_information,
        hold_till,
        booking_owner_id: str,
        customers: List[HouseViewCustomerDto],
        room_stays: List[HouseViewRoomStayDto],
        version,
        hotel_dto_for_context: HotelDtoForContext,
        created_at=None,
        modified_at=None,
        company_legal_name=None,
        travel_agent_legal_name=None,
    ):
        self.booking_id = booking_id
        self.reference_number = reference_number
        self.bill_id = bill_id
        self.checkin_date = checkin_date
        self.checkout_date = checkout_date
        self.actual_checkin_date = actual_checkin_date
        self.actual_checkout_date = actual_checkout_date
        self.status = status
        self.group_name = group_name
        self.source = source
        self.comments = comments
        self.extra_information = extra_information
        self.hold_till = hold_till
        self.customers = customers
        self.booking_owner = None
        for customer in self.customers:
            if customer.customer_id == booking_owner_id:
                self.booking_owner = customer
        self.room_stays = room_stays
        self.version = version
        self.created_at = created_at
        self.modified_at = modified_at
        self.web_checkin_status = None

        self.hotel_dto_for_context = hotel_dto_for_context
        self.stay_start = dateutils.to_date(self.checkin_date)
        self.stay_end = None
        if not self.actual_checkout_date:
            checkout_date = dateutils.to_date(self.checkout_date)
            self.stay_end = (
                dateutils.add(checkout_date, 1)
                if checkout_date == self.stay_start
                else checkout_date
            )
        else:
            checkout_date = self.hotel_dto_for_context.hotel_checkout_date(
                self.actual_checkout_date
            )
            self.stay_end = (
                dateutils.add(checkout_date, 1)
                if checkout_date == self.stay_start
                else checkout_date
            )

        self.balance_to_clear_before_checkout = None
        self.company_legal_name = company_legal_name
        self.travel_agent_legal_name = travel_agent_legal_name

    def set_room_allocation_houseview_labels(self):
        houseview_label = (
            self.group_name
            or self.travel_agent_legal_name
            or self.company_legal_name
            or self.booking_owner.full_name
            if self.booking_owner
            else None
        )

        for room_stay in self.room_stays:
            room_stay.set_room_allocation_houseview_labels(
                self.customers, houseview_label=houseview_label
            )

    def tag_room_stay_overflows(self, overflowing_room_stay_ids):
        for room_stay in self.room_stays:
            if room_stay.room_stay_id in overflowing_room_stay_ids:
                room_stay.is_overflow = True

    def update_balance_to_clear_before_checkout(self, balance):
        self.balance_to_clear_before_checkout = balance

    def update_web_checkin_status(self, web_checkin_status):
        self.web_checkin_status = web_checkin_status


class HouseViewTodaysStatsDTO:
    def __init__(
        self, expected_checkin_count, expected_checkout_count, active_dnr_count
    ):
        self.expected_checkin_count = expected_checkin_count
        self.expected_checkout_count = expected_checkout_count
        self.active_dnr_count = active_dnr_count
