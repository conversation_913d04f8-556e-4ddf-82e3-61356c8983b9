class NewBookingDomainDto:
    def __init__(
        self,
        hotel_id=None,
        room_stays=None,
        booking_owner=None,
        source=None,
        reference_number=None,
        status=None,
        comments=None,
        extra_information=None,
        fees=None,
        hold_till=None,
        guests=None,
        payments=None,
        company_details=None,
        travel_agent_details=None,
        account_details=None,
        group_name=None,
        default_billed_entity_category=None,
        rate_plans=None,
        default_payment_instruction=None,
        default_billed_entity_category_for_extras=None,
        default_payment_instruction_for_extras=None,
        segments=None,
        guarantee_information=None,
        discount_details=None,
    ):
        self.hotel_id = hotel_id
        self.room_stays = room_stays
        self.booking_owner = booking_owner
        self.source = source
        self.reference_number = reference_number

        self.comments = comments
        self.extra_information = extra_information
        self.fees = fees
        self.hold_till = hold_till
        self.guests = guests
        self.payments = payments

        # Status in new booking dto can be reserved, temporary or confirmed
        # If it's not confirmed, then we'll use status as temporary or reserved internally, based on hold_till value
        self.status = status

        # To be set later from booking creation application service method, while creating rate plan dto
        self.rate_plans = rate_plans
        self.company_details = company_details
        self.travel_agent_details = travel_agent_details
        self.account_details = account_details
        self.group_name = group_name
        self.default_billed_entity_category = default_billed_entity_category
        self.default_payment_instruction = default_payment_instruction
        self.default_billed_entity_category_for_extras = (
            default_billed_entity_category_for_extras
        )
        self.default_payment_instruction_for_extras = (
            default_payment_instruction_for_extras
        )
        self.segments = segments
        self.guarantee_information = guarantee_information
        self.discount_details = discount_details
