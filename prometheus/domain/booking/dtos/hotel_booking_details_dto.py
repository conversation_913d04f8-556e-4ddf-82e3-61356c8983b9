class GuestDetailsDto:
    def __init__(self, booking_detail):
        self.guest_id = booking_detail['guest_id']
        self.first_name = booking_detail['first_name']
        self.last_name = booking_detail['last_name']
        self.profile_id = booking_detail['user_profile_id']
        self.salutation = booking_detail['salutation']
        self.is_vip = booking_detail['is_vip']


class HotelBookingDetailsDto:
    def __init__(self, booking_detail):
        self.hotel_id = booking_detail['hotel_id']
        self.booking_id = booking_detail['booking_id']
        self.disallow_charge_addition = booking_detail['disallow_charge_addition']
        self.room_no = booking_detail['room_no']
        self.room_stay_id = booking_detail['room_stay_id']
        self.checkin_date = self.format_date(booking_detail['checkin_date'])
        self.checkout_date = self.format_date(booking_detail['checkout_date'])
        self.comments = booking_detail['comments']
        self.room_type_id = booking_detail['room_type_id']
        self.room_rate_plans = booking_detail['room_rate_plans']
        self.group_name = booking_detail['group_name']
        self.segments = booking_detail['segments']
        self.guest_details = GuestDetailsDto(booking_detail)
        self.room_type = None
        self.rate_plan_name = None
        self.package_name = None
        self.adult = None
        self.child = None
        self.origin = None

    @staticmethod
    def format_date(booking_date):
        return booking_date.date().strftime('%d-%b-%y').upper()


class BookingRatePlanDetailsDto:
    def __init__(self, booking_rate_plan_detail):
        self.booking_id = booking_rate_plan_detail['booking_id']
        self.rate_plan_code = booking_rate_plan_detail['short_code']
        self.rate_plan_id = booking_rate_plan_detail['rate_plan_id']
        self.package_name = booking_rate_plan_detail['package_name']


class BookingGuestDetailsDto:
    def __init__(self, booking_guest_detail):
        self.booking_id = booking_guest_detail['booking_id']
        self.room_stay_id = booking_guest_detail['room_stay_id']
        self.age_group = booking_guest_detail['age_group']
