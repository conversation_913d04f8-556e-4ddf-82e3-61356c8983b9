class DCAEventData(object):
    def __init__(
        self, room_id, room_number, room_type_id, room_stay_id, disallow_charge_addition
    ):
        self.room_id = room_id
        self.room_number = room_number
        self.room_type_id = room_type_id
        self.room_stay_id = room_stay_id
        self.disallow_charge_addition = disallow_charge_addition
        self.room_type = None

    def set_room_type(self, room_type_map):
        self.room_type = room_type_map.get(self.room_type_id).room_type.type.title()
