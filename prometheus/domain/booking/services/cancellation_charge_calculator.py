from collections import defaultdict

from treebo_commons.money import Money
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import date_range

from object_registry import register_instance
from prometheus import crs_context
from prometheus.domain.billing.dto.taxable_item import TaxableItem
from prometheus.domain.billing.services import TaxService
from prometheus.domain.booking.repositories import ExpenseItemRepository
from ths_common.constants.booking_constants import CancellationChargeType


@register_instance(dependencies=[ExpenseItemRepository, TaxService])
class CancellationChargeCalculator(object):
    GET_PRETAX_AMOUNT = 'get_pretax_amount_post_allowance'
    GET_POSTTAX_AMOUNT = 'get_posttax_amount_post_allowance'

    def __init__(
        self, expense_item_repository: ExpenseItemRepository, tax_service: TaxService
    ):
        self.expense_item_repository = expense_item_repository
        self.tax_service = tax_service

    def calculate_room_stay_wise_cancellation_charges(
        self,
        booking_aggregate,
        bill_aggregate,
        room_stays,
        cancellation_datetime,
        is_rate_manager_enabled,
        **kwargs
    ):
        use_posttax_for_tax_calculation = kwargs.get(
            'use_posttax_for_tax_calculation', False
        )
        cancellation_charge_expense_item = kwargs.get(
            'cancellation_charge_expense_item'
        )

        room_stay_wise_cancellation_charge = dict()
        if is_rate_manager_enabled:
            for room_stay in room_stays:
                cancellation_start_date = room_stay.checkin_date
                cancellation_end_date = room_stay.checkout_date
                cancellation_charge = self.calculate_for_room_stay_with_rate_plan(
                    booking_aggregate,
                    bill_aggregate,
                    room_stay,
                    cancellation_datetime,
                    cancellation_start_date,
                    cancellation_end_date,
                    **kwargs
                )
                room_stay_wise_cancellation_charge[
                    room_stay.room_stay_id
                ] = cancellation_charge

            room_stay_wise_cancellation_charge = (
                self.calculate_posttax_cancellation_charges(
                    booking_aggregate,
                    room_stay_wise_cancellation_charge,
                    cancellation_charge_expense_item,
                    use_posttax_for_tax_calculation,
                )
            )

        else:
            for room_stay in room_stays:
                cancellation_charge = self.calculate_for_room_stay_without_rate_plan(
                    booking_aggregate, bill_aggregate, room_stay, cancellation_datetime
                )

                room_stay_wise_cancellation_charge[
                    room_stay.room_stay_id
                ] = cancellation_charge

        return room_stay_wise_cancellation_charge

    def calculate_room_stay_cancellation_charge(
        self,
        room_stay,
        cancellation_datetime,
        cancellation_start_date,
        booking_aggregate,
        bill_aggregate,
        cancellation_end_date,
        is_rate_manager_enabled,
        **kwargs
    ):
        use_posttax_for_tax_calculation = kwargs.get(
            'use_posttax_for_tax_calculation', False
        )
        cancellation_charge_expense_item = kwargs.get(
            'cancellation_charge_expense_item'
        )
        """Calculate total cancellation charge for a room stay"""
        if is_rate_manager_enabled:
            cancellation_charge = self.calculate_for_room_stay_with_rate_plan(
                booking_aggregate,
                bill_aggregate,
                room_stay,
                cancellation_datetime,
                cancellation_start_date,
                cancellation_end_date,
                **kwargs
            )
            return self.calculate_posttax_cancellation_charge(
                booking_aggregate,
                pretax_amount=cancellation_charge
                if not use_posttax_for_tax_calculation
                else None,
                posttax_amount=cancellation_charge
                if use_posttax_for_tax_calculation
                else None,
                cancellation_charge_expense_item=cancellation_charge_expense_item,
            )
        else:
            return self.calculate_for_room_stay_without_rate_plan(
                booking_aggregate, bill_aggregate, room_stay, cancellation_datetime
            )

    def calculate_for_room_stay_with_rate_plan(
        self,
        booking_aggregate,
        bill_aggregate,
        room_stay,
        cancellation_datetime,
        cancellation_start_date,
        cancellation_end_date,
        **kwargs
    ):
        use_posttax_for_tax_calculation = kwargs.get(
            'use_posttax_for_tax_calculation', False
        )
        amount_key = (
            self.GET_POSTTAX_AMOUNT
            if use_posttax_for_tax_calculation
            else self.GET_PRETAX_AMOUNT
        )
        charge_ids = booking_aggregate.get_all_applicable_charges_on_room_stay(
            room_stay.room_stay_id
        )
        charges = bill_aggregate.filter_charges_by_ids(charge_ids)
        charges_group_by_date = defaultdict(list)
        rate_plan_id_dict = dict()

        total_amount = 0
        for charge in charges:
            if charge.is_cancelled:
                continue
            charges_group_by_date[dateutils.to_date(charge.applicable_date)].append(
                charge
            )
        for cancellation_date in date_range(
            end_date=cancellation_end_date,
            start_date=cancellation_start_date,
            end_inclusive=True,
        ):
            total_amount += sum(
                [
                    getattr(charge, amount_key)()
                    for charge in charges_group_by_date.get(
                        dateutils.to_date(cancellation_date), []
                    )
                ],
                0,
            )

        first_night_charge = sum(
            [
                getattr(charge, amount_key)()
                for charge in charges_group_by_date.get(
                    dateutils.to_date(cancellation_start_date), []
                )
            ],
            0,
        )

        total_cancellation_charge = Money(0, bill_aggregate.bill.base_currency)
        for cancellation_date in date_range(
            end_date=cancellation_end_date,
            start_date=cancellation_start_date,
            end_inclusive=True,
        ):
            if dateutils.to_date(cancellation_date) == dateutils.to_date(
                room_stay.checkout_date
            ):
                for value in rate_plan_id_dict.values():
                    total_cancellation_charge += value.get("amount")
                return total_cancellation_charge

            rate_plan_id = [
                rp.rate_plan_id
                for rp in room_stay.room_rate_plans
                if rp.stay_date == dateutils.to_date(cancellation_date)
            ][0]
            rate_plan = booking_aggregate.get_rate_plan(rate_plan_id)
            if not rate_plan.policies or not rate_plan.policies.cancellation_policies:
                continue

            (
                cancellation_type,
                cancellation_charge_value,
            ) = rate_plan.get_cancellation_details(
                booking_aggregate.booking.checkin_date, cancellation_datetime
            )

            per_day_charge = sum(
                [
                    getattr(charge, amount_key)()
                    for charge in charges_group_by_date.get(
                        dateutils.to_date(cancellation_date), []
                    )
                ],
                0,
            )

            if rate_plan_id not in rate_plan_id_dict.keys():
                if (
                    cancellation_type
                    == CancellationChargeType.PERCENT_BOOKING_VALUE.value
                ):
                    amount = per_day_charge * (int(cancellation_charge_value) / 100)
                else:
                    amount = first_night_charge * (int(cancellation_charge_value) / 100)
                rate_plan_id_dict[rate_plan_id] = {
                    "cancellation_type": cancellation_type,
                    "amount": amount,
                }
            elif (
                rate_plan_id in rate_plan_id_dict.keys()
                and cancellation_type
                == CancellationChargeType.PERCENT_BOOKING_VALUE.value
            ):
                rate_plan_id_dict[rate_plan_id]["amount"] += per_day_charge * (
                    int(cancellation_charge_value) / 100
                )

        return total_cancellation_charge

    @staticmethod
    def calculate_for_room_stay_without_rate_plan(
        booking_aggregate, bill_aggregate, room_stay, cancellation_datetime
    ):
        if cancellation_datetime < (
            room_stay.checkin_date - dateutils.timedelta(days=1)
        ):
            return Money(0, crs_context.hotel_context.base_currency)
        else:
            return bill_aggregate.get_charge(
                booking_aggregate.per_night_charge(room_stay.room_stay_id)
            ).get_posttax_amount_post_allowance()

    def calculate_posttax_cancellation_charges(
        self,
        booking_aggregate,
        room_stay_wise_cancellation_charges,
        cancellation_charge_expense_item,
        use_posttax_for_tax_calculation,
    ):
        room_stay_wise_taxable_items = dict()
        taxable_items = []
        applicable_date = crs_context.get_hotel_context().current_date()
        for (
            room_stay_id,
            cancellation_charge,
        ) in room_stay_wise_cancellation_charges.items():
            pretax_amount = (
                cancellation_charge if not use_posttax_for_tax_calculation else None
            )
            posttax_amount = (
                cancellation_charge if use_posttax_for_tax_calculation else None
            )
            taxable_item = TaxableItem(
                sku_category_id=cancellation_charge_expense_item.sku_category_id,
                applicable_date=applicable_date,
                pretax_amount=pretax_amount,
                posttax_amount=posttax_amount,
            )
            room_stay_wise_taxable_items[room_stay_id] = taxable_item
            taxable_items.append(taxable_item)

        # Updates tax details on taxable items in-memory
        self.tax_service.calculate_taxes(
            taxable_items,
            buyer_gst_details=booking_aggregate.booking_owner_gst_details(),
            seller_has_lut=self.tax_service.seller_has_lut(
                booking_aggregate.booking.seller_model, crs_context.get_hotel_context()
            ),
            hotel_id=crs_context.get_hotel_context().hotel_id,
        )

        room_stay_wise_cancellation_charges = dict()
        for room_stay_id, taxable_item in room_stay_wise_taxable_items.items():
            room_stay_wise_cancellation_charges[
                room_stay_id
            ] = taxable_item.posttax_amount

        return room_stay_wise_cancellation_charges

    def calculate_posttax_cancellation_charge(
        self,
        booking_aggregate,
        pretax_amount=None,
        posttax_amount=None,
        cancellation_charge_expense_item=None,
    ):
        hotel_context = crs_context.hotel_context
        expense_item = cancellation_charge_expense_item
        applicable_date = dateutils.datetime_at_given_time(
            hotel_context.current_date(),
            dateutils.to_time(booking_aggregate.booking.checkin_date),
        )
        taxable_item = TaxableItem(
            sku_category_id=expense_item.sku_category_id,
            applicable_date=applicable_date,
            pretax_amount=pretax_amount,
            posttax_amount=posttax_amount,
        )
        booking_owner_gst_details = booking_aggregate.booking_owner_gst_details()
        taxable_items = self.tax_service.calculate_taxes(
            [taxable_item],
            buyer_gst_details=booking_owner_gst_details,
            seller_has_lut=self.tax_service.seller_has_lut(
                booking_aggregate.booking.seller_model, crs_context.get_hotel_context()
            ),
            hotel_id=hotel_context.hotel_id,
        )
        return taxable_items[0].posttax_amount
