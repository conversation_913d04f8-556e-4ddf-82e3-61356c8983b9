from prometheus.domain.hotel_config.aggregates.hotel_config_aggregate import (
    HotelConfigAggregate,
)
from prometheus.domain.hotel_config.entities.hotel_config import HotelConfig


class HotelConfigFactory(object):
    @staticmethod
    def create_hotel_config(hotel_id, migration_start_date=None, managed_by=None):
        hotel_config = HotelConfig(
            hotel_id, migration_start_date, None, None, managed_by
        )
        return HotelConfigAggregate(hotel_config)
