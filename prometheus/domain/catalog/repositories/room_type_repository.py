from sqlalchemy import func

from object_registry import register_instance
from prometheus import crs_context
from prometheus.domain.catalog.aggregates.room_type_aggregate import RoomTypeAggregate
from prometheus.domain.catalog.entities.room_type import RoomType
from prometheus.domain.catalog.models import RoomTypeModel
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.exceptions import AggregateNotFound


@register_instance()
class RoomTypeRepository(BaseRepository):
    HOUSE_ACCOUNT_ROOM_TYPE = 'House Accounts'

    def is_valid_room_type(self, room_type_id):
        room_type = self.get(RoomTypeModel, room_type_id=room_type_id)
        return True if room_type else False

    def load(self, room_type_id):
        # NOTE: Unused method. Remove
        room_type = self.get(RoomTypeModel, room_type_id=room_type_id)
        if not room_type:
            raise AggregateNotFound("RoomTypeAggregate", room_type_id)
        room_type_entity = RoomType(
            room_type_id=room_type.room_type_id,
            type=room_type.type,
            deleted=room_type.deleted,
        )
        return RoomTypeAggregate(room_type_entity)

    def load_all(self, room_type_ids):
        # NOTE: Unused method. Remove
        room_types = self.filter(
            RoomTypeModel, RoomTypeModel.room_type_id.in_(room_type_ids)
        )
        room_type_aggregates = []
        for room_type in room_types:
            room_type_entity = RoomType(
                room_type_id=room_type.room_type_id,
                type=room_type.type,
                deleted=room_type.deleted,
            )
            room_type_aggregates.append(RoomTypeAggregate(room_type_entity))
        return room_type_aggregates

    def load_type_map(self):
        room_type_map = crs_context.get_room_type_map()
        if room_type_map is not None:
            return room_type_map
        room_type_map = dict()
        room_types = self.filter(RoomTypeModel)
        for room_type in room_types:
            room_type_entity = RoomType(
                room_type_id=room_type.room_type_id,
                type=room_type.type,
                deleted=room_type.deleted,
            )
            room_type_map[room_type_entity.room_type_id] = RoomTypeAggregate(
                room_type_entity
            )
        crs_context.set_room_type_map(room_type_map)
        return room_type_map

    def get_house_accounts_room_type_id(self):
        house_accounts_room_type = (
            self.query(RoomTypeModel.room_type_id)
            .filter(
                func.lower(RoomTypeModel.type) == self.HOUSE_ACCOUNT_ROOM_TYPE.lower()
            )
            .first()
        )
        if house_accounts_room_type:
            return house_accounts_room_type[0]
        return None

    def save(self, room_type_aggregate):
        room_type_model = RoomTypeModel(
            room_type_id=room_type_aggregate.room_type.room_type_id,
            type=room_type_aggregate.room_type.type,
        )
        self._save(room_type_model)
        self.flush_session()

    def update(self, room_type_aggregate):
        room_type_model = RoomTypeModel(
            room_type_id=room_type_aggregate.room_type.room_type_id,
            type=room_type_aggregate.room_type.type,
        )
        self._update(room_type_model)
        self.flush_session()

    def get_room_type_details(self):
        return self.query(RoomTypeModel).all() or None
