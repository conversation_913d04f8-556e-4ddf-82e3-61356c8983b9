# coding=utf-8
from treebo_commons.money.constants import CurrencyType

from object_registry import register_instance
from prometheus.common.decorators import timed
from prometheus.core.globals import HotelDtoForContext
from prometheus.domain.catalog.adaptors.hotel_adaptor import HotelAdaptor
from prometheus.domain.catalog.adaptors.room_type_config_adaptor import (
    RoomTypeConfigAdaptor,
)
from prometheus.domain.catalog.aggregates.hotel_aggregate import HotelAggregate
from prometheus.domain.catalog.dtos.finance_erp_dtos import HotelDetailsDto
from prometheus.domain.catalog.models import HotelModel
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.catalog_constants import HotelStatus
from ths_common.exceptions import AggregateNotFound, DatabaseError
from ths_common.utils import dateutils


@register_instance()
class HotelRepository(BaseRepository):
    hotel_adaptor = HotelAdaptor()
    room_type_config_adaptor = RoomTypeConfigAdaptor()

    def to_aggregate(self, **kwargs):
        hotel = kwargs['hotel']
        room_type_configs = kwargs['room_type_configs']
        hotel_entity = self.hotel_adaptor.to_domain_entity(hotel)

        room_type_config_entities = []
        for room_type_config in room_type_configs:
            room_type_config_entities.append(
                self.room_type_config_adaptor.to_domain_entity(room_type_config)
            )
        return HotelAggregate(hotel_entity, room_type_config_entities)

    def from_aggregate(self, aggregate=None):
        hotel_entity = aggregate.hotel
        hotel = self.hotel_adaptor.to_db_entity(hotel_entity)

        hotel_room_type_configs = []
        for room_type_config in aggregate.room_type_configs:
            hotel_room_type_configs.append(
                self.room_type_config_adaptor.to_db_entity(
                    room_type_config, hotel_entity.hotel_id
                )
            )
        return hotel, hotel_room_type_configs

    def save(self, hotel_aggregate):
        """
        Create hotel aggregate
        :param hotel_aggregate:
        :return:
        """
        hotel, hotel_room_type_configs = self.from_aggregate(hotel_aggregate)
        try:
            self._save(hotel)
            self._save_all(hotel_room_type_configs)
            self.flush_session()
        except Exception:
            raise DatabaseError

    def update(self, hotel_aggregate):
        """
        Update hotel aggregate
        :param hotel_aggregate:
        :return:
        """
        hotel_entity = hotel_aggregate.hotel
        hotel = self.hotel_adaptor.to_db_entity(hotel_entity)

        hotel_room_type_configs = []
        for room_type_config in hotel_aggregate.room_type_configs:
            hotel_room_type_configs.append(
                self.room_type_config_adaptor.to_db_entity(
                    room_type_config, hotel_entity.hotel_id
                )
            )
        try:
            self._update(hotel)
            self._update_all(hotel_room_type_configs)
            self.flush_session()
        except Exception:
            raise DatabaseError

    def load(self, hotel_id):
        hotel = self.get(HotelModel, hotel_id=hotel_id)
        if not hotel:
            raise AggregateNotFound("HotelAggregate", hotel_id)
        hotel_aggregate = self.to_aggregate(
            hotel=hotel, room_type_configs=hotel.room_type_configs
        )
        return hotel_aggregate

    def load_for_update(self, hotel_id, wait_for_lock=False):
        hotel = self.get_for_update(
            HotelModel, hotel_id=hotel_id, nowait=not wait_for_lock
        )
        if not hotel:
            raise AggregateNotFound("HotelAggregate", hotel_id)
        hotel_aggregate = self.to_aggregate(
            hotel=hotel, room_type_configs=hotel.room_type_configs
        )
        return hotel_aggregate

    def load_all(self, hotel_ids=None):
        if hotel_ids:
            q = self.query(HotelModel)
            hotels = q.filter(HotelModel.hotel_id.in_(hotel_ids))
        else:
            hotels = self.filter(HotelModel)

        hotel_aggregates = []
        for hotel in hotels:
            hotel_aggregate = self.to_aggregate(
                hotel=hotel, room_type_configs=hotel.room_type_configs
            )
            hotel_aggregates.append(hotel_aggregate)
        return hotel_aggregates

    def exists(self, hotel_id):
        hotel = self.get(HotelModel, hotel_id=hotel_id)
        return True if hotel else False

    def load_hotels(self, include_inactive=True):
        q = self.query(HotelModel)
        if not include_inactive:
            q = q.filter(HotelModel.status == HotelStatus.ACTIVE.value)
        hotels = q.all()
        hotel_aggregates = []
        for hotel in hotels:
            hotel_aggregate = self.to_aggregate(
                hotel=hotel, room_type_configs=hotel.room_type_configs
            )
            hotel_aggregates.append(hotel_aggregate)

        return hotel_aggregates

    def fetch_current_business_date(self, hotel_id):
        current_business_date = (
            self.query(HotelModel.current_business_date)
            .filter(HotelModel.hotel_id == hotel_id)
            .first()
        )
        return current_business_date[0]

    def get_base_currency(self, hotel_id):
        base_currency = (
            self.query(HotelModel.base_currency)
            .filter(HotelModel.hotel_id == hotel_id)
            .first()
        )
        return CurrencyType(base_currency[0])

    @timed
    def get_hotel_dto_for_context(self, hotel_id) -> HotelDtoForContext:
        hotel_data = (
            self.query(
                HotelModel.hotel_id,
                HotelModel.switch_over_time,
                HotelModel.free_late_checkout_time,
                HotelModel.checkin_time,
                HotelModel.checkout_time,
                HotelModel.base_currency,
                HotelModel.current_business_date,
                HotelModel.timezone,
            )
            .filter(HotelModel.hotel_id == hotel_id)
            .first()
        )

        return HotelDtoForContext(
            hotel_id=hotel_data[0],
            switch_over_time=dateutils.time_str_to_time(hotel_data[1]),
            free_late_checkout_time=dateutils.time_str_to_time(hotel_data[2]),
            checkin_time=dateutils.time_str_to_time(hotel_data[3]),
            checkout_time=dateutils.time_str_to_time(hotel_data[4]),
            base_currency=CurrencyType(hotel_data[5]) if hotel_data[5] else None,
            current_business_date=hotel_data[6],
            timezone=hotel_data[7],
        )

    def get_hotel_data_for_given_hotels(self, hotel_ids):
        q = self.query(HotelModel.hotel_id, HotelModel.name)
        q = q.filter(HotelModel.hotel_id.in_(hotel_ids))

        hotel_details = q.all()

        hotel_details = [
            HotelDetailsDto(hotel_detail) for hotel_detail in hotel_details
        ]

        return hotel_details

    def get_hotel_with_pending_night_audit(self, pending_days):
        from sqlalchemy import Integer, func

        q = self.query(HotelModel).filter(
            HotelModel.status == 'active',
            HotelModel.current_business_date < func.current_date(),
        )
        if pending_days is not None:
            q = q.filter(
                HotelModel.current_business_date
                >= func.current_date() - func.cast(pending_days, Integer)
            )
        hotel_details = q.all()
        return [self.hotel_adaptor.to_domain_entity(hotel) for hotel in hotel_details]
