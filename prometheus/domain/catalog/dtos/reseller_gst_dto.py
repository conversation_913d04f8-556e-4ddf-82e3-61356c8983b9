class ResellerGstDto(object):
    def __init__(
        self,
        id,
        state_id,
        state_name,
        gstin_number,
        date_of_registration,
        address_line_1,
        address_line_2,
        address_city,
        address_pincode,
        reseller_legal_name,
    ):
        self.id = id
        self.state_id = state_id
        self.state_name = state_name
        self.gstin_number = gstin_number
        self.date_of_registration = date_of_registration
        self.address_line_1 = address_line_1
        self.address_line_2 = address_line_2
        self.address_city = address_city
        self.address_pincode = address_pincode
        self.reseller_legal_name = reseller_legal_name

    @staticmethod
    def create_from_catalog_data(catalog_reseller_gst_detail_data):
        data = catalog_reseller_gst_detail_data

        return ResellerGstDto(
            id=data.get('id'),
            state_id=data.get('state').get('id'),
            state_name=data.get('state').get('name'),
            gstin_number=data.get('gstin'),
            date_of_registration=data.get('date_of_registration'),
            address_line_1=data.get('address_line_1'),
            address_line_2=data.get('address_line_2'),
            address_city=data.get('address_city'),
            address_pincode=data.get('address_pincode'),
            reseller_legal_name=data.get('legal_name'),
        )
