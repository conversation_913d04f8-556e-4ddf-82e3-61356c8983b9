from ths_common.value_objects import NotAssigned


class SellerTableDto(object):
    def __init__(
        self,
        table_id: str,
        seller_id: str,
        name: str,
        table_number: str,
        current_status=None,
        status_updated_at=None,
        created_at=None,
        modified_at=None,
        deleted=None,
    ):
        self.table_id = table_id
        self.seller_id = seller_id
        self.name = name
        self.table_number = table_number
        self.current_status = current_status
        self.status_updated_at = status_updated_at
        self.created_at = created_at
        self.modified_at = modified_at
        self.deleted = deleted

    @staticmethod
    def create_from_catalog_data(data):
        return SellerTableDto(
            table_id=str(data.get("table_id")),
            seller_id=data.get("seller_id"),
            name=data.get("name"),
            table_number=data.get("table_number"),
            current_status=data.get("current_status"),
            status_updated_at=data.get("status_updated_at"),
            created_at=data.get("created_at"),
            modified_at=data.get("modified_at"),
            deleted=data.get("is_deleted"),
        )
