from ths_common.constants.catalog_constants import RoomStatus


class RoomDto(object):
    def __init__(
        self, hotel_id, room_type_id, room_number, status, room_id, linked_room_id=None
    ):
        self.room_id = room_id
        self.hotel_id = hotel_id
        self.room_type_id = room_type_id
        self.room_number = room_number
        self.status = status
        self.linked_room_id = linked_room_id

    @staticmethod
    def create_from_catalog_data(hotel_id, catalog_room_data):
        data = catalog_room_data
        return RoomDto(
            room_id=data.get('id'),
            hotel_id=hotel_id,
            room_type_id=data.get('room_type').get('code'),
            room_number=data.get('room_number'),
            status=RoomStatus.ACTIVE if data.get('is_active') else RoomStatus.INACTIVE,
            linked_room_id=data.get('linked_room_identifier'),
        )

    def __str__(self):
        return f"Room {self.room_number} ({self.hotel_id} - {self.room_id})"
