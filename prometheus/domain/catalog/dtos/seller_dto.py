from treebo_commons.money.constants import CurrencyType


class SellerDto(object):
    def __init__(
        self,
        seller_id,
        name,
        seller_category_id,
        state_id,
        state_name,
        city_id,
        city_name,
        pincode,
        legal_name,
        legal_address,
        gstin_number,
        fssai_license_number,
        legal_signature,
        legal_city_id,
        legal_city_name,
        legal_state_id,
        legal_state_name,
        legal_pincode,
        status,
        timezone,
        base_currency=CurrencyType.INR,
        hotel_id=None,
        current_business_date=None,
        seller_config=None,
    ):
        self.seller_id = seller_id
        self.name = name
        self.seller_category_id = seller_category_id
        self.state_id = state_id
        self.state_name = state_name
        self.city_id = city_id
        self.city_name = city_name
        self.pincode = pincode
        self.legal_name = legal_name
        self.legal_address = legal_address
        self.gstin_number = gstin_number
        self.fssai_license_number = fssai_license_number
        self.legal_signature = legal_signature
        self.legal_city_id = legal_city_id
        self.legal_city_name = legal_city_name
        self.legal_state_id = legal_state_id
        self.legal_state_name = legal_state_name
        self.legal_pincode = legal_pincode
        self.status = status
        self.base_currency = base_currency
        self.timezone = timezone
        self.hotel_id = hotel_id
        self.current_business_date = current_business_date
        self.seller_config = seller_config

    @staticmethod
    def create_from_catalog_data(catalog_seller_detail_data):
        data = catalog_seller_detail_data
        if data.get('legal_city'):
            legal_city_id = data.get('legal_city').get('id')
            legal_city_name = data.get('legal_city').get('name')
            legal_state_id = (data.get('legal_city').get('id'),)
            legal_state_name = (data.get('legal_city').get('code'),)
        else:
            legal_city_id = legal_city_name = legal_state_id = legal_state_name = None
        return SellerDto(
            seller_id=data.get('seller_id'),
            name=data.get('name'),
            seller_category_id=data.get('seller_category_id'),
            state_id=data.get('city').get('state').get('id'),
            state_name=data.get('city').get('state').get('name'),
            city_id=data.get('city').get('id'),
            city_name=data.get('city').get('name'),
            pincode=data.get('pincode'),
            legal_name=data.get('legal_name'),
            legal_address=data.get('legal_address'),
            gstin_number=data.get('gstin'),
            fssai_license_number=data.get('fssai_license_number'),
            legal_signature=data.get('legal_signature'),
            legal_city_id=legal_city_id,
            legal_city_name=legal_city_name,
            legal_state_id=legal_state_id,
            legal_state_name=legal_state_name,
            legal_pincode=data.get('legal_pincode'),
            status=data.get('status'),
            base_currency=CurrencyType(data.get('base_currency_code'))
            if data.get('base_currency_code')
            else CurrencyType.INR,
            timezone=data.get('timezone') if data.get("timezone") else None,
            hotel_id=data.get('property_id'),
            current_business_date=data.get('current_business_date'),
            seller_config=data.get('seller_config'),
        )

    @staticmethod
    def create_sellers_from_catalog_data(catalog_sellers_detail_data):
        seller_dtos = []
        for data in catalog_sellers_detail_data['sellers']:
            seller_dtos.append(SellerDto.create_from_catalog_data(data))

        return seller_dtos
