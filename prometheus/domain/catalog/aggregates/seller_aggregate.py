import logging

from treebo_commons.utils import dateutils

from prometheus.domain.catalog.entities.seller import Seller
from ths_common.value_objects import City, State

logger = logging.getLogger(__name__)


class SellerAggregate(object):
    def __init__(self, seller: Seller):
        self.seller = seller

    def update(self):
        pass

    def update_seller(self, seller_detail_dto):
        seller = Seller(
            seller_id=self.seller.seller_id,
            name=seller_detail_dto.name,
            state=State(seller_detail_dto.state_id, seller_detail_dto.state_name),
            city=City(seller_detail_dto.city_id, seller_detail_dto.city_name),
            pincode=seller_detail_dto.pincode,
            legal_name=seller_detail_dto.legal_name,
            legal_address=seller_detail_dto.legal_address,
            gstin_num=seller_detail_dto.gstin_number,
            fssai_license_number=seller_detail_dto.fssai_license_number,
            legal_signature=seller_detail_dto.legal_signature,
            legal_city=City(
                seller_detail_dto.legal_city_id, seller_detail_dto.legal_city_name
            ),
            legal_state=State(
                seller_detail_dto.legal_state_id, seller_detail_dto.legal_state_name
            ),
            legal_pincode=seller_detail_dto.legal_pincode,
            category_id=seller_detail_dto.seller_category_id,
            status=seller_detail_dto.status,
            base_currency=seller_detail_dto.base_currency,
            timezone=seller_detail_dto.timezone,
            hotel_id=seller_detail_dto.hotel_id,
            current_business_date=seller_detail_dto.current_business_date,
            seller_config=seller_detail_dto.seller_config,
        )
        self.seller = seller

    def rollover_current_business_date(self, hotel_current_business_date=None):
        if not hotel_current_business_date:
            logger.info(
                f"Incrementing the Business Date of seller: {self.seller.seller_id} to "
                f"date: {dateutils.add(self.seller.current_business_date, days=1)}"
            )
            self.seller.current_business_date = dateutils.add(
                self.seller.current_business_date, days=1
            )
        else:
            self.seller.current_business_date = hotel_current_business_date
            logger.info(
                f"Updating the Business Date of seller: {self.seller.seller_id} to "
                f"date: {hotel_current_business_date}"
            )
