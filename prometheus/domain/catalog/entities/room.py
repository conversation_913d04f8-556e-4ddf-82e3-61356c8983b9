# coding=utf-8
from ths_common.constants.catalog_constants import RoomStatus


class Room(object):
    def __init__(
        self, room_id, hotel_id, room_type_id, room_number, status, deleted=False
    ):
        self.room_id = room_id
        self.hotel_id = hotel_id
        self.room_type_id = room_type_id
        self.room_number = room_number
        self.status = status
        self.deleted = deleted

    def update_room_type(self, room_type_id):
        self.room_type_id = room_type_id
        # TODO: Update room type for inventory

    def update_room_number(self, room_number):
        if self.room_number != room_number:
            self.room_number = room_number
        return True
        # TODO: Update room number for inventory and booking

    def mark_status_inactive(self):
        self.status = RoomStatus.INACTIVE

    def mark_status_active(self):
        self.status = RoomStatus.ACTIVE

    def is_active(self):
        return self.status == RoomStatus.ACTIVE
