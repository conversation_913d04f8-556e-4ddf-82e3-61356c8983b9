from ths_common.utils.common_utils import extract_state_code_from_gstin
from ths_common.value_objects import Address, GSTDetails


class Seller(object):
    def __init__(
        self,
        seller_id,
        name,
        state,
        city,
        pincode,
        legal_name,
        legal_address,
        gstin_num,
        fssai_number,
        legal_signature,
        legal_city,
        legal_state,
        legal_pincode,
        category_id,
        status,
        base_currency,
        timezone,
        hotel_id,
        current_business_date=None,
        seller_config=None,
    ):
        self.seller_id = seller_id
        self.category_id = category_id
        self.name = name
        self.state = state
        self.city = city
        self.pincode = pincode
        self.legal_name = legal_name
        self.legal_address = legal_address
        self.gstin_num = gstin_num
        self.fssai_number = fssai_number
        self.legal_signature = legal_signature
        self.legal_city = legal_city
        self.legal_state = legal_state
        self.legal_pincode = legal_pincode
        self.status = status
        self.base_currency = base_currency
        self.timezone = timezone
        self.hotel_id = hotel_id
        self.current_business_date = current_business_date
        self.seller_config = seller_config

    @property
    def gst_details(self):
        return GSTDetails(
            legal_name=self.legal_name,
            gstin_num=self.gstin_num,
            address=self.gst_address,
        )

    @property
    def gst_address(self):
        return Address(
            field_1=self.legal_address,
            field_2="",
            city=self.legal_city.name if self.legal_city else None,
            state=self.legal_state.name if self.legal_state else None,
            country="",
            pincode=self.legal_pincode,
        )

    @property
    def state_code(self):
        if self.gstin_num:
            return extract_state_code_from_gstin(self.gstin_num)
        else:
            return None

    @property
    def legal_state_id(self):
        return self.legal_state.id if self.legal_state else self.state.id

    @property
    def state_id(self):
        return self.state.id
