from ths_common.constants.catalog_constants import SkuCategoryStatus
from ths_common.value_objects import ItemCode


class SkuCategory(object):
    def __init__(
        self,
        sku_category_id: str,
        item_code: ItemCode,
        name: str,
        status: SkuCategoryStatus,
        has_slab_based_taxation=False,
    ):
        self._item_code = item_code
        self._name = name
        self._sku_category_id = sku_category_id
        self._status = status
        self.has_slab_based_taxation = has_slab_based_taxation

    @property
    def sku_category_id(self):
        return self._sku_category_id

    @property
    def item_code(self):
        return self._item_code

    @property
    def name(self):
        return self._name

    @property
    def status(self):
        return self._status
