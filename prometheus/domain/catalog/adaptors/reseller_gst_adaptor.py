from prometheus.domain.catalog.entities.reseller_gst import ResellerGST
from prometheus.domain.catalog.models import ResellerGSTModel
from ths_common.value_objects import State


class ResellerGstAdaptor:
    def to_db_entity(self, domain_entity):
        return ResellerGSTModel(
            id=domain_entity.reseller_gst_id,
            gstin_num=domain_entity.gstin_num,
            state_id=domain_entity.state.id,
            state_name=domain_entity.state.name,
            date_of_registration=domain_entity.date_of_registration,
            address=domain_entity.address,
        )

    def to_domain_entity(self, db_entity):
        return ResellerGST(
            id=db_entity.id,
            gstin_num=db_entity.gstin_num,
            state=State(db_entity.state_id, db_entity.state_name),
            address=db_entity.address,
            date_of_registration=db_entity.date_of_registration,
        )
