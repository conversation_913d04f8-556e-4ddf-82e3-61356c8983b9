import logging

from kombu import Exchange, Producer
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from object_registry import register_instance
from prometheus.infrastructure.messaging.queue_service import BaseQueueService
from ths_common.exceptions import CRSException

logger = logging.getLogger(__name__)


@register_instance()
class IntegrationEventPublisher(BaseQueueService):
    def _setup_entities(self):
        argument = {'hash-header': 'hash-on'}
        self._integration_event_exchange = Exchange(
            'crs-events',
            type='topic',
            durable=True,
            argument=argument,
        )
        self._tenant_wise_producers = dict()
        for tenant_id, conn in self.tenant_wise_connection.items():
            self._tenant_wise_producers[tenant_id] = Producer(
                channel=conn.channel(), exchange=self._integration_event_exchange
            )

    def publish(self, event):
        current_tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        self._initialize()
        payload = IntegrationEventPublisher.get_event_payload(event)
        headers = IntegrationEventPublisher.get_event_headers(event)

        logger.debug('Publishing event %s', payload)

        if not self._tenant_wise_producers[current_tenant_id]:
            raise CRSException(
                description="RMQ Producer not configured for tenant_id: {0}".format(
                    current_tenant_id
                )
            )

        self._publish(
            self._tenant_wise_producers[current_tenant_id],
            payload,
            event.event_type.routing_key,
            headers=headers,
        )

    @staticmethod
    def get_event_payload(event):
        return event.body

    @staticmethod
    def get_event_headers(event):
        hotel_id = getattr(event, 'hotel_id', None)
        if hotel_id:
            return {'hash-on': hotel_id}
