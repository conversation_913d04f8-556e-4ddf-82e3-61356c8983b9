import logging

from kombu import Exchange, Producer
from treebo_commons.request_tracing.context import get_current_tenant_id

from object_registry import register_instance
from prometheus.infrastructure.messaging.queue_service import BaseQueueService
from ths_common.exceptions import CRSException

logger = logging.getLogger(__name__)


@register_instance()
class ReplayIntegrationEventPublisher(BaseQueueService):
    def _setup_entities(self):
        self._integration_event_exchange = Exchange(
            'crs-replay-events', type='topic', durable=True
        )
        self._tenant_wise_producers = dict()
        for tenant_id, conn in self.tenant_wise_connection.items():
            self._tenant_wise_producers[tenant_id] = Producer(
                channel=conn.channel(), exchange=self._integration_event_exchange
            )

    def publish(self, event):
        current_tenant_id = get_current_tenant_id()
        self._initialize()
        payload = ReplayIntegrationEventPublisher.get_event_payload(event)

        logger.debug('Publishing event %s', payload)

        if not self._tenant_wise_producers[current_tenant_id]:
            raise CRSException(
                description="RMQ Producer not configured for tenant_id: {0}".format(
                    current_tenant_id
                )
            )

        self._publish(
            self._tenant_wise_producers[current_tenant_id],
            payload,
            event.event_type.routing_key,
        )

    @staticmethod
    def get_event_payload(event):
        return event.body
