from prometheus.core.globals import crs_context
from prometheus.domain.integration_event.aggregates.integration_event_aggregate import (
    IntegrationEventAggregate,
)
from prometheus.domain.integration_event.entities.integration_event_entity import (
    IntegrationEventEntity,
)
from ths_common.constants.integration_event_constants import IntegrationEventStatus
from ths_common.utils.id_generator_utils import random_id_generator


class IntegrationEventFactory:
    @staticmethod
    def create_integration_event(integration_event_dto):
        event_type = integration_event_dto.event_type
        hotel_id = integration_event_dto.hotel_id
        generated_at = integration_event_dto.generated_at
        event_id = random_id_generator('EVT')
        current_booking_id = crs_context.get_current_booking_id()

        body = dict(
            message_id=event_id,
            generated_at=generated_at,
            events=integration_event_dto.body,
            user_action=integration_event_dto.user_action,
            event_type=event_type.value,
            root_application=integration_event_dto.root_application,
        )
        event_entity = IntegrationEventEntity(
            event_type=event_type,
            hotel_id=hotel_id,
            generated_at=generated_at,
            status=IntegrationEventStatus.UNPUBLISHED,
            body=body,
            event_id=event_id,
            user_action=integration_event_dto.user_action,
            booking_id=current_booking_id,
        )
        return IntegrationEventAggregate(event_entity)
