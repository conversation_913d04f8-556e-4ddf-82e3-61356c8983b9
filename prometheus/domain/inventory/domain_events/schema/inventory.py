from marshmallow import Schema, fields

from prometheus.common.serializers import UserDefinedEnumValidator
from prometheus.infrastructure.external_clients.catalog_service.user_defined_enums import (
    UserDefinedEnums,
)

__all__ = [
    'DNRCreatedEventSchema',
    'AttributeUpdateSchema',
    'OverBookingCreatedEventSchema',
]


class DNRCreatedEventSchema(Schema):
    dnr_id = fields.String(
        description='Unique identifier generated at the time of creating a DNR'
    )
    hotel_id = fields.String()
    room_id = fields.Integer()
    from_date = fields.Date(
        attribute="start_date", description='DNR start date (inclusive)'
    )
    to_date = fields.Date(attribute="end_date", description='DNR end date (inclusive)')
    source = fields.String(
        validate=UserDefinedEnumValidator(UserDefinedEnums.DNR_SOURCE),
        required=True,
        description='DNR created by, FDM, QAM, etc.',
    )
    type = fields.String(validate=UserDefinedEnumValidator(UserDefinedEnums.DNR_TYPE))
    subtype = fields.String(
        validate=UserDefinedEnumValidator(UserDefinedEnums.DNR_SUBTYPE)
    )
    comments = fields.String()
    assigned_by = fields.String(
        description='Email Address of the user creating the DNR'
    )


class OverBookingCreatedEventSchema(Schema):
    room_type_id = fields.String()
    old_inventory_count = fields.Integer()
    new_inventory_count = fields.Integer()


class AttributeUpdateSchema(Schema):
    attribute = fields.String()
    old_value = fields.String()
    new_value = fields.String()
