from ths_common.base_entity import EntityChangeTracker
from ths_common.constants.inventory_constants import RoomCurrentStatus


class RoomInventory(EntityChangeTracker):
    __slots__ = ('hotel_id', 'room_id', 'current_status', 'reservation_statuses')

    def __init__(
        self,
        hotel_id,
        room_id,
        current_status: RoomCurrentStatus = RoomCurrentStatus.VACANT,
        reservation_statuses=None,
        dirty=True,
        new=True,
    ):
        super().__init__(dirty=dirty, new=new)
        self.hotel_id = hotel_id
        self.room_id = room_id
        self.current_status = current_status
        self.reservation_statuses = (
            reservation_statuses if reservation_statuses else set()
        )

    def update_current_status(self, current_status):
        self.current_status = current_status
        self.mark_dirty()

    def update_reservation_statuses(self, reservation_statuses):
        self.reservation_statuses = reservation_statuses
        self.mark_dirty()
