from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.domain.domain_events.domain_event_registry import register_event
from prometheus.domain.inventory.domain_events.dnr_modified import DNRModifiedEvent
from prometheus.domain.inventory.domain_events.dnr_resolved import DNRResolvedEvent
from prometheus.domain.inventory.errors import InventoryError
from prometheus.domain.inventory.exceptions import InventoryException
from ths_common.constants.inventory_constants import DNREffectiveStatus, DNRStatus
from ths_common.value_objects import DNRTypeValueObject


class DNR(object):
    def __init__(
        self,
        dnr_id,
        start_date,
        end_date,
        hotel_id,
        room_id,
        dnr_type,
        status,
        source,
        assigned_by,
        date_inactivated=None,
        deleted=False,
        version=None,
        room_allotment_id=None,
    ):
        self.dnr_id = dnr_id
        self.start_date = start_date
        self.end_date = end_date
        self.hotel_id = hotel_id
        self.room_id = room_id
        # DNRType value object
        self.dnr_type = dnr_type
        # DNRStatus Enum
        self.status = status
        self.source = source
        self.assigned_by = assigned_by
        self.date_inactivated = date_inactivated
        self.deleted = deleted
        self.version = version
        self.room_allotment_id = room_allotment_id

    def is_inactive(self):
        return self.status == DNRStatus.INACTIVE

    @property
    def effective_end_date(self):
        return (
            self.end_date
            if not self.date_inactivated
            else dateutils.to_date(self.date_inactivated)
        )

    @property
    def effective_status(self):
        current_business_date = crs_context.get_hotel_context().current_date()
        effective_duration = self.effective_duration
        if not self.date_inactivated:
            if current_business_date < self.end_date:
                return DNREffectiveStatus.ACTIVE
            else:
                return DNREffectiveStatus.PAST
        else:
            if effective_duration >= 1:
                return DNREffectiveStatus.PAST
            else:
                return DNREffectiveStatus.DELETED

    @property
    def effective_duration(self):
        if not self.date_inactivated:
            return (self.end_date - self.start_date).days
        elif dateutils.to_date(self.date_inactivated) > self.start_date:
            return (dateutils.to_date(self.date_inactivated) - self.start_date).days
        else:
            return 0

    def mark_status_inactive(self, date_inactivated):
        if self.status == DNRStatus.INACTIVE:
            raise InventoryException(error=InventoryError.INACTIVE_DNR)
        self.date_inactivated = date_inactivated
        self.status = DNRStatus.INACTIVE
        register_event(DNRResolvedEvent(dnr_id=self.dnr_id))

    def update_type(self, dnr_type: DNRTypeValueObject):
        old_dnr_type = self.dnr_type
        self.dnr_type = dnr_type
        if self.dnr_type.type != old_dnr_type.type:
            register_event(
                DNRModifiedEvent(
                    dnr_id=self.dnr_id,
                    attribute="type",
                    old_value=str(old_dnr_type.type)
                    if old_dnr_type.type is not None
                    else None,
                    new_value=str(dnr_type.type) if dnr_type.type is not None else None,
                )
            )

        if self.dnr_type.subtype != old_dnr_type.subtype:
            register_event(
                DNRModifiedEvent(
                    dnr_id=self.dnr_id,
                    attribute="subtype",
                    old_value=str(old_dnr_type.subtype)
                    if old_dnr_type.subtype is not None
                    else None,
                    new_value=str(dnr_type.subtype)
                    if dnr_type.subtype is not None
                    else None,
                )
            )

        if self.dnr_type.comments != old_dnr_type.comments:
            register_event(
                DNRModifiedEvent(
                    dnr_id=self.dnr_id,
                    attribute="comments",
                    old_value=str(old_dnr_type.comments)
                    if old_dnr_type.comments is not None
                    else None,
                    new_value=str(dnr_type.comments)
                    if dnr_type.comments is not None
                    else None,
                )
            )

    def update_from_date(self, from_date):
        old_start_date = self.start_date
        self.start_date = from_date
        if self.start_date != old_start_date:
            register_event(
                DNRModifiedEvent(
                    dnr_id=self.dnr_id,
                    attribute="from_date",
                    old_value=str(old_start_date),
                    new_value=str(self.start_date),
                )
            )

    def update_to_date(self, to_date):
        old_end_date = self.end_date
        self.end_date = to_date
        if self.end_date != old_end_date:
            register_event(
                DNRModifiedEvent(
                    dnr_id=self.dnr_id,
                    attribute="to_date",
                    old_value=str(old_end_date),
                    new_value=str(self.end_date),
                )
            )

    def set_room_allotment_id(self, room_allotment_id):
        self.room_allotment_id = room_allotment_id

    def __str__(self):
        if self.dnr_id:
            return "DNR ({i}: {s})".format(i=self.dnr_id, s=self.status)
        else:
            return "DNR (h:{h}, r:{r}, t:{t}, s:{s} " "{sd} -> {ed})".format(
                h=self.hotel_id,
                r=self.room_id,
                t=self.dnr_type,
                s=self.source,
                sd=self.start_date,
                ed=self.end_date,
            )

    __repr__ = __str__
