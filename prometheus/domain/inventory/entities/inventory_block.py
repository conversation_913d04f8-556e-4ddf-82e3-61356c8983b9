from datetime import date
from typing import Optional

from pydantic import <PERSON><PERSON><PERSON><PERSON>, ConfigDict, model_validator

from ths_common.constants.inventory_constants import (
    InventoryBlockStatus,
    InventoryBlockType,
)


class InventoryBlock(BaseModel):
    block_id: str
    hotel_id: str
    room_type_id: str
    start_date: date
    end_date: date
    booking_id: str
    status: InventoryBlockStatus

    block_type: Optional[InventoryBlockType] = None
    room_stay_id: Optional[int] = None

    model_config = ConfigDict(
        validate_assignment=True,
        arbitrary_types_allowed=True,
    )

    # ---- Invariants --------------------------------------------------------
    @model_validator(mode="after")
    def _check_date_range(self):
        if self.end_date < self.start_date:
            raise ValueError("end_date must be on or after start_date")
        return self

    # ---- Domain behaviour --------------------------------------------------
    def is_releasable(self):
        return self.status in {
            InventoryBlockStatus.BLOCKED,
            InventoryBlockStatus.PROVISIONALLY_CONSUMED,
        }

    def is_fulfillable(self):
        return self.status in {
            InventoryBlockStatus.PROVISIONALLY_CONSUMED,
        }

    def mark_released(self):
        if not self.is_releasable():
            raise RuntimeError("This block can't be released")
        self.status = InventoryBlockStatus.RELEASED

    def mark_fulfilled(self):
        if not self.is_fulfillable():
            raise RuntimeError("This block can't be full filled")

        self.status = InventoryBlockStatus.FULL_FILLED

    def consume(self):
        if self.block_type and self.block_type.is_virtual:
            self.status = InventoryBlockStatus.PROVISIONALLY_CONSUMED
        else:
            self.status = InventoryBlockStatus.CONSUMED
