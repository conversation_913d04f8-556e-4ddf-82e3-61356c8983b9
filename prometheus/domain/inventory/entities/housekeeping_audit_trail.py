class HouseKeepingAuditTrail:
    def __init__(
        self,
        housekeeping_audit_trail_id,
        hotel_id,
        room_id,
        action_performed,
        action_datetime,
        old_housekeeping_status,
        new_housekeeping_status,
        old_room_status,
        new_room_status,
        user,
        auth_id=None,
        application=None,
        application_trace=None,
        request_id=None,
        remarks=None,
    ):
        self.housekeeping_audit_trail_id = housekeeping_audit_trail_id
        self.hotel_id = hotel_id
        self.room_id = room_id
        self.action_performed = action_performed
        self.action_datetime = action_datetime
        self.old_housekeeping_status = old_housekeeping_status
        self.new_housekeeping_status = new_housekeeping_status
        self.old_room_status = old_room_status
        self.new_room_status = new_room_status
        self.user = user
        self.auth_id = auth_id
        self.application = application
        self.application_trace = application_trace
        self.request_id = request_id
        self.remarks = remarks
