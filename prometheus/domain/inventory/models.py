# coding=utf-8
"""
Models
"""

from sqlalchemy import J<PERSON><PERSON>, Column, Date, DateTime, Index, Integer, String, Text
from sqlalchemy.dialects.postgresql import JSONB
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from shared_kernel.infrastructure.database.orm_base import DeleteMixin, TimeStampMixin
from ths_common.constants.inventory_constants import RoomCurrentStatus

__all__ = [
    'RoomInventoryAvailabilityModel',
    'RoomInventoryCurrentStatusModel',
    'RoomTypeInventoryAvailabilityModel',
    'DNRModel',
    'HouseKeepingRecordModel',
    'DNRAuditTrailModel',
    'RoomAllotmentModel',
    'RoomCurrentStatus',
    'RoomInventoryModel',
    'InventoryAuditTrailModel',
    'HouseKeepingAuditTrailModel',
    'HouseKeepingOccupancyAudit',
    'InventoryBlockModel',
]


class RoomInventoryAvailabilityModel(Base, TimeStampMixin):
    __tablename__ = "room_inventory_availability"

    hotel_id = Column('hotel_id', String, primary_key=True)
    room_id = Column('room_id', Integer, primary_key=True)
    date = Column('date', Date, primary_key=True)
    status = Column('status', String)  # Enum(RoomStatus))


class RoomInventoryCurrentStatusModel(Base, TimeStampMixin):
    __tablename__ = "room_inventory_current_status"

    hotel_id = Column('hotel_id', String, primary_key=True)
    room_id = Column('room_id', Integer, primary_key=True)
    status = Column('status', String)  # Enum(RoomCurrentStatus))


class RoomTypeInventoryAvailabilityModel(Base, TimeStampMixin):
    __tablename__ = "room_type_inventory_availability"

    hotel_id = Column('hotel_id', String, primary_key=True)
    room_type_id = Column('room_type_id', String, primary_key=True)
    date = Column('date', Date, primary_key=True)
    count = Column('count', Integer, nullable=False, default=0)

    def mapping_dict(self):
        return {
            "hotel_id": self.hotel_id,
            "room_type_id": self.room_type_id,
            "date": self.date,
            "count": self.count,
        }


class InventoryAuditTrailModel(Base, TimeStampMixin):
    __tablename__ = "inventory_audit_trail"

    audit_id = Column('audit_id', String, primary_key=True)
    hotel_id = Column('hotel_id', String, index=True)
    room_type_id = Column('room_type_id', String)
    date = Column('date', Date, index=True)
    available_count = Column('available_count', Integer)
    integration_event_id = Column('integration_event_id', String, index=True)
    user_type = Column('user_type', String)
    application = Column('application', String)
    application_trace = Column(Text)
    user_action = Column('user_action', String)
    booking_id = Column('booking_id', String, index=True)


class DNRModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "dnr"

    dnr_id = Column('id', String, primary_key=True)
    hotel_id = Column('hotel_id', String, nullable=False, index=True)
    room_id = Column('room_id', Integer, nullable=False)
    start_date = Column('start_date', Date)
    end_date = Column('end_date', Date)
    type = Column('type', String)  # Enum(DNRType))
    sub_type = Column('sub_type', String)  # Enum(DNRSubType))
    source = Column('source', String, nullable=False)
    status = Column(
        'status', String, nullable=False
    )  # Enum(DNRStatus), nullable=False)
    assigned_by = Column('assigned_by', String)
    comments = Column('comments', Text)
    date_inactivated = Column('date_inactivated', DateTime(timezone=True))
    version = Column('version', Integer, nullable=True)
    room_allotment_id = Column('room_allotment_id', String)


class RoomInventoryModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "room_inventory"

    hotel_id = Column('hotel_id', String, primary_key=True)
    room_id = Column('room_id', Integer, primary_key=True)
    current_status = Column('current_status', String, nullable=True)
    reservation_statuses = Column('reservation_statuses', JSONB)


class RoomAllotmentModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "room_allotment"

    allotment_id = Column('allotment_id', String, primary_key=True)
    hotel_id = Column('hotel_id', String, index=True)
    room_id = Column('room_id', Integer, index=True)
    start_time = Column('start_time', DateTime(timezone=True))
    actual_start_time = Column('actual_start_time', DateTime(timezone=True))
    expected_end_time = Column('expected_end_time', DateTime(timezone=True))
    actual_end_time = Column('actual_end_time', DateTime(timezone=True))
    allotted_for = Column('allotted_for', String)


class HouseKeepingRecordModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "housekeeping_record"

    hotel_id = Column('hotel_id', String, primary_key=True)
    room_id = Column('room_id', Integer, primary_key=True)
    housekeeping_status = Column('housekeeping_status', String, nullable=False)
    housekeeper_id = Column('housekeeper_id', Integer)
    remarks = Column('remarks', Text)
    hk_occupancy = Column('hk_occupancy', JSON)


class HouseKeepingOccupancyAudit(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "housekeeping_occupancy_audit"

    housekeeping_occupancy_audit_id = Column(
        'housekeeping_occupancy_audit_id', String, primary_key=True
    )
    hotel_id = Column('hotel_id', String)
    room_id = Column('room_id', Integer)
    hk_room_status = Column('hk_room_status', String)
    fd_room_status = Column('fd_room_status', String)
    hk_occupancy_count = Column('hk_occupancy_count', Integer)
    fd_occupancy = Column('fd_occupancy', JSON)
    housekeeper_id = Column('housekeeper_id', Integer)
    applicable_for = Column('applicable_for', Date)
    hk_comments = Column('hk_comments', Text)


class DNRAuditTrailModel(Base, TimeStampMixin):
    __tablename__ = "dnr_audit_trail"

    audit_id = Column('audit_id', String, primary_key=True)
    user = Column('user', String)
    user_type = Column('user_type', String)
    application = Column('application', String)
    application_trace = Column(Text)
    request_id = Column('request_id', String)
    dnr_id = Column('dnr_id', String, index=True)
    audit_type = Column('audit_type', String)
    audit_payload = Column('audit_payload', JSON)


Index('idx_dnr_date_range', DNRModel.start_date, DNRModel.end_date.desc()),


class HouseKeepingAuditTrailModel(Base, TimeStampMixin):
    __tablename__ = "housekeeping_audit_trail"

    housekeeping_audit_trail_id = Column(String, primary_key=True)
    hotel_id = Column(String)
    room_id = Column(Integer)
    action_performed = Column(String)
    action_datetime = Column(DateTime(timezone=True))
    old_housekeeping_status = Column(String)
    new_housekeeping_status = Column(String)
    old_room_status = Column(String)
    new_room_status = Column(String)
    user = Column(String)
    auth_id = Column(String)
    application = Column(String)
    application_trace = Column(Text)
    request_id = Column(String)
    remarks = Column(Text)


class InventoryBlockModel(Base, TimeStampMixin):
    __tablename__ = "inventory_block"

    block_id = Column('block_id', String, primary_key=True)
    hotel_id = Column('hotel_id', String)
    room_type_id = Column('room_type_id', String)
    start_date = Column(Date)
    end_date = Column(Date)
    status = Column('status', String)
    booking_id = Column('booking_id', String)
    room_stay_id = Column('room_stay_id', String)
    block_type = Column('block_type', String)

    __table_args__ = (
        Index(
            'ix_inventory_block_block_id',
            'block_id',
        ),
    )
