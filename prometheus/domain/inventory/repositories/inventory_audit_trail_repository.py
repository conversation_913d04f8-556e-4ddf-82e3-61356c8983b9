from treebo_commons.request_tracing.context import ApplicationSourceTrace

from object_registry import register_instance
from prometheus.domain.inventory.aggregates.inventory_audit_trail_aggregate import (
    InventoryAuditTrailAggregate,
)
from prometheus.domain.inventory.entities.inventory_audit_trail import (
    InventoryAuditTrail,
)
from prometheus.domain.inventory.models import InventoryAuditTrailModel
from prometheus.infrastructure.database.base_repository import BaseRepository


@register_instance()
class InventoryAuditTrailRepository(BaseRepository):
    def to_aggregate(self, **kwargs):
        audit_trail_model = kwargs.get('audit_trail_model')
        application_trace = (
            audit_trail_model.application_trace or audit_trail_model.application
        )
        audit_trail_entity = InventoryAuditTrail(
            audit_id=audit_trail_model.audit_id,
            hotel_id=audit_trail_model.hotel_id,
            room_type_id=audit_trail_model.room_type_id,
            date=audit_trail_model.date,
            available_count=audit_trail_model.available_count,
            integration_event_id=audit_trail_model.integration_event_id,
            user_type=audit_trail_model.user_type,
            application=audit_trail_model.application,
            application_trace=ApplicationSourceTrace.from_string(application_trace)
            if application_trace
            else None,
            user_action=audit_trail_model.user_action,
            booking_id=audit_trail_model.booking_id,
        )
        return InventoryAuditTrailAggregate(inventory_audit_trail=audit_trail_entity)

    def from_aggregate(self, aggregate=None):
        audit_trail_entity = aggregate.audit_trail
        # noinspection PyArgumentList
        audit_trail_model = InventoryAuditTrailModel(
            audit_id=audit_trail_entity.audit_id,
            hotel_id=audit_trail_entity.hotel_id,
            room_type_id=audit_trail_entity.room_type_id,
            date=audit_trail_entity.date,
            available_count=audit_trail_entity.available_count,
            integration_event_id=audit_trail_entity.integration_event_id,
            user_type=audit_trail_entity.user_type,
            application=audit_trail_entity.application,
            application_trace=str(audit_trail_entity.application_trace)
            if audit_trail_entity.application_trace
            else None,
            user_action=audit_trail_entity.user_action,
            booking_id=audit_trail_entity.booking_id,
        )
        return audit_trail_model

    def save(self, audit_trail_aggregate):
        audit_trail_model = self.from_aggregate(aggregate=audit_trail_aggregate)
        self._save(audit_trail_model)
        self.flush_session()

    def save_all(self, audit_trail_aggregates):
        audit_trail_models = [
            self.from_aggregate(audit_trail_aggregate)
            for audit_trail_aggregate in audit_trail_aggregates
        ]
        self._save_all(audit_trail_models)
        self.flush_session()

    def update(self, audit_trail_aggregate):
        raise NotImplementedError("Inventory AuditTrail update is not allowed")
