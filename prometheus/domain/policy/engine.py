import logging

from prometheus import crs_context
from prometheus.domain.policy.rule.access_entity import AccessEntity
from prometheus.domain.policy.rule.add_guest_rule import AddGuestRule
from prometheus.domain.policy.rule.add_room_rule import AddRoomRule
from prometheus.domain.policy.rule.allowed_action_rule import BookingStateTransitionRule
from prometheus.domain.policy.rule.attachment_rule import (
    AccessAttachmentRule,
    DeleteAttachmentRule,
    EditAttachmentRule,
)
from prometheus.domain.policy.rule.cancel_allowance_rule import CancelAllowanceRule
from prometheus.domain.policy.rule.cancel_booking_rule import CancelBookingRule
from prometheus.domain.policy.rule.cash_register_rules import (
    OpenCashRegisterRules,
    UpdateCashRegisterRules,
)
from prometheus.domain.policy.rule.cashier_payment_rule import (
    CashierPaymentAddRules,
    CashierPaymentUpdateRules,
)
from prometheus.domain.policy.rule.cashier_rule import AccessCashierRule
from prometheus.domain.policy.rule.cashier_session_rule import (
    OpenCashierSessionRule,
    UpdateCashierSessionRule,
)
from prometheus.domain.policy.rule.change_booking_owner_rules import (
    ChangeBookingOwnerDetailRule,
)
from prometheus.domain.policy.rule.change_stay_dates_rule import ChangeStayDatesRule
from prometheus.domain.policy.rule.charge_entity_rules import (
    ChargeEditRules,
    InvoiceConfirmationRule,
    InvoicedChargeEditRule,
)
from prometheus.domain.policy.rule.checkin_rule import CheckinRule
from prometheus.domain.policy.rule.checkout_rule import CheckOutRule
from prometheus.domain.policy.rule.confirm_booking_rule import ConfirmBookingRule
from prometheus.domain.policy.rule.create_addon_rule import CreateAddonRule
from prometheus.domain.policy.rule.create_booking_rule import CreateBookingRule
from prometheus.domain.policy.rule.create_expense_rule import CreateExpenseRule
from prometheus.domain.policy.rule.create_expense_v3_rule import CreateExpenseV3Rule
from prometheus.domain.policy.rule.create_inventory_block_rule import (
    CreateInventoryBockRule,
)
from prometheus.domain.policy.rule.delete_addon_rule import DeleteAddonRule
from prometheus.domain.policy.rule.edit_addon_rule import EditAddonRule
from prometheus.domain.policy.rule.edit_billing_instruction_rule import (
    EditBillingInstructionRule,
)
from prometheus.domain.policy.rule.edit_booking_rule import EditBookingRule
from prometheus.domain.policy.rule.edit_dnr_rule import EditDNRRule
from prometheus.domain.policy.rule.edit_guarantee_information_rule import (
    EditGuaranteeInformationRule,
)
from prometheus.domain.policy.rule.edit_guest_details_rule import EditGuestDetailsRule
from prometheus.domain.policy.rule.edit_payment_rule import EditPaymentRule
from prometheus.domain.policy.rule.generate_credit_note_rule import (
    GenerateCreditNoteRule,
)
from prometheus.domain.policy.rule.generate_invoices_for_billed_entity_accounts_rule import (
    GenerateInvoicesForBilledEntityAccountsRule,
)
from prometheus.domain.policy.rule.generate_invoices_for_new_charges_rule import (
    GenerateInvoicesForNewChargesRule,
)
from prometheus.domain.policy.rule.hotel_invoice_generation_rule import (
    HotelInvoiceGenerationRule,
)
from prometheus.domain.policy.rule.housekeeping_occupancy_record_rule import (
    HouseKeepingOccupancyRecordRule,
)
from prometheus.domain.policy.rule.inventory_block_release_rule import (
    InventoryBlockReleaseRule,
)
from prometheus.domain.policy.rule.manual_funding_request_rule import FundingRequestRule
from prometheus.domain.policy.rule.mark_dnr_rule import MarkDNRRule
from prometheus.domain.policy.rule.mark_noshow_rule import NoshowRule
from prometheus.domain.policy.rule.night_audit_rule import NightAuditRules
from prometheus.domain.policy.rule.overbooking_rule import OverBookingRule
from prometheus.domain.policy.rule.payment_entity_rules import PaymentRules
from prometheus.domain.policy.rule.post_allowance_rule import PostAllowanceRule
from prometheus.domain.policy.rule.redeem_credit_shell_rule import RedeemCreditShellRule
from prometheus.domain.policy.rule.reissue_invoice_without_buy_side_rule import (
    ReissueInvoiceWithoutBuySide,
)
from prometheus.domain.policy.rule.remove_dnr_rule import RemoveDNRRule
from prometheus.domain.policy.rule.remove_guest_rule import RemoveGuestRule
from prometheus.domain.policy.rule.remove_room_rule import RemoveRoomRule
from prometheus.domain.policy.rule.report_rule import AccessReportRule
from prometheus.domain.policy.rule.resolve_dnr_rule import ResolveDNRRule
from prometheus.domain.policy.rule.spot_credit_rule import SettleBySpotCreditRule
from prometheus.domain.policy.rule.swap_overflow_rule import SwapOverflowRule
from prometheus.domain.policy.rule.transfer_charge_rule import TransferChargeRule
from prometheus.domain.policy.rule.undo_cancel_booking_rule import (
    UndoCancelNoShowBookingRule,
)
from prometheus.domain.policy.rule.undo_checkin_booking_rule import (
    UndoCheckinBookingRule,
)
from prometheus.domain.policy.rule.undo_checkout_booking_rule import (
    UndoCheckoutBookingRule,
)
from prometheus.domain.policy.rule.update_room_stay_room_type_rule import (
    UpdateRoomStayRoomTypeRule,
)
from prometheus.domain.policy.rule.update_ta_commission_rule import (
    UpdateTaCommissionRule,
)
from prometheus.infrastructure.external_clients.role_privilege.role_privilege_client import (
    RoleManagerClient,
)
from prometheus.infrastructure.external_clients.role_privilege.role_privilege_dto import (
    RolePrivilegesDTO,
)
from ths_common.exceptions import PolicyAuthException
from ths_common.value_objects import NotAssigned

logger = logging.getLogger(__name__)


class RuleEngine(object):
    action_rule_map = dict(
        create_booking=CreateBookingRule,
        replace_booking=CreateBookingRule,
        edit_booking=EditBookingRule,
        overbooking=OverBookingRule,
        add_room_stay=AddRoomRule,
        edit_booking_owner=ChangeBookingOwnerDetailRule,
        add_guest_stay=AddGuestRule,
        change_stay_dates=ChangeStayDatesRule,
        cancel_booking=CancelBookingRule,
        undo_cancel_booking=UndoCancelNoShowBookingRule,
        undo_noshow_booking=UndoCancelNoShowBookingRule,
        remove_room_stay=RemoveRoomRule,
        remove_guest_stay=RemoveGuestRule,
        mark_dnr=MarkDNRRule,
        resolve_dnr=ResolveDNRRule,
        remove_dnr=RemoveDNRRule,
        create_addon=CreateAddonRule,
        edit_addon=EditAddonRule,
        delete_addon=DeleteAddonRule,
        create_expense=CreateExpenseRule,
        create_v3_expense=CreateExpenseV3Rule,
        payment_add_or_edit=PaymentRules,
        edit_payment=EditPaymentRule,
        charge_edit=ChargeEditRules,
        invoiced_charge_edit=InvoicedChargeEditRule,
        regenerate_invoices=InvoicedChargeEditRule,
        confirm_invoices=InvoiceConfirmationRule,
        edit_guest_details=EditGuestDetailsRule,
        reverse_checkin=UndoCheckinBookingRule,
        reverse_checkout=UndoCheckoutBookingRule,
        edit_dnr=EditDNRRule,
        generate_credit_note=GenerateCreditNoteRule,
        generate_hotel_invoice=HotelInvoiceGenerationRule,
        checkin=CheckinRule,
        checkout=CheckOutRule,
        mark_noshow=NoshowRule,
        confirm_booking=ConfirmBookingRule,
        update_room_type=UpdateRoomStayRoomTypeRule,
        swap_overflow=SwapOverflowRule,
        delete_attachment=DeleteAttachmentRule,
        access_entity=AccessEntity,
        access_attachment=AccessAttachmentRule,
        generate_invoices_for_new_charges=GenerateInvoicesForNewChargesRule,
        generate_invoice_for_billed_entity_accounts=GenerateInvoicesForBilledEntityAccountsRule,
        open_cashier_session=OpenCashierSessionRule,
        update_cashier_session=UpdateCashierSessionRule,
        cashier_payment_edit=CashierPaymentUpdateRules,
        cashier_payment_add=CashierPaymentAddRules,
        open_cash_register=OpenCashRegisterRules,
        update_cash_register=UpdateCashRegisterRules,
        access_report=AccessReportRule,
        edit_attachment=EditAttachmentRule,
        access_cashier=AccessCashierRule,
        reschedule_night_audit=NightAuditRules,
        transfer_charge=TransferChargeRule,
        post_allowance=PostAllowanceRule,
        cancel_allowance=CancelAllowanceRule,
        edit_billing_instruction=EditBillingInstructionRule,
        settle_by_spot_credit=SettleBySpotCreditRule,
        reissue_invoice_without_buy_side=ReissueInvoiceWithoutBuySide,
        housekeeping_occupancy_record=HouseKeepingOccupancyRecordRule,
        update_ta_commission_rule=UpdateTaCommissionRule,
        redeem_credit_shell=RedeemCreditShellRule,
        allowed_action_rule=BookingStateTransitionRule,
        edit_guarantee_information=EditGuaranteeInformationRule,
        add_funding_request=FundingRequestRule,
        inventory_block_release=InventoryBlockReleaseRule,
        create_inventory_block=CreateInventoryBockRule,
    )

    @staticmethod
    def action_allowed(action, facts, fail_on_error=False):
        rule = RuleEngine.action_rule_map.get(action)
        assert rule is not None, f"mapping not defined for {action} in action_rule_map"

        privileges = dict()

        if crs_context.privileges_as_dict is not NotAssigned:
            privileges = crs_context.privileges_as_dict
        else:
            if facts.user_type:
                logger.debug(
                    "Getting user_type {0} for action {1}".format(
                        facts.user_type, action
                    )
                )
                try:
                    role_privilege_dtos = (
                        RoleManagerClient().get_privilege_by_role_name(facts.user_type)
                    )
                except:
                    role_privilege_dtos = []

                privileges = RolePrivilegesDTO.array_to_dict(
                    role_privilege_dtos=role_privilege_dtos
                )
                crs_context.privileges_as_dict = privileges
                crs_context.role_privilege_dtos = role_privilege_dtos

        try:
            return rule().allow(facts, privileges)
        except PolicyAuthException as pae:
            if fail_on_error:
                raise
            logger.debug(
                "Policy evaluation failed for action: %s: %s", action, pae.message
            )
            return False
