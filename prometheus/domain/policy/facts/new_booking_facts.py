from treebo_commons.utils import dateutils

from prometheus.core.globals import HotelContext
from prometheus.domain.booking.dtos.new_booking_dto import NewBookingDomainDto
from prometheus.domain.policy.facts import Facts
from prometheus.infrastructure.external_clients.core.constants import CORPORATE_CHANNELS
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    ChargeTypes,
    PaymentReceiverTypes,
    PaymentTypes,
)
from ths_common.constants.booking_constants import (
    BookingApplications,
    BookingChannels,
    BookingStatus,
    BookingSubChannels,
)


class NewBookingFacts(Facts):
    def __init__(
        self,
        user_type=None,
        action_payload=None,
        current_time=None,
        hotel_context: HotelContext = None,
        **aggregates
    ):
        super().__init__(
            current_time=current_time,
            user_type=user_type,
            action_payload=action_payload,
            hotel_context=hotel_context,
            **aggregates
        )

    def get_new_booking_source(self):
        if isinstance(self.action_payload, NewBookingDomainDto):
            return self.action_payload.source
        else:
            return self.action_payload.get("source")

    def is_direct_booking_received(self):
        return (
            self.get_new_booking_source().get('channel_code')
            == BookingChannels.DIRECT.value
        )

    def is_ota_booking_received(self):
        return (
            self.get_new_booking_source().get('channel_code')
            == BookingChannels.OTA.value
        )

    def is_b2b_booking_received(self):
        return (
            self.get_new_booking_source().get('channel_code')
            == BookingChannels.B2B.value
        )

    def is_ta_booking_received(self):
        return (
            self.get_new_booking_source().get('channel_code')
            == BookingChannels.TA.value
        )

    def is_b2b_ta_booking_received(self):
        return self.is_b2b_booking_received() or self.is_ta_booking_received()

    def is_default_billed_entity_category_received(self):
        if isinstance(self.action_payload, NewBookingDomainDto):
            return self.action_payload.default_billed_entity_category
        return self.action_payload.get('default_billed_entity_category')

    def is_b2b_ta_bulk_booking_received(self):
        return self.get_new_booking_source().get('subchannel_code') in {
            BookingSubChannels.B2B_BULK.value,
            BookingSubChannels.TA_BULK.value,
        }

    def is_non_bulk_b2b_ta_booking_received(self):
        return (
            self.is_b2b_ta_booking_received()
            and not self.is_b2b_ta_bulk_booking_received()
        )

    def is_treebo_internal_booking_received(self):
        return (
            self.get_new_booking_source().get('channel_code')
            == BookingChannels.TREEBO_INTERNAL.value
        )

    def is_booking_received_from_su(self):
        return (
            self.get_new_booking_source().get('application_code')
            == BookingApplications.SU.value
        )

    def is_booking_received_from_emma(self):
        return (
            self.get_new_booking_source().get('application_code')
            == BookingApplications.EMMA.value
        )

    def is_booking_received_from_treebo_pms(self):
        return (
            self.get_new_booking_source().get('application_code')
            == BookingApplications.TREEBO_PMS.value
        )

    def booking_channel_code(self):
        return self.get_new_booking_source().get('channel_code')

    def get_room_stays_in_new_booking(self):
        if isinstance(self.action_payload, NewBookingDomainDto):
            return self.action_payload.room_stays
        else:
            return self.action_payload.get('room_stays')

    def has_credit_charge(self):
        for room_stay in self.get_room_stays_in_new_booking():
            for price in room_stay.get('prices'):
                if price.type == ChargeTypes.CREDIT:
                    return True
        return False

    def is_past_dated_booking_received(self):
        booking_checkin_date = min(
            r.get('checkin_date') for r in self.get_room_stays_in_new_booking()
        )
        return (
            dateutils.to_date(booking_checkin_date) < self.hotel_context.current_date()
        )

    def is_future_booking_after_live_date_received(self):
        booking_checkin_date = min(
            dateutils.to_date(r.get('checkin_date'))
            for r in self.get_room_stays_in_new_booking()
        )
        return booking_checkin_date >= self.hotel_context.live_date

    def get_new_booking_status(self):
        if isinstance(self.action_payload, NewBookingDomainDto):
            return self.action_payload.status
        else:
            return self.action_payload.get('status')

    def is_soft_booking_received(self):
        return self.get_new_booking_status() == BookingStatus.TEMPORARY

    def is_reserved_booking_received(self):
        return self.get_new_booking_status() == BookingStatus.RESERVED

    def get_payments_in_new_booking(self):
        if isinstance(self.action_payload, NewBookingDomainDto):
            return self.action_payload.payments
        else:
            return self.action_payload.get("payments")

    def all_payment_requests_has_paid_to_hotel(self):
        if self.get_payments_in_new_booking():
            for payment in self.get_payments_in_new_booking():
                paid_to = payment.get('paid_to')
                if paid_to and paid_to != PaymentReceiverTypes.HOTEL:
                    return False
        return True

    def all_payment_requests_has_paid_to_guest(self):
        if self.get_payments_in_new_booking():
            for payment in self.get_payments_in_new_booking():
                paid_to = payment.get('paid_to')
                if paid_to and paid_to != PaymentReceiverTypes.GUEST:
                    return False
        return True

    def all_payment_requests_has_paid_by_guest(self):
        if self.get_payments_in_new_booking():
            for payment in self.get_payments_in_new_booking():
                paid_by = payment.get('paid_by')
                if paid_by and paid_by != PaymentReceiverTypes.GUEST:
                    return False
        return True

    def all_payment_requests_has_paid_by_hotel(self):
        if self.get_payments_in_new_booking():
            for payment in self.get_payments_in_new_booking():
                paid_by = payment.get('paid_by')
                if paid_by and paid_by != PaymentReceiverTypes.HOTEL:
                    return False
        return True

    def all_payment_requests_are_refund(self):
        if self.get_payments_in_new_booking():
            for payment in self.get_payments_in_new_booking():
                payment_type = payment.get('payment_type')
                if payment_type and PaymentTypes(payment_type) != PaymentTypes.REFUND:
                    return False
        return True

    def has_booking_received_payments(self):
        return bool(self.get_payments_in_new_booking())

    def allowed_payment_modes(self, modes):
        if not self.get_payments_in_new_booking():
            return True

        for payment in self.get_payments_in_new_booking():
            if payment.get('payment_mode') not in modes:
                return False
        return True

    def are_mandatory_gst_details_received(self):
        if isinstance(self.action_payload, NewBookingDomainDto):
            booking_owner_data = self.action_payload.booking_owner
        else:
            booking_owner_data = self.action_payload.get('booking_owner')

        if booking_owner_data and ('gst_details' in booking_owner_data):
            gst_details = booking_owner_data.get('gst_details')
            return (
                gst_details
                and gst_details.legal_name
                and gst_details.address
                and gst_details.address.field_1
                and gst_details.address.country
                and gst_details.address.state
                and gst_details.address.city
                and gst_details.address.pincode
            )
        return False

    def are_company_details_received(self):
        if isinstance(self.action_payload, NewBookingDomainDto):
            company_details = self.action_payload.company_details
        else:
            company_details = self.action_payload.get('company_details')
        return company_details or self.are_mandatory_gst_details_received()

    def are_travel_agent_details_received(self):
        if isinstance(self.action_payload, NewBookingDomainDto):
            travel_agent_details = self.action_payload.travel_agent_details
        else:
            travel_agent_details = self.action_payload.get('travel_agent_details')
        return travel_agent_details or self.are_mandatory_gst_details_received()

    def either_company_or_travel_agent_details_received(self):
        return (
            self.are_company_details_received()
            or self.are_travel_agent_details_received()
        )

    def is_default_billed_entity_category_travel_agent(self):
        if isinstance(self.action_payload, NewBookingDomainDto):
            return (
                self.action_payload.default_billed_entity_category
                == BilledEntityCategory.TRAVEL_AGENT
            )
        else:
            return (
                self.action_payload.get('default_billed_entity_category')
                == BilledEntityCategory.TRAVEL_AGENT
            )

    def is_default_billed_entity_category_booker_company(self):
        if isinstance(self.action_payload, NewBookingDomainDto):
            return (
                self.action_payload.default_billed_entity_category
                == BilledEntityCategory.BOOKER_COMPANY
            )
        else:
            return (
                self.action_payload.get('default_billed_entity_category')
                == BilledEntityCategory.BOOKER_COMPANY
            )

    def is_corporate_booking(self):
        booking_channel = self.get_new_booking_source().get('channel_code')
        corporate_channels = [BookingChannels.B2B.value, BookingChannels.TA.value]
        if self.tenant_facts_context and self.tenant_facts_context.corporate_channels:
            corporate_channels = self.tenant_facts_context.corporate_channels
        return booking_channel in corporate_channels
