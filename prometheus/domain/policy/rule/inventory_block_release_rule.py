from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class InventoryBlockReleaseRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if PrivilegeCode.RELEASE_INVENTORY_BLOCKS not in privileges:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_RELEASE_INVENTORY_BLOCKS
            )

        return True
