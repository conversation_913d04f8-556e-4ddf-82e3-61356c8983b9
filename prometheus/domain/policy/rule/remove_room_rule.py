from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.facts.room_stay_facts import RoomStayFacts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class RemoveRoomRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        assert isinstance(facts, RoomStayFacts)

        if (
            PrivilegeCode.EDIT_BOOKING_FOR_CH not in privileges
            or facts.get_channel_code()
            not in privileges[PrivilegeCode.EDIT_BOOKING_FOR_CH]
        ):
            raise PolicyAuthException(
                error=PolicyError.CANCELLATION_OF_ROOM_NOT_ALLOWED_FOR_BOOKING_FROM_THIS_CHANNEL
            )

        if (
            facts.user_type_fdm_or_aom()
            and facts.walk_in_or_hotel_corp_booking()
            and facts.current_time > facts.midnight_of_checkin_date()
        ):
            raise PolicyAuthException(
                error=PolicyError.CANCELLATION_OF_ROOM_ONLY_ALLOWED_TILL_MIDNIGHT_OF_CHECKIN_DATE
            )

        if (
            not facts.hotel_live()
            and PrivilegeCode.FULL_ACCESS_IF_HOTEL_NOT_LIVE not in privileges
        ):
            raise PolicyAuthException(
                error=PolicyError.ROOM_CANCELLATION_NOT_ALLOWED_BEFORE_HOTEL_LIVE_DATE
            )

        return True
