from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.dnr_facts import DNRFacts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode, UserType
from ths_common.exceptions import PolicyAuthException


class ResolveDNRRule(BaseRule):
    def allow(self, facts: DNRFacts, privileges=None):
        if facts.is_room_inactive_dnr() and not facts.is_backend_system_user_type():
            raise PolicyAuthException(
                error=PolicyError.NOT_ALLOWED_TO_RESOLVE_DNR_FOR_INACTIVE_ROOM
            )

        if (
            facts.user_type in [UserType.CR_TEAM.value, UserType.AOM.value]
            and facts.is_past_date_dnr()
        ):
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_RESOLVE_PAST_DATED_DNR
            )

        if PrivilegeCode.RESOLVE_DNR not in privileges:
            raise PolicyAuthException(error=PolicyError.NOT_AUTHORIZED_TO_RESOLVE_DNR)

        return True
