from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class AccessCashierRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if PrivilegeCode.VIEW_CSHR not in privileges:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_ACCESS_CASHIER_MODULE
            )
        return True
