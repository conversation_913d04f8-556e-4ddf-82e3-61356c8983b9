from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.night_audit_facts import NightAuditFacts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class NightAuditRules(BaseRule):
    def allow(self, facts: NightAuditFacts, privileges=None):
        if facts.is_future_day_night_audit():
            if PrivilegeCode.CAN_PERFORM_NIGHT_AUDIT_FOR_FUTURE_DATES not in privileges:
                raise PolicyAuthException(
                    error=PolicyError.NOT_AUTHORIZED_TO_PERFORM_NIGHT_AUDIT_FOR_FUTURE_DATE
                )

        else:
            if PrivilegeCode.CAN_PERFORM_NIGHT_AUDIT_FOR_PAST_DATE not in privileges:
                raise PolicyAuthException(
                    error=PolicyError.NOT_AUTHORIZED_TO_PERFORM_NIGHT_AUDIT_FOR_PAST_DATE
                )
