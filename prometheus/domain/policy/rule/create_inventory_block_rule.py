from prometheus import crs_context
from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.inventory_block_facts import InventoryBlockFacts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.exceptions import PolicyAuthException


class CreateInventoryBockRule(BaseRule):
    def allow(self, facts: InventoryBlockFacts, privileges=None):
        if facts.is_hotel_bounded_request():
            if crs_context.user_data.hotel_id != facts.hotel_id:
                raise PolicyAuthException(error=PolicyError.NOT_AUTHORIZED)

        return True
