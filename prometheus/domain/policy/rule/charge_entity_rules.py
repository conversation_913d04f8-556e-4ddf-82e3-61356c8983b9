import logging

from prometheus import crs_context
from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.charge_facts import ChargeFacts
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException

logger = logging.getLogger(__name__)


class ChargeEditRules(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        assert isinstance(facts, ChargeFacts)

        if facts.is_billing_instructions_edited():
            charge = facts.get_charge()

            if facts.is_new_account_getting_created_in_edit_billing_instructions():
                if (
                    PrivilegeCode.CAN_CREATE_NEW_ACCOUNT_IN_BILLED_ENTITY
                    not in privileges
                ):
                    raise PolicyAuthException(
                        error=PolicyError.NOT_AUTHORIZED_TO_CREATE_NEW_ACCOUNT
                    )
            privilege_to_check = (
                PrivilegeCode.CAN_EDIT_BILLING_DETAILS_OF_RATE_PLAN_CHARGE
                if charge.is_rate_plan_charge()
                else PrivilegeCode.CAN_EDIT_BILLING_DETAILS_OF_EXTRA_CHARGE
            )

            if privilege_to_check not in privileges:
                if (
                    facts.is_edit_request_only_for_updating_billed_entity_account()
                    and PrivilegeCode.CAN_EDIT_BILLED_ENTITY_ACCOUNT_ON_CHARGE
                    in privileges
                ):
                    privilege_to_check = (
                        PrivilegeCode.CAN_EDIT_BILLED_ENTITY_ACCOUNT_ON_CHARGE
                    )
                else:
                    raise PolicyAuthException(
                        error=PolicyError.CANNOT_EDIT_BILLING_DETAILS_OF_THIS_CHARGE
                    )

            attribute_values = privileges[privilege_to_check]
            attribute_values = [av.lower() for av in attribute_values]

            logger.info(
                "Verifying privilege: '%s' in edit_billing_details against attribute values: '%s', and "
                "charge_status: '%s', channel_code: '%s'",
                privilege_to_check,
                attribute_values,
                facts.get_charge_status().lower(),
                facts.get_channel_code().lower(),
            )

            if facts.get_charge_status().lower() not in attribute_values:
                raise PolicyAuthException(
                    error=PolicyError.CANNOT_EDIT_BILLING_DETAILS_OF_THIS_CHARGE
                )

            if facts.get_channel_code().lower() not in attribute_values:
                raise PolicyAuthException(
                    error=PolicyError.CANNOT_EDIT_BILLING_DETAILS_OF_THIS_CHARGE
                )

        if (
            crs_context.is_treebo_tenant()
            and not facts.is_edit_request_only_for_updating_billed_entity_account()
        ):
            if (facts.is_charge_room_rent() or facts.is_charge_credit()) and (
                PrivilegeCode.MODIFY_CR_CHARGE not in privileges
            ):
                raise PolicyAuthException(
                    error=PolicyError.CANNOT_MODIFY_ROOM_STAY_OR_CREDIT_CHARGE
                )

        if (
            facts.is_edit_request_for_cancel()
            and facts.is_booking_closed()
            and (PrivilegeCode.MODIFY_NON_CR_CHARGE not in privileges)
        ):
            raise PolicyAuthException(
                error=PolicyError.CHARGE_CANCELLATION_POST_CHECKOUT_NOT_ALLOWED
            )

        if facts.has_invoice_attached():
            raise PolicyAuthException(error=PolicyError.CANNOT_MODIFY_INVOICED_CHARGE)

        if facts.has_credit_note_attached():
            raise PolicyAuthException(
                error=PolicyError.CANNOT_MODIFY_CREDIT_NOTE_CHARGE
            )

        if (
            facts.is_booking_closed()
            and PrivilegeCode.ADD_CHARGE_AFTER_CO not in privileges
        ):
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_EDIT_CHARGE_POST_CHECKOUT
            )

        if (
            not facts.hotel_live()
            and PrivilegeCode.FULL_ACCESS_IF_HOTEL_NOT_LIVE not in privileges
        ):
            raise PolicyAuthException(
                error=PolicyError.EDIT_CHARGE_NOT_ALLOWED_BEFORE_HOTEL_LIVE_DATE
            )

        if PrivilegeCode.MODIFY_NON_CR_CHARGE not in privileges:
            raise PolicyAuthException(error=PolicyError.NOT_AUTHORIZED_TO_MODIFY_CHARGE)

        return True


class InvoicedChargeEditRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if PrivilegeCode.RI_INV_BY_MODIFY_CHARGE_AND_BY_GEN_CN not in privileges:
            raise PolicyAuthException(error=PolicyError.CANNOT_MODIFY_INVOICED_CHARGE)
        return True


class InvoiceConfirmationRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if not facts.user_type_super_admin():
            raise PolicyAuthException(error=PolicyError.CANNOT_CONFIRM_INVOICE)
        return True
