from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class UpdateTaCommissionRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if PrivilegeCode.EDIT_COMMISSION_IN_BOOKING not in privileges:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_EDIT_COMMISSION_IN_BOOKING
            )
        if facts.is_booking_checked_out() or facts.is_cancelled_or_noshow_booking():
            raise PolicyAuthException(
                error=PolicyError.INVALID_STATE_OF_BOOKING_TO_EDIT_COMMISSION
            )
        booking_aggregate = facts.get_booking_aggregate()
        if not booking_aggregate.is_commission_applicable():
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_EDIT_COMMISSION_IN_BOOKING,
                description="TA commission is not applicable for booking from this particular channel",
            )
        travel_agent_details = booking_aggregate.get_travel_agent_details()
        if not travel_agent_details:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_EDIT_COMMISSION_IN_BOOKING,
                description="TA details are missing for this booking",
            )
        return True
