from prometheus import crs_context
from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class OpenCashierSessionRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if PrivilegeCode.MANAGE_CSHR_SESSION not in privileges:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_CREATE_CASHIER_SESSION
            )

        return True


class UpdateCashierSessionRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if PrivilegeCode.MANAGE_CSHR_SESSION not in privileges:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_CREATE_CASHIER_SESSION
            )

        return True
