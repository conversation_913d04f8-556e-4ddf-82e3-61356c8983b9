from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class NoshowRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if PrivilegeCode.MARK_NOSHOW not in privileges:
            raise PolicyAuthException(error=PolicyError.NOT_AUTHORIZED_TO_MARK_NOSHOW)

        if (
            not facts.hotel_live()
            and PrivilegeCode.FULL_ACCESS_IF_HOTEL_NOT_LIVE not in privileges
        ):
            raise PolicyAuthException(
                error=PolicyError.CANNOT_MARK_NOSHOW_PRIOR_TO_HOTEL_LAUNCH
            )

        return True
