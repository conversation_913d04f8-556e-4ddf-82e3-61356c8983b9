from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class DeleteAttachmentRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if not facts.is_attachment_created_by_same_user() and (
            PrivilegeCode.DELETE_ATCH_TYPE not in privileges
            or facts.attachment_type() not in privileges[PrivilegeCode.DELETE_ATCH_TYPE]
        ):
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_DELETE_ATTACHMENT
            )

        return True


class AccessAttachmentRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if (
            PrivilegeCode.VIEW_AND_DL_ATCH_TYPE not in privileges
            or facts.attachment_type()
            not in privileges[PrivilegeCode.VIEW_AND_DL_ATCH_TYPE]
        ):
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_ACCESS_ATTACHMENT
            )

        return True


class EditAttachmentRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if PrivilegeCode.MODIFY_PROOF_ON_WCI not in privileges:
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_MODIFY_ATTACHMENT
            )

        return True
