import logging
from collections import defaultdict

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.decorators import set_hotel_context
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories import RoomTypeRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from prometheus.reporting.departure_report.departure_report_generator import (
    DepartureReportGenerator,
)
from prometheus.reporting.reporting_service import ReportingApplicationService
from prometheus.reporting.utils import CsvWriter
from ths_common.constants.billing_constants import PaymentStatus
from ths_common.constants.booking_constants import BookingStatus
from ths_common.utils.collectionutils import chunks

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        RoomTypeRepository,
        ReportingApplicationService,
    ]
)
class DepartureReportingService(object):
    """
    Generic application service for reports/reporting
    """

    def __init__(
        self,
        booking_repository,
        bill_repository,
        hotel_repository,
        room_type_repository,
        reporting_application_service,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.hotel_repository = hotel_repository
        self.room_type_repository = room_type_repository
        self.reporting_application_service = reporting_application_service

    @staticmethod
    def _fail_if_user_not_authorized_to_access_reports():
        return RuleEngine.action_allowed(
            action="access_report",
            facts=Facts(
                user_type=crs_context.user_data.user_type,
                hotel_context=crs_context.get_hotel_context(),
            ),
            fail_on_error=True,
        )

    def _base_report_data(self, start_date, end_date, hotel_aggregate):
        booking_aggregates_for_date_range = (
            self.booking_repository.departure_report_query(
                dateutils.date_to_ymd_str(start_date),
                dateutils.date_to_ymd_str(end_date),
                hotel_aggregate.hotel_id,
            )
        )

        hotel_map = {hotel_aggregate.hotel_id: hotel_aggregate}
        room_type_map = self.room_type_repository.load_type_map()
        return booking_aggregates_for_date_range, hotel_map, room_type_map

    @set_hotel_context()
    def report_summary(
        self, start_date, end_date, user_data=None, hotel_aggregate=None
    ):
        self._fail_if_user_not_authorized_to_access_reports()
        booking_aggregates, hotel_map, room_type_map = self._base_report_data(
            start_date, end_date, hotel_aggregate
        )
        departure_report_summary = {'summary': [], 'datewise_summary': []}
        for bookings in chunks(booking_aggregates, 1000):
            departure_report_summary_chunk = self._generate_report_summary(
                bookings, hotel_map, room_type_map, start_date, end_date
            )
            departure_report_summary['summary'].extend(
                departure_report_summary_chunk['summary']
            )
            departure_report_summary['datewise_summary'].extend(
                departure_report_summary_chunk['datewise_summary']
            )

        return departure_report_summary

    def _generate_report_summary(
        self, booking_aggregates, hotel_map, room_type_map, start_date, end_date
    ):
        bill_ids = {
            booking_aggregate.bill_id for booking_aggregate in booking_aggregates
        }
        aggregate_maps = self.reporting_application_service.build_aggregate_maps(
            bill_ids=bill_ids,
            load_invoices_by_bill_id=True,
            hotel_map=hotel_map,
            room_type_map=room_type_map,
        )
        report_aggregates = []
        for booking_aggregate in booking_aggregates:
            bill_aggregate = aggregate_maps.bill_map.get(
                booking_aggregate.booking.bill_id
            )
            hotel_aggregate = aggregate_maps.hotel_map.get(
                booking_aggregate.booking.hotel_id
            )
            departure_report_aggregates = DepartureReportGenerator(
                booking_aggregate,
                bill_aggregate,
                hotel_aggregate,
                aggregate_maps.room_type_map,
                start_date,
                end_date,
            ).generate()
            if departure_report_aggregates:
                report_aggregates.append(departure_report_aggregates)

        return {
            'summary': self._channel_wise_report_summary(
                report_aggregates, start_date, end_date
            ),
            'datewise_summary': self._get_datewise_report_summary(
                report_aggregates, start_date, end_date
            ),
        }

    def _channel_wise_report_summary(self, report_aggregates, start_date, end_date):
        channel_wise_booking_summary = defaultdict(dict)
        already_visited_bookings = []
        for departure_report_aggregates in report_aggregates:
            booking_counted = 0
            for departure_report_aggregate in departure_report_aggregates:
                channel = departure_report_aggregate.get_channel
                if (
                    departure_report_aggregate.booking_id
                    not in already_visited_bookings
                ):
                    total_booking_amount = (
                        departure_report_aggregate.bill_aggregate.total_posttax_amount()
                    )
                    booking_amount = (
                        total_booking_amount
                        + channel_wise_booking_summary[channel].get(
                            'total_booking_amount', 0
                        )
                    )
                    channel_wise_booking_summary[channel][
                        'total_booking_amount'
                    ] = booking_amount
                    already_visited_bookings.append(
                        departure_report_aggregate.booking_id
                    )
                if not channel_wise_booking_summary[channel].get('number_of_guests'):
                    channel_wise_booking_summary[channel]['number_of_guests'] = {
                        'adults': 0,
                        'children': 0,
                    }
                channel_wise_booking_summary[channel]['number_of_guests']['adults'] = (
                    departure_report_aggregate.adult_count
                    + channel_wise_booking_summary[channel]['number_of_guests'][
                        'adults'
                    ]
                )
                channel_wise_booking_summary[channel]['number_of_guests'][
                    'children'
                ] = (
                    departure_report_aggregate.child_count
                    + channel_wise_booking_summary[channel]['number_of_guests'][
                        'children'
                    ]
                )
                channel_wise_booking_summary[channel]['channel_name'] = channel
                if booking_counted == 0:
                    booking_counted = 1
                    if not channel_wise_booking_summary[channel].get(
                        'number_of_bookings'
                    ):
                        channel_wise_booking_summary[channel]['number_of_bookings'] = 1
                    else:
                        channel_wise_booking_summary[channel]['number_of_bookings'] += 1
        return list(channel_wise_booking_summary.values())

    def _get_datewise_report_summary(self, report_aggregates, start_date, end_date):
        datewise_channel_wise_summary = defaultdict(lambda: defaultdict(dict))
        datewise_summary = []
        already_visited_bookings = []
        for departure_report_aggregates in report_aggregates:
            booking_counted = 0
            for departure_report_aggregate in departure_report_aggregates:
                channel = departure_report_aggregate.get_channel
                adult_count = departure_report_aggregate.adult_count
                child_count = departure_report_aggregate.child_count
                checkout_date = (
                    departure_report_aggregate.booking_aggregate.booking.actual_checkout_date
                    or departure_report_aggregate.booking_aggregate.booking.checkout_date
                )
                checkout_date = crs_context.hotel_context.hotel_checkout_date(
                    checkout_date
                )
                if (
                    departure_report_aggregate.booking_id
                    not in already_visited_bookings
                ):
                    total_booking_amount = (
                        departure_report_aggregate.bill_aggregate.total_posttax_amount()
                    )
                    booking_amount = (
                        total_booking_amount
                        + datewise_channel_wise_summary[checkout_date][channel].get(
                            'total_booking_amount', 0
                        )
                    )
                    datewise_channel_wise_summary[checkout_date][channel][
                        'total_booking_amount'
                    ] = booking_amount
                    already_visited_bookings.append(
                        departure_report_aggregate.booking_id
                    )

                if not datewise_channel_wise_summary[checkout_date][channel].get(
                    'number_of_guests'
                ):
                    datewise_channel_wise_summary[checkout_date][channel][
                        'number_of_guests'
                    ] = {'adults': 0, 'children': 0}

                datewise_channel_wise_summary[checkout_date][channel][
                    'date'
                ] = checkout_date
                datewise_channel_wise_summary[checkout_date][channel][
                    'channel_name'
                ] = channel
                if booking_counted == 0:
                    booking_counted = 1
                    if not datewise_channel_wise_summary[checkout_date][channel].get(
                        'booking_count'
                    ):
                        datewise_channel_wise_summary[checkout_date][channel][
                            'booking_count'
                        ] = 1
                    else:
                        datewise_channel_wise_summary[checkout_date][channel][
                            'booking_count'
                        ] += 1
                if not datewise_channel_wise_summary[checkout_date][channel].get(
                    'number_of_rooms'
                ):
                    datewise_channel_wise_summary[checkout_date][channel][
                        'number_of_rooms'
                    ] = 1
                else:
                    datewise_channel_wise_summary[checkout_date][channel][
                        'number_of_rooms'
                    ] += 1

                datewise_channel_wise_summary[checkout_date][channel][
                    'number_of_guests'
                ]['adults'] = (
                    adult_count
                    + datewise_channel_wise_summary[checkout_date][channel][
                        'number_of_guests'
                    ]['adults']
                )
                datewise_channel_wise_summary[checkout_date][channel][
                    'number_of_guests'
                ]['children'] = (
                    child_count
                    + datewise_channel_wise_summary[checkout_date][channel][
                        'number_of_guests'
                    ]['children']
                )

        for date, summary_for_date in datewise_channel_wise_summary.items():
            for channel, booking_summary in summary_for_date.items():
                datewise_summary.append(booking_summary)

        return datewise_summary

    @set_hotel_context()
    def report_details(
        self, start_date, end_date, user_data=None, hotel_aggregate=None
    ):
        self._fail_if_user_not_authorized_to_access_reports()
        booking_aggregates, hotel_map, room_type_map = self._base_report_data(
            start_date, end_date, hotel_aggregate
        )

        departure_report_details = []
        for bookings in chunks(booking_aggregates, 1000):
            departure_report_details.extend(
                self._generate_report_details(
                    bookings, hotel_map, room_type_map, start_date, end_date
                )
            )
        if departure_report_details:
            departure_report_details = sorted(
                departure_report_details,
                key=lambda departure_report_detail: departure_report_detail[
                    'checkout_date'
                ],
            )
        return {'reports': departure_report_details}

    def _generate_report_details(
        self, booking_aggregates, hotel_map, room_type_map, start_date, end_date
    ):
        bill_ids = {
            booking_aggregate.bill_id for booking_aggregate in booking_aggregates
        }
        aggregate_maps = self.reporting_application_service.build_aggregate_maps(
            bill_ids=bill_ids,
            load_invoices_by_bill_id=True,
            hotel_map=hotel_map,
            room_type_map=room_type_map,
        )
        report_details_response = []
        for booking_aggregate in booking_aggregates:
            bill_aggregate = aggregate_maps.bill_map.get(
                booking_aggregate.booking.bill_id
            )
            hotel_aggregate = aggregate_maps.hotel_map.get(
                booking_aggregate.booking.hotel_id
            )

            departure_report_aggregates = DepartureReportGenerator(
                booking_aggregate,
                bill_aggregate,
                hotel_aggregate,
                aggregate_maps.room_type_map,
                start_date,
                end_date,
            ).generate()
            if departure_report_aggregates:
                for departure_report_aggregate in departure_report_aggregates:
                    report_details_response.append(
                        {
                            "booking_id": departure_report_aggregate.booking_id,
                            "booking_status": departure_report_aggregate.booking_status,
                            "guest_name": departure_report_aggregate.guest_name,
                            "number_of_guests": {
                                "adults": departure_report_aggregate.adult_count,
                                "children": departure_report_aggregate.child_count,
                            },
                            "room_number": departure_report_aggregate.room_number,
                            "room_type": departure_report_aggregate.room_type,
                            "checkin_date": departure_report_aggregate.checkin_date,
                            "checkout_date": departure_report_aggregate.checkout_date,
                            "booking_channel_type": departure_report_aggregate.get_channel,
                            "booking_amount": departure_report_aggregate.room_related_charges_posttax,
                            "amount_paid": departure_report_aggregate.payment_amount,
                            "remaining_balance_due": bill_aggregate.summary.balance,
                        }
                    )
        return report_details_response

    @set_hotel_context()
    def generate_csv_report(
        self, start_date, end_date, user_data=None, hotel_aggregate=None
    ):
        self._fail_if_user_not_authorized_to_access_reports()

        booking_aggregates, hotel_map, room_type_map = self._base_report_data(
            start_date, end_date, hotel_aggregate
        )

        file_path = DepartureReportGenerator.generate_departure_report_file_name()
        with CsvWriter(file_path) as csv_writer:
            for bookings in chunks(booking_aggregates, 1000):
                self._generate_csv_report(
                    bookings, hotel_map, room_type_map, csv_writer, start_date, end_date
                )
            presigned_url = AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                DepartureReportGenerator.DEPARTURE_REPORT_FOLDER_NAME,
                csv_writer.file_path,
                DepartureReportGenerator.get_default_expiration_time(),
            )

        return presigned_url

    def _generate_csv_report(
        self,
        booking_aggregates,
        hotel_map,
        room_type_map,
        csv_writer,
        start_date,
        end_date,
    ):
        bill_ids = {
            booking_aggregate.bill_id for booking_aggregate in booking_aggregates
        }
        aggregate_maps = self.reporting_application_service.build_aggregate_maps(
            bill_ids=bill_ids,
            load_invoices_by_bill_id=True,
            hotel_map=hotel_map,
            room_type_map=room_type_map,
        )
        report_aggregates = []
        for booking_aggregate in booking_aggregates:
            bill_aggregate = aggregate_maps.bill_map.get(
                booking_aggregate.booking.bill_id
            )
            hotel_aggregate = aggregate_maps.hotel_map.get(
                booking_aggregate.booking.hotel_id
            )
            csv_report_aggregates = DepartureReportGenerator(
                booking_aggregate,
                bill_aggregate,
                hotel_aggregate,
                aggregate_maps.room_type_map,
                start_date,
                end_date,
            ).generate()
            if csv_report_aggregates:
                report_aggregates.extend(csv_report_aggregates)

        # for marvin_report_aggregates in report_aggregates:
        csv_writer.write_aggregates(
            report_aggregates, DepartureReportGenerator.REPORT_COLUMNS
        )
