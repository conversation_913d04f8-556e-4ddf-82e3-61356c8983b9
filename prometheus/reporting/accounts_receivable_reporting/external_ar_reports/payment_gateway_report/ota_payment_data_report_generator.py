import logging
from collections import defaultdict
from datetime import datetime
from typing import Dict, List

from _decimal import Decimal
from treebo_commons.utils import dateutils

from object_registry import locate_instance
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.billing.dto.ar_reports_dto import CommissionDataDto
from prometheus.domain.billing.repositories import BillRepository, InvoiceRepository
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.infrastructure.ar_service_constants import EntryTypes, TaxType
from prometheus.reporting.accounts_receivable_reporting.constants import ar_context
from prometheus.reporting.accounts_receivable_reporting.external_ar_reports.payment_gateway_report.commission_report_aggregate import (
    CommissionReportAggregate,
)
from prometheus.reporting.accounts_receivable_reporting.external_ar_reports.payment_gateway_report.financial_report_aggregate import (
    FinancialReportAggregate,
)
from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.repository.booking.booking_report_repository import (
    BookingReportRepository,
)
from ths_common.constants.billing_constants import PaymentModes
from ths_common.constants.booking_constants import TACommissionStatus

logger = logging.getLogger(__name__)


class OTACommissionDataReportGenerator(BaseReportGenerator):
    def __init__(self, **aggregates):
        super().__init__(**aggregates)
        self.booking_repo: BookingRepository = locate_instance(BookingRepository)
        self.bill_repo: BillRepository = locate_instance(BillRepository)
        self.tenant_settings: TenantSettings = locate_instance(TenantSettings)
        self.booking_report_repo: BookingReportRepository = locate_instance(
            BookingReportRepository
        )
        self.invoice_repository: InvoiceRepository = locate_instance(InvoiceRepository)

    def generate(self):
        report_date = ar_context.posting_date
        statuses = [
            TACommissionStatus.LOCKED.value,
            TACommissionStatus.NULLIFIED_BY_REISSUE.value,
        ]
        entries = self.booking_report_repo.load_ta_commission_details_for_bookings_locked_on_date_and_status(
            statuses=statuses, report_date=report_date.date()
        )
        commissions_by_booking = self._group_by_booking(entries)

        booking_ids = list(commissions_by_booking.keys())
        bookings = self.booking_repo.load_all(booking_ids)
        bill_ids = [b.bill_id for b in bookings]
        bookings = self.booking_repo.get_booking_data_for_given_bills(bill_ids)
        invoices_data = self.invoice_repository.get_invoice_data_for_given_bills(
            bill_ids
        )
        booking_map = {b.booking_id: b for b in bookings}

        financial_aggregates = []
        commission_aggregates = []

        for booking_id, ta_entries in commissions_by_booking.items():
            booking = booking_map[booking_id]

            financial_aggregates.extend(
                self._build_financial_aggregates(booking, invoices_data)
            )

            commission_aggregates.extend(
                self._build_commission_aggregates(booking, ta_entries)
            )

        return financial_aggregates + commission_aggregates

    @staticmethod
    def _group_by_booking(entries) -> Dict[int, List]:
        grouped = defaultdict(list)
        for entry in entries:
            grouped[entry.booking_id].append(entry)
        return grouped

    def _build_financial_aggregates(
        self, booking, invoices_data
    ) -> List[FinancialReportAggregate]:
        payments = self.bill_repo.get_payments_under_given_payment_modes_for_bills(
            [booking.bill_id],
            [PaymentModes.PAID_AT_OTA],
        )
        invoice_map = {
            id.billed_entity_account_number: id.invoice_id for id in invoices_data
        }
        return [
            CommissionReportAggregate(
                p, booking, invoice_map.get(p.billed_entity_account_number)
            )
            for p in payments
        ]

    @staticmethod
    def _build_commission_aggregates(
        booking, ta_entries
    ) -> List[CommissionReportAggregate]:
        locked_ta_commissions = [
            ta for ta in ta_entries if ta.status == TACommissionStatus.LOCKED.value
        ]
        linked_ta_commission_map = {
            ta.linked_ta_commission_id: ta
            for ta in locked_ta_commissions
            if ta.linked_ta_commission_id
        }
        total_comm, total_tax = Decimal('0.00'), Decimal('0.00')
        total_reissued_comm, total_reissued_tax = Decimal('0.00'), Decimal('0.00')

        for ta in locked_ta_commissions:
            pretax, posttax = ta.pretax_amount, ta.posttax_amount
            tax = posttax - pretax

            if ta.ta_commission_id not in linked_ta_commission_map.values():
                total_comm += posttax
                total_tax += tax
            else:
                parent = linked_ta_commission_map.get(ta.ta_commission_id)
                if parent and parent.locked_on != ta.locked_on:
                    total_reissued_comm += posttax
                    total_reissued_tax += tax
                else:
                    total_comm -= posttax
                    total_tax -= tax
        commission_tax_components = booking.travel_agent_details[
            'ta_commission_details'
        ].get('commission_tax')
        tax_component_rates = (
            {
                TaxType.TAX: commission_tax_components.get('tax'),
                TaxType.TDS: commission_tax_components.get('tds'),
                TaxType.TCS: commission_tax_components.get('tcs'),
                TaxType.RCM: commission_tax_components.get('rcm'),
            }
            if commission_tax_components
            else None
        )
        tax_entry_map = {
            TaxType.TAX: EntryTypes.COMMISSION_TAX.value,
            TaxType.TDS: EntryTypes.COMMISSION_TDS.value,
            TaxType.TCS: EntryTypes.COMMISSION_TCS.value,
            TaxType.RCM: EntryTypes.RCM_COMMISSION.value,
        }

        def breakdown(posttax_amount, tax_amount):
            if not tax_component_rates:
                return {}
            pretax_mount = posttax_amount - tax_amount
            return {
                tax_type: round(float(pretax_mount) * (rate / 100.0), 2)
                for tax_type, rate in tax_component_rates.items()
                if rate
            }

        tax_component_amounts = breakdown(total_comm, total_tax)
        tax_component_reissue_amounts = (
            breakdown(total_reissued_comm, total_reissued_tax)
            if total_reissued_comm
            else {}
        )

        checkout_date = booking.checkout_date
        base_kwargs = dict(
            posted_date=checkout_date,
            date_of_payment=checkout_date,
            bill_id=booking.bill_id,
            payment_mode=PaymentModes.PAID_AT_OTA,
            created_at=dateutils.current_datetime(),
        )
        items = [(EntryTypes.OTA_COMMISSION.value, total_comm)]
        items += [
            (tax_entry_map[tax_type], amt)
            for tax_type, amt in tax_component_amounts.items()
        ]
        if total_reissued_comm:
            items.append((EntryTypes.OTA_COMMISSION.value, -total_reissued_comm))
            items += [
                (tax_entry_map[tax_type], -amt)
                for tax_type, amt in tax_component_reissue_amounts.items()
            ]

        dtos = [
            CommissionDataDto(
                {**base_kwargs, 'payment_type': payment_type, 'amount': amount}
            )
            for payment_type, amount in items
        ]
        return [CommissionReportAggregate(dto, booking) for dto in dtos]
