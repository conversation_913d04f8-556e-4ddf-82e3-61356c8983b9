from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.mis_report.mis_report_aggregate import MisReportAggregate
from ths_common.constants.booking_constants import BookingStatus


class MisReportGenerator(BaseReportGenerator):
    """
    Responsible for generating report rows for a given booking
    """

    def __init__(
        self, booking_aggregate, bill_aggregate, hotel_aggregate, room_type_map
    ):
        self.hotel_aggregate = hotel_aggregate
        self.booking_aggregate = booking_aggregate
        self.bill_aggregate = bill_aggregate
        self.room_type_map = room_type_map

    def generate(self):
        active_roomstays = (
            rs
            for rs in self.booking_aggregate.get_active_room_stays(as_generator=True)
            if rs.status
            in {
                BookingStatus.CHECKED_OUT,
                BookingStatus.PART_CHECKOUT,
                BookingStatus.CHECKED_IN,
                BookingStatus.PART_CHECKIN,
                BookingStatus.RESERVED,
            }
        )
        return [
            MisReportAggregate(
                self.booking_aggregate,
                self.bill_aggregate,
                self.hotel_aggregate,
                room_stay,
            )
            for room_stay in active_roomstays
        ]
