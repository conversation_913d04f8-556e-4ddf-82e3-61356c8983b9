from prometheus.core.globals import consumer_context
from prometheus.reporting.finance_erp_reporting.constants import RevenueCenters


class FinancialDataBaseDto:
    def __init__(self, details, is_reissued_folio=False):
        self.bill_id = details.bill_id
        self.hotel_id = details.vendor_id
        self.booking_id = details.parent_reference_number
        self.category = details.category
        self.owner_name = details.first_name
        self.folio_number = details.folio_number
        self.billed_entity_id = str(details.billed_entity_id)
        self.account_number = str(details.account_number)
        self.booking_reference_number = details.booking_reference_number
        self.is_reissued_folio = is_reissued_folio
        self.fin_erp_posting_date = consumer_context.report_date
        self.booker_external_id = None
        self.is_credit_folio = None
        self.payment_split_details = None
        self.folio_status = None


class TaxDetailsDto:
    def __init__(self, tax_detail, is_credit_note_created=False):
        self.amount = self._calculate_amount(tax_detail.amount, is_credit_note_created)
        self.percentage = tax_detail.percentage
        self.tax_amount = self._calculate_amount(
            tax_detail.tax_amount, is_credit_note_created
        )
        self.tax_type = tax_detail.tax_type

    @staticmethod
    def _calculate_amount(amount, is_credit_note_created):
        return -amount if is_credit_note_created else amount


class FinancialDataChargeDetailsDto(FinancialDataBaseDto):
    def __init__(self, charge_details):
        super().__init__(charge_details)
        self.charge_id = charge_details.charge_id
        self.charge_split_id = charge_details.charge_split_id
        self.pretax_amount = charge_details.pre_tax
        self.posttax_amount = charge_details.post_tax
        self.tax_amount = charge_details.tax
        self.tax_details = (
            [TaxDetailsDto(td) for td in charge_details.tax_details]
            if charge_details.tax_details
            else []
        )
        self.charge_type = charge_details.charge_type
        self.bill_to_type = charge_details.bill_to_type
        self.item_id = charge_details.item_id or charge_details.item_name
        self.sku_category_id = charge_details.sku_category_id
        self.applicable_business_date = charge_details.applicable_business_date
        self.posting_date = charge_details.posting_date
        self.is_inclusion_charge = charge_details.is_inclusion_charge
        self.revenue_center = self._determine_revenue_center(charge_details.item_detail)

    @staticmethod
    def _determine_revenue_center(item_detail):
        if item_detail.get("is_touche_pos_charge") and item_detail.get("is_pos_charge"):
            return item_detail.get("revenue_center", "")
        elif item_detail.get("is_pos_charge"):
            return item_detail.get("seller_name", "")
        return RevenueCenters.FRONT_DESK.value


class FinancialDataPaymentDetailsDto(FinancialDataBaseDto):
    def __init__(self, payment_details):
        super().__init__(payment_details)
        self.payment_id = getattr(payment_details, 'payment_id', None)
        self.payment_split_id = getattr(payment_details, 'payment_split_id', None)
        self.paid_to = getattr(payment_details, 'paid_to', None)
        self.payment_type = getattr(payment_details, 'payment_type', None)
        self.amount = getattr(payment_details, 'amount', None)
        self.posting_date = getattr(payment_details, 'posting_date', None)
        self.date_of_payment = getattr(payment_details, 'date_of_payment', None)
        self.payment_mode = getattr(payment_details, 'payment_mode', None)
        self.payment_mode_sub_type = getattr(
            payment_details, 'payment_mode_sub_type', None
        )
        self.crs_payment_mode = getattr(payment_details, 'payment_mode', None)
        self.crs_payment_mode_sub_type = getattr(
            payment_details, 'payment_mode_sub_type', None
        )
        self.payment_ref_id = getattr(payment_details, 'payment_ref_id', None)
        self.payment_channel = getattr(payment_details, 'payment_channel', None)
        self.revenue_center = RevenueCenters.FRONT_DESK.value
        self.checkin_date = getattr(payment_details, 'checkin_date', None)
        self.checkout_date = getattr(payment_details, 'checkout_date', None)
        self.guest_name = getattr(payment_details, 'guest_name', None)
        self.room_no = getattr(payment_details, 'room_no', None)


class FinancialDataAllowanceDetailsDto(FinancialDataBaseDto):
    def __init__(self, allowance_details, is_credit_note_created=False):
        super().__init__(allowance_details)
        self.allowance_id = allowance_details.allowance_id
        self.charge_id = allowance_details.charge_id
        self.charge_split_id = allowance_details.charge_split_id
        self.posting_date = allowance_details.posting_date
        self.tax_amount = self._calculate_amount(
            allowance_details.tax_amount, is_credit_note_created
        )
        self.posttax_amount = self._calculate_amount(
            allowance_details.posttax_amount, is_credit_note_created
        )
        self.pretax_amount = self._calculate_amount(
            allowance_details.pretax_amount, is_credit_note_created
        )
        self.tax_details = (
            [
                TaxDetailsDto(td, is_credit_note_created)
                for td in allowance_details.tax_details
            ]
            if allowance_details.tax_details
            else []
        )
        self.charge_type = allowance_details.charge_type
        self.bill_to_type = allowance_details.bill_to_type
        self.item_id = allowance_details.item_id or allowance_details.item_name
        self.applicable_business_date = allowance_details.applicable_business_date
        self.sku_category_id = allowance_details.sku_category_id
        self.revenue_center = self._determine_revenue_center(
            allowance_details.item_detail
        )

    @staticmethod
    def _calculate_amount(amount, is_credit_note_created):
        return -amount if is_credit_note_created else amount

    @staticmethod
    def _determine_revenue_center(item_detail):
        if item_detail.get("is_touche_pos_charge") and item_detail.get("is_pos_charge"):
            return item_detail.get("revenue_center", "")
        elif item_detail.get("is_pos_charge"):
            return item_detail.get("seller_name", "")
        return RevenueCenters.FRONT_DESK.value


class FinancialDataInvoiceDetails:
    def __init__(self, invoice_details):
        self.billed_entity_id = str(invoice_details.billed_entity_id)
        self.billed_entity_account_number = str(
            invoice_details.billed_entity_account_number
        )
        self.bill_id = invoice_details.bill_id
        self.is_credit_invoice = invoice_details.allowed_charge_types[0] == 'credit'
        self.posttax_amount = invoice_details.posttax_amount
        self.invoice_id = invoice_details.invoice_id

    @property
    def identifier(self):
        return (self.bill_id, self.billed_entity_id, self.billed_entity_account_number)


class FinancialDataPaymentSplitsDetails:
    def __init__(self, payment_split_details):
        self.identifier = (
            payment_split_details.bill_id
            + str(payment_split_details.billed_entity_id)
            + str(payment_split_details.billed_entity_account_number)
        )
        self.payment_id = payment_split_details.payment_id
        self.payment_split_id = payment_split_details.payment_split_id
        self.amount = payment_split_details.amount


class FinancialDataCreditNoteDetails:
    def __init__(self, credit_note_details):
        self.credit_note_id = credit_note_details.credit_note_id
        self.invoice_id = credit_note_details.invoice_id
