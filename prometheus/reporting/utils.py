import csv
import json
import logging
import os
from functools import wraps
from typing import List

import requests
from flask import current_app as app
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from prometheus.infrastructure.external_clients.notification_service_client import (
    NotificationEmailIds,
    NotificationServiceClient,
)
from prometheus.reporting.finance_erp_reporting.utils import to_dmy_str
from ths_common.constants.reporting_constants import (
    REPORT_NEW_LINE,
    ReportAssemblyFormat,
    ReportingTypes,
)
from ths_common.exceptions import InvalidOperationError

logger = logging.getLogger(__name__)


class CsvWriter(object):
    def __init__(self, file_path, delimiter=','):
        self.file_path = file_path
        self._file_object = open(self.file_path, 'a+', encoding='utf-8')
        self.csv_writer = csv.writer(self._file_object, delimiter=delimiter)
        self._write_header_row = True

    def __enter__(self):
        return self

    def __exit__(self, type, value, traceback):
        self._file_object.close()
        self.tear_down()

    def flush(self):
        self._file_object.flush()

    def write_aggregates(
        self,
        aggregates: List,
        attributes: List,
        report_assembly_format=ReportAssemblyFormat.HORIZONTAL,
        report_header_titles=None,
    ):
        """
        write a list of aggregates to csv file
        given the list of attributes to write to column
        """
        if not aggregates:
            raise InvalidOperationError(
                description="Empty aggregate list to generate CSV file"
            )

        if report_assembly_format == ReportAssemblyFormat.VERTICAL:
            rows_for_csv = self._rows_for_csv_vertical_assembly(
                aggregates, attributes, report_header_titles
            )
        else:
            rows_for_csv = self._rows_for_csv_horizontal_assembly(
                aggregates, attributes
            )

        try:
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)
        except Exception:
            raise

        for row in rows_for_csv:
            self.csv_writer.writerow(row)

        self.flush()

    def _rows_for_csv_horizontal_assembly(self, aggregates: List, attributes: List):
        rows_for_csv = self._get_data_rows(attributes, aggregates)
        if self._write_header_row:
            header_row = self._get_header_row(attributes, aggregates[0])
            rows_for_csv = [header_row] + rows_for_csv
            self._write_header_row = False

        return rows_for_csv

    def _rows_for_csv_vertical_assembly(
        self, aggregates: List, attributes: List, report_header_titles=None
    ):
        merged_aggregates = self._merge_aggregates(attributes, aggregates)
        rows_for_csv = []
        if report_header_titles:
            rows_for_csv.append(report_header_titles)
        for header in merged_aggregates.keys():
            if header.startswith(REPORT_NEW_LINE):
                rows_for_csv.append("")
            else:
                csv_row = list()
                csv_row.append(header)
                for data in merged_aggregates[header]:
                    csv_row.append(data)
                rows_for_csv.append(csv_row)

        return rows_for_csv

    def _get_header_row(self, attributes, aggregate):
        header_row = list()
        for attribute in attributes:
            attrvalue = getattr(aggregate, attribute)
            if isinstance(attrvalue, dict):
                dict_keys = [
                    attribute + '_' + dict_key for dict_key in attrvalue.keys()
                ]
                header_row.extend(dict_keys)
            else:
                if attribute == REPORT_NEW_LINE:
                    attribute = ""
                header_row.append(attribute)
        return header_row

    def _get_data_rows(self, attributes, aggregates):
        data_rows = list()
        for aggregate in aggregates:
            data_row = []
            for attribute in attributes:
                attrvalue = getattr(aggregate, attribute)
                if isinstance(attrvalue, dict):
                    data_row.extend(attrvalue.values())
                else:
                    data_row.append(attrvalue)
            data_rows.append(data_row)

        return data_rows

    def _merge_aggregates(self, attributes, aggregates):
        merged_aggregate_rows = dict()
        new_line_index = 0
        aggregates_length = len(aggregates)
        for attribute in attributes:
            aggregate_index = 0
            for aggregate in aggregates:
                attrvalue = getattr(aggregate, attribute)
                if attribute == REPORT_NEW_LINE:
                    merged_aggregate_rows[
                        REPORT_NEW_LINE + '_' + str(new_line_index)
                    ] = attrvalue
                    if aggregate_index == 0:
                        new_line_index += 1
                elif isinstance(attrvalue, dict):
                    for dict_key in attrvalue.keys():
                        if not merged_aggregate_rows.get(attribute + "_" + dict_key):
                            merged_aggregate_rows[attribute + "_" + dict_key] = [
                                None
                            ] * aggregates_length

                        merged_aggregate_rows[attribute + "_" + dict_key][
                            aggregate_index
                        ] = attrvalue[dict_key]
                else:
                    merged_aggregate_rows[attribute] = merged_aggregate_rows.get(
                        attribute, []
                    ) + [getattr(aggregate, attribute)]
                aggregate_index += 1
        return merged_aggregate_rows

    def tear_down(self):
        try:
            app_env = os.environ.get('APP_ENV', 'local')
            if app_env in ['local', 'testing']:
                pass
            else:
                os.remove(self.file_path)
        except FileNotFoundError:
            logger.info(f"No file found to tear down")
        except OSError as e:
            logger.exception(f"Unable to tear down file due to {e}")

    def write_dicts(self, data_list):
        if not data_list:
            return
        headers = list(data_list[0].keys())
        rows_for_csv = [[data.get(key, '') for key in headers] for data in data_list]
        rows_for_csv = [headers] + rows_for_csv
        try:
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)
        except Exception:
            raise

        for row in rows_for_csv:
            self.csv_writer.writerow(row)

        self.flush()


def alert_on_failure(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            r_val = func(*args, **kwargs)
        except Exception as e:
            logger.exception(str(e))
            slack_webhook_url = None
            msg = "{0}: {1} push has failed for date {2} with error {3}".format(
                kwargs.get('reporting_type'),
                kwargs.get('report_name'),
                kwargs.get('posting_date'),
                e.args[0],
            )
            if kwargs.get('reporting_type') == ReportingTypes.ARReporting:
                slack_webhook_url = app.config[
                    'CRS_AR_DATA_PUSH_ALERTS_SLACK_WEBHOOK_URL'
                ]
            elif kwargs.get('reporting_type') == ReportingTypes.FinanceERPReporting:
                send_finance_error_report(
                    e.args[0], kwargs.get('posting_date'), kwargs.get('report_name')
                )
                slack_webhook_url = app.config['FINANCE_CRON_ALERTS_SLACK_WEBHOOK_URL']
            send_slack_alert(
                tenant_id=get_current_tenant_id() or TenantClient.get_default_tenant(),
                slack_webhook_url=slack_webhook_url,
                msg=msg,
            )
            return True
        return r_val

    return wrapper


def send_slack_alert(tenant_id=None, slack_webhook_url=None, msg=None):
    try:
        app_env = os.environ.get('APP_ENV', 'local')
        if app_env not in ['production', 'prod', 'staging', 'stag']:
            return
        payload = {
            "text": "`(Tenant: {0}) Reporting ({1})`: ```{2}```".format(
                tenant_id, app_env.upper(), msg
            ),
            "username": 'CRS',
        }
        json_string = json.dumps(payload, default=lambda o: o.__dict__)
        headers = {"content-type": "application/json"}
        response = requests.post(slack_webhook_url, data=json_string, headers=headers)
        if response.status_code != 200:
            raise Exception(
                'Received response status: {status}'.format(status=response.status_code)
            )
    except Exception as ex:
        logger.exception(
            "Error while sending slack error on channel. {ex}".format(ex=ex)
        )


def send_finance_error_report(msg, posting_date, report_name):
    subject = "Finance ERP Push Error report for date {0}".format(
        to_dmy_str(posting_date)
    )
    html = "Hi,<br/><br/>Finance ERP {0} push has failed with error {1}".format(
        report_name, msg
    )
    NotificationServiceClient().email(
        body_html=html,
        subject=subject,
        sender=NotificationEmailIds.NOREPLY.value,
        recievers=app.config['FINANCE_ERROR_REPORT_RECEIVER_LIST'],
    )
