import itertools
import logging
from collections import defaultdict

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.decorators import set_hotel_context
from prometheus.common.decorators import bypass_access_entity_checks
from prometheus.domain.billing.dto.cashier_session_search_query import (
    CashierSessionSearchQuery,
)
from prometheus.domain.billing.entities.cash_counter_payment import CashierPayment
from prometheus.domain.billing.repositories.cash_register_repository import (
    CashRegisterRepository,
)
from prometheus.domain.billing.repositories.cashier_session_repository import (
    CashierSessionRepository,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from prometheus.infrastructure.external_clients.catalog_service.user_defined_enums import (
    UserDefinedEnums,
)
from prometheus.reporting.cashier_session_reports.cash_report_generator import (
    CashReportGenerator,
)
from prometheus.reporting.cashier_session_reports.non_cash_report_generator import (
    NonCashReportGenerator,
)
from prometheus.reporting.cashier_session_reports.overall_report_generator import (
    OverallReportGenerator,
)
from prometheus.reporting.cashier_session_reports.session_report_generator import (
    SessionReportGenerator,
)
from prometheus.reporting.cashier_session_reports.user_report_generator import (
    UserReportGenerator,
)
from prometheus.reporting.utils import CsvWriter
from ths_common.constants.billing_constants import PaymentModes

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[CashierSessionRepository, CashRegisterRepository, UserDefinedEnums]
)
class CashierReportApplicationService:
    INFLOW_PAYMENT_TYPE = 'inflow'
    OUTFLOW_PAYMENT_TYPE = 'outflow'

    def __init__(
        self, cashier_session_repository, cash_register_repository, user_defined_enums
    ):
        self.cashier_session_repository = cashier_session_repository
        self.cash_register_repository = cash_register_repository
        self.user_defined_enums = user_defined_enums

    @staticmethod
    def _fail_if_user_not_authorized_to_access_reports():
        return RuleEngine.action_allowed(
            action="access_report",
            facts=Facts(
                user_type=crs_context.user_data.user_type,
                hotel_context=crs_context.get_hotel_context(),
            ),
            fail_on_error=True,
        )

    def get_cash_register_dict(self, cash_register_ids):
        cash_register_aggregates = self.cash_register_repository.search(
            {"cash_register_ids": cash_register_ids}
        )
        return {
            cash_register_aggregate.cash_register.cash_register_id: cash_register_aggregate
            for cash_register_aggregate in cash_register_aggregates
        }

    def _base_report_cashier_session_details(
        self, cash_register_ids=None, cashier_session_ids=None
    ):
        query = CashierSessionSearchQuery(
            cashier_session_ids=cashier_session_ids,
            cash_register_ids=cash_register_ids,
            limit=None,
        )
        cashier_session_aggregates = self.cashier_session_repository.search(query)
        cash_register_ids = set(
            [
                cashier_session_aggregate.cashier_session.cash_register_id
                for cashier_session_aggregate in cashier_session_aggregates
            ]
        )
        cashier_payments = []
        [
            cashier_payments.extend(cashier_session_aggregate.non_cancelled_payments)
            for cashier_session_aggregate in cashier_session_aggregates
        ]
        cash_register_dict = self.get_cash_register_dict(cash_register_ids)
        return cashier_session_aggregates, cashier_payments, cash_register_dict

    @set_hotel_context()
    @bypass_access_entity_checks
    def get_cashier_overall_report_summary_data(
        self,
        cash_register_ids,
        start_datetime,
        end_datetime,
        user_data=None,
        hotel_aggregate=None,
    ):
        """
        Added bypass_access_entity_checks in all cashier reports to handle the scenario where multiple
        cash_register/cashier_session are selected and those can belong to different vendor.
        """
        self._fail_if_user_not_authorized_to_access_reports()
        (
            cashier_session_aggregates,
            cashier_payments,
            cash_register_dict,
        ) = self._base_report_cashier_session_details(
            cash_register_ids=cash_register_ids
        )
        currency_wise_summary = self._get_currency_wise_summary(
            start_datetime, end_datetime, cashier_payments
        )
        transaction_type_wise_summary = self._get_transaction_type_wise_summary(
            start_datetime,
            end_datetime,
            hotel_aggregate.hotel.base_currency,
            cashier_payments,
        )
        payment_date_wise_summary_grouped_on_payment_mode = (
            self._get_payment_date_wise_summary_grouped_on_payment_mode(
                start_datetime,
                end_datetime,
                hotel_aggregate.hotel.base_currency,
                cashier_session_aggregates,
            )
        )
        cash_register_wise_summary_grouped_on_currency = (
            self._get_cash_register_wise_summary_grouped_on_currency(
                start_datetime,
                end_datetime,
                cashier_session_aggregates,
                cash_register_dict,
            )
        )
        return {
            "currency_wise_summary": currency_wise_summary,
            "transaction_type_wise_summary": transaction_type_wise_summary,
            "payment_date_wise_summary": payment_date_wise_summary_grouped_on_payment_mode,
            "cash_register_wise_summary": cash_register_wise_summary_grouped_on_currency,
        }

    @set_hotel_context()
    @bypass_access_entity_checks
    def get_cashier_overall_report_details_data(
        self,
        cash_register_ids,
        start_datetime,
        end_datetime,
        user_data=None,
        hotel_aggregate=None,
    ):
        self._fail_if_user_not_authorized_to_access_reports()
        (
            cashier_session_aggregates,
            cashier_payments,
            cash_register_dict,
        ) = self._base_report_cashier_session_details(
            cash_register_ids=cash_register_ids
        )
        cashier_session_report_aggregates = []
        for cashier_session_aggregate in cashier_session_aggregates:
            cash_register_id = (
                cashier_session_aggregate.cashier_session.cash_register_id
            )
            cash_register_aggregate = cash_register_dict.get(cash_register_id)
            cashier_session_report_aggregates.append(
                OverallReportGenerator(
                    start_datetime,
                    end_datetime,
                    cashier_session_aggregate,
                    cash_register_aggregate.cash_register,
                    hotel_aggregate,
                ).generate()
            )
        return list(itertools.chain.from_iterable(cashier_session_report_aggregates))

    @set_hotel_context()
    def get_cashier_overall_report_details_download(
        self,
        cash_register_ids,
        start_datetime,
        end_datetime,
        user_data=None,
        hotel_aggregate=None,
    ):
        self._fail_if_user_not_authorized_to_access_reports()
        report_detail_aggregates = self.get_cashier_overall_report_details_data(
            cash_register_ids, start_datetime, end_datetime
        )
        return self._generate_csv_presigned_url(
            report_detail_aggregates, OverallReportGenerator
        )

    @set_hotel_context()
    @bypass_access_entity_checks
    def get_cashier_cash_report_summary_data(
        self,
        cash_register_ids,
        start_datetime,
        end_datetime,
        user_data=None,
        hotel_aggregate=None,
    ):
        self._fail_if_user_not_authorized_to_access_reports()
        (
            cashier_session_aggregates,
            cashier_payments,
            cash_register_dict,
        ) = self._base_report_cashier_session_details(
            cash_register_ids=cash_register_ids
        )
        currency_wise_summary = self._get_currency_wise_summary(
            start_datetime,
            end_datetime,
            cashier_payments,
            payment_modes=[PaymentModes.CASH],
        )
        cash_register_wise_summary_grouped_on_currency = (
            self._get_cash_register_wise_summary_grouped_on_currency(
                start_datetime,
                end_datetime,
                cashier_session_aggregates,
                cash_register_dict,
                payment_modes=[PaymentModes.CASH],
            )
        )
        payment_date_wise_summary_grouped_on_currency = (
            self._get_payment_date_wise_summary_grouped_on_currency(
                start_datetime,
                end_datetime,
                cashier_session_aggregates,
                payment_modes=[PaymentModes.CASH],
            )
        )
        return {
            "currency_wise_summary": currency_wise_summary,
            "cash_register_wise_summary": cash_register_wise_summary_grouped_on_currency,
            "payment_date_wise_summary": payment_date_wise_summary_grouped_on_currency,
        }

    @set_hotel_context()
    @bypass_access_entity_checks
    def get_cashier_cash_report_details_data(
        self,
        cash_register_ids,
        start_datetime,
        end_datetime,
        user_data=None,
        hotel_aggregate=None,
    ):
        self._fail_if_user_not_authorized_to_access_reports()
        (
            cashier_session_aggregates,
            cashier_payments,
            cash_register_dict,
        ) = self._base_report_cashier_session_details(
            cash_register_ids=cash_register_ids
        )
        cashier_session_report_aggregates = []
        for cashier_session_aggregate in cashier_session_aggregates:
            cash_register_id = (
                cashier_session_aggregate.cashier_session.cash_register_id
            )
            cash_register_aggregate = cash_register_dict.get(cash_register_id)
            cashier_session_report_aggregates.append(
                CashReportGenerator(
                    start_datetime,
                    end_datetime,
                    cashier_session_aggregate,
                    cash_register_aggregate.cash_register,
                    hotel_aggregate,
                ).generate()
            )
        return list(itertools.chain.from_iterable(cashier_session_report_aggregates))

    def get_cashier_cash_report_details_download(
        self, cash_register_ids, start_datetime, end_datetime, user_data=None
    ):
        report_detail_aggregates = self.get_cashier_cash_report_details_data(
            cash_register_ids, start_datetime, end_datetime, user_data
        )
        return self._generate_csv_presigned_url(
            report_detail_aggregates, CashReportGenerator
        )

    @set_hotel_context()
    @bypass_access_entity_checks
    def get_cashier_non_cash_report_summary_data(
        self,
        cash_register_ids,
        start_date,
        end_date,
        user_data=None,
        hotel_aggregate=None,
    ):
        self._fail_if_user_not_authorized_to_access_reports()
        (
            cashier_session_aggregates,
            cashier_payments,
            cash_register_dict,
        ) = self._base_report_cashier_session_details(
            cash_register_ids=cash_register_ids
        )

        enum_values = self.user_defined_enums.get_enum(
            hotel_id=hotel_aggregate.hotel.hotel_id,
            enum_name=UserDefinedEnums.PAYMENT_MODE,
        )
        payment_modes = [
            enum_value
            for enum_value in enum_values
            if enum_value.lower() != PaymentModes.CASH.lower()
        ]
        payment_mode_wise_summary = self._get_payment_mode_wise_summary(
            start_date,
            end_date,
            hotel_aggregate.hotel.base_currency,
            cashier_payments,
            payment_modes,
        )
        cash_register_wise_summary_grouped_on_payment_mode = (
            self._get_cash_register_wise_summary_grouped_on_payment_mode(
                start_date,
                end_date,
                hotel_aggregate.hotel.base_currency,
                cashier_session_aggregates,
                cash_register_dict,
                payment_modes,
            )
        )
        payment_date_wise_summary_grouped_on_payment_mode = (
            self._get_payment_date_wise_summary_grouped_on_payment_mode(
                start_date,
                end_date,
                hotel_aggregate.hotel.base_currency,
                cashier_session_aggregates,
                payment_modes,
            )
        )
        return {
            "payment_mode_wise_summary": payment_mode_wise_summary,
            "cash_register_wise_summary": cash_register_wise_summary_grouped_on_payment_mode,
            "payment_date_wise_summary": payment_date_wise_summary_grouped_on_payment_mode,
        }

    @set_hotel_context()
    @bypass_access_entity_checks
    def get_cashier_non_cash_report_details_data(
        self,
        cash_register_ids,
        start_datetime,
        end_datetime,
        user_data=None,
        hotel_aggregate=None,
    ):
        self._fail_if_user_not_authorized_to_access_reports()
        (
            cashier_session_aggregates,
            cashier_payments,
            cash_register_dict,
        ) = self._base_report_cashier_session_details(
            cash_register_ids=cash_register_ids
        )
        cashier_session_report_aggregates = []
        enum_values = self.user_defined_enums.get_enum(
            hotel_id=hotel_aggregate.hotel.hotel_id,
            enum_name=UserDefinedEnums.PAYMENT_MODE,
        )
        non_cash_payment_modes = [
            enum_value
            for enum_value in enum_values
            if enum_value.lower() != PaymentModes.CASH.lower()
        ]
        for cashier_session_aggregate in cashier_session_aggregates:
            cash_register_id = (
                cashier_session_aggregate.cashier_session.cash_register_id
            )
            cash_register_aggregate = cash_register_dict.get(cash_register_id)
            cashier_session_report_aggregates.append(
                NonCashReportGenerator(
                    start_datetime,
                    end_datetime,
                    cashier_session_aggregate,
                    cash_register_aggregate.cash_register,
                    non_cash_payment_modes,
                    hotel_aggregate,
                ).generate()
            )
        return list(itertools.chain.from_iterable(cashier_session_report_aggregates))

    def get_cashier_non_cash_report_details_download(
        self, cash_register_ids, start_datetime, end_datetime, user_data=None
    ):
        report_detail_aggregates = self.get_cashier_non_cash_report_details_data(
            cash_register_ids, start_datetime, end_datetime, user_data
        )
        return self._generate_csv_presigned_url(
            report_detail_aggregates, NonCashReportGenerator
        )

    @set_hotel_context()
    @bypass_access_entity_checks
    def get_cashier_session_report_summary_data(
        self, cash_session_ids, user_data=None, hotel_aggregate=None
    ):
        self._fail_if_user_not_authorized_to_access_reports()
        (
            cashier_session_aggregates,
            cashier_payments,
            cash_register_dict,
        ) = self._base_report_cashier_session_details(
            cashier_session_ids=cash_session_ids
        )
        session_wise_summary = self._get_session_wise_summary(
            cashier_session_aggregates,
            cash_register_dict,
            hotel_aggregate.hotel.base_currency,
        )
        return {"session_wise_summary": session_wise_summary}

    @set_hotel_context()
    @bypass_access_entity_checks
    def get_cashier_session_report_details_data(
        self, cash_session_ids, user_data=None, hotel_aggregate=None
    ):
        self._fail_if_user_not_authorized_to_access_reports()
        (
            cashier_session_aggregates,
            cashier_payments,
            cash_register_dict,
        ) = self._base_report_cashier_session_details(
            cashier_session_ids=cash_session_ids
        )
        cashier_session_report_aggregates = []
        for cashier_session_aggregate in cashier_session_aggregates:
            cash_register_id = (
                cashier_session_aggregate.cashier_session.cash_register_id
            )
            cash_register_aggregate = cash_register_dict.get(cash_register_id)
            cashier_session_report_aggregates.append(
                SessionReportGenerator(
                    cashier_session_aggregate,
                    cash_register_aggregate.cash_register,
                    hotel_aggregate,
                ).generate()
            )
        return list(itertools.chain.from_iterable(cashier_session_report_aggregates))

    def get_cashier_session_report_details_download(
        self, cash_session_ids, user_data=None
    ):
        report_detail_aggregates = self.get_cashier_session_report_details_data(
            cash_session_ids, user_data
        )
        return self._generate_csv_presigned_url(
            report_detail_aggregates, SessionReportGenerator
        )

    @set_hotel_context()
    @bypass_access_entity_checks
    def get_cashier_user_report_summary_data(
        self,
        start_datetime,
        cash_register_ids,
        user,
        user_data=None,
        hotel_aggregate=None,
    ):
        self._fail_if_user_not_authorized_to_access_reports()
        (
            cashier_session_aggregates,
            cashier_payments,
            cash_register_dict,
        ) = self._base_report_cashier_session_details(
            cash_register_ids=cash_register_ids
        )
        user_session_wise_summary_grouped_on_currency = (
            self._get_user_session_wise_summary_grouped_on_currency(
                start_datetime, user, cashier_session_aggregates, cash_register_dict
            )
        )
        user_session_wise_summary_grouped_on_payment_mode = (
            self._get_user_session_wise_summary_grouped_on_payment_mode(
                start_datetime,
                user,
                cashier_session_aggregates,
                cash_register_dict,
                hotel_aggregate.hotel.base_currency,
            )
        )
        return {
            "currency_wise_summary": user_session_wise_summary_grouped_on_currency,
            "payment_mode_wise_summary": user_session_wise_summary_grouped_on_payment_mode,
        }

    @set_hotel_context()
    @bypass_access_entity_checks
    def get_cashier_user_report_details_data(
        self,
        start_datetime,
        cash_register_ids,
        user,
        user_data=None,
        hotel_aggregate=None,
    ):
        self._fail_if_user_not_authorized_to_access_reports()
        (
            cashier_session_aggregates,
            cashier_payments,
            cash_register_dict,
        ) = self._base_report_cashier_session_details(
            cash_register_ids=cash_register_ids
        )
        cashier_session_report_aggregates = []
        for cashier_session_aggregate in cashier_session_aggregates:
            cash_register_id = (
                cashier_session_aggregate.cashier_session.cash_register_id
            )
            cash_register_aggregate = cash_register_dict.get(cash_register_id)
            cashier_session_report_aggregates.append(
                UserReportGenerator(
                    start_datetime,
                    cashier_session_aggregate,
                    cash_register_aggregate.cash_register,
                    hotel_aggregate,
                    user,
                ).generate()
            )

        return list(itertools.chain.from_iterable(cashier_session_report_aggregates))

    def get_cashier_user_report_details_download(
        self, start_datetime, cash_register_ids, user, user_data=None
    ):
        report_detail_aggregates = self.get_cashier_user_report_details_data(
            start_datetime, cash_register_ids, user, user_data
        )
        return self._generate_csv_presigned_url(
            report_detail_aggregates, UserReportGenerator
        )

    @set_hotel_context()
    @bypass_access_entity_checks
    def get_cashier_user_report_user_data(
        self,
        start_datetime,
        cash_register_ids,
        user,
        user_data=None,
        hotel_aggregate=None,
    ):
        self._fail_if_user_not_authorized_to_access_reports()
        (
            cashier_session_aggregates,
            cashier_payments,
            cash_register_dict,
        ) = self._base_report_cashier_session_details(
            cash_register_ids=cash_register_ids
        )
        users = set(
            [
                payment.added_by.lower()
                for payment in cashier_payments
                if payment.added_by
                and start_datetime <= payment.date_of_payment
                and dateutils.to_date(start_datetime)
                == dateutils.to_date(payment.date_of_payment)
                and (not user or (user and user.lower() == payment.added_by.lower()))
            ]
        )
        return [{"user": user} for user in users]

    def _get_currency_wise_summary(
        self,
        start_datetime,
        end_datetime,
        cashier_payments: [CashierPayment],
        payment_modes=None,
    ):
        if not cashier_payments:
            return []
        currency_wise_summary = defaultdict(lambda: defaultdict(Money))
        for cashier_payment in cashier_payments:
            if not start_datetime <= cashier_payment.date_of_payment <= end_datetime:
                continue
            if payment_modes and not cashier_payment.payment_mode in payment_modes:
                continue
            currency_type = cashier_payment.amount_in_payment_currency.currency
            currency_wise_summary[
                currency_type
            ] = self._set_payment_currency_wise_summary_values(
                currency_type, cashier_payment, currency_wise_summary[currency_type]
            )
            currency_wise_summary[currency_type]['currency_name'] = currency_type.value
        return currency_wise_summary.values()

    def _get_payment_mode_wise_summary(
        self,
        start_datetime,
        end_datetime,
        base_currency,
        cashier_payments: [CashierPayment],
        payment_modes=None,
    ):
        if not cashier_payments:
            return []
        payment_mode_wise_summary = defaultdict(lambda: defaultdict(dict))

        for cashier_payment in cashier_payments:
            if not start_datetime <= cashier_payment.date_of_payment <= end_datetime:
                continue
            payment_mode = cashier_payment.payment_mode
            if payment_modes and not payment_mode in payment_modes:
                continue
            payment_mode_wise_summary[
                payment_mode
            ] = self._set_base_currency_wise_summary_values(
                base_currency, cashier_payment, payment_mode_wise_summary[payment_mode]
            )
            payment_mode_wise_summary[payment_mode]['payment_mode'] = payment_mode

        return list(payment_mode_wise_summary.values())

    def _get_transaction_type_wise_summary(
        self,
        start_datetime,
        end_datetime,
        base_currency,
        cashier_payments: [CashierPayment],
    ):
        if not cashier_payments:
            return []
        transaction_type_wise_summary = defaultdict(lambda: defaultdict(dict))
        for cashier_payment in cashier_payments:
            if not start_datetime <= cashier_payment.date_of_payment <= end_datetime:
                continue
            transaction_type = cashier_payment.payment_mode
            if not transaction_type == 'cash':
                transaction_type = 'non_cash'
            transaction_type_wise_summary[
                transaction_type
            ] = self._set_base_currency_wise_summary_values(
                base_currency,
                cashier_payment,
                transaction_type_wise_summary[transaction_type],
            )
            transaction_type_wise_summary[transaction_type][
                'transaction_type'
            ] = transaction_type
        return list(transaction_type_wise_summary.values())

    def _get_payment_date_wise_summary_grouped_on_currency(
        self,
        start_datetime,
        end_datetime,
        cashier_session_aggregates,
        payment_modes=None,
    ):
        if not cashier_session_aggregates:
            return []
        payment_datewise_summary_grouped_on_currency = defaultdict(
            lambda: defaultdict(dict)
        )
        payment_datewise_summary_report = []
        for cashier_session_aggregate in cashier_session_aggregates:
            cashier_payments = cashier_session_aggregate.non_cancelled_payments
            if not cashier_payments:
                continue
            for cashier_payment in cashier_payments:
                if (
                    not start_datetime
                    <= cashier_payment.date_of_payment
                    <= end_datetime
                ):
                    continue
                if payment_modes and not cashier_payment.payment_mode in payment_modes:
                    continue
                payment_date = dateutils.to_date(cashier_payment.date_of_payment)
                currency_type = cashier_payment.amount_in_payment_currency.currency
                payment_datewise_summary_grouped_on_currency[payment_date][
                    currency_type
                ] = self._set_payment_currency_wise_summary_values(
                    currency_type,
                    cashier_payment,
                    payment_datewise_summary_grouped_on_currency[payment_date][
                        currency_type
                    ],
                )
                payment_datewise_summary_grouped_on_currency[payment_date][
                    currency_type
                ]['payment_date'] = payment_date

        for (
            payment_date,
            currency_wise_summary_payment_date_map,
        ) in payment_datewise_summary_grouped_on_currency.items():
            result = []
            currency_wise_summary = self._extract_bottom_down_value_from_nested_dict(
                currency_wise_summary_payment_date_map, result
            )
            payment_datewise_summary_report.extend(currency_wise_summary)
        return payment_datewise_summary_report

    def _get_cash_register_wise_summary_grouped_on_currency(
        self,
        start_datetime,
        end_datetime,
        cashier_session_aggregates,
        cash_register_dict,
        payment_modes=None,
    ):
        if not cashier_session_aggregates:
            return []
        cash_register_wise_summary_grouped_on_currency = defaultdict(
            lambda: defaultdict(dict)
        )
        cash_register_wise_report_summary = []
        for cashier_session_aggregate in cashier_session_aggregates:
            cash_register_id = (
                cashier_session_aggregate.cashier_session.cash_register_id
            )
            cash_register = cash_register_dict.get(cash_register_id)
            cash_register_name = (
                cash_register.cash_register.cash_register_name
                if cash_register
                else None
            )
            if not cash_register_name:
                continue
            for cashier_payment in cashier_session_aggregate.non_cancelled_payments:
                if (
                    not start_datetime
                    <= cashier_payment.date_of_payment
                    <= end_datetime
                ):
                    continue
                if payment_modes and not cashier_payment.payment_mode in payment_modes:
                    continue
                currency_type = cashier_payment.amount_in_payment_currency.currency
                cash_register_wise_summary_grouped_on_currency[cash_register_name][
                    currency_type
                ] = self._set_payment_currency_wise_summary_values(
                    currency_type,
                    cashier_payment,
                    cash_register_wise_summary_grouped_on_currency[cash_register_name][
                        currency_type
                    ],
                )
                cash_register_wise_summary_grouped_on_currency[cash_register_name][
                    currency_type
                ]['cash_register_name'] = cash_register_name

        for (
            register_name,
            currency_wise_summary_register_map,
        ) in cash_register_wise_summary_grouped_on_currency.items():
            result = []
            currency_wise_summary = self._extract_bottom_down_value_from_nested_dict(
                currency_wise_summary_register_map, result
            )
            cash_register_wise_report_summary.extend(currency_wise_summary)

        return cash_register_wise_report_summary

    def _get_cash_register_wise_summary_grouped_on_payment_mode(
        self,
        start_datetime,
        end_datetime,
        base_currency,
        cashier_session_aggregates,
        cash_register_dict,
        payment_modes=None,
    ):
        if not cashier_session_aggregates:
            return []
        cash_register_wise_summary_grouped_on_payment_mode = defaultdict(
            lambda: defaultdict(dict)
        )
        cash_register_wise_report_summary = []
        for cashier_session_aggregate in cashier_session_aggregates:
            cash_register_id = (
                cashier_session_aggregate.cashier_session.cash_register_id
            )
            cash_register = cash_register_dict.get(cash_register_id)
            cash_register_name = (
                cash_register.cash_register.cash_register_name
                if cash_register
                else None
            )
            if not cash_register_name:
                continue
            for cashier_payment in cashier_session_aggregate.non_cancelled_payments:
                if (
                    not start_datetime
                    <= cashier_payment.date_of_payment
                    <= end_datetime
                ):
                    continue
                payment_mode = cashier_payment.payment_mode
                if payment_modes and not cashier_payment.payment_mode in payment_modes:
                    continue
                cash_register_wise_summary_grouped_on_payment_mode[cash_register_name][
                    payment_mode
                ] = self._set_base_currency_wise_summary_values(
                    base_currency,
                    cashier_payment,
                    cash_register_wise_summary_grouped_on_payment_mode[
                        cash_register_name
                    ][payment_mode],
                )
                cash_register_wise_summary_grouped_on_payment_mode[cash_register_name][
                    payment_mode
                ].update(
                    {
                        "payment_mode": payment_mode,
                        "cash_register_name": cash_register_name,
                    }
                )

        for (
            register,
            payment_mode_wise_summary_register_map,
        ) in cash_register_wise_summary_grouped_on_payment_mode.items():
            result = []
            payment_mode_summary = self._extract_bottom_down_value_from_nested_dict(
                payment_mode_wise_summary_register_map, result
            )
            cash_register_wise_report_summary.extend(payment_mode_summary)
        return cash_register_wise_report_summary

    def _get_payment_date_wise_summary_grouped_on_payment_mode(
        self,
        start_datetime,
        end_datetime,
        base_currency,
        cashier_session_aggregates,
        payment_modes=None,
    ):
        if not cashier_session_aggregates:
            return []
        payment_date_wise_summary_grouped_on_payment_mode = defaultdict(
            lambda: defaultdict(dict)
        )
        payment_datewise_summary_report = []
        for cashier_session_aggregate in cashier_session_aggregates:
            cashier_payments = cashier_session_aggregate.non_cancelled_payments
            if not cashier_payments:
                continue
            for cashier_payment in cashier_payments:
                if (
                    not start_datetime
                    <= cashier_payment.date_of_payment
                    <= end_datetime
                ):
                    continue
                payment_mode = cashier_payment.payment_mode
                if payment_modes and not cashier_payment.payment_mode in payment_modes:
                    continue
                payment_date = dateutils.to_date(cashier_payment.date_of_payment)
                payment_date_wise_summary_grouped_on_payment_mode[payment_date][
                    payment_mode
                ] = self._set_base_currency_wise_summary_values(
                    base_currency,
                    cashier_payment,
                    payment_date_wise_summary_grouped_on_payment_mode[payment_date][
                        payment_mode
                    ],
                )
                payment_date_wise_summary_grouped_on_payment_mode[payment_date][
                    payment_mode
                ].update({"payment_date": payment_date, "payment_mode": payment_mode})

        for (
            payment_date,
            payment_mode_wise_summary_payment_date_map,
        ) in payment_date_wise_summary_grouped_on_payment_mode.items():
            result = []
            payment_mode_summary = self._extract_bottom_down_value_from_nested_dict(
                payment_mode_wise_summary_payment_date_map, result
            )
            payment_datewise_summary_report.extend(payment_mode_summary)
        return payment_datewise_summary_report

    def _get_session_wise_summary(
        self, cashier_session_aggregates, cash_register_dict, base_currency
    ):
        if not cashier_session_aggregates:
            return []
        session_wise_summary = defaultdict(
            lambda: defaultdict(
                lambda: defaultdict(
                    lambda: defaultdict(lambda: defaultdict(lambda: defaultdict(dict)))
                )
            )
        )
        session_wise_summary_report_data = []
        for cashier_session_aggregate in cashier_session_aggregates:
            cash_register_aggregate = cash_register_dict.get(
                cashier_session_aggregate.cashier_session.cash_register_id
            )
            cash_register_name = (
                cash_register_aggregate.cash_register.cash_register_name
            )
            if not cash_register_name:
                continue
            session_number = cashier_session_aggregate.cashier_session.session_number
            start_datetime = dateutils.localize_datetime(
                cashier_session_aggregate.cashier_session.start_datetime
            )
            end_datetime = (
                dateutils.localize_datetime(
                    cashier_session_aggregate.cashier_session.end_datetime
                )
                if cashier_session_aggregate.cashier_session.end_datetime
                else None
            )
            opening_balance_in_base_currency = (
                cashier_session_aggregate.cashier_session.opening_balance_in_base_currency
            )
            closing_balance_in_base_currency = (
                cashier_session_aggregate.cashier_session.closing_balance_in_base_currency
            )

            for cashier_payment in cashier_session_aggregate.non_cancelled_payments:
                payment_mode = cashier_payment.payment_mode
                currency_type = cashier_payment.amount_in_payment_currency.currency
                session_wise_summary[cash_register_name][session_number][
                    start_datetime
                ][end_datetime][payment_mode][
                    currency_type
                ] = self._set_payment_currency_wise_summary_values(
                    currency_type,
                    cashier_payment,
                    session_wise_summary[cash_register_name][session_number][
                        start_datetime
                    ][end_datetime][payment_mode][currency_type],
                )
                session_wise_summary[cash_register_name][session_number][
                    start_datetime
                ][end_datetime][payment_mode][currency_type].update(
                    {
                        "cash_register_name": cash_register_name,
                        "session_number": session_number,
                        "start_datetime": start_datetime,
                        "end_datetime": end_datetime,
                        "payment_mode": payment_mode,
                        "opening_balance_in_base_currency": opening_balance_in_base_currency,
                        "closing_balance_in_base_currency": closing_balance_in_base_currency,
                    }
                )

        for register, session_wise_summary_register_map in session_wise_summary.items():
            result = []
            session_summary_detail = self._extract_bottom_down_value_from_nested_dict(
                session_wise_summary_register_map, result
            )
            session_wise_summary_report_data.extend(session_summary_detail)

        return session_wise_summary_report_data

    def _get_user_session_wise_summary_grouped_on_currency(
        self,
        payment_start_datetime,
        user,
        cashier_session_aggregates,
        cash_register_dict,
    ):
        if not cashier_session_aggregates:
            return []
        if not user:
            return []
        user_currency_wise_summary = defaultdict(
            lambda: defaultdict(
                lambda: defaultdict(
                    lambda: defaultdict(lambda: defaultdict(lambda: defaultdict(dict)))
                )
            )
        )
        user_currency_wise_summary_report_data = []
        for cashier_session_aggregate in cashier_session_aggregates:
            cash_register_id = (
                cashier_session_aggregate.cashier_session.cash_register_id
            )
            cash_register = cash_register_dict.get(cash_register_id)
            cash_register_name = (
                cash_register.cash_register.cash_register_name
                if cash_register
                else None
            )
            if not cash_register_name:
                continue
            session_number = cashier_session_aggregate.cashier_session.session_number
            start_datetime = dateutils.localize_datetime(
                cashier_session_aggregate.cashier_session.start_datetime
            )
            end_datetime = (
                dateutils.localize_datetime(
                    cashier_session_aggregate.cashier_session.end_datetime
                )
                if cashier_session_aggregate.cashier_session.end_datetime
                else None
            )
            for cashier_payment in cashier_session_aggregate.non_cancelled_payments:
                if not (
                    payment_start_datetime <= cashier_payment.date_of_payment
                    and dateutils.to_date(payment_start_datetime)
                    == dateutils.to_date(cashier_payment.date_of_payment)
                ):
                    continue
                if (
                    cashier_payment.added_by
                    and not cashier_payment.added_by.lower() == user.lower()
                ):
                    continue
                currency_type = cashier_payment.amount_in_payment_currency.currency
                user_currency_wise_summary[user][cash_register_name][session_number][
                    start_datetime
                ][end_datetime][
                    currency_type
                ] = self._set_payment_currency_wise_summary_values(
                    currency_type,
                    cashier_payment,
                    user_currency_wise_summary[user][cash_register_name][
                        session_number
                    ][start_datetime][end_datetime][currency_type],
                )
                user_currency_wise_summary[user][cash_register_name][session_number][
                    start_datetime
                ][end_datetime][currency_type].update(
                    {
                        "user": user,
                        "cash_register_name": cash_register_name,
                        "session_number": session_number,
                        "start_datetime": start_datetime,
                        "end_datetime": end_datetime,
                        "currency_name": currency_type.value,
                    }
                )

        for (
            user,
            user_currency_wise_summary_user_map,
        ) in user_currency_wise_summary.items():
            result = []
            user_currency_wise_summary = (
                self._extract_bottom_down_value_from_nested_dict(
                    user_currency_wise_summary_user_map, result
                )
            )
            user_currency_wise_summary_report_data.extend(user_currency_wise_summary)

        return user_currency_wise_summary_report_data

    def _get_user_session_wise_summary_grouped_on_payment_mode(
        self,
        payment_start_datetime,
        user,
        cashier_session_aggregates,
        cash_register_dict,
        base_currency,
    ):
        if not cashier_session_aggregates:
            return []
        if not user:
            return []
        user_mode_wise_summary = defaultdict(
            lambda: defaultdict(
                lambda: defaultdict(
                    lambda: defaultdict(lambda: defaultdict(lambda: defaultdict(dict)))
                )
            )
        )
        user_mode_wise_summary_report_data = []
        for cashier_session_aggregate in cashier_session_aggregates:
            cash_register_id = (
                cashier_session_aggregate.cashier_session.cash_register_id
            )
            cash_register = cash_register_dict.get(cash_register_id)
            cash_register_name = (
                cash_register.cash_register.cash_register_name
                if cash_register
                else None
            )
            if not cash_register_name:
                continue
            session_number = cashier_session_aggregate.cashier_session.session_number
            start_datetime = dateutils.localize_datetime(
                cashier_session_aggregate.cashier_session.start_datetime
            )
            end_datetime = (
                dateutils.localize_datetime(
                    cashier_session_aggregate.cashier_session.end_datetime
                )
                if cashier_session_aggregate.cashier_session.end_datetime
                else None
            )
            for cashier_payment in cashier_session_aggregate.non_cancelled_payments:
                if not (
                    payment_start_datetime <= cashier_payment.date_of_payment
                    and dateutils.to_date(payment_start_datetime)
                    == dateutils.to_date(cashier_payment.date_of_payment)
                ):
                    continue
                if (
                    cashier_payment.added_by
                    and not cashier_payment.added_by.lower() == user.lower()
                ):
                    continue
                payment_mode = cashier_payment.payment_mode
                user_mode_wise_summary[user][cash_register_name][session_number][
                    start_datetime
                ][end_datetime][
                    payment_mode
                ] = self._set_base_currency_wise_summary_values(
                    base_currency,
                    cashier_payment,
                    user_mode_wise_summary[user][cash_register_name][session_number][
                        start_datetime
                    ][end_datetime][payment_mode],
                )

                user_mode_wise_summary[user][cash_register_name][session_number][
                    start_datetime
                ][end_datetime][payment_mode].update(
                    {
                        "user": user,
                        "cash_register_name": cash_register_name,
                        "session_number": session_number,
                        "start_datetime": start_datetime,
                        "end_datetime": end_datetime,
                        "payment_mode": payment_mode,
                    }
                )

        for user, user_mode_wise_summary_user_map in user_mode_wise_summary.items():
            result = []
            user_mode_wise_summary = self._extract_bottom_down_value_from_nested_dict(
                user_mode_wise_summary_user_map, result
            )
            user_mode_wise_summary_report_data.extend(user_mode_wise_summary)

        return user_mode_wise_summary_report_data

    def _extract_bottom_down_value_from_nested_dict(self, nested_dict, result):
        for key, value in nested_dict.items():
            if not isinstance(value, dict):
                if not nested_dict in result:
                    result.append(nested_dict)
            else:
                self._extract_bottom_down_value_from_nested_dict(value, result)
        return result

    def _generate_csv_presigned_url(self, report_detail_aggregates, report_generator):
        file_path = report_generator.generate_cashier_report_file_name()
        folder_path = report_generator.CASHIER_SESSION_REPORTS_FOLDER_NAME
        url_expiry_time = report_generator.get_default_expiration_time()
        if not report_detail_aggregates:
            return
        with CsvWriter(file_path) as csv_writer:
            csv_writer.write_aggregates(
                report_detail_aggregates, report_generator.REPORT_DETAILS_COLUMNS
            )
            presigned_url = AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                folder_path, csv_writer.file_path, url_expiry_time
            )
        return presigned_url

    def _set_payment_currency_wise_summary_values(
        self, currency_type, cashier_payment: CashierPayment, summary_dict: defaultdict
    ):
        if not summary_dict.get('inflow_amount_in_payment_currency'):
            summary_dict['inflow_amount_in_payment_currency'] = Money(0, currency_type)
        if not summary_dict.get('outflow_amount_in_payment_currency'):
            summary_dict['outflow_amount_in_payment_currency'] = Money(0, currency_type)
        if not summary_dict.get('balance_in_payment_currency'):
            summary_dict['balance_in_payment_currency'] = Money(0, currency_type)
        if (
            cashier_payment.payment_type
            and cashier_payment.payment_type.value == self.INFLOW_PAYMENT_TYPE
        ):
            summary_dict['inflow_amount_in_payment_currency'] = (
                cashier_payment.amount_in_payment_currency
                + summary_dict['inflow_amount_in_payment_currency']
            )
            summary_dict[
                'balance_in_payment_currency'
            ] += cashier_payment.amount_in_payment_currency
        if (
            cashier_payment.payment_type
            and cashier_payment.payment_type.value == self.OUTFLOW_PAYMENT_TYPE
        ):
            summary_dict['outflow_amount_in_payment_currency'] = (
                cashier_payment.amount_in_payment_currency
                + summary_dict['outflow_amount_in_payment_currency']
            )
            summary_dict[
                'balance_in_payment_currency'
            ] -= cashier_payment.amount_in_payment_currency
        summary_dict['currency_name'] = currency_type.value
        return summary_dict

    def _set_base_currency_wise_summary_values(
        self, currency_type, cashier_payment: CashierPayment, summary_dict: defaultdict
    ):
        if not summary_dict.get('inflow_amount_in_base_currency'):
            summary_dict['inflow_amount_in_base_currency'] = Money(0, currency_type)
        if not summary_dict.get('outflow_amount_in_base_currency'):
            summary_dict['outflow_amount_in_base_currency'] = Money(0, currency_type)
        if not summary_dict.get('balance_in_base_currency'):
            summary_dict['balance_in_base_currency'] = Money(0, currency_type)
        if (
            cashier_payment.payment_type
            and cashier_payment.payment_type.value == self.INFLOW_PAYMENT_TYPE
        ):
            summary_dict['inflow_amount_in_base_currency'] = (
                cashier_payment.amount + summary_dict['inflow_amount_in_base_currency']
            )
            summary_dict['balance_in_base_currency'] += cashier_payment.amount
        if (
            cashier_payment.payment_type
            and cashier_payment.payment_type.value == self.OUTFLOW_PAYMENT_TYPE
        ):
            summary_dict['outflow_amount_in_base_currency'] = (
                cashier_payment.amount + summary_dict['outflow_amount_in_base_currency']
            )
            summary_dict['balance_in_base_currency'] -= cashier_payment.amount
        summary_dict['currency_name'] = currency_type.value
        return summary_dict
