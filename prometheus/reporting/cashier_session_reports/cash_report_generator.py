import os
import uuid

from prometheus.domain.billing.aggregates.cashier_session_aggregate import (
    CashierSessionAggregate,
)
from prometheus.domain.billing.entities.cash_register import CashRegister
from prometheus.domain.catalog.aggregates.hotel_aggregate import HotelAggregate
from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.cashier_session_reports.report_aggregate import (
    CashierSessionReportAggregate,
)
from ths_common.constants.billing_constants import CashierPaymentStatus, PaymentModes


class CashReportGenerator(BaseReportGenerator):
    CASHIER_SESSION_REPORTS_FOLDER_NAME = 'cashier_reports/'

    REPORT_DETAILS_COLUMNS = [
        "hotel_name",
        "hotel_id",
        "cash_register_name",
        "shift_number",
        "added_by",
        "booking_id",
        "transaction_id",
        "transaction_date",
        "guest_name",
        "payment_type",
        "payment_mode",
        "currency",
        "amount_in_payment_currency",
        "amount_in_base_currency",
        "notes",
        "shift_opening_balance",
        "balance",
        "shift_opened_by",
        "shift_open_time",
        "shift_closed_by",
        "shift_close_time",
    ]

    def __init__(
        self,
        payment_start_datetime,
        payment_end_datetime,
        cashier_session_aggregate: CashierSessionAggregate,
        cash_register_aggregate: CashRegister,
        hotel_aggregate: HotelAggregate,
    ):
        self.payment_start_datetime = payment_start_datetime
        self.payment_end_datetime = payment_end_datetime
        self.cashier_session_aggregate = cashier_session_aggregate
        self.hotel_aggregate = hotel_aggregate
        self.cash_register_aggregate = cash_register_aggregate

    @staticmethod
    def generate_cashier_report_file_name(extension='csv', identifier=None):
        if identifier is None:
            identifier = str(uuid.uuid4())

        file_name = f"cashier-session-cash-report-{identifier}.{extension}"
        return (
            os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp/reports/") + file_name
        )

    @staticmethod
    def get_default_expiration_time():
        return 604800  # 7 days

    def generate(self):
        payments = [
            payment
            for payment in self.cashier_session_aggregate.payments
            if payment.status != CashierPaymentStatus.CANCELLED
            and payment.payment_mode == PaymentModes.CASH
            and self.payment_start_datetime
            <= payment.date_of_payment
            <= self.payment_end_datetime
        ]
        return [
            CashierSessionReportAggregate(
                self.cashier_session_aggregate.cashier_session,
                self.cashier_session_aggregate.opening_balance_amount,
                self.cash_register_aggregate,
                payment,
                self.hotel_aggregate,
            )
            for payment in payments
        ]
