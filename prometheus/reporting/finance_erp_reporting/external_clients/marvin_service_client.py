from flask import current_app as app

from object_registry import register_instance
from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)


@register_instance()
class MarvinServiceClient(BaseExternalClient):
    def __init__(self):
        super().__init__(timeout=3000)

    page_map = {
        "fetch_settlement_data": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/finance/final-settlements",
        )
    }

    def get_domain(self):
        return app.config['MARVIN_SERVICE_ENDPOINT_URL']

    def fetch_settlement_data(self, month, year):
        page_name = "fetch_settlement_data"
        data = dict(month=month, year=year)
        response = self.make_call(page_name, data=data)
        if not response.is_success():
            raise Exception(
                "Marvin API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response
