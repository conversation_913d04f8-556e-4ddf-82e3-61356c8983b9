from object_registry import locate_instance
from prometheus.domain.billing.repositories import BillRepository
from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.finance_erp_reporting.external_finance_reports.ta_commission.ta_commission_report_aggregate import (
    TACommissionReportAggregate,
)


class TACommissionReportGenerator(BaseReportGenerator):
    def __init__(self, booking_aggregates):
        self.booking_aggregates = booking_aggregates
        self.bill_repository = locate_instance(BillRepository)

    def generate(self):
        bill_aggregates = self.bill_repository.load_all_with_yield_per(
            {booking_aggregate.bill_id for booking_aggregate in self.booking_aggregates}
        )
        bill_map = {
            bill_aggregate.bill_id: bill_aggregate for bill_aggregate in bill_aggregates
        }
        return [
            TACommissionReportAggregate(
                booking_aggregate, bill_map[booking_aggregate.bill_id]
            )
            for booking_aggregate in self.booking_aggregates
        ]
