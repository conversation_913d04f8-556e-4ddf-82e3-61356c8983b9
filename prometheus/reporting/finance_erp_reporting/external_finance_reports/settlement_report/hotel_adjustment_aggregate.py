from decimal import Decimal

from treebo_commons.utils import dateutils

from prometheus.reporting.finance_erp_reporting.constants import (
    DocTypes,
    HotelAdjustmentTypeKey,
    HotelAdjustmentTypes,
    TreeboFeeEntryTypes,
    finance_context,
)
from prometheus.reporting.finance_erp_reporting.utils import to_dmy_str


class HotelAdjustmentAggregate(object):
    def __init__(
        self,
        hotel_id,
        entry_type,
        adjustment_type_data: HotelAdjustmentTypeKey,
        settlement,
        invoice_data=None,
        is_reversal=False,
    ):
        self.hotel_code = hotel_id
        self.entry_type = entry_type
        self.adjustment_type = adjustment_type_data.adjustment_type
        self.invoice_data = invoice_data
        if is_reversal:
            self._amount = (
                float(settlement.get(adjustment_type_data.reversal_adjustment_field))
                if isinstance(
                    settlement.get(adjustment_type_data.reversal_adjustment_field), str
                )
                else settlement.get(adjustment_type_data.reversal_adjustment_field)
            )
        elif (
            adjustment_type_data.adjustment_type
            == HotelAdjustmentTypes.GSTDebit.value.adjustment_type
        ):
            self._amount = float(
                settlement.get(
                    HotelAdjustmentTypes.GSTResellerDebit.value.adjustment_field
                )
            ) + float(
                settlement.get(
                    HotelAdjustmentTypes.GSTResellerCredit.value.adjustment_field
                )
            )
        else:
            self._amount = (
                float(settlement.get(adjustment_type_data.adjustment_field))
                if isinstance(
                    settlement.get(adjustment_type_data.adjustment_field), str
                )
                else settlement.get(adjustment_type_data.adjustment_field)
            )

    @property
    def posting_date(self):
        return to_dmy_str(finance_context.settlement_date)

    @property
    def amount(self):
        return round(abs(self._amount), 2)

    @property
    def invoice_date(self):
        return (
            to_dmy_str(dateutils.ymd_str_to_date(self.invoice_data['invoice_date']))
            if self.invoice_data
            else None
        )

    @property
    def invoice_number(self):
        return self.invoice_data['invoice_number'] if self.invoice_data else None

    @property
    def invoice_amount(self):
        return (
            abs(float(self.invoice_data['amount']))
            if self.invoice_data and isinstance(self.invoice_data['amount'], str)
            else None
        )

    @property
    def remarks(self):
        return "{0} of {1} for {2}".format(
            self.entry_type,
            self.adjustment_type,
            finance_context.settlement_date.strftime("%b %y"),
        )

    @property
    def is_non_zero_entry(self):
        return self._amount and self._amount != Decimal('0')

    @property
    def doc_type(self):
        return DocTypes.ORDER if self._amount < 0 else DocTypes.CREDIT
