from decimal import Decimal

from prometheus.reporting.finance_erp_reporting.constants import (
    DocTypes,
    TreeboFeeEntryType<PERSON>ey,
    TreeboFeeEntryTypes,
    finance_context,
)
from prometheus.reporting.finance_erp_reporting.utils import to_dmy_str


class SettlementTreeboFeeAggregate(object):
    def __init__(self, hotel_id, entry_type_data: TreeboFeeEntryTypeKey, settlement):
        self.hotel_code = hotel_id
        self.entry_type = entry_type_data.treebo_fee_type
        self._amount = (
            float(settlement.get(entry_type_data.treebo_fee_type_field))
            if isinstance(settlement.get(entry_type_data.treebo_fee_type_field), str)
            else settlement.get(entry_type_data.treebo_fee_type_field)
        )
        self.gst_percent = entry_type_data.treebo_fee_gst_percent
        self.hsn_code = entry_type_data.treebo_fee_hsn_code
        if self.is_non_zero_entry:
            finance_context.increment_treebo_fee_invoice_number()
        self.remarks = "%s_%s/%s" % (
            self.entry_type,
            finance_context.settlement_date.strftime("%b_%y").upper(),
            finance_context.treebo_fee_invoice_number,
        )

    @property
    def settlement_date(self):
        return to_dmy_str(finance_context.settlement_date)

    @property
    def description(self):
        return "{0} for {1}".format(
            self.entry_type, finance_context.settlement_date.strftime("%b %y")
        )

    @property
    def amount(self):
        if self.entry_type == TreeboFeeEntryTypes.OTSFFranchise.value.treebo_fee_type:
            return round(abs(self._amount) / 1.18, 2)
        return abs(self._amount)

    @property
    def is_non_zero_entry(self):
        return self._amount and self._amount != Decimal('0')

    @property
    def doc_type(self):
        return DocTypes.ORDER if self._amount < 0 else DocTypes.CREDIT

    @property
    def is_order(self):
        return bool(self.doc_type == DocTypes.ORDER)
