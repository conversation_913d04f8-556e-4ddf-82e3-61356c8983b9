from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.finance_erp_reporting.external_finance_reports.customer_invoice_report.customer_invoice_report_aggregate import (
    CustomerInvoiceReportAggregate,
)


class CustomerInvoiceReportGenerator(BaseReportGenerator):
    def __init__(self, invoice_report_aggregates, buy_side_mappings):
        self.invoice_report_aggregates = invoice_report_aggregates
        self.buy_side_mappings = buy_side_mappings

    def generate(self):
        customer_invoice_report_aggregates = [
            CustomerInvoiceReportAggregate(
                invoice_report_aggregate,
                self.buy_side_mappings.get(invoice_report_aggregate.invoice_number),
            )
            for invoice_report_aggregate in self.invoice_report_aggregates
        ]
        return customer_invoice_report_aggregates
