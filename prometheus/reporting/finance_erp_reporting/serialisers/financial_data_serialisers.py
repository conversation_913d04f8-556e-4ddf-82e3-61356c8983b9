from marshmallow import Schema, fields, post_dump
from treebo_commons.money.money_field import MoneyField


class TaxDetailSchema(Schema):
    amount = MoneyField()
    percentage = fields.Decimal(as_string=True)
    tax_amount = MoneyField()
    tax_type = fields.String()


class PaymentSplitDetailSchema(Schema):
    amount = fields.Decimal(required=True, as_string=True)
    payment_id = fields.Integer(allow_none=True)
    payment_split_id = fields.Integer(allow_none=True)


class BasePushSchema(Schema):
    bill_id = fields.String()
    hotel_id = fields.String()
    booking_id = fields.String()
    category = fields.String()
    owner_name = fields.String()
    folio_number = fields.String()
    billed_entity_id = fields.String()
    account_number = fields.String()
    booking_reference_number = fields.String()
    booker_external_id = fields.String(allow_none=True)
    is_credit_folio = fields.Boolean(allow_none=True)
    folio_status = fields.String(allow_none=True)
    payment_split_details = fields.Nested(
        PaymentSplitDetailSchema, many=True, allow_none=True
    )
    fin_erp_posting_date = fields.Date(required=True, allow_none=False)

    @post_dump
    def transform_keys(self, data):
        if "booker_external_id" in data:
            data["debtor_code"] = data.pop("booker_external_id")
        return data


class ChargeDetailsPushSchema(BasePushSchema):
    charge_id = fields.String()
    charge_split_id = fields.String()
    pretax_amount = fields.Decimal(required=True, as_string=True)
    posttax_amount = fields.Decimal(required=True, as_string=True)
    tax_amount = fields.Decimal(required=True, as_string=True)
    tax_details = fields.Nested(TaxDetailSchema, many=True, allow_none=True)
    charge_type = fields.String()
    bill_to_type = fields.String()
    item_id = fields.String()
    sku_category_id = fields.String()
    applicable_business_date = fields.Date(allow_none=True)
    posting_date = fields.Date(allow_none=True)
    is_inclusion_charge = fields.Boolean()
    revenue_center = fields.String(allow_none=True)


class PaymentDetailsPushSchema(BasePushSchema):
    payment_id = fields.String()
    payment_split_id = fields.String()
    payment_type = fields.String()
    amount = fields.Decimal(required=True, as_string=True)
    posting_date = fields.Date(allow_none=True)
    date_of_payment = fields.Date(allow_none=True)
    payment_mode = fields.String()
    crs_payment_mode = fields.String()
    payment_mode_sub_type = fields.String(allow_none=True)
    crs_payment_mode_sub_type = fields.String(allow_none=True)
    payment_ref_id = fields.String(allow_none=True)
    payment_channel = fields.String(allow_none=True)
    revenue_center = fields.String(allow_none=True)
    checkin_date = fields.Date(allow_none=True)
    checkout_date = fields.Date(allow_none=True)
    guest_name = fields.String(allow_none=True)
    room_number = fields.String(allow_none=True)


class AllowanceDetailsPushSchema(BasePushSchema):
    allowance_id = fields.String()
    charge_id = fields.String()
    charge_split_id = fields.String()
    posting_date = fields.Date(allow_none=True)
    tax_amount = fields.Decimal(as_string=True, allow_none=True)
    posttax_amount = fields.Decimal(as_string=True, allow_none=True)
    pretax_amount = fields.Decimal(as_string=True, allow_none=True)
    tax_details = fields.Nested(TaxDetailSchema, many=True, allow_none=True)
    charge_type = fields.String()
    bill_to_type = fields.String()
    item_id = fields.String()
    sku_category_id = fields.String()
    revenue_center = fields.String(allow_none=True)


class InvoiceDetailsPushSchema(Schema):
    billed_entity_id = fields.String()
    billed_entity_account_number = fields.String()
    bill_id = fields.String()
    is_credit_invoice = fields.Boolean()
    posttax_amount = fields.Decimal(required=True, as_string=True)
    invoice_id = fields.String(allow_none=True)
