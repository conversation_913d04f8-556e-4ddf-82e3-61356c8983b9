from prometheus.reporting.base_report_aggregate import BaseReportAggregate


class InTouchProfileReportAggregate(BaseReportAggregate):
    def __init__(self, booking_customer):
        self.booking_customer = booking_customer

    @property
    def pms_profile_id(self):
        return self.booking_customer.pms_profile_id

    @property
    def profile_type(self):
        return self.booking_customer.profile_type

    @property
    def name(self):
        return self.booking_customer.name

    @property
    def legal_name(self):
        return self.booking_customer.legal_name

    @property
    def company_name(self):
        return self.booking_customer.legal_name

    @property
    def title(self):
        return self.booking_customer.title

    @property
    def first_name(self):
        return self.booking_customer.first_name

    @property
    def last_name(self):
        return self.booking_customer.last_name

    @property
    def address(self):
        return self.booking_customer.address

    @property
    def city(self):
        return self.booking_customer.city

    @property
    def state(self):
        return self.booking_customer.state

    @property
    def zip_code(self):
        return self.booking_customer.zip_code

    @property
    def country(self):
        return self.booking_customer.country

    @property
    def nationality(self):
        return self.booking_customer.nationality

    @property
    def phone(self):
        return self.booking_customer.phone

    @property
    def email(self):
        return self.booking_customer.email

    @property
    def date_of_birth(self):
        return self.booking_customer.date_of_birth

    @property
    def gender(self):
        return self.booking_customer.gender

    @property
    def membership_id(self):
        return self.booking_customer.membership_id

    @property
    def membership_type(self):
        return self.booking_customer.membership_type

    @property
    def external_profile_identifier(self):
        return self.booking_customer.external_profile_identifier

    @property
    def insert_date(self):
        return self.booking_customer.insert_date

    @property
    def update_date(self):
        return self.booking_customer.update_date

    @property
    def is_vip(self):
        return self.booking_customer.is_vip

    @property
    def address_line_1(self):
        return self.booking_customer.address_line_1

    @property
    def address_line_2(self):
        return self.booking_customer.address_line_2

    @property
    def address_line_3(self):
        return None

    @property
    def pms_profile_id_context(self):
        return "Accor"

    @property
    def code(self):
        return None

    @property
    def doc_type(self):
        return self.booking_customer.doc_type

    @property
    def doc_id(self):
        return self.booking_customer.doc_id

    @property
    def effective_date(self):
        return self.booking_customer.effective_date

    @property
    def doc_issue_location(self):
        return self.booking_customer.doc_issue_location

    @property
    def doc_issue_country_code(self):
        return self.booking_customer.doc_issue_country_code

    @property
    def third_party_communication(self):
        return None

    @property
    def receive_promotion(self):
        return None

    @property
    def is_primary_address(self):
        return "Yes"
