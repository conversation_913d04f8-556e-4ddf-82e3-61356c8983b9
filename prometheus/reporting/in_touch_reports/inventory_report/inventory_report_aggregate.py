from prometheus.reporting.base_report_aggregate import BaseReportAggregate


class InTouchInventoryReportAggregate(BaseReportAggregate):
    def __init__(
        self,
        room_type_inventories_count,
        room_type_id,
        inventory_date,
        housekeeping_room_status_count,
        total_rooms_count,
        ooo_rooms_count,
    ):
        self.room_type_inventories_count = room_type_inventories_count
        self.room_type_id = room_type_id
        self.total_rooms_count = total_rooms_count
        self.inventory_date = inventory_date
        self.housekeeping_room_status_count = housekeeping_room_status_count
        self.ooo_rooms_count = ooo_rooms_count

    @property
    def room_type(self):
        return self.room_type_id

    @property
    def date(self):
        return self.inventory_date

    @property
    def inventory(self):
        return self.room_type_inventories_count

    @property
    def out_of_order_rooms(self):
        return self.ooo_rooms_count

    @property
    def out_of_service_rooms(self):
        return self.housekeeping_room_status_count['out_of_service_rooms']

    @property
    def total_rooms(self):
        return self.total_rooms_count

    @property
    def hotel_open(self):
        if self.total_rooms - (self.out_of_order_rooms + self.out_of_service_rooms) > 0:
            return 'Y'
        else:
            return 'N'
