import logging

from flask.cli import with_appcontext
from sentry_sdk.integrations.serverless import serverless_function
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.utils import dateutils

from prometheus.common.decorators import consumer_middleware
from prometheus.infrastructure.consumers.base_consumer import BaseRMQConsumer
from prometheus.infrastructure.consumers.consumer_config import ReportingConfig
from prometheus.middlewares.common_middlewares import exception_handler
from prometheus.reporting.accounts_receivable_reporting.ar_reports_generator import (
    ARReportsGenerator,
)
from prometheus.reporting.finance_erp_reporting.finance_reports_generator import (
    FinanceReportsGenerator,
)
from prometheus.reporting.finance_erp_reporting.financial_data_sync.service import (
    FinancialDataReportingService,
)
from prometheus.reporting.invoice_report.invoice_report_generator import (
    InvoiceReportGenerator,
)
from prometheus.reporting.marvin_report.marvin_report_generator import (
    MarvinReportGenerator,
)
from prometheus.reporting.sme_report.sme_report_generator import SmeReportGenerator

logger = logging.getLogger(__name__)


class ReportingJobConsumer(BaseRMQConsumer):
    def __init__(
        self,
        reporting_app_service,
        finance_reporting_service,
        ar_reporting_service,
        tenant_id=TenantClient.get_default_tenant(),
    ):
        super().__init__(ReportingConfig(tenant_id=tenant_id))
        self.reporting_app_service = reporting_app_service
        self.finance_reporting_service = finance_reporting_service
        self.ar_reporting_service = ar_reporting_service

    def get_consumers(self, Consumer, channel):
        consumer = Consumer(queues=[self.queue], callbacks=[self.process_message])
        consumer.qos(prefetch_count=1, apply_global=True)
        return [consumer]

    @serverless_function
    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        obj = body
        logger.info(
            "Reporting Job process_message called for entity: %s", obj.get('event_type')
        )

        try:
            if obj.get('event_type') == MarvinReportGenerator.EVENT_TYPE:
                self.process_marvin_report(obj)
            if obj.get('event_type') == InvoiceReportGenerator.EVENT_TYPE:
                self.process_invoice_report(obj)
            if obj.get('event_type') == SmeReportGenerator.EVENT_TYPE:
                self.process_sme_report(obj)
            if obj.get('event_type') == FinanceReportsGenerator.FP_EVENT_TYPE:
                self.process_finance_data_push(obj)
            if obj.get('event_type') == ARReportsGenerator.FP_EVENT_TYPE:
                self.process_ar_data_push(obj)
            if obj.get('event_type') == FinancialDataReportingService.EVENT_TYPE:
                self.process_financial_data_sync(obj)

        except Exception as exc:
            exception_handler(exc, from_consumer=True)

        logger.info("Reporting process complete. Message acknowledged")
        message.ack()

    def process_marvin_report(self, obj):
        hotel_ids = obj.get('hotel_ids')
        self.reporting_app_service.generate_between_date_range_and_trigger_marvin_report_email(
            email=obj.get('email'),
            start_date=obj.get('start_date'),
            end_date=obj.get('end_date'),
            report_type=obj.get('report_type'),
            hotel_ids=hotel_ids,
        )

    def process_invoice_report(self, obj):
        hotel_ids = obj.get('hotel_ids')
        self.reporting_app_service.generate_between_date_range_and_trigger_invoice_report_email(
            email=obj.get('email'),
            start_date=obj.get('start_date'),
            end_date=obj.get('end_date'),
            hotel_ids=hotel_ids,
            corporate_id=obj.get('corporate_id'),
            report_type=obj.get('report_type'),
        )

    def process_sme_report(self, obj):
        start_date = dateutils.ymd_str_to_date(obj.get('start_date'))
        end_date = dateutils.ymd_str_to_date(obj.get('end_date'))
        self.reporting_app_service.generate_between_date_range_and_trigger_sme_report_email(
            start_date=start_date, end_date=end_date
        )

    def process_finance_data_push(self, obj):
        self.finance_reporting_service.push_finance_reports(
            date=obj.get('date'),
            report_names=obj.get('report_names'),
            month=obj.get('month'),
            year=obj.get('year'),
            named_arg=obj.get('named_arg'),
        )

    def process_ar_data_push(self, obj):
        self.ar_reporting_service.push_ar_reports(
            date=obj.get('date'), report_names=obj.get('report_names')
        )

    def process_financial_data_sync(self, obj):
        self.reporting_app_service.generate_and_push_financial_data_for_date(
            report_date=obj.get('report_date'), hotel_id=obj.get('hotel_id')
        )
