from collections import defaultdict
from decimal import Decimal

from treebo_commons.money import Money

from prometheus import crs_context
from ths_common.constants.billing_constants import PaymentTypes
from ths_common.value_objects import TaxDetail


class DepositLedgerDTOV2:
    def __init__(self):
        self.total_payments = []
        self.total_charges = []
        self.revenue_components = defaultdict(list)
        self.non_revenue_components = defaultdict(list)
        self.payment_components = defaultdict(list)

    def calculate_charge_components(self, charges, allowances):
        base_currency = crs_context.get_hotel_context().base_currency
        for charge in charges:
            self.total_charges.append(Money(charge.posttax_amount, base_currency))
            self.revenue_components[charge.sku_category_id].append(
                Money(charge.pretax_amount, base_currency)
            )

            for tax in charge.tax_details or []:
                self.non_revenue_components[tax.tax_type].append(
                    Money(tax.amount, base_currency)
                )

        for allowance in allowances:
            self.total_charges.append(Money(-allowance.posttax_amount, base_currency))
            self.revenue_components[allowance.sku_category_id].append(
                Money(-allowance.pretax_amount, base_currency)
            )
            for tax in allowance.tax_details or []:
                self.non_revenue_components[tax.tax_type].append(
                    Money(-tax.amount, base_currency)
                )

    def calculate_payment_components(self, payments):
        base_currency = crs_context.get_hotel_context().base_currency
        for payment in payments:
            do_payment_mode = f"do_{payment.payment_mode}"
            payment_amount_in_base_currency = (
                Money(-payment.amount, base_currency)
                if payment.payment_type == PaymentTypes.PAYMENT.value
                else Money(payment.amount, base_currency)
            )
            self.payment_components[do_payment_mode].append(
                payment_amount_in_base_currency
            )
            self.total_payments.append(payment_amount_in_base_currency)
