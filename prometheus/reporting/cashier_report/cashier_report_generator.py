import os
import uuid

from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.cashier_report.cashier_report_aggregate import (
    CashierReportAggregate,
)
from ths_common.constants.billing_constants import PaymentStatus


class CashierReportGenerator(BaseReportGenerator):
    """
    Responsible for generating report rows for a given booking
    """

    REPORT_COLUMNS = [
        "hotel_name",
        "hotel_id",
        "booking_id",
        "added_by",
        "payment_type",
        "payment_amount_in_payment_currency",
        "payment_amount",
        "payment_date",
        "payment_mode",
        "sub_payment_mode",
        "currency",
        "checkin_date",
        "checkout_date",
        "get_channel",
        "booking_status",
    ]

    CASHIER_REPORT_FOLDER_NAME = 'cashier_reports/'

    def __init__(
        self, booking_aggregate, bill_aggregate, hotel_aggregate, room_type_map
    ):
        self.hotel_aggregate = hotel_aggregate
        self.booking_aggregate = booking_aggregate
        self.bill_aggregate = bill_aggregate
        self.room_type_map = room_type_map

    def generate(self):
        payments = [
            payment
            for payment in self.bill_aggregate.payments
            if payment.status != PaymentStatus.CANCELLED
        ]
        return [
            CashierReportAggregate(
                self.booking_aggregate,
                self.bill_aggregate,
                self.hotel_aggregate,
                payment,
            )
            for payment in payments
        ]

    def generate_csv(self):
        payments = [
            payment
            for payment in self.bill_aggregate.payments
            if payment.status != PaymentStatus.CANCELLED
        ]
        return [
            CashierReportAggregate(
                self.booking_aggregate,
                self.bill_aggregate,
                self.hotel_aggregate,
                payment,
            )
            for payment in payments
        ]

    @staticmethod
    def generate_cashier_report_file_name(extension='csv', identifier=None):
        if identifier is None:
            identifier = str(uuid.uuid4())

        file_name = f"cashier-report-{identifier}.{extension}"
        return (
            os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp/reports/") + file_name
        )

    @staticmethod
    def get_default_expiration_time():
        return 604800  # 7 days
