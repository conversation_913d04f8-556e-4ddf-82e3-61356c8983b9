import logging
from collections import defaultdict

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.decorators import set_hotel_context
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories import RoomTypeRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from prometheus.reporting.cashier_report.cashier_report_generator import (
    CashierReportGenerator,
)
from prometheus.reporting.reporting_service import ReportingApplicationService
from prometheus.reporting.utils import CsvWriter
from ths_common.utils.collectionutils import chunks

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        RoomTypeRepository,
        ReportingApplicationService,
    ]
)
class CashierReportingService(object):
    """
    Generic application service for reports/reporting
    """

    INFLOW_PAYMENT_TYPE = 'inflow'
    OUTFLOW_PAYMENT_TYPE = 'outflow'

    def __init__(
        self,
        booking_repository,
        bill_repository,
        hotel_repository,
        room_type_repository,
        reporting_application_service,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.hotel_repository = hotel_repository
        self.room_type_repository = room_type_repository
        self.reporting_application_service = reporting_application_service

    @staticmethod
    def _fail_if_user_not_authorized_to_access_reports():
        return RuleEngine.action_allowed(
            action="access_report",
            facts=Facts(
                user_type=crs_context.user_data.user_type,
                hotel_context=crs_context.get_hotel_context(),
            ),
            fail_on_error=True,
        )

    def _base_data_for_report(self, start_date, end_date, hotel_aggregate):
        bill_aggregates = self.bill_repository.cashier_report_query(
            hotel_id=hotel_aggregate.hotel_id,
            start_date=dateutils.date_to_ymd_str(start_date),
            end_date=dateutils.date_to_ymd_str(end_date),
        )
        booking_aggregates = self.booking_repository.load_for_bill_ids_with_yield_per(
            [bill_aggregate.bill_id for bill_aggregate in bill_aggregates]
        )
        hotel_map = {hotel_aggregate.hotel_id: hotel_aggregate}
        room_type_map = self.room_type_repository.load_type_map()
        return booking_aggregates, bill_aggregates, hotel_map, room_type_map

    @set_hotel_context()
    def report_summary(
        self, start_date, end_date, user_data=None, hotel_aggregate=None
    ):
        self._fail_if_user_not_authorized_to_access_reports()
        (
            booking_aggregates,
            bill_aggregates,
            hotel_map,
            room_type_map,
        ) = self._base_data_for_report(start_date, end_date, hotel_aggregate)
        cashier_report_summary = {
            'mode_wise_summary': [],
            'datewise_summary': [],
            'currency_wise_summary': [],
        }
        for bookings in chunks(booking_aggregates, 1000):
            channel_wise_booking_summary_chunk = self._generate_report_summary(
                bookings, hotel_map, room_type_map, start_date, end_date
            )
            cashier_report_summary['mode_wise_summary'].extend(
                channel_wise_booking_summary_chunk['mode_wise_summary']
            )
            cashier_report_summary['datewise_summary'].extend(
                channel_wise_booking_summary_chunk['datewise_summary']
            )
            cashier_report_summary['currency_wise_summary'].extend(
                channel_wise_booking_summary_chunk['currency_wise_summary']
            )
        return cashier_report_summary

    def _generate_report_summary(
        self, booking_aggregates, hotel_map, room_type_map, start_date, end_date
    ):
        bill_ids = {
            booking_aggregate.bill_id for booking_aggregate in booking_aggregates
        }
        aggregate_maps = self.reporting_application_service.build_aggregate_maps(
            bill_ids=bill_ids,
            load_invoices_by_bill_id=True,
            hotel_map=hotel_map,
            room_type_map=room_type_map,
        )
        report_aggregates = []
        for booking_aggregate in booking_aggregates:
            bill_aggregate = aggregate_maps.bill_map.get(
                booking_aggregate.booking.bill_id
            )
            hotel_aggregate = aggregate_maps.hotel_map.get(
                booking_aggregate.booking.hotel_id
            )
            cashier_report_aggregates = CashierReportGenerator(
                booking_aggregate,
                bill_aggregate,
                hotel_aggregate,
                aggregate_maps.room_type_map,
            ).generate()
            if cashier_report_aggregates:
                report_aggregates.append(cashier_report_aggregates)
        return {
            "mode_wise_summary": self._get_mode_wise_booking_summary(
                report_aggregates, start_date, end_date
            ),
            "currency_wise_summary": self._get_currency_wise_booking_summary(
                report_aggregates, start_date, end_date
            ),
            "datewise_summary": self._get_datewise_report_summary(
                report_aggregates, start_date, end_date
            ),
        }

    def _get_currency_wise_booking_summary(
        self, report_aggregates, start_date, end_date
    ):
        currency_wise_booking_summary = defaultdict(lambda: defaultdict(Money))
        for cashier_report_aggregates in report_aggregates:
            for cashier_report_aggregate in cashier_report_aggregates:
                if (
                    not start_date
                    <= cashier_report_aggregate.payment_date.date()
                    <= end_date
                ):
                    continue
                currency_type = cashier_report_aggregate.payment_currency
                if not currency_wise_booking_summary[currency_type]['amount_inflow']:
                    currency_wise_booking_summary[currency_type][
                        'amount_inflow'
                    ] = Money(0, currency_type)
                if not currency_wise_booking_summary[currency_type]['amount_outflow']:
                    currency_wise_booking_summary[currency_type][
                        'amount_outflow'
                    ] = Money(0, currency_type)
                if not currency_wise_booking_summary[currency_type]['balance']:
                    currency_wise_booking_summary[currency_type]['balance'] = Money(
                        0, currency_type
                    )

                if cashier_report_aggregate.payment_type == self.INFLOW_PAYMENT_TYPE:
                    currency_wise_booking_summary[currency_type]['amount_inflow'] = (
                        cashier_report_aggregate.payment_amount_in_payment_currency
                        + currency_wise_booking_summary[currency_type]['amount_inflow']
                    )
                    currency_wise_booking_summary[currency_type][
                        'balance'
                    ] += cashier_report_aggregate.payment_amount_in_payment_currency
                if cashier_report_aggregate.payment_type == self.OUTFLOW_PAYMENT_TYPE:
                    currency_wise_booking_summary[currency_type]['amount_outflow'] = (
                        cashier_report_aggregate.payment_amount_in_payment_currency
                        + currency_wise_booking_summary[currency_type]['amount_outflow']
                    )
                    currency_wise_booking_summary[currency_type][
                        'balance'
                    ] -= cashier_report_aggregate.payment_amount_in_payment_currency
                currency_wise_booking_summary[currency_type][
                    'currency_name'
                ] = currency_type.value

        return list(currency_wise_booking_summary.values())

    def _get_mode_wise_booking_summary(self, report_aggregates, start_date, end_date):
        if not report_aggregates:
            return []
        mode_wise_booking_summary = defaultdict(lambda: defaultdict(Money))
        for cashier_report_aggregates in report_aggregates:
            for cashier_report_aggregate in cashier_report_aggregates:
                if (
                    not start_date
                    <= cashier_report_aggregate.payment_date.date()
                    <= end_date
                ):
                    continue
                mode = cashier_report_aggregate.payment_mode
                if not mode_wise_booking_summary[mode]['amount_inflow']:
                    mode_wise_booking_summary[mode]['amount_inflow'] = Money(
                        0, cashier_report_aggregate.currency
                    )
                if not mode_wise_booking_summary[mode]['amount_outflow']:
                    mode_wise_booking_summary[mode]['amount_outflow'] = Money(
                        0, cashier_report_aggregate.currency
                    )
                if not mode_wise_booking_summary[mode]['balance']:
                    mode_wise_booking_summary[mode]['balance'] = Money(
                        0, cashier_report_aggregate.currency
                    )
                if cashier_report_aggregate.payment_type == self.INFLOW_PAYMENT_TYPE:
                    mode_wise_booking_summary[mode]['amount_inflow'] = (
                        cashier_report_aggregate.payment_amount
                        + mode_wise_booking_summary[mode]['amount_inflow']
                    )
                    mode_wise_booking_summary[mode][
                        'balance'
                    ] += cashier_report_aggregate.payment_amount
                if cashier_report_aggregate.payment_type == self.OUTFLOW_PAYMENT_TYPE:
                    mode_wise_booking_summary[mode]['amount_outflow'] = (
                        cashier_report_aggregate.payment_amount
                        + mode_wise_booking_summary[mode]['amount_outflow']
                    )
                    mode_wise_booking_summary[mode][
                        'balance'
                    ] -= cashier_report_aggregate.payment_amount
                mode_wise_booking_summary[mode]['mode_name'] = mode

        return list(mode_wise_booking_summary.values())

    def _get_datewise_report_summary(self, report_aggregates, start_date, end_date):
        datewise_mode_wise_summary = defaultdict(lambda: defaultdict(dict))
        report_summary_for_dates = []
        for cashier_report_aggregates in report_aggregates:
            for cashier_report_aggregate in cashier_report_aggregates:
                if (
                    not start_date
                    <= cashier_report_aggregate.payment_date.date()
                    <= end_date
                ):
                    continue
                payment_date = cashier_report_aggregate.payment_date.date()
                mode = cashier_report_aggregate.payment_mode
                if not datewise_mode_wise_summary[payment_date][mode]:
                    datewise_mode_wise_summary[payment_date][mode] = {
                        'amount_collected': Money(0, cashier_report_aggregate.currency)
                    }
                datewise_mode_wise_summary[payment_date][mode]['amount_collected'] = (
                    cashier_report_aggregate.payment_amount
                    + datewise_mode_wise_summary[payment_date][mode]['amount_collected']
                )
                datewise_mode_wise_summary[payment_date][mode]['date'] = payment_date
                datewise_mode_wise_summary[payment_date][mode]['mode_name'] = mode

        for date, mode_wise_summary in datewise_mode_wise_summary.items():
            for mode, payment_summary in mode_wise_summary.items():
                report_summary_for_dates.append(payment_summary)
        return report_summary_for_dates

    @set_hotel_context()
    def report_details(
        self, start_date, end_date, user_data=None, hotel_aggregate=None
    ):
        self._fail_if_user_not_authorized_to_access_reports()

        (
            booking_aggregates,
            bill_aggregates,
            hotel_map,
            room_type_map,
        ) = self._base_data_for_report(start_date, end_date, hotel_aggregate)
        channel_wise_booking_details = []
        for bookings in chunks(booking_aggregates, 1000):
            channel_wise_booking_details.extend(
                self._generate_report_details(
                    bookings, hotel_map, room_type_map, start_date, end_date
                )
            )
        return {'reports': channel_wise_booking_details}

    def _generate_report_details(
        self, booking_aggregates, hotel_map, room_type_map, start_date, end_date
    ):
        bill_ids = {
            booking_aggregate.bill_id for booking_aggregate in booking_aggregates
        }
        aggregate_maps = self.reporting_application_service.build_aggregate_maps(
            bill_ids=bill_ids,
            load_invoices_by_bill_id=True,
            hotel_map=hotel_map,
            room_type_map=room_type_map,
        )
        report_details_response = []
        for booking_aggregate in booking_aggregates:
            bill_aggregate = aggregate_maps.bill_map.get(
                booking_aggregate.booking.bill_id
            )
            hotel_aggregate = aggregate_maps.hotel_map.get(
                booking_aggregate.booking.hotel_id
            )
            cashier_report_aggregates = CashierReportGenerator(
                booking_aggregate,
                bill_aggregate,
                hotel_aggregate,
                aggregate_maps.room_type_map,
            ).generate()
            if cashier_report_aggregates:
                for cashier_report_aggregate in cashier_report_aggregates:
                    if (
                        not start_date
                        <= cashier_report_aggregate.payment_date.date()
                        <= end_date
                    ):
                        continue
                    report_details_response.append(
                        {
                            "booking_id": cashier_report_aggregate.booking_id,
                            "guest_name": cashier_report_aggregate.guest_name,
                            "payment_type": cashier_report_aggregate.payment_type,
                            "amount": cashier_report_aggregate.payment_amount,
                            "payment_date": cashier_report_aggregate.payment_date,
                            "payment_mode": cashier_report_aggregate.payment_mode,
                            "sub_payment_mode": cashier_report_aggregate.sub_payment_mode,
                            "currency": cashier_report_aggregate.currency.value,
                        }
                    )

        return report_details_response

    @set_hotel_context()
    def generate_csv_report(
        self, start_date, end_date, user_data=None, hotel_aggregate=None
    ):
        self._fail_if_user_not_authorized_to_access_reports()

        (
            booking_aggregates,
            bill_aggregates,
            hotel_map,
            room_type_map,
        ) = self._base_data_for_report(start_date, end_date, hotel_aggregate)

        file_path = CashierReportGenerator.generate_cashier_report_file_name()
        with CsvWriter(file_path) as csv_writer:
            for bookings in chunks(booking_aggregates, 1000):
                self._generate_csv_report(
                    bookings, hotel_map, room_type_map, csv_writer, start_date, end_date
                )
            presigned_url = AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                CashierReportGenerator.CASHIER_REPORT_FOLDER_NAME,
                csv_writer.file_path,
                CashierReportGenerator.get_default_expiration_time(),
            )

        return presigned_url

    def _generate_csv_report(
        self,
        booking_aggregates,
        hotel_map,
        room_type_map,
        csv_writer,
        start_date,
        end_date,
    ):
        bill_ids = {
            booking_aggregate.bill_id for booking_aggregate in booking_aggregates
        }
        aggregate_maps = self.reporting_application_service.build_aggregate_maps(
            bill_ids=bill_ids,
            load_invoices_by_bill_id=True,
            hotel_map=hotel_map,
            room_type_map=room_type_map,
        )
        report_aggregates = []
        for booking_aggregate in booking_aggregates:
            bill_aggregate = aggregate_maps.bill_map.get(
                booking_aggregate.booking.bill_id
            )
            hotel_aggregate = aggregate_maps.hotel_map.get(
                booking_aggregate.booking.hotel_id
            )
            csv_report_aggregates = CashierReportGenerator(
                booking_aggregate,
                bill_aggregate,
                hotel_aggregate,
                aggregate_maps.room_type_map,
            ).generate_csv()
            csv_report_aggregates = [
                csv_report_aggregate
                for csv_report_aggregate in csv_report_aggregates
                if start_date <= csv_report_aggregate.payment_date.date() <= end_date
            ]
            if csv_report_aggregates:
                report_aggregates.extend(csv_report_aggregates)
        csv_writer.write_aggregates(
            report_aggregates, CashierReportGenerator.REPORT_COLUMNS
        )
