import itertools
import logging
from collections import defaultdict

import click
from flask.cli import with_appcontext
from treebo_commons.money import Money
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context
from treebo_commons.utils import dateutils

from object_registry import inject
from prometheus import crs_context
from prometheus.application.decorators import session_manager
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.dtos import BookingSearchQuery
from prometheus.domain.booking.repositories import (
    AddonRepository,
    ExpenseItemRepository,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories import HotelRepository
from ths_common.utils.collectionutils import chunks
from ths_common.utils.common_utils import group_list

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    '--booking_ids',
    help="comma separated booking_ids to which has got 1 or more linked addons "
    "attached",
    default=None,
)
@click.option(
    '--exclude_booking_ids',
    help="comma separated booking_ids to which has got 1 or more linked addons "
    "attached",
    default=None,
)
@click.option(
    '--checkout_start_date',
    help="checkout start date of bookings that we need to attach billed entity "
    "accounts to charges in ymd format",
    default=None,
)
@click.option(
    '--checkout_end_date',
    help="checkout end date of bookings that we need to attach billed entity "
    "accounts to charges in ymd format",
    default=None,
)
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@inject(
    booking_repo=BookingRepository,
    bill_repo=BillRepository,
    addon_repo=AddonRepository,
    hotel_repo=HotelRepository,
    expense_item_repo=ExpenseItemRepository,
)
def update_pretax_price_in_linked_charge_components(
    booking_repo: BookingRepository,
    bill_repo: BillRepository,
    addon_repo: AddonRepository,
    hotel_repo: HotelRepository,
    expense_item_repo: ExpenseItemRepository,
    booking_ids,
    exclude_booking_ids,
    checkout_start_date,
    checkout_end_date,
    tenant_id,
):
    """
    flask update_pretax_price_in_linked_charge_components --booking_ids=1900-4959-8784,1900-8628-4931
    """
    request_context.tenant_id = tenant_id
    crs_context.set_tenant_id(tenant_id)

    booking_ids = booking_repo.load_all_bookings_to_migrate_charge_components(
        booking_ids=booking_ids.split(','),
        search_query=BookingSearchQuery(sort_by='checkin'),
        checkout_start_date=checkout_start_date,
        checkout_end_date=checkout_end_date,
        exclude_booking_ids=exclude_booking_ids.split(',')
        if exclude_booking_ids
        else [],
    )

    count = 0
    for bookings in chunks(booking_ids, 500):
        try:
            _update_pretax_price_in_charge_components(
                hotel_repo,
                booking_repo,
                bill_repo,
                addon_repo,
                expense_item_repo,
                bookings,
            )
            count += 500
            logger.info("Completed %s / %s", count, len(booking_ids))
        except Exception as e:
            logger.exception(
                "Pretax price update in charge component failed for bookings: %s",
                bookings,
            )


@session_manager(commit=True)
def _update_pretax_price_in_charge_components(
    hotel_repo: HotelRepository,
    booking_repo,
    bill_repo,
    addon_repo,
    expense_item_repo,
    booking_ids,
):
    # Load all bookings, bills, linked addons, and expense items
    booking_aggregates = booking_repo.load_all_with_yield_per(booking_ids)
    hotel_aggregates = hotel_repo.load_all(
        hotel_ids=[aggregate.hotel_id for aggregate in booking_aggregates]
    )
    grouped_hotel_aggregate = {agg.hotel_id: agg for agg in hotel_aggregates}
    booking_by_hotel_ids = group_list(booking_aggregates, 'hotel_id')
    bill_ids = {aggregate.bill_id for aggregate in booking_aggregates}
    bill_aggregates = bill_repo.load_all_for_update(bill_ids)
    grouped_bill_aggregates = {
        aggregate.bill_id: aggregate for aggregate in bill_aggregates
    }
    linked_addon_aggregates = []
    for hotel_id, bookings in booking_by_hotel_ids.items():
        hotel_agg = grouped_hotel_aggregate.get(hotel_id)
        crs_context.set_hotel_context(hotel_agg)
        linked_addon_aggregates.extend(
            addon_repo.load_linked_addons([agg.booking_id for agg in bookings])
        )

    booking_wise_addons = group_list(linked_addon_aggregates, 'booking_id')

    expense_items = expense_item_repo.load_all(include_linked=True)
    grouped_expense_items = {ei.expense_item_id: ei for ei in expense_items}

    for booking_aggregate in booking_aggregates:
        hotel_agg = grouped_hotel_aggregate.get(booking_aggregate.hotel_id)
        crs_context.set_hotel_context(hotel_agg)
        bill_aggregate: BillAggregate = grouped_bill_aggregates[
            booking_aggregate.bill_id
        ]
        addon_aggregates = booking_wise_addons[booking_aggregate.booking_id]
        room_stay_wise_addons = group_list(addon_aggregates, 'room_stay_id')

        for room_stay in booking_aggregate.room_stays:
            if room_stay.room_stay_id not in room_stay_wise_addons:
                continue
            addons = room_stay_wise_addons[room_stay.room_stay_id]

            date_wise_addons = defaultdict(list)

            # Group all addons date wise
            for addon_aggregate in addons:
                for d in dateutils.date_range(
                    addon_aggregate.addon.start_date,
                    addon_aggregate.addon.end_date,
                    end_inclusive=True,
                ):
                    date_wise_addons[d].append(addon_aggregate.addon)

            for d, addons in date_wise_addons.items():
                # For each date, get the charge, and update it's charge components, based on all linked addons on
                # this date
                charge_id = room_stay.get_charge_for_date(d)
                charge = bill_aggregate.get_charge(charge_id)

                # We need to update pretax_price in charge component, and also the quantity (which was not there
                # earlier)

                # Get the expense_item wise quantity first, applied on this date.
                expense_item_wise_qty = get_expense_item_wise_quantity(addons)

                for expense_item_id, qty in expense_item_wise_qty.items():
                    expense_item = grouped_expense_items[expense_item_id]

                    # Get existing charge component for this expense item
                    charge_component = charge.get_charge_component(
                        name=expense_item.charge_component_name
                    )
                    if not charge_component:
                        continue

                    # Update quantity and pretax_amount (using posttax_amount ratio)
                    # NOTE (Rohit): Modifying in-place to simplify this method, since it's only a migration command
                    # to be used once. Not to be used as ideal code example
                    charge_component.quantity = qty
                    if not charge.posttax_amount:
                        charge_component.pretax_amount = Money(
                            '0', bill_aggregate.bill.base_currency
                        )
                    else:
                        charge_component.pretax_amount = (
                            charge_component.posttax_amount
                            / charge.posttax_amount.amount
                            * charge.pretax_amount.amount
                        )

                # Ensure that sum of all charge components pretax_amount is equal to charge pretax amount
                try:
                    assert (
                        sum(
                            component.pretax_amount
                            for component in charge.charge_components
                        )
                        == charge.pretax_amount
                    )
                except:
                    logger.info(
                        "Assertion failed for bill: %s, charge: %s",
                        bill_aggregate.bill_id,
                        charge.charge_id,
                    )
                # Since we're not using charge method to update. So directly marking dirty here.
                # Again, it's ok to do these in one-time use migration command
                charge.mark_dirty()

    bill_repo.update_all(bill_aggregates)


def get_expense_item_wise_quantity(addons):
    expense_item_wise_total_qty = dict()
    for expense_item_id, addons in itertools.groupby(
        sorted(addons, key=lambda a: a.expense_item_id), key=lambda a: a.expense_item_id
    ):
        total_quantity = sum(addon.quantity for addon in addons)
        expense_item_wise_total_qty[expense_item_id] = total_quantity
    return expense_item_wise_total_qty
