import logging

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context
from treebo_commons.utils import dateutils

from object_registry import inject
from prometheus.application.decorators import session_manager
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.dtos import BookingSearchQuery
from prometheus.domain.booking.repositories import BookingRepository
from ths_common.utils.collectionutils import chunks

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    '--booking_ids',
    help="comma separated booking_ids to which folios are to be attached",
    default=None,
)
@click.option(
    '--start_date',
    help="start date of bookings that we need to attach folios ",
    default=None,
)
@click.option(
    '--end_date',
    help="end date of bookings that we need to attach folios",
    default=None,
)
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@click.option(
    '--batch_size',
    help="Batch Size - Number of records That should be processed & persisted at a given point of time. Default - 500",
    default=500,
)
@with_appcontext
@inject(
    booking_repo=BookingRepository,
    bill_repo=BillRepository,
)
@with_appcontext
def update_payor_entity_in_payment(
    booking_repo,
    bill_repo,
    booking_ids,
    start_date,
    end_date,
    tenant_id,
    batch_size,
):
    request_context.tenant_id = tenant_id
    logger.info('Started create_and_attach_folio.. ')
    if booking_ids and (start_date or end_date):
        raise Exception(
            'Please provide either the booking_ids or start_date & end_date.'
        )

    if not (start_date and end_date) and not booking_ids:
        raise Exception('Please provide both start_date & end_date.')
    if booking_ids:
        logger.info('Booking IDs were provided. Proceeding with provided bookings.')
        booking_aggregates = booking_repo.load_all(booking_ids.split(','))
    else:
        logger.info('Start & End Date were provided. Proceeding with Start & End Date.')
        booking_aggregates = booking_repo.search(
            BookingSearchQuery(
                checkin_start=dateutils.ymd_str_to_date(start_date),
                checkin_end=dateutils.ymd_str_to_date(end_date),
                limit=1000000,
            )
        )

    count = 0
    for booking_aggregates_batch in chunks(booking_aggregates, batch_size):
        bill_wise_booking_aggregate_map = dict()
        for booking_aggregate in booking_aggregates_batch:
            bill_wise_booking_aggregate_map[
                booking_aggregate.bill_id
            ] = booking_aggregate
        _update_payor_entity_in_payment(bill_repo, bill_wise_booking_aggregate_map)
        count += batch_size

    logger.info('Completed create_and_attach_folio')


@session_manager(commit=True)
def _update_payor_entity_in_payment(bill_repo, booking_aggregates_by_bill_map):
    bill_aggregates = bill_repo.load_all_for_update(
        booking_aggregates_by_bill_map.keys()
    )
    for bill_aggregate in bill_aggregates:
        logger.info(
            f"Processing for Booking ID: {bill_aggregate.parent_reference_number}"
        )
        for payment in bill_aggregate.payments:
            payment.payor_billed_entity_id = payment.payment_splits[
                0
            ].billed_entity_account.billed_entity_id

    bill_repo.update_all(bill_aggregates)
