import logging

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from object_registry import inject
from prometheus.application.decorators import session_manager
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.billing.repositories.credit_shell_repository import (
    CreditShellRepository,
)

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@inject(
    bill_repo=BillRepository,
    credit_shell_repo=CreditShellRepository,
)
@session_manager(commit=True)
def populate_paid_by_paid_to_in_credit_shell(
    bill_repo,
    credit_shell_repo,
    tenant_id,
):
    request_context.tenant_id = tenant_id
    credit_shell_ids = credit_shell_repo.load_all_for_paid_by_paid_to_migration()
    click.echo(f"Total Credit Shell loaded:  {len(credit_shell_ids)}")
    for credit_shell_id in credit_shell_ids:
        click.echo(f"Updating For Credit Shell: {credit_shell_id}")
        credit_shell_aggregate = credit_shell_repo.load_for_update(credit_shell_id)
        bill_aggregate = bill_repo.load(
            bill_id=credit_shell_aggregate.credit_shell.bill_id
        )
        paid_by, paid_to = bill_aggregate.get_paid_by_and_paid_to_for_credit_shell(
            credit_shell_id
        )
        credit_shell_aggregate.update_paid_by_and_paid_to(
            paid_by=paid_by, paid_to=paid_to
        )
        credit_shell_repo.update(credit_shell_aggregate)
