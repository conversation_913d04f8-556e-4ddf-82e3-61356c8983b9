# coding=utf-8
import csv
import logging

import click
import requests
from flask.cli import with_appcontext

from ths_common.constants.user_constants import UserType

logger = logging.getLogger(__name__)


@click.command()
@with_appcontext
def regenerate_invoices():
    booking_ids = set()
    with open('booking_ids.csv', 'r') as csvfile:
        file_data = csv.reader(csvfile, delimiter=',')
        for row_data in file_data:
            # set parameters
            booking_id = row_data[0]
            version = row_data[1]
            booking_ids.add((booking_id, version))

    failed_booking_ids = set()
    success_booking_ids = set()
    for booking_id, version in booking_ids:
        try:
            payload = dict(
                data=dict(
                    action_type="invoice_regeneration",
                    payload=dict(
                        invoice_regeneration=dict(generate_hotel_invoice=False)
                    ),
                ),
                resource_version=int(version),
            )
            response = requests.post(
                url="http://crs.treebo.com/v1/bookings/{0}/actions".format(booking_id),
                json=payload,
                headers={"X-User-Type": UserType.SUPER_ADMIN.value},
            )
            if response.status_code == 200:
                success_booking_ids.add(booking_id)
                continue
            else:
                failed_booking_ids.add(booking_id)
                logger.error(
                    "Non Success status code: %s for booking_id: %s. Content: %s",
                    response.status_code,
                    booking_id,
                    response.json(),
                )
        except:
            failed_booking_ids.add(booking_id)
            logger.exception(
                "Exception occurred while regenerating the invoice for booking_id: %s",
                booking_id,
            )

    logger.info(
        "Failed booking ids: %s. Count: %s", failed_booking_ids, len(failed_booking_ids)
    )
    logger.info(
        "Success booking ids: %s. Count: %s",
        success_booking_ids,
        len(success_booking_ids),
    )
