import logging

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from object_registry import inject
from prometheus.application.decorators import session_manager
from prometheus.elastic_search.constants import ESDataSyncProcesses
from prometheus.elastic_search.es_data_sync_service import ElasticSearchDataSyncService

logger = logging.getLogger(__name__)


@click.command('sync_data_to_elastic_search')
@click.option(
    '--date',
    help="Datetime in iso format from which the sync will happens",
    default=None,
)
@click.option(
    '--resource_ids',
    help="comma separated resource_ids of records to sync",
    default=None,
)
@click.option(
    '--process_name',
    help="Supported sync jobs",
    default=ESDataSyncProcesses.BOOKING_DATA_SYNC.value,
)
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@session_manager(commit=False)
@inject(elastic_search_data_sync_service=ElasticSearchDataSyncService)
def sync_data_to_elastic_search(
    elastic_search_data_sync_service: ElasticSearchDataSyncService,
    date,
    resource_ids,
    process_name,
    tenant_id,
):
    """Cli command to manually trigger data sync to elastic search"""
    click.echo("Tenant ID: %s" % tenant_id)
    request_context.tenant_id = tenant_id
    if resource_ids:
        resource_ids = resource_ids.split(',')
    elastic_search_data_sync_service.sync_data_to_elastic_search(
        process_name, date, resource_ids=resource_ids
    )
    return True
