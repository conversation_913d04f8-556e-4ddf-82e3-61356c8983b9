import logging

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from object_registry import inject
from prometheus import crs_context
from prometheus.application.decorators import session_manager
from prometheus.application.helpers.event_payload_generator import EventPayloadGenerator
from prometheus.application.services.integration_event_application_service import (
    write_event,
)
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.services import ChargeEditService
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.repositories import HotelRepository
from ths_common.constants.integration_event_constants import IntegrationEventType

logger = logging.getLogger(__name__)


@click.command()
@click.option('--booking_id', help="booking_id of the charges")
@click.option(
    '--charge_ids',
    help="comma seperated charge-ids for which the tax is to be recomputed",
)
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@session_manager(commit=True)
@inject(
    charge_edit_service=ChargeEditService,
    booking_repo=BookingRepository,
    bill_repo=BillRepository,
    hotel_repo=HotelRepository,
)
def recompute_tax_for_charge(
    charge_edit_service,
    booking_repo,
    bill_repo,
    hotel_repo,
    booking_id,
    charge_ids,
    tenant_id,
):
    request_context.tenant_id = tenant_id
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load_for_update(booking_aggregate.bill_id)
    hotel_aggregate = hotel_repo.load(booking_aggregate.booking.hotel_id)
    crs_context.set_hotel_context(hotel_aggregate)
    charge_edit_service.recompute_tax_on_all_charges(
        bill_aggregate,
        gst_details=booking_aggregate.booking_owner_gst_details(),
        change_posttax=True,
        seller_model=booking_aggregate.booking.seller_model,
        charge_ids=[int(charge_id) for charge_id in charge_ids.split(',')],
    )
    bill_repo.update(bill_aggregate)

    event_dto = EventPayloadGenerator.generate_event_dto(
        event_type=IntegrationEventType.BILL_UPDATED, bill_aggregate=bill_aggregate
    )
    write_event(event_dto, ignore_context=True)
