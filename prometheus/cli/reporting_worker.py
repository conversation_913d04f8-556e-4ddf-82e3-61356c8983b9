"""
Flask command to start Easy Job Lite Workers
"""

import logging

import click
import newrelic
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from object_registry import inject
from prometheus.core.globals import consumer_context
from prometheus.reporting.accounts_receivable_reporting.ar_reporting_service import (
    ARReportingService,
)
from prometheus.reporting.finance_erp_reporting.finance_reporting_service import (
    FinanceReportingService,
)
from prometheus.reporting.reporting_job_consumer import ReportingJobConsumer
from prometheus.reporting.reporting_service import ReportingApplicationService

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@newrelic.agent.background_task()
@inject(
    reporting_app_service=ReportingApplicationService,
    finance_reporting_service=FinanceReportingService,
    ar_reporting_service=ARReportingService,
)
def reporting_worker(
    reporting_app_service, finance_reporting_service, ar_reporting_service, tenant_id
):
    """Start the reporting job consumers"""
    click.echo("Tenant ID: %s" % tenant_id)
    consumer_context.tenant_id = tenant_id
    request_context.tenant_id = tenant_id

    consumer = ReportingJobConsumer(
        reporting_app_service,
        finance_reporting_service,
        ar_reporting_service,
        tenant_id,
    )
    consumer.start_consumer()
