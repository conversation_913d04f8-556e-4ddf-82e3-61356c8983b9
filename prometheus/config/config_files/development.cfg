import logging

DEBUG = True

# Database
DB_PASSWORD = "treebo"
DB_USER = "treebo"
DB_MASTER_URL = "postgres"
DB_SLAVE_URL = "postgres"
DB_PORT = "5432"
DB = "prometheus"

SQLALCHEMY_TRACK_MODIFICATIONS = True
SQLALCHEMY_DATABASE_URI = 'postgresql://%s:%s@%s:%s/%s' % (DB_USER, DB_PASSWORD, DB_MASTER_URL,
                                                           DB_PORT, DB)
SQLALCHEMY_BINDS = {
    'slave': 'postgresql://%s:%s@%s:%s/%s' % (DB_USER, DB_PASSWORD, DB_SLAVE_URL, DB_PORT, DB)
}

# Logging
LOG_LEVEL = logging.DEBUG
LOG_ROOT = '/var/log/'

# Rabbit MQ
RABBITMQ_HOST = 'amqp://guest:guest@rabbitmq:5672/'
