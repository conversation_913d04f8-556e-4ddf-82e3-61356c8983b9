import logging

DEBUG = False

# Database
DB_PASSWORD = "crs_paSSword"
DB_USER = "crs_user"
DB_MASTER_URL = "crs-rds-reader.treebo.com"
DB_PORT = "5432"
DB = "crs_db"
SQLALCHEMY_TRACK_MODIFICATIONS = False
SQLALCHEMY_POOL_SIZE = 20
SQLALCHEMY_DATABASE_URI = 'postgresql://%s:%s@%s:%s/%s?application_name=crs_app' % (DB_USER, DB_PASSWORD, DB_MASTER_URL, DB_PORT, DB)

# Logging
LOG_LEVEL = logging.INFO
LOG_ROOT = '/var/log/prometheus/'

# RabbitMQ
RABBITMQ_HOST = 'amqp://crs:<EMAIL>:5672/crs'

TEMPLATE_SERVICE_ENDPOINT_URL = "https://vortex.treebo.com"
TAX_SERVICE_ENDPOINT_URL = "http://tax.treebo.com"
CATALOG_SERVICE_ENDPOINT_URL = "https://catalog.treebo.com"
NOTIFICATION_SERVICE_ENDPOINT_URL = "http://notification.treebo.com"
REALISATION_SERVICE_ENDPOINT_URL = "http://growth.treebohotels.com"
RESELLER_SERVICE_ENDPOINT_URL = 'https://reseller.treebo.com'
FINANCE_ERROR_REPORT_RECEIVER_LIST = ['<EMAIL>']
FINANCE_CRON_ALERTS_SLACK_WEBHOOK_URL = '*******************************************************************************'
CRS_AR_DATA_PUSH_ALERTS_SLACK_WEBHOOK_URL = '*******************************************************************************'
ATHENA_SERVICE_ENDPOINT_URL = 'https://corporates.treebo.com'
PAYMENT_SERVICE_ENDPOINT_URL = 'https://payments.treebo.com'
UNIRATE_SERVICE_ENDPOINT_URL = "http://cm.treebo.com"
MARVIN_SERVICE_ENDPOINT_URL = "https://marvin.treebo.com"
ACCOUNT_RECEIVABLE_SERVICE_ENDPOINT_URL = "http://ar.treebo.com"
INTERNAL_PAYMENT_SERVICE_URL = 'https://payments-navision.treebo.com'

AWS_REGION = 'ap-south-1'
AWS_ACCESS_KEY_ID = '********************'
AWS_SECRET_ACCESS_KEY = '3+DhXLfkKq9cbFEzTeKlo4vFohHbqJj71y6RLT2k'
AWS_S3_BUCKET_NAME = 'crs-p-mum'
TEMPORARY_REPORTS_DIRECTORY = '/tmp/reports/'
DUMMY_HOTEL_IDS = '9907195,1438960,1502702'

B2B_SOFT_BOOKING_EXPIRY_EMAIL_RECEIVER_LIST = ['<EMAIL>', '<EMAIL>']
