import logging
import os

import flask
from treebo_commons.request_tracing.flask import after_request, before_request

from prometheus.common.request_parsers import set_user_data_from_request
from prometheus.middlewares.common_middlewares import (
    after_request as custom_after_request,
)
from prometheus.middlewares.common_middlewares import (
    before_request as custom_before_request,
)
from prometheus.middlewares.common_middlewares import (
    cleanup_domain_bus,
    setup_domain_bus,
)


class DefaultConfig(object):
    """
    Base config
    """

    DEBUG = False
    TESTING = False

    # Database
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SECRET_KEY = 'q_\xdd\x1c\xbd\x15\xeb\xdb\x8dD5\xc8\xfcR\x84\xd8?\xc5\x03rC=\x12\x98'
    DB_PASSWORD = os.environ.get("DB_PASSWORD", "")
    DB_USER = os.environ.get("DB_USER", "postgres")
    DB_MASTER_URL = os.environ.get("DB_HOST", "localhost")
    DB_SLAVE_URL = os.environ.get("DB_HOST", "localhost")
    DB_PORT = os.environ.get("DB_PORT", "5432")
    DB = os.environ.get("DB_NAME", "crs_db")
    SQLALCHEMY_DATABASE_URI = 'postgresql://%s:%s@%s:%s/%s?application_name=crs' % (
        DB_USER,
        DB_PASSWORD,
        DB_MASTER_URL,
        DB_PORT,
        DB,
    )

    # Rabbit MQ
    RABBITMQ_HOST = os.environ.get('RABBITMQ_HOST', '127.0.0.1')
    URL_PREFIX = None

    # Logging
    LOG_FORMAT = '%(asctime)s:%(name)s:%(levelname)s:%(message)s'
    LOG_PATH = 'prometheus.log'
    LOG_LEVEL = logging.DEBUG
    LOG_ROOT = os.environ.get("LOG_ROOT", ".")

    # Middlewares
    WSGI_MIDDLEWARES = []
    BEFORE_REQUEST_MIDDLEWARES = [
        lambda: before_request(flask.request),
        lambda: set_user_data_from_request(),
        lambda: custom_before_request(flask.request.headers),
        setup_domain_bus,
    ]
    AFTER_REQUEST_MIDDLEWARES = [
        lambda resp: after_request(resp, flask.request),
        custom_after_request,
        cleanup_domain_bus,
    ]

    # URL Paths
    BB_URL = "http://9ab94ac6.ngrok.io/"
    PUSH_DATA_TO_HX = False
    JSONIFY_PRETTYPRINT_REGULAR = False

    EASYJOBLITE_CONFIG_PATH = "config/easyjoblite.yaml"

    # Tasks
    BB_BOOKING_TASK = 'push_data_from_bb2crs'
    CRS_BOOKING_TASK = 'push_data_from_crs2bb'
    # Queues
    # Exchanges
    BB_BOOKING_QUEUE = 'dev_push_data_from_bb2crs_queue'
    CRS_BOOKING_QUEUE = 'dev_push_data_from_crs2bb_queue'

    # Segment
    SEGMENT_EVENTS_KEY = 'YM3lD59ZFsZ8r5ZkYQZPgDn4fWl8OMtB'

    # Cleartax
    CLEARTAX_ENDPOINT_URL = "https://einvoicing.internal.cleartax.co"  # For testing

    # Slack Corpbot
    SLACK_URL = 'https://slack.com'
    ESCALATION_URL = (
        'https://utobvillvk.execute-api.ap-southeast-1.amazonaws.com/slack_alert_b2b'
    )
    B2B_ALERTS_CHANNEL_ID = 'C0577N11PDF'
