# coding=utf-8
"""
Reporting
"""

from marshmallow import (
    Schema,
    ValidationError,
    fields,
    post_load,
    pre_load,
    validates_schema,
)
from marshmallow.validate import OneOf

from prometheus.core.api_docs import swag_schema
from prometheus.reporting.accounts_receivable_reporting.constants import ARReports
from prometheus.reporting.finance_erp_reporting.constants import FinanceReports
from ths_common.constants.reporting_constants import (
    FlashManagerReportDatePeriods,
    FlashManagerReportFileType,
    MarvinReportTypes,
)


@swag_schema
class GenerateMarvinReportSchema(Schema):
    hotel_id = fields.String(required=False)
    email = fields.String(required=True)
    report_type = fields.String(
        validate=[
            OneOf(
                MarvinReportTypes.all(),
                error="'{input}' is not a valid choice for action type",
            )
        ],
        required=True,
        description='Should be either of "checkin" or "checkout"',
    )
    start_date = fields.Date(
        required=True,
        error_messages={
            'null': 'From date may not be null.',
            'required': 'Please provide from date.',
            'validator_failed': "'{input}' is not a valid value for From date.",
        },
    )
    end_date = fields.Date(
        required=True,
        error_messages={
            'null': 'To date may not be null.',
            'required': 'Please provide to date.',
            'validator_failed': "'{input}' is not a valid value for To date.",
        },
    )


@swag_schema
class GenerateInvoiceReportSchema(Schema):
    hotel_id = fields.String(required=False)
    email = fields.String(required=True)
    start_date = fields.Date(
        required=True,
        error_messages={
            'null': 'From date may not be null.',
            'required': 'Please provide from date.',
            'validator_failed': "'{input}' is not a valid value for From date.",
        },
    )
    end_date = fields.Date(
        required=True,
        error_messages={
            'null': 'To date may not be null.',
            'required': 'Please provide to date.',
            'validator_failed': "'{input}' is not a valid value for To date.",
        },
    )
    corporate_id = fields.String(required=False)
    report_type = fields.String(required=True)


@swag_schema
class GenerateInvoiceReportSyncSchema(GenerateInvoiceReportSchema):
    report_type = fields.String(required=False)


@swag_schema
class GenerateSmeReportSchema(Schema):
    start_date = fields.Date(
        required=True,
        error_messages={
            'null': 'From date may not be null.',
            'required': 'Please provide from date.',
            'validator_failed': "'{input}' is not a valid value for From date.",
        },
    )
    end_date = fields.Date(
        required=True,
        error_messages={
            'null': 'To date may not be null.',
            'required': 'Please provide to date.',
            'validator_failed': "'{input}' is not a valid value for To date.",
        },
    )


@swag_schema
class ArrivalReportsSchema(Schema):
    start_date = fields.Date(
        required=True,
        error_messages={
            'null': 'From date may not be null.',
            'required': 'Please provide from date.',
            'validator_failed': "'{input}' is not a valid value for From date.",
        },
    )
    end_date = fields.Date(
        required=True,
        error_messages={
            'null': 'To date may not be null.',
            'required': 'Please provide to date.',
            'validator_failed': "'{input}' is not a valid value for To date.",
        },
    )


@swag_schema
class DepartureReportsSchema(Schema):
    start_date = fields.Date(
        required=True,
        error_messages={
            'null': 'From date may not be null.',
            'required': 'Please provide from date.',
            'validator_failed': "'{input}' is not a valid value for From date.",
        },
    )
    end_date = fields.Date(
        required=True,
        error_messages={
            'null': 'To date may not be null.',
            'required': 'Please provide to date.',
            'validator_failed': "'{input}' is not a valid value for To date.",
        },
    )


@swag_schema
class CashierReportsSchema(Schema):
    start_date = fields.Date(
        required=True,
        error_messages={
            'null': 'From date may not be null.',
            'required': 'Please provide from date.',
            'validator_failed': "'{input}' is not a valid value for From date.",
        },
    )
    end_date = fields.Date(
        required=True,
        error_messages={
            'null': 'To date may not be null.',
            'required': 'Please provide to date.',
            'validator_failed': "'{input}' is not a valid value for To date.",
        },
    )


@swag_schema
class GuestInHouseReportsSchema(Schema):
    start_date = fields.Date(
        required=True,
        error_messages={
            'null': 'From date may not be null.',
            'required': 'Please provide from date.',
            'validator_failed': "'{input}' is not a valid value for From date.",
        },
    )
    end_date = fields.Date(
        required=True,
        error_messages={
            'null': 'To date may not be null.',
            'required': 'Please provide to date.',
            'validator_failed': "'{input}' is not a valid value for To date.",
        },
    )


@swag_schema
class MisReportsSchema(Schema):
    start_date = fields.Date(
        required=True,
        error_messages={
            'null': 'From date may not be null.',
            'required': 'Please provide from date.',
            'validator_failed': "'{input}' is not a valid value for From date.",
        },
    )
    end_date = fields.Date(
        required=True,
        error_messages={
            'null': 'To date may not be null.',
            'required': 'Please provide to date.',
            'validator_failed': "'{input}' is not a valid value for To date.",
        },
    )
    room_type_id = fields.String(required=False)


@swag_schema
class CashierCommonReportSchema(Schema):
    start_datetime = fields.DateTime(
        required=True,
        error_messages={
            'null': 'start_date may not be null.',
            'required': 'Please provide from datetime.',
            'validator_failed': "'{input}' is not a valid value for start_date.",
        },
        timezone=True,
    )
    end_datetime = fields.DateTime(
        required=True,
        error_messages={
            'null': 'end_date may not be null.',
            'required': 'Please provide to datetime.',
            'validator_failed': "'{input}' is not a valid value for end_date.",
        },
        timezone=True,
    )
    cash_register_ids = fields.List(fields.String(), required=True)

    @pre_load
    def convert_comma_separated_string_to_list(self, data):
        if data.get('cash_register_ids'):
            data['cash_register_ids'] = [
                cash_register_id.strip()
                for cash_register_id in data.get('cash_register_ids').split(',')
            ]
        return data


@swag_schema
class CashierSessionReportSchema(Schema):
    cashier_session_ids = fields.List(fields.String(), required=True)

    @pre_load
    def convert_comma_separated_string_to_list(self, data):
        if data.get('cashier_session_ids'):
            data['cashier_session_ids'] = [
                cashier_session_id.strip()
                for cashier_session_id in data.get('cashier_session_ids').split(',')
            ]
        return data


@swag_schema
class CashierUserReportSchema(Schema):
    start_datetime = fields.DateTime(
        required=True,
        error_messages={
            'null': 'date may not be null.',
            'required': 'Please provide from datetime.',
            'validator_failed': "'{input}' is not a valid value for start_date.",
        },
        timezone=True,
    )
    cash_register_ids = fields.List(fields.String(), required=True)
    user = fields.String(required=True)

    @pre_load
    def convert_comma_separated_string_to_list(self, data):
        if data.get('cash_register_ids'):
            data['cash_register_ids'] = [
                cash_register_id.strip()
                for cash_register_id in data.get('cash_register_ids').split(',')
            ]
        return data


@swag_schema
class CashierUserReportUserDetailsSchema(Schema):
    start_datetime = fields.DateTime(
        required=True,
        error_messages={
            'null': 'date may not be null.',
            'required': 'Please provide from datetime.',
            'validator_failed': "'{input}' is not a valid value for start_date.",
        },
        timezone=True,
    )
    cash_register_ids = fields.List(fields.String(), required=True)
    user = fields.String()

    @pre_load
    def convert_comma_separated_string_to_list(self, data):
        if data.get('cash_register_ids'):
            data['cash_register_ids'] = [
                cash_register_id.strip()
                for cash_register_id in data.get('cash_register_ids').split(',')
            ]
        return data


@swag_schema
class PushFinanceReportsSchema(Schema):
    date = fields.String(
        required=False,
        error_messages={
            'null': 'date may not be null.',
            'required': 'Please provide date.',
            'validator_failed': "'{input}' is not a valid value for date.",
        },
    )
    report_names = fields.String(
        required=False,
        description='Comma separated report names from {0}'.format(
            FinanceReports.all()
        ),
    )
    month = fields.String(required=False)
    year = fields.String(required=False)
    named_arg = fields.String(required=False)
    execute_in_sync = fields.Boolean(default=False)

    @validates_schema
    def validate_data(self, data):
        if data.get('report_names'):
            if not set(data['report_names'].split(',')).issubset(FinanceReports.all()):
                raise ValidationError('Not a valid report name')
            if FinanceReports.SETTLEMENT_REPORT.value in data['report_names'].split(
                ','
            ) and not (data.get('month') and data.get('year')):
                raise ValidationError(
                    "On settlement report push please provide both month and year"
                )

        if not data.get('date') and not (
            data.get('report_names')
            and data.get('report_names') == FinanceReports.SETTLEMENT_REPORT.value
        ):
            raise ValidationError(
                "Date should be provided for pushing reports other than settlement report"
            )

    @post_load
    def convert_string_to_list(self, data):
        if data.get('report_names'):
            data['report_names'] = data['report_names'].split(',')


@swag_schema
class TrailBalanceReportsSchema(Schema):
    start_date = fields.Date(
        required=True,
        error_messages={
            'null': 'From date may not be null.',
            'required': 'Please provide from date.',
            'validator_failed': "'{input}' is not a valid value for From date.",
        },
    )
    end_date = fields.Date(
        required=True,
        error_messages={
            'null': 'To date may not be null.',
            'required': 'Please provide to date.',
            'validator_failed': "'{input}' is not a valid value for To date.",
        },
    )

    @validates_schema
    def validate_data(self, data):
        if data['end_date'] < data['start_date']:
            raise ValidationError(
                "End Date should be greater than start date",
                field_names=['start_date', 'end_date'],
            )


@swag_schema
class TrailBalanceReportsSchemaV2(Schema):
    business_date = fields.Date(
        required=True,
        error_messages={
            'null': 'date may not be null.',
            'required': 'Please provide from date.',
            'validator_failed': "'{input}' is not a valid value for From date.",
        },
    )


class FlashManagerReportsSchema(Schema):
    report_date = fields.Date(
        required=True,
        error_messages={
            'null': 'date may not be null.',
            'required': 'Please provide from date.',
            'validator_failed': "'{input}' is not a valid value for From date.",
        },
    )
    date_periods = fields.List(
        fields.String(
            validate=[
                OneOf(
                    FlashManagerReportDatePeriods.all(),
                    error="'{input}' is not a valid choice for report date period",
                )
            ],
            required=True,
        )
    )
    tax_inclusive = fields.Boolean()

    @pre_load
    def convert_comma_separated_string_to_list(self, data):
        if data.get('date_periods'):
            data['date_periods'] = [
                date_period.strip()
                for date_period in data.get('date_periods').split(',')
            ]
        return data


@swag_schema
class FlashManagerReportsDownloadSchema(FlashManagerReportsSchema):
    file_type = fields.String(
        validate=[
            OneOf(
                FlashManagerReportFileType.all(),
                error=f"'{input}' is not a valid choice for report download file type",
            )
        ],
        required=True,
    )


@swag_schema
class PushARPaymentsReportSchema(Schema):
    date = fields.String(
        required=True,
        error_messages={
            'null': 'date may not be null.',
            'required': 'Please provide from date.',
            'validator_failed': "'{input}' is not a valid value for From date.",
        },
    )
    report_names = fields.String(
        required=False,
        description='Comma separated report names from {0}'.format(ARReports.all()),
    )
    execute_in_sync = fields.Boolean(default=False)

    @validates_schema
    def validate_data(self, data):
        if data.get('report_names'):
            if not set(data['report_names'].split(',')).issubset(ARReports.all()):
                raise ValidationError('Not a valid report name')

    @post_load
    def convert_string_to_list(self, data):
        if data.get('report_names'):
            data['report_names'] = data['report_names'].split(',')


class FinancialDataReportSyncSchema(Schema):
    report_date = fields.Date(
        required=False,
        error_messages={
            'null': 'Date may not be null.',
            'required': 'Please provide a valid report date.',
            'validator_failed': "'{input}' is not a valid value for the report date.",
        },
    )
    hotel_id = fields.String(
        required=False,
        error_messages={
            'null': 'Hotel ID may not be null.',
            'required': 'Please provide a valid hotel ID.',
        },
    )
    execute_in_sync = fields.Boolean(
        required=False,
        default=False,
    )


class ResourceDataSchema(Schema):
    resource_id = fields.String(required=True)
    resource_unique_id = fields.String(required=True)


class FetchFinanceReportRequestSchema(Schema):
    report_name = fields.String(required=True, validate=OneOf(FinanceReports.all()))
    resource_data = fields.Nested(ResourceDataSchema, many=True)
