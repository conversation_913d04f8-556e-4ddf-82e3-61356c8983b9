from marshmallow import Schema, ValidationError, fields
from marshmallow.decorators import post_load
from marshmallow.validate import OneOf
from treebo_commons.money.money_field import MoneyField

from prometheus.core.api_docs import swag_schema
from shared_kernel.serializers.validators import validate_empty_string
from ths_common.constants.billing_constants import ChargeBillToTypes, ChargeTypes
from ths_common.constants.booking_constants import (
    AddonRelativeDate,
    AddonStatus,
    ExpenseAddedBy,
)


@swag_schema
class AddonBaseSchema(Schema):
    room_stay_id = fields.Integer(
        required=True,
        description='Unique Identifier of the room to which the charges for the expense to '
        'be associated',
        error_messages={
            'null': 'Room stay id may not be null.',
            'required': 'Room stay id data missing.',
            'validator_failed': 'Invalid value for Room stay id.',
        },
    )
    name = fields.String(
        required=True,
        description='Name as expected on the Invoice',
        validate=validate_empty_string,
        error_messages={
            'null': 'name may not be null.',
            'required': 'name data missing.',
            'validator_failed': 'Invalid value for name.',
        },
    )
    expense_item_id = fields.String(
        required=False,
        description="Expense Item ID as provided in the Expense Item within CRS",
        error_messages={
            'null': 'Charge item data may not be null.',
            'required': 'Charge item data missing.',
            'validator_failed': 'Invalid Charge item.',
        },
    )
    sku_id = fields.String(required=False)
    pretax_price = MoneyField(
        allow_none=True,
        description='Price per addon exclusive of tax',
        missing=None,
        as_string=True,
    )
    posttax_price = MoneyField(
        allow_none=True,
        description='Price per addon inclusive of tax',
        as_string=True,
        missing=None,
    )
    quantity = fields.Integer(
        required=True,
        description='Number of expenses to be created per room',
        error_messages={
            'null': 'Quantity may not be null.',
            'required': 'Quantity data missing.',
            'validator_failed': 'Invalid value for quantity.',
        },
    )
    added_by = fields.String(
        validate=OneOf(
            ExpenseAddedBy.all(),
            error="'{input}' is not a valid choice for Expense Added By",
        ),
        required=False,
        description='Added by Treebo/Hotel',
    )

    @post_load
    def validate_expense_item_id_and_sku_id(self, data):
        if not data.get('expense_item_id') and not data.get('sku_id'):
            raise ValidationError(
                "Please provide either expense_item_id or sku_id",
                field_names=['expense_item_id', 'sku_id'],
            )


@swag_schema
class AddonSchema(AddonBaseSchema):
    charge_checkin = fields.Boolean(
        required=True,
        description='Create an expense on the Check-in date',
        error_messages={
            'null': 'Should charge check-in may not be null.',
            'required': 'Please provide should charge check-in or not.',
            'validator_failed': 'Charge check-in should be a True/False.',
        },
    )
    charge_checkout = fields.Boolean(
        required=True,
        description='Create an expense on the Check-out date',
        error_messages={
            'null': 'Should charge check-out may not be null.',
            'required': 'Please provide should charge check-out or not.',
            'validator_failed': 'Charge check-out should be a True/False.',
        },
    )
    charge_other_days = fields.Boolean(
        required=True,
        description='Create an expense for each of the other dates',
        error_messages={
            'null': 'Should charge other days may not be null.',
            'required': 'Please provide should charge other days or not.',
            'validator_failed': 'Charge other days should be a True/False.',
        },
    )
    charge_type = fields.String(
        required=True,
        validate=OneOf(
            ChargeTypes.all(), error="'{input}' is not a valid choice for Charge Type"
        ),
        description='Charge type of the addon to be created',
    )
    bill_to_type = fields.String(
        required=True,
        validate=OneOf(
            ChargeBillToTypes.all(),
            error="'{input}' is not a valid choice for " "Bill-To Type",
        ),
        description='Bill to type of the addon to be created',
    )

    @post_load
    def convert_enums(self, data):
        if data.get('added_by'):
            data['added_by'] = ExpenseAddedBy(data['added_by'])

    @post_load
    def set_status(self, data):
        data['status'] = AddonStatus.CREATED
        data['charge_type'] = ChargeTypes(data['charge_type'])
        data['bill_to_type'] = ChargeBillToTypes(data['bill_to_type'])


class AddonSchemaV2(AddonBaseSchema):
    start_relative = fields.String(
        required=False,
        validate=OneOf(AddonRelativeDate.all()),
        allow_none=True,
        error="'{input}' is not a valid choice for Relative Start Date Type",
        description='Should create an addon starting from date relative to checkin',
    )

    end_relative = fields.String(
        required=False,
        validate=OneOf(AddonRelativeDate.all()),
        allow_none=True,
        error="'{input}' is not a valid choice for Relative end Date Type",
        description='Should create an addon till date relative to checkout',
    )

    start_date = fields.Date(
        required=False,
        description='Should create Addon starting from this absolute Date',
        allow_none=True,
    )

    end_date = fields.Date(
        required=False,
        description='Should create Addon till this absolute Date',
        allow_none=True,
    )

    charge_type = fields.String(
        required=False,
        validate=OneOf(
            ChargeTypes.all(), error="'{input}' is not a valid choice for Charge Type"
        ),
        description='Charge type of the addon to be created',
    )
    bill_to_type = fields.String(
        required=False,
        validate=OneOf(
            ChargeBillToTypes.all(),
            error="'{input}' is not a valid choice for " "Bill-To Type",
        ),
        description='Bill to type of the addon to be created',
    )
    is_rate_plan_addon = fields.Boolean(
        required=False,
        default=False,
        description='Pass true if addon is added via rate plan',
    )

    @post_load
    def convert_enums(self, data):
        if data.get('added_by'):
            data['added_by'] = ExpenseAddedBy(data['added_by'])
        if data.get('start_relative'):
            data['start_relative'] = AddonRelativeDate(data['start_relative'])
        if data.get('end_relative'):
            data['end_relative'] = AddonRelativeDate(data['end_relative'])

    @post_load
    def set_status(self, data):
        data['status'] = AddonStatus.CREATED
        if data.get('charge_type'):
            data['charge_type'] = ChargeTypes(data['charge_type'])
        if data.get('bill_to_type'):
            data['bill_to_type'] = ChargeBillToTypes(data['bill_to_type'])

    @post_load
    def validate_date_range(self, data):
        if data.get('start_relative') and data.get('start_date'):
            raise ValidationError(
                "Please provide either relative date or the absolute start date, not both",
                field_names=['start_relative', 'start_date'],
            )
        if data.get('end_relative') and data.get('end_date'):
            raise ValidationError(
                "Please provide either relative date or the absolute end date, not both",
                field_names=['end_relative', 'end_date'],
            )
        if not (data.get('start_relative') or data.get('start_date')):
            raise ValidationError(
                "Please provide either relative date or the absolute start date",
                field_names=['start_relative', 'start_date'],
            )
        if not (data.get('end_relative') or data.get('end_date')):
            raise ValidationError(
                "Please provide either relative date or the absolute start date",
                field_names=['end_relative', 'end_date'],
            )

    @post_load
    def validate_price(self, data):
        if data['pretax_price'] and data['posttax_price']:
            raise ValidationError(
                "please provide either pretax or posttax price not both",
                field_names=['pretax_price', 'posttax_price'],
            )


@swag_schema
class AddonsSchemaV2(Schema):
    addons = fields.Nested(AddonSchemaV2, many=True)


@swag_schema
class UpdateAddonSchemaV2(Schema):
    pretax_price = MoneyField(
        allow_none=True,
        description='Price per addon exclusive of tax',
        missing=None,
        as_string=True,
    )
    posttax_price = MoneyField(
        allow_none=True,
        description='Price per addon inclusive of tax',
        as_string=True,
        missing=None,
    )
    quantity = fields.Integer(
        allow_none=True,
        required=False,
        description='Number of expenses to be created per room',
        error_messages={'validator_failed': 'Invalid value for quantity.'},
    )

    start_relative = fields.String(
        required=False,
        validate=OneOf(AddonRelativeDate.all()),
        allow_none=True,
        error="'{input}' is not a valid choice for Relative Start Date Type",
        description='Should create an addon starting from date relative to checkin',
    )

    end_relative = fields.String(
        required=False,
        validate=OneOf(AddonRelativeDate.all()),
        allow_none=True,
        error="'{input}' is not a valid choice for Relative end Date Type",
        description='Should create an addon till date relative to checkout',
    )

    start_date = fields.Date(
        required=False,
        description='Should create Addon starting from this absolute Date',
    )

    end_date = fields.Date(
        required=False, description='Should create Addon till this absolute Date'
    )

    charge_type = fields.String(
        allow_none=True,
        required=False,
        validate=OneOf(
            ChargeTypes.all(), error="'{input}' is not a valid choice for Charge Type"
        ),
    )
    bill_to_type = fields.String(
        allow_none=True,
        required=False,
        validate=OneOf(
            ChargeBillToTypes.all(),
            error="'{input}' is not a valid choice for Bill-To Type",
        ),
    )

    @post_load
    def set_status(self, data):
        if data.get('charge_type'):
            data['charge_type'] = ChargeTypes(data['charge_type'])
        if data.get('bill_to_type'):
            data['bill_to_type'] = ChargeBillToTypes(data['bill_to_type'])
        if data.get('start_relative'):
            data['start_relative'] = AddonRelativeDate(data['start_relative'])
        if data.get('end_relative'):
            data['end_relative'] = AddonRelativeDate(data['end_relative'])

    @post_load
    def validate_price(self, data):
        if data['pretax_price'] and data['posttax_price']:
            raise ValidationError(
                "please provide either pretax or posttax price not both",
                field_names=['pretax_price', 'posttax_price'],
            )

    @post_load
    def validate_date_range(self, data):
        if data.get('start_relative') and data.get('start_date'):
            raise ValidationError(
                "Please provide either relative date or the absolute start date, not both",
                field_names=['start_relative', 'start_date'],
            )
        if data.get('end_relative') and data.get('end_date'):
            raise ValidationError(
                "Please provide either relative date or the absolute end date, not both",
                field_names=['end_relative', 'end_date'],
            )


@swag_schema
class UpdateAddonSchema(Schema):
    pretax_price = MoneyField(
        allow_none=True,
        description='Price per addon exclusive of tax',
        missing=None,
        as_string=True,
    )
    posttax_price = MoneyField(
        allow_none=True,
        description='Price per addon inclusive of tax',
        as_string=True,
        missing=None,
    )
    quantity = fields.Integer(
        allow_none=True,
        required=False,
        description='Number of expenses to be created per room',
        error_messages={'validator_failed': 'Invalid value for quantity.'},
    )
    charge_checkin = fields.Boolean(
        allow_none=True,
        required=False,
        description='Create an expense on the Check-in date',
        error_messages={'validator_failed': 'Charge check-in should be a True/False.'},
    )
    charge_checkout = fields.Boolean(
        allow_none=True,
        required=False,
        description='Create an expense on the Check-out date',
        error_messages={'validator_failed': 'Charge check-out should be a True/False.'},
    )
    charge_other_days = fields.Boolean(
        allow_none=True,
        required=False,
        description='Create an expense for each of the other dates',
        error_messages={
            'validator_failed': 'Charge other days should be a True/False.'
        },
    )
    charge_type = fields.String(
        allow_none=True,
        required=False,
        validate=OneOf(
            ChargeTypes.all(), error="'{input}' is not a valid choice for Charge Type"
        ),
    )
    bill_to_type = fields.String(
        allow_none=True,
        required=False,
        validate=OneOf(
            ChargeBillToTypes.all(),
            error="'{input}' is not a valid choice for Bill-To Type",
        ),
    )

    @post_load
    def set_status(self, data):
        if data.get('charge_type'):
            data['charge_type'] = ChargeTypes(data['charge_type'])
        if data.get('bill_to_type'):
            data['bill_to_type'] = ChargeBillToTypes(data['bill_to_type'])


@swag_schema
class AddonFilterSchema(Schema):
    include_linked = fields.Boolean(required=False, allow_none=True)
