# coding=utf-8
"""
Options marshmallow schema
"""

from marshmallow import Schema, fields
from marshmallow.decorators import pre_load

from prometheus.core.api_docs import swag_schema


@swag_schema
class OptionSchema(Schema):
    keys = fields.List(fields.String(), required=False)

    @pre_load
    def transform(self, data):
        if 'keys' in data:
            data['keys'] = data['keys'].split(',')
        return data
