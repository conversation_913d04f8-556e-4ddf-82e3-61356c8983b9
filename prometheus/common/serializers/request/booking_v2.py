from marshmallow import (
    Schema,
    ValidationError,
    fields,
    post_load,
    validate,
    validates_schema,
)

from prometheus.common.serializers.request.booking import NewBookingSchema
from prometheus.common.serializers.request.room_stay_v2 import (
    ExistingRoomStaySchemaV2,
    NewRoomStaySchemaV2,
)
from prometheus.core.api_docs import swag_schema
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    PaymentInstruction,
)
from ths_common.constants.booking_constants import BookingStatus


@swag_schema
class NewBookingSchemaV2(NewBookingSchema):
    room_stays = fields.Nested(
        NewRoomStaySchemaV2,
        required=True,
        many=True,
        description='Details about the Room Stay',
        validate=validate.Length(
            min=1, error="Booking creation not allowed with 0 room"
        ),
        error_messages={
            'null': 'Room stays may not be null.',
            'required': 'Please provide valid room stays.',
            'validator_failed': "'{input}' is not a valid list of room stays.",
        },
    )

    @post_load
    def transform(self, data):
        if data.get('status'):
            data['status'] = BookingStatus(data['status'])

        if data.get('default_billed_entity_category'):
            data['default_billed_entity_category'] = BilledEntityCategory(
                data['default_billed_entity_category']
            )
        if data.get('default_billed_entity_category_for_extras'):
            data['default_billed_entity_category_for_extras'] = BilledEntityCategory(
                data['default_billed_entity_category_for_extras']
            )
        if data.get('default_payment_instruction'):
            data['default_payment_instruction'] = PaymentInstruction(
                data['default_payment_instruction']
            )
        if data.get('default_payment_instruction_for_extras'):
            data['default_payment_instruction_for_extras'] = PaymentInstruction(
                data['default_payment_instruction_for_extras']
            )

        return data


@swag_schema
class ReplaceBookingSchemaV2(NewBookingSchemaV2):
    room_stays = fields.Nested(
        ExistingRoomStaySchemaV2,
        required=True,
        many=True,
        description='Details about the Room Stay',
        validate=validate.Length(min=1),
        error_messages={
            'null': 'Room stays may not be null.',
            'required': 'Please provide valid room stays.',
            'validator_failed': "'{input}' is not a valid list of room stays.",
        },
    )


@swag_schema
class BookingDetailsV2Schema(Schema):
    show_bill_summary = fields.Boolean(required=False)


@swag_schema
class HouseViewBookingQuery(Schema):
    from_date = fields.Date(
        description='Search bookings where checkout_date is greater than or equal to from_date'
    )
    to_date = fields.Date(
        description='Search bookings where checkin_date is less than or equal to to_date'
    )
    total_count_required = fields.Boolean(
        description="When true return total_count", missing=True
    )
    limit = fields.Integer(description='Number of results required', missing=20)
    offset = fields.Integer(
        description='Offset from which results are required', missing=0
    )
    with_balance_payable = fields.Boolean(
        description="When true return total_count", missing=False
    )
    exclude_customers = fields.Boolean(
        description="When true returns customers array per booking", missing=False
    )
    booking_id = fields.String(
        description="When passed, only this booking will be returned in houseview booking "
        "response"
    )

    @validates_schema(skip_on_field_errors=True)
    def validate_data(self, data):
        """
        :param data:
        :return:
        """
        if not data.get('booking_id'):
            if not (data.get('from_date') and data.get('to_date')):
                raise ValidationError(
                    "from_date and to_date are required to get bookings for houseview"
                )

        if data.get('limit') < 0:
            raise ValidationError('Limit should be a positive integer')
        if data.get('offset') < 0:
            raise ValidationError('Offset should be a positive integer')


@swag_schema
class GetVoucherSchema(Schema):
    pass
