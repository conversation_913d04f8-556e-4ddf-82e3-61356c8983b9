from marshmallow import Schema, fields


class Data<PERSON>eyField(fields.Field):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        data_key = kwargs.get('data_key')
        if data_key is not None:
            if self.load_from is not None or self.dump_to is not None:
                raise RuntimeError(
                    "load_from and dump_to cannot be given when data_key is given"
                )
            self.load_from = data_key
            self.dump_to = data_key


class StringField(fields.String, DataKeyField):
    pass


class IntegerField(fields.Integer, DataKeyField):
    pass


class DecimalField(fields.Decimal, DataKeyField):
    pass


class FloatField(fields.Float, DataKeyField):
    pass


class NestedField(fields.Nested, DataKeyField):
    pass


class HeaderSchema(Schema):  # cleartax: under basic details
    # in_crs: booking.owner.gstin / pwc: repeated in buyer info?
    user_gstin = StringField(data_key='User_GSTIN', required=True)

    tax_schema = StringField(data_key='Taxsch', required=True)  # const: gst
    version = StringField(data_key='Version', required=True)  # const: 1
    irn = StringField(data_key='IRN', required=False)  # should be stored in crs?


class TransactionDetailsSchema(Schema):
    supply_type = StringField(data_key='SupTyp', required=True)
    tax_schema = StringField(data_key='TaxSch', required=True)  # const: gst


class DocumentDetailsSchema(Schema):  # cleartax: under basic details
    document_type = StringField(
        data_key='Typ', required=True
    )  # const: INV/DBN/CRN, in_crs:?
    document_number = StringField(
        data_key='No', required=True
    )  # invoice number, in_crs:inv_number
    document_date = StringField(
        data_key='Dt', required=True
    )  # invoice date, in_crs:inv date


class SupplierInformationSchema(Schema):  # cleartax: under supplier information
    """
    in_crs: vendor details
    """

    gstin = StringField(data_key='Gstin', required=True)  # in_crs: ruptub.gstin
    legal_name = StringField(
        data_key='LglNm', required=True
    )  # in_crs: ruptub.legal_name
    address_field1 = StringField(data_key='Addr1', required=True)
    address_field2 = StringField(data_key='Addr2', required=False)
    location = StringField(data_key='Loc', required=True)  # in_crs: ruptub.city
    pincode = IntegerField(data_key='Pin', required=True)  # in_crs: ruptub.pincode
    state_code = IntegerField(
        data_key='Stcd', required=True
    )  # in_crs: ruptub.legal_state_code
    phone_number = IntegerField(
        data_key='Ph', required=False
    )  # in_crs: ruptub.phone_number
    email_id = StringField(data_key='Em', required=False)  # in_crs: ruptub.email


class BuyerInformationSchema(Schema):
    # in_crs: booking.owner
    gstin = StringField(data_key='Gstin', required=True)  # in_crs: booking.owner.gstin
    legal_name = StringField(data_key='LglNm', required=True)
    place_of_supply = StringField(data_key='Pos', required=True)
    address_field1 = StringField(data_key='Addr1', required=True)
    address_field2 = StringField(data_key='Addr2', required=False)
    location = StringField(data_key='Loc', required=True)
    pincode = IntegerField(data_key='Pin', required=True)
    state_code = IntegerField(data_key='Stcd', required=True)
    # phone_number = IntegerField(data_key='Ph', required=False)
    # email_id = StringField(data_key='Em', required=False)


class DispatchFromSchema(Schema):
    # Not needed
    gstin = StringField(data_key='DispDtls_Gstin', required=True)
    trade_name = StringField(data_key='DispDtls_TrdNm', required=True)
    building_number = StringField(data_key='DispDtls_Bno', required=False)
    building_name = StringField(data_key='DispDtls_Bnm', required=False)
    floor_number = StringField(data_key='DispDtls_Flno', required=False)
    location = StringField(data_key='DispDtls_Loc', required=True)
    district = StringField(data_key='DispDtls_Dst', required=False)
    pincode = IntegerField(data_key='DispDtls_Pin', required=True)
    state_code = IntegerField(data_key='DispDtls_Stcd', required=True)
    phone_number = IntegerField(data_key='DispDtls_Ph', required=False)
    email_id = StringField(data_key='DispDtls_Em', required=False)


class ShipToSchema(Schema):
    gstin = StringField(data_key='ShipDtls_Gstin', required=True)
    trade_name = StringField(data_key='ShipDtls_TrdNm', required=True)
    building_number = StringField(data_key='ShipDtls_Bno', required=False)
    building_name = StringField(data_key='ShipDtls_Bnm', required=False)
    floor_number = StringField(data_key='ShipDtls_Flno', required=False)
    location = StringField(data_key='ShipDtls_Loc', required=True)
    district = StringField(data_key='ShipDtls_Dst', required=False)
    pincode = IntegerField(data_key='ShipDtls_Pin', required=True)
    state_code = IntegerField(data_key='ShipDtls_Stcd', required=True)
    phone_number = IntegerField(data_key='ShipDtls_Ph', required=False)
    email_id = StringField(data_key='ShipDtls_Em', required=False)


class ItemDetailsSchema(Schema):
    # cleartax: itemdetails
    '''
    New fields:
    GstRt
    SINo
    IsServc
    '''
    serial_number = StringField(data_key='SlNo', required=True)
    is_service = StringField(data_key='IsServc', required=True)
    product_name = StringField(data_key='PrdNm', required=True)
    product_description = StringField(data_key='PrdDesc', required=False)
    hsn_code = StringField(data_key='HsnCd', required=True)
    # bar_code = StringField(data_key='Barcde', required=False)
    quantity = FloatField(data_key='Qty', required=True)
    free_quantity = FloatField(data_key='FreeQty', required=False)
    unit_of_measure = StringField(data_key='Unit', required=True)
    unit_price = FloatField(data_key='UnitPrice', required=True)
    total_amount = FloatField(data_key='TotAmt', required=False)
    discount = FloatField(data_key='Discount', required=False)
    other_charge = FloatField(data_key='OthChrg', required=False)
    assessable_amount = FloatField(
        data_key='AssAmt', required=True
    )  # cleartax: optional
    gst = FloatField(data_key='GstRt', required=True)
    cgst = FloatField(data_key='CgstRt', required=False)
    sgst = FloatField(data_key='SgstRt', required=False)
    igst = FloatField(data_key='IgstRt', required=False)
    cess = FloatField(data_key='CesRt', required=False)
    cess_non_advalorem = FloatField(data_key='CesNonAdval', required=True)
    state_cess = FloatField(data_key='StateCesRt', required=True)
    cgst_amount = FloatField(data_key='CgstAmt', required=False)
    sgst_amount = FloatField(data_key='SgstAmt', required=False)
    igst_amount = FloatField(data_key='IgstAmt', required=False)
    cess_amount = FloatField(data_key='CesAmt', required=False)
    state_cess_amount = FloatField(data_key='StateCesAmt', required=False)
    total_item_value = FloatField(data_key='TotItemVal', required=True)
    # batch_name = StringField(data_key='Nm', required=False)
    # expiry_date = StringField(data_key='ExpDt', required=False)
    # warranty_date = StringField(data_key='WrDt', required=False)


class TotalValueDetailsSchema(Schema):
    # cleartax: Document total, Total details -  Only final val (Total invoice amount) and tax total mandatory
    assessable_amount = FloatField(data_key='AssVal', required=True)
    cgst = FloatField(data_key='CgstVal', required=False)
    sgst = FloatField(data_key='SgstVal', required=False)
    igst = FloatField(data_key='IgstVal', required=False)
    cess = FloatField(data_key='CesVal', required=False)
    state_cess = FloatField(data_key='StCesVal', required=False)
    discount = FloatField(data_key='Disc', required=False)
    other_charge = FloatField(data_key='OthChrg', required=False)
    final_invoice_value = FloatField(data_key='TotInvVal', required=True)


class PayeeInformationSchema(Schema):
    payee_name = StringField(data_key='PayDtls_Nam', required=False)
    mode_of_payment = StringField(data_key='PayDtls_Mode', required=False)
    branch_or_ifsc_code = StringField(data_key='PayDtls_FinInsBr', required=False)
    terms_of_payment = StringField(data_key='PayDtls_PayTerm', required=False)
    payment_instructions = StringField(data_key='PayDtls_PayInstr', required=False)
    credit_transfer = StringField(data_key='PayDtls_CrTrn', required=False)
    direct_debit = StringField(data_key='PayDtls_DirDr', required=False)
    credit_days = StringField(data_key='PayDtls_CrDay', required=False)
    balance_amount_to_be_paid = StringField(data_key='PayDtls_BalAmt', required=False)
    payment_due_date = StringField(data_key='PayDtls_PayDueDt', required=False)
    account_details = StringField(data_key='PayDtls_AcctDet', required=False)


class ReferenceDetailsSchema(Schema):
    # cleartax: Under batch details
    invoice_remarks = StringField(data_key='RefDtls_InvRmk', required=False)
    invoice_start_date = StringField(data_key='RefDtls_InvStDt', required=False)
    invoice_end_date = StringField(data_key='RefDtls_InvEndDt', required=False)
    preceding_invoice_number = StringField(data_key='RefDtls_PrecInvNo', required=False)
    preceding_invoice_date = StringField(data_key='RefDtls_PrecInvDt', required=False)
    receipt_advise_number = StringField(data_key='RefDtls_RecAdvRef', required=False)
    batch_reference_number = StringField(data_key='RefDtls_TendRef', required=False)
    contract_reference_number = StringField(data_key='RefDtls_ContrRef', required=False)
    other_reference_number = StringField(data_key='RefDtls_ExtRef', required=False)
    project_reference_number = StringField(data_key='RefDtls_ProRef', required=False)
    vendor_po_reference_number = StringField(data_key='RefDtls_PORef', required=False)


class ExportDetailsSchema(Schema):
    category = StringField(data_key='ExpDtls_ExpCat', required=True)
    is_with_payment = StringField(data_key='ExpDtls_WthPay', required=True)
    shipping_bill_number = StringField(data_key='ExpDtls_ShipBNo', required=False)
    shipping_bill_date = StringField(data_key='ExpDtls_ShipBDt', required=False)
    port_code = StringField(data_key='ExpDtls_Port', required=False)
    invoice_value_in_currency = StringField(data_key='ExpDtls_InvForCur', required=True)
    currency_code = StringField(data_key='ExpDtls_ForCur', required=True)
    country_code = StringField(data_key='ExpDtls_CntCode', required=True)


class ClearTaxSchemaV1(Schema):
    version = StringField(data_key='Version', required=True)  # const: 1
    irn = StringField(data_key='Irn', required=False)  # should be stored in crs?

    transaction_details = NestedField(
        TransactionDetailsSchema, required=True, data_key='TranDtls'
    )
    document_details = NestedField(
        DocumentDetailsSchema, required=True, data_key='DocDtls'
    )
    supplier_information = NestedField(
        SupplierInformationSchema, required=True, data_key='SellerDtls'
    )
    buyer_information = NestedField(
        BuyerInformationSchema, required=True, data_key='BuyerDtls'
    )
    # dispatch_from = fields.Nested(DispatchFromSchema, required=False)
    # ship_to = fields.Nested(ShipToSchema, required=False)
    item_details = NestedField(
        ItemDetailsSchema, required=True, data_key='ItemList', many=True
    )
    total_value_details = NestedField(
        TotalValueDetailsSchema, required=True, data_key='ValDtls'
    )
    # payee_information = fields.Nested(PayeeInformationSchema, required=False)
    # reference_details = fields.Nested(ReferenceDetailsSchema, required=False)
    # export_details = fields.Nested(ExportDetailsSchema, required=False)
