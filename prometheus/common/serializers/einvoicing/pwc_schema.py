from marshmallow import Schema, fields


class Data<PERSON>eyField(fields.Field):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        data_key = kwargs.get('data_key')
        if data_key is not None:
            if self.load_from is not None or self.dump_to is not None:
                raise RuntimeError(
                    "load_from and dump_to cannot be given when data_key is given"
                )
            self.load_from = data_key
            self.dump_to = data_key


class StringField(fields.String, DataKeyField):
    pass


class IntegerField(fields.Integer, DataKeyField):
    pass


class DecimalField(fields.Decimal, DataKeyField):
    pass


class HeaderSchema(Schema):  # cleartax: under basic details
    # in_crs: booking.owner.gstin / pwc: repeated in buyer info?
    user_gstin = StringField(data_key='User_GSTIN', required=True)

    tax_schema = StringField(data_key='Taxsch', required=True)  # const: gst
    version = StringField(data_key='Version', required=True)  # const: 1
    irn = StringField(data_key='IRN', required=False)  # should be stored in crs?


class TransactionDetailsSchema(Schema):
    category = StringField(data_key='TranDtls_Catg', required=True)
    regular_or_reverse_charge = StringField(data_key='TranDtls_Regrev', required=True)
    address_type = StringField(data_key='TranDtls_Typ', required=True)
    is_ecm_transaction = StringField(data_key='TranDtls_EcmTrn', required=False)
    gstin_of_ecommerce_operator = StringField(
        data_key='TranDtls_EcmGstin', required=False
    )


class DocumentDetailsSchema(Schema):  # cleartax: under basic details
    document_type = StringField(
        data_key='DocDtls_Typ', required=True
    )  # const: INV/DBN/CRN, in_crs:?
    document_number = StringField(
        data_key='DocDtls_No', required=True
    )  # invoice number, in_crs:inv_number
    document_date = StringField(
        data_key='DocDtls_Dt', required=True
    )  # invoice date, in_crs:inv date
    original_invoice_number = StringField(
        data_key='DocDtls_OrgInvNo', required=False
    )  # in_crs:?


class SupplierInformationSchema(Schema):  # cleartax: under supplier information
    """
    Missing address1(Mandatory) in cleartax
    Missing legal_name(Mandatory) in cleartax

    in_crs: vendor details
    """

    gstin = StringField(
        data_key='SellerDtls_Gstin', required=True
    )  # in_crs: ruptub.gstin
    trade_name = StringField(
        data_key='SellerDtls_TrdNm', required=True
    )  # in_crs: ruptub.legal_name
    building_number = StringField(data_key='SellerDtls_Bno', required=False)
    building_name = StringField(data_key='SellerDtls_Bnm', required=False)
    floor_number = StringField(data_key='SellerDtls_Flno', required=False)
    location = StringField(
        data_key='SellerDtls_Loc', required=True
    )  # in_crs: ruptub.city
    district = StringField(data_key='SellerDtls_Dst', required=False)
    pincode = IntegerField(
        data_key='SellerDtls_Pin', required=True
    )  # in_crs: ruptub.pincode
    state_code = IntegerField(
        data_key='SellerDtls_Stcd', required=True
    )  # in_crs: ruptub.legal_state_code
    phone_number = IntegerField(
        data_key='SellerDtls_Ph', required=False
    )  # in_crs: ruptub.phone_number
    email_id = StringField(
        data_key='SellerDtls_Em', required=False
    )  # in_crs: ruptub.email


class BuyerInformationSchema(Schema):
    # in_crs: booking.owner
    gstin = StringField(
        data_key='BuyerDtls_Gstin', required=True
    )  # in_crs: booking.owner.gstin
    trade_name = StringField(data_key='BuyerDtls_TrdNm', required=True)
    building_number = StringField(data_key='BuyerDtls_Bno', required=False)
    building_name = StringField(data_key='BuyerDtls_Bnm', required=False)
    floor_number = StringField(data_key='BuyerDtls_Flno', required=False)
    location = StringField(data_key='BuyerDtls_Loc', required=True)
    district = StringField(data_key='BuyerDtls_Dst', required=False)
    pincode = IntegerField(data_key='BuyerDtls_Pin', required=True)
    state_code = IntegerField(data_key='BuyerDtls_Stcd', required=True)
    phone_number = IntegerField(data_key='BuyerDtls_Ph', required=False)
    email_id = StringField(data_key='BuyerDtls_Em', required=False)


class DispatchFromSchema(Schema):
    # cleartax: mandatory, pwc: optional
    # in_crs: ?vendor details
    gstin = StringField(data_key='DispDtls_Gstin', required=True)
    trade_name = StringField(data_key='DispDtls_TrdNm', required=True)
    building_number = StringField(data_key='DispDtls_Bno', required=False)
    building_name = StringField(data_key='DispDtls_Bnm', required=False)
    floor_number = StringField(data_key='DispDtls_Flno', required=False)
    location = StringField(data_key='DispDtls_Loc', required=True)
    district = StringField(data_key='DispDtls_Dst', required=False)
    pincode = IntegerField(data_key='DispDtls_Pin', required=True)
    state_code = IntegerField(data_key='DispDtls_Stcd', required=True)
    phone_number = IntegerField(data_key='DispDtls_Ph', required=False)
    email_id = StringField(data_key='DispDtls_Em', required=False)


class ShipToSchema(Schema):
    gstin = StringField(data_key='ShipDtls_Gstin', required=True)
    trade_name = StringField(data_key='ShipDtls_TrdNm', required=True)
    building_number = StringField(data_key='ShipDtls_Bno', required=False)
    building_name = StringField(data_key='ShipDtls_Bnm', required=False)
    floor_number = StringField(data_key='ShipDtls_Flno', required=False)
    location = StringField(data_key='ShipDtls_Loc', required=True)
    district = StringField(data_key='ShipDtls_Dst', required=False)
    pincode = IntegerField(data_key='ShipDtls_Pin', required=True)
    state_code = IntegerField(data_key='ShipDtls_Stcd', required=True)
    phone_number = IntegerField(data_key='ShipDtls_Ph', required=False)
    email_id = StringField(data_key='ShipDtls_Em', required=False)


class ItemDetailsSchema(Schema):
    # cleartax: itemdetails
    product_name = StringField(data_key='ItemsList_PrdNm', required=True)
    product_description = StringField(data_key='ItemsList_PrdDesc', required=False)
    hsn_code = StringField(data_key='ItemsList_HsnCd', required=True)
    bar_code = StringField(data_key='ItemsList_BarCde', required=False)
    quantity = StringField(data_key='ItemsList_Qty', required=True)
    free_quantity = StringField(data_key='ItemsList_FreeQty', required=False)
    unit_of_measure = StringField(data_key='ItemsList_Unit', required=True)
    unit_price = StringField(data_key='ItemsList_UnitPrice', required=True)
    total_amount = StringField(data_key='ItemsList_TotAmt', required=False)
    discount = StringField(data_key='ItemsList_Discount', required=False)
    other_charge = StringField(data_key='ItemsList_OthChrg', required=False)
    assessable_amount = StringField(
        data_key='ItemsList_AssAmt', required=True
    )  # cleartax: optional
    cgst = StringField(data_key='ItemsList_CgstRt', required=True)
    sgst = StringField(data_key='ItemsList_SgstRt', required=True)
    igst = StringField(data_key='ItemsList_IgstRt', required=True)
    cess = StringField(data_key='ItemsList_CessRt', required=True)
    cess_non_advalorem = StringField(data_key='ItemsList_CessNonAdval', required=True)
    state_cess = StringField(data_key='ItemsList_StateCes', required=True)
    total_item_value = StringField(data_key='ItemsList_TotItemVal', required=True)
    batch_name = StringField(data_key='ItemsList_Nm', required=False)
    expiry_date = StringField(data_key='ItemsList_ExpDt', required=False)
    warranty_date = StringField(data_key='ItemsList_WrDt', required=False)


class TotalValueDetailsSchema(Schema):
    # cleartax: Document total, Total details -  Only final val (Total invoice amount) and tax total mandatory
    assessable_amount = StringField(data_key='ValDtls_AssVal', required=True)
    cgst = StringField(data_key='ValDtls_CgstVal', required=True)
    sgst = StringField(data_key='ValDtls_SgstVal', required=True)
    igst = StringField(data_key='ValDtls_IgstVal', required=True)
    cess = StringField(data_key='ValDtls_CessVal', required=True)
    state_cess = StringField(data_key='ValDtls_StCessVal', required=True)
    cess_non_advalorem = StringField(data_key='ValDtls_CesNonAdval', required=True)
    discount = StringField(data_key='ValDtls_Disc', required=False)
    other_charge = StringField(data_key='ValDtls_OthChrg', required=False)
    final_invoice_value = StringField(data_key='ValDtls_TotInvVal', required=True)


class PayeeInformationSchema(Schema):
    payee_name = StringField(data_key='PayDtls_Nam', required=False)
    mode_of_payment = StringField(data_key='PayDtls_Mode', required=False)
    branch_or_ifsc_code = StringField(data_key='PayDtls_FinInsBr', required=False)
    terms_of_payment = StringField(data_key='PayDtls_PayTerm', required=False)
    payment_instructions = StringField(data_key='PayDtls_PayInstr', required=False)
    credit_transfer = StringField(data_key='PayDtls_CrTrn', required=False)
    direct_debit = StringField(data_key='PayDtls_DirDr', required=False)
    credit_days = StringField(data_key='PayDtls_CrDay', required=False)
    balance_amount_to_be_paid = StringField(data_key='PayDtls_BalAmt', required=False)
    payment_due_date = StringField(data_key='PayDtls_PayDueDt', required=False)
    account_details = StringField(data_key='PayDtls_AcctDet', required=False)


class ReferenceDetailsSchema(Schema):
    # cleartax: Under batch details
    invoice_remarks = StringField(data_key='RefDtls_InvRmk', required=False)
    invoice_start_date = StringField(data_key='RefDtls_InvStDt', required=False)
    invoice_end_date = StringField(data_key='RefDtls_InvEndDt', required=False)
    preceding_invoice_number = StringField(data_key='RefDtls_PrecInvNo', required=False)
    preceding_invoice_date = StringField(data_key='RefDtls_PrecInvDt', required=False)
    receipt_advise_number = StringField(data_key='RefDtls_RecAdvRef', required=False)
    batch_reference_number = StringField(data_key='RefDtls_TendRef', required=False)
    contract_reference_number = StringField(data_key='RefDtls_ContrRef', required=False)
    other_reference_number = StringField(data_key='RefDtls_ExtRef', required=False)
    project_reference_number = StringField(data_key='RefDtls_ProRef', required=False)
    vendor_po_reference_number = StringField(data_key='RefDtls_PORef', required=False)


class ExportDetailsSchema(Schema):
    category = StringField(data_key='ExpDtls_ExpCat', required=True)
    is_with_payment = StringField(data_key='ExpDtls_WthPay', required=True)
    shipping_bill_number = StringField(data_key='ExpDtls_ShipBNo', required=False)
    shipping_bill_date = StringField(data_key='ExpDtls_ShipBDt', required=False)
    port_code = StringField(data_key='ExpDtls_Port', required=False)
    invoice_value_in_currency = StringField(data_key='ExpDtls_InvForCur', required=True)
    currency_code = StringField(data_key='ExpDtls_ForCur', required=True)
    country_code = StringField(data_key='ExpDtls_CntCode', required=True)


class PWCSchema(Schema):
    header = fields.Nested(HeaderSchema, required=True)
    transaction_details = fields.Nested(TransactionDetailsSchema, required=True)
    document_details = fields.Nested(DocumentDetailsSchema, required=True)
    supplier_information = fields.Nested(SupplierInformationSchema, required=True)
    buyer_information = fields.Nested(BuyerInformationSchema, required=True)
    dispatch_from = fields.Nested(DispatchFromSchema, required=False)
    ship_to = fields.Nested(ShipToSchema, required=False)
    item_details = fields.Nested(ItemDetailsSchema, required=True)
    total_value_details = fields.Nested(TotalValueDetailsSchema, required=True)
    payee_information = fields.Nested(PayeeInformationSchema, required=False)
    reference_details = fields.Nested(ReferenceDetailsSchema, required=False)
    export_details = fields.Nested(ExportDetailsSchema, required=False)
