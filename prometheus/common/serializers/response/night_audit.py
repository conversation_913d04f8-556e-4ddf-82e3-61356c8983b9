from marshmallow import Schema, fields
from marshmallow.validate import OneOf

from prometheus.core.api_docs import swag_schema
from ths_common.constants.catalog_constants import NightAuditStatus


@swag_schema
class PendingCriticalTaskSchema(Schema):
    hotel_id = fields.String()
    seller_id = fields.String()


@swag_schema
class ScheduledNightAuditResponseSchema(Schema):
    night_audit_id = fields.String()
    hotel_id = fields.String()
    business_date = fields.Date()
    scheduled_time = fields.LocalDateTime()
    status = fields.String(validate=OneOf(NightAuditStatus.all()))
    failure_message = fields.String()
    vendors_with_pending_critical_tasks = fields.Nested(
        PendingCriticalTaskSchema, many=True
    )
