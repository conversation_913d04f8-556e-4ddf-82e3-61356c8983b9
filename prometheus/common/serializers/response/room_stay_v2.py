from marshmallow import Schema, fields

from prometheus.common.serializers.response import (
    NameSchema,
    ShallowGuestStayResponseSchema,
    ShallowRoomStayResponseSchema,
)
from prometheus.core.api_docs import swag_schema
from shared_kernel.serializers.value_objects import PhoneSchema


class ShallowGuestDetailsResponseSchema(Schema):
    dummy = fields.Boolean()
    name = fields.Nested(NameSchema)
    phone = fields.Nested(PhoneSchema)
    is_primary = fields.Boolean()


class ShallowGuestStayV2ResponseSchema(ShallowGuestStayResponseSchema):
    guest_details = fields.Nested(ShallowGuestDetailsResponseSchema, allow_none=True)


@swag_schema
class ShallowRoomStayV2ResponseSchema(ShallowRoomStayResponseSchema):
    guest_stays = fields.Nested(ShallowGuestStayV2ResponseSchema, many=True)
