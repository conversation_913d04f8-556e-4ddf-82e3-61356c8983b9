from marshmallow import Schema, fields
from treebo_commons.money.money_field import <PERSON><PERSON>ield

from prometheus.core.api_docs import swag_schema
from schema_instances import register_schema


@swag_schema
class BasicCreditShellResponseSchema(Schema):
    issue_date = fields.String()
    credit_shell_id = fields.String()
    remaining_credit = MoneyField()
    total_credit = MoneyField()


@swag_schema
class CreditShellResponseSchema(BasicCreditShellResponseSchema):
    billed_entity_id = fields.String()
    credit_note_id = fields.String()
    is_refundable_folio = fields.Boolean()
    paid_by = fields.String()
    paid_to = fields.String()
    bill_id = fields.String()
    version = fields.Integer()


@register_schema(many=True)
class CreditShellTransactionLogSchema(Schema):
    action_datetime = fields.LocalDateTime()
    event_type = fields.String()
    target_booking_reference_number = fields.String()
    credit_shell_id = fields.String()
    transaction_amount = MoneyField()
    credit_shell_audit_trail_id = fields.Integer()
    remaining_amount = MoneyField()
    remarks = fields.String()


@register_schema()
class CreditShellRefundResponseSchema(Schema):
    amount = MoneyField()
    credit_shell_id = fields.String()
    credit_shell_refund_id = fields.String()
    payment_mode = fields.String()
    remarks = fields.String()


@register_schema()
class CreditShellRefundEventSchema(CreditShellRefundResponseSchema):
    bill_id = fields.String()
    booking_id = fields.String()
