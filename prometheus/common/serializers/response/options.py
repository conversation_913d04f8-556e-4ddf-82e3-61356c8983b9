from marshmallow import Schema, fields, pre_dump

from prometheus.core.api_docs import swag_schema


@swag_schema
class OptionKeySchema(Schema):
    key = fields.String(required=True)
    label = fields.String(required=True)


@swag_schema
class OptionValueSchema(Schema):
    value = fields.String(required=True)
    label = fields.String(required=True)


@swag_schema
class OptionResponseSchema(Schema):
    option = fields.Nested(OptionKeySchema)
    values = fields.Nested(OptionValueSchema, many=True)

    @pre_dump
    def transform(self, data):
        d = {
            'option': dict(key=data[0].key(), label=data[0].option_label()),
            'values': data[1],
        }
        return d
