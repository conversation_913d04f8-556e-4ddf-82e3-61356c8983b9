from marshmallow import Schema, fields

from prometheus.core.api_docs import swag_schema


@swag_schema
class HotelDetailsEntity(Schema):
    city = fields.String(description="City where the hotel is located", required=True)
    current_business_date = fields.String(
        description="Current business date", required=True
    )
    name = fields.String(description="Name of the hotel", required=True)


@swag_schema
class PendingNightAuditHotelsResponseSchema(Schema):
    hotels = fields.Dict(
        keys=fields.String(),
        values=fields.Nested(HotelDetailsEntity),
        required=True,
    )
