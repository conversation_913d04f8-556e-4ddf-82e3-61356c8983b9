from datetime import datetime, timedelta

from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import current_date, to_date

from prometheus.tests.factories.aggregate_factories import RoomTypeAggregateFactory
from prometheus.tests.mockers import mock_role_manager


def test_available_room_slots_api_required_fields(client, hotel_repo):
    room_type_aggregate = RoomTypeAggregateFactory()

    today = current_date()
    tomorrow = to_date(today + timedelta(days=1))
    url = (
        f'v1/hotels/0016932/available-room-slots?from_date={today}&to_date={tomorrow}&'
        f'room_type_id={room_type_aggregate.room_type.room_type_id}'
    )
    with mock_role_manager():
        response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    data = response.json.get('data')
    fields = data[0].keys()
    assert fields.__contains__('room_id') is True
    assert fields.__contains__('available_from') is True
    assert fields.__contains__('available_till') is True
    assert fields.__contains__('housekeeping_status') is True


def test_available_room_slots_api_values(client, hotel_repo):
    room_type_aggregate = RoomTypeAggregateFactory()

    today = current_date()
    tomorrow = to_date(today + timedelta(days=1))
    url = (
        f'v1/hotels/0016932/available-room-slots?from_date={today}&to_date={tomorrow}&'
        f'room_type_id={room_type_aggregate.room_type.room_type_id}'
    )
    with mock_role_manager():
        response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    data = response.json.get('data')
    assert data is not None and len(data) > 0
    assert data[0].get('room_id') == 15
    assert data[0].get('housekeeping_status') == 'Clean'
    assert (
        dateutils.isoformat_str_to_datetime(data[0].get('available_from')).date()
        == today
    )
    assert (
        dateutils.isoformat_str_to_datetime(data[0].get('available_till')).date()
        == tomorrow
    )
