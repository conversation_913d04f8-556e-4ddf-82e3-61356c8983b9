import json

from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import date_range

from prometheus.tests.mockers import mock_role_manager
from ths_common.constants.inventory_constants import DNRStatus


def get_room_type_inventory(
    room_type_inventory_repo, active_hotel_aggregate, room_type_ids
):
    room_type_inventory_aggregates = room_type_inventory_repo.load_multiple(
        active_hotel_aggregate.hotel.hotel_id,
        dateutils.add(active_hotel_aggregate.hotel.current_business_date, days=0),
        dateutils.add(active_hotel_aggregate.hotel.current_business_date, days=5),
        room_type_ids=room_type_ids,
    )
    room_type_inventory_map = {
        ag.room_type_inventory.room_type_id: ag for ag in room_type_inventory_aggregates
    }
    return room_type_inventory_map


def mark_dnrs_payload(current_business_date):
    data = {
        "dnrs": [
            {
                "room_id": "15",
                "from_date": dateutils.add(current_business_date, days=0).isoformat(),
                "to_date": dateutils.add(current_business_date, days=2).isoformat(),
                "source": "Prowl",
                "type": "maintenance",
                "subtype": "tv_not_working",
                "assigned_by": "<EMAIL>",
            },
            {
                "room_id": "160",
                "from_date": dateutils.add(current_business_date, days=2).isoformat(),
                "to_date": dateutils.add(current_business_date, days=4).isoformat(),
                "source": "Prowl",
                "type": "maintenance",
                "subtype": "tv_not_working",
                "assigned_by": "<EMAIL>",
            },
        ]
    }
    return data


def create_multiple_dnrs(client, payload, hotel_id, expected_status_code=201):
    with mock_role_manager():
        url = "v1/hotels/{0}/dnrs-list".format(hotel_id)
        response = client.post(
            url,
            data=json.dumps(payload),
            content_type="application/json",
            headers={"X-User-Type": "super-admin", "X-Hotel-Id": "0016932"},
        )
        assert response.status_code == expected_status_code
        return response.json


def bulk_resolve_multiple_dnrs(client, payload, hotel_id, expected_status_code=200):
    with mock_role_manager():
        url = "v1/hotels/{0}/dnrs/bulk-resolve-dnr".format(hotel_id)
        response = client.post(
            url,
            data=json.dumps(payload),
            content_type="application/json",
            headers={"X-User-Type": "super-admin", "X-Hotel-Id": "0016932"},
        )
        assert response.status_code == expected_status_code
        return response.json


def test_bulk_mark_dnr_apis(
    client, dnr_repository, room_type_inventory_repo, active_hotel_aggregate
):
    room_type_ids = ['rt01', 'rt02']
    pre_room_type_inventory = get_room_type_inventory(
        room_type_inventory_repo, active_hotel_aggregate, room_type_ids
    )
    current_business_date = active_hotel_aggregate.hotel.current_business_date
    dnr_payload = mark_dnrs_payload(current_business_date)
    payload = {"data": dnr_payload}
    create_multiple_dnrs(client, payload, active_hotel_aggregate.hotel.hotel_id)
    post_room_type_inventory = get_room_type_inventory(
        room_type_inventory_repo, active_hotel_aggregate, room_type_ids
    )

    pre_date_avail_map = {
        avail.date: avail
        for avail in pre_room_type_inventory['rt01'].room_type_inventory_availabilities
    }
    post_date_avail_map = {
        avail.date: avail
        for avail in post_room_type_inventory['rt01'].room_type_inventory_availabilities
    }
    for date in date_range(
        dateutils.add(current_business_date, days=0),
        dateutils.add(current_business_date, days=2),
    ):
        assert (
            pre_date_avail_map[date].actual_count
            == post_date_avail_map[date].actual_count + 1
        )

    pre_date_avail_map = {
        avail.date: avail
        for avail in pre_room_type_inventory['rt02'].room_type_inventory_availabilities
    }
    post_date_avail_map = {
        avail.date: avail
        for avail in post_room_type_inventory['rt02'].room_type_inventory_availabilities
    }
    for date in date_range(
        dateutils.add(current_business_date, days=2),
        dateutils.add(current_business_date, days=4),
    ):
        assert (
            pre_date_avail_map[date].actual_count
            == post_date_avail_map[date].actual_count + 1
        )
    start_date = dateutils.add(current_business_date, days=0)
    end_date = dateutils.add(current_business_date, days=4)
    dnrs = dnr_repository.load_dnrs(
        active_hotel_aggregate.hotel.hotel_id, ['15', '160'], start_date, end_date
    )
    active_dnrs = [dnr for dnr in dnrs if dnr.dnr.status == DNRStatus.ACTIVE]
    assert len(active_dnrs) == 2


def test_bulk_resolve_dnr_apis(
    client, dnr_repository, room_type_inventory_repo, active_hotel_aggregate
):
    room_type_ids = ['rt01', 'rt02']
    pre_room_type_inventory = get_room_type_inventory(
        room_type_inventory_repo, active_hotel_aggregate, room_type_ids
    )
    current_business_date = active_hotel_aggregate.hotel.current_business_date
    dnr_payload = mark_dnrs_payload(current_business_date)
    payload = {"data": dnr_payload}
    dnrs = create_multiple_dnrs(client, payload, active_hotel_aggregate.hotel.hotel_id)[
        "data"
    ]["dnrs"]
    dnr_ids = [dnr["dnr_id"] for dnr in dnrs]
    dnr_resolve_payload = {"data": {"dnr_ids": dnr_ids}}
    bulk_resolve_multiple_dnrs(
        client, dnr_resolve_payload, active_hotel_aggregate.hotel.hotel_id
    )
    post_room_type_inventory = get_room_type_inventory(
        room_type_inventory_repo, active_hotel_aggregate, room_type_ids
    )

    pre_date_avail_map = {
        avail.date: avail
        for avail in pre_room_type_inventory['rt01'].room_type_inventory_availabilities
    }
    post_date_avail_map = {
        avail.date: avail
        for avail in post_room_type_inventory['rt01'].room_type_inventory_availabilities
    }
    for date in date_range(
        dateutils.add(current_business_date, days=0),
        dateutils.add(current_business_date, days=5),
    ):
        assert (
            pre_date_avail_map[date].actual_count
            == post_date_avail_map[date].actual_count
        )

    pre_date_avail_map = {
        avail.date: avail
        for avail in pre_room_type_inventory['rt02'].room_type_inventory_availabilities
    }
    post_date_avail_map = {
        avail.date: avail
        for avail in post_room_type_inventory['rt02'].room_type_inventory_availabilities
    }
    for date in date_range(
        dateutils.add(current_business_date, days=0),
        dateutils.add(current_business_date, days=5),
    ):
        assert (
            pre_date_avail_map[date].actual_count
            == post_date_avail_map[date].actual_count
        )
    start_date = dateutils.add(current_business_date, days=0)
    end_date = dateutils.add(current_business_date, days=4)
    dnrs = dnr_repository.load_dnrs(
        active_hotel_aggregate.hotel.hotel_id, ['15', '160'], start_date, end_date
    )
    in_active_dnrs = [dnr for dnr in dnrs if dnr.dnr.status == DNRStatus.INACTIVE]
    assert len(in_active_dnrs) == 2
