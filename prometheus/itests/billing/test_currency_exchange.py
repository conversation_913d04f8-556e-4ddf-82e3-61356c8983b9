import json
from decimal import Decimal

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from prometheus.domain.billing.aggregates.cashier_session_aggregate import (
    CashierSessionAggregate,
)
from prometheus.infrastructure.database import db_engine
from prometheus.itests.billing.test_payment_api import create_cash_register_and_session
from prometheus.tests.mockers import mock_template_service
from ths_common.constants.billing_constants import CashRegisterNames
from ths_common.value_objects import IDProof, TaxDetail


def create_currency_exchange_payload():
    new_currency_exchange = dict(
        data={
            "amount_in_base_currency": "INR 3500",
            "amount_in_foreign_currency": "USD 50",
            "currency_seller_detail": {
                "booking_id": "DIR-REF1",
                "guest_name": "<PERSON><PERSON><PERSON>",
                "id_proof": {
                    "attachment_id": "attachment_id_test",
                    "id_number": "PASSPORT-123",
                    "id_proof_country_code": "IN",
                    "id_proof_type": "passport",
                    "issued_date": "2021-01-01 12:00:00+05:30",
                    "issued_place": "Bengaluru",
                },
                "room_number": "101-Oak",
            },
            "exchange_rate": "1 USD = 70.00 INR",
            "foreign_currency_payment_mode": "Cash",
            "remarks": "CD-123",
            "tax_amount": "25",
            "tax_details": [{"amount": "25", "percentage": 10, "tax_type": "cgst"}],
            "taxable_amount": "250",
            "round_off": "0.00",
            "total_payable_in_base_currency": "INR {0}".format(3500 - 25),
        }
    )
    return new_currency_exchange


def test_currency_exchange_on_closed_session_results_in_400_bad_request_error(
    client, booking_and_bill, currency_exchange_repo, cashier_session_repo
):
    bill_aggregate = booking_and_bill[1]
    cash_register_id, cashier_session_id = create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    cashier_session_aggregate: CashierSessionAggregate = cashier_session_repo.load(
        cashier_session_id
    )
    cashier_session_aggregate.mark_session_as_closed()
    cashier_session_repo.update(cashier_session_aggregate)
    db_engine.get_session().commit()

    currency_exchange_payload = create_currency_exchange_payload()
    url = (
        'v1/cashier/cash-registers/{0}/cashier-sessions/{1}/currency-exchanges'.format(
            cash_register_id, cashier_session_id
        )
    )
    response = client.post(
        url,
        data=json.dumps(currency_exchange_payload),
        content_type='application/json',
        headers={"X-User-Type": "super-admin", "X-Hotel-Id": bill_aggregate.vendor_id},
    )
    assert response.status_code == 400


def test_currency_exchange_saves_new_currency_exchange_entity_with_correct_details(
    client, booking_and_bill, currency_exchange_repo, cashier_session_repo
):
    bill_aggregate = booking_and_bill[1]
    cash_register_id, cashier_session_id = create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    currency_exchange_payload = create_currency_exchange_payload()
    url = (
        'v1/cashier/cash-registers/{0}/cashier-sessions/{1}/currency-exchanges'.format(
            cash_register_id, cashier_session_id
        )
    )
    last_sequence = currency_exchange_repo.get_last_used_sequence_no(
        bill_aggregate.vendor_id
    )

    with mock_template_service():
        response = client.post(
            url,
            data=json.dumps(currency_exchange_payload),
            content_type='application/json',
            headers={
                "X-User-Type": "super-admin",
                "X-Hotel-Id": bill_aggregate.vendor_id,
            },
        )
        assert response.status_code == 201

    response_data = response.json.get('data')
    currency_exchange_aggregate = currency_exchange_repo.load(
        bill_aggregate.base_currency, response_data.get('currency_exchange_id')
    )
    assert currency_exchange_aggregate.transaction_id == response_data.get(
        'transaction_id'
    )
    assert currency_exchange_aggregate.exchange_rate == "1 USD = 70.00 INR"
    assert currency_exchange_aggregate.amount_in_base_currency == Money('3500 INR')
    assert currency_exchange_aggregate.amount_in_foreign_currency == Money('50 USD')
    assert currency_exchange_aggregate.total_payable_in_base_currency == Money(
        '3475 INR'
    )
    assert currency_exchange_aggregate.currency_exchange.round_off == Money('0 INR')
    assert currency_exchange_aggregate.currency_seller_detail.guest_name == "Rohit Jain"
    assert currency_exchange_aggregate.currency_seller_detail.room_number == "101-Oak"
    assert (
        currency_exchange_aggregate.currency_seller_detail.id_proof.id_proof_type
        == 'passport'
    )
    assert (
        currency_exchange_aggregate.currency_seller_detail.id_proof.id_proof_country_code
        == 'IN'
    )
    assert (
        currency_exchange_aggregate.currency_seller_detail.id_proof.id_number
        == 'PASSPORT-123'
    )
    assert (
        currency_exchange_aggregate.currency_seller_detail.id_proof.issued_place
        == 'Bengaluru'
    )
    assert (
        currency_exchange_aggregate.currency_seller_detail.id_proof.issued_date
        == dateutils.isoformat_str_to_datetime('2021-01-01T12:00:00+05:30')
    )
    assert response_data.get('tax_details') == [
        {"amount": "25.00", "percentage": '10', "tax_type": "cgst"}
    ]

    assert currency_exchange_aggregate.encashment_certificate_url == "mock_itest_url"
    assert currency_exchange_aggregate.foreign_currency_payment_mode == "Cash"
    assert currency_exchange_aggregate.transaction_date == dateutils.current_date()
    if not last_sequence:
        next_sequence = 1
    else:
        next_sequence = last_sequence + 1
    assert (
        currency_exchange_aggregate.certificate_number
        == currency_exchange_repo.generate_certificate_no(
            bill_aggregate.vendor_id, next_sequence
        )
    )
    assert currency_exchange_aggregate.remarks == 'CD-123'
    assert currency_exchange_aggregate.tax_amount == Money('25 INR')
    assert currency_exchange_aggregate.taxable_amount == Money('250 INR')


def test_currency_exchange_creates_an_inflow_entry_equivalent_to_foreign_currency_sold_in_cashier_session(
    client, booking_and_bill, currency_exchange_repo, cashier_session_repo
):
    bill_aggregate = booking_and_bill[1]
    cash_register_id, cashier_session_id = create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    currency_exchange_payload = create_currency_exchange_payload()
    url = (
        'v1/cashier/cash-registers/{0}/cashier-sessions/{1}/currency-exchanges'.format(
            cash_register_id, cashier_session_id
        )
    )
    with mock_template_service():
        response = client.post(
            url,
            data=json.dumps(currency_exchange_payload),
            content_type='application/json',
            headers={
                "X-User-Type": "super-admin",
                "X-Hotel-Id": bill_aggregate.vendor_id,
                "X-User": "FDM 1",
                "X-Auth-Id": "123",
            },
        )
        assert response.status_code == 201

    response_data = response.json.get('data')

    cashier_session_aggregate: CashierSessionAggregate = cashier_session_repo.load(
        cashier_session_id
    )
    cashier_payments = [
        cp
        for cp in cashier_session_aggregate.payments
        if response_data.get('transaction_id') in cp.transaction_id
    ]
    assert len(cashier_payments) == 2

    inflow = [cp for cp in cashier_payments if cp.is_inflow_amount()]
    assert len(inflow) == 1

    inflow = inflow[0]

    assert inflow.amount == Money('3500 INR')
    assert inflow.amount_in_payment_currency == Money('50 USD')
    assert inflow.added_by == "FDM 1"
    assert inflow.paid_to == "FDM 1 (123)"


def test_currency_exchange_creates_an_outflow_entry_equivalent_to_total_payable_in_base_currency_in_cashier_session(
    client, booking_and_bill, cashier_session_repo
):
    bill_aggregate = booking_and_bill[1]
    cash_register_id, cashier_session_id = create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    currency_exchange_payload = create_currency_exchange_payload()
    url = (
        'v1/cashier/cash-registers/{0}/cashier-sessions/{1}/currency-exchanges'.format(
            cash_register_id, cashier_session_id
        )
    )
    with mock_template_service():
        response = client.post(
            url,
            data=json.dumps(currency_exchange_payload),
            content_type='application/json',
            headers={
                "X-User-Type": "super-admin",
                "X-Hotel-Id": bill_aggregate.vendor_id,
                "X-User": "FDM 1",
                "X-Auth-Id": "123",
            },
        )
        assert response.status_code == 201

    response_data = response.json.get('data')

    cashier_session_aggregate: CashierSessionAggregate = cashier_session_repo.load(
        cashier_session_id
    )
    cashier_payments = [
        cp
        for cp in cashier_session_aggregate.payments
        if response_data.get('transaction_id') in cp.transaction_id
    ]
    assert len(cashier_payments) == 2

    outflow = [cp for cp in cashier_payments if cp.is_outflow_amount()]
    assert len(outflow) == 1

    outflow = outflow[0]

    assert outflow.amount == Money('3475 INR')
    assert outflow.amount_in_payment_currency == Money('3475 INR')
    assert outflow.added_by == "FDM 1"
    assert outflow.paid_to == response_data.get("currency_seller_detail").get(
        "guest_name"
    )


def test_currency_exchange_should_update_the_balance_on_cashier_session_after_inflow_and_outflow(
    client, booking_and_bill, currency_exchange_repo, cashier_session_repo
):
    bill_aggregate = booking_and_bill[1]
    cash_register_id, cashier_session_id = create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    cashier_session_aggregate: CashierSessionAggregate = cashier_session_repo.load(
        cashier_session_id
    )
    old_cashier_session_balance = (
        cashier_session_aggregate.current_balance_in_base_currency()
    )

    currency_exchange_payload = create_currency_exchange_payload()
    url = (
        'v1/cashier/cash-registers/{0}/cashier-sessions/{1}/currency-exchanges'.format(
            cash_register_id, cashier_session_id
        )
    )
    with mock_template_service():
        response = client.post(
            url,
            data=json.dumps(currency_exchange_payload),
            content_type='application/json',
            headers={
                "X-User-Type": "super-admin",
                "X-Hotel-Id": bill_aggregate.vendor_id,
            },
        )
        assert response.status_code == 201

    response_data = response.json.get('data')
    currency_exchange_aggregate = currency_exchange_repo.load(
        bill_aggregate.base_currency, response_data.get('currency_exchange_id')
    )
    cashier_session_aggregate: CashierSessionAggregate = cashier_session_repo.load(
        cashier_session_id
    )
    assert (
        cashier_session_aggregate.current_balance_in_base_currency()
        == old_cashier_session_balance
        + currency_exchange_aggregate.amount_in_base_currency
        - currency_exchange_aggregate.total_payable_in_base_currency
    )


def test_currency_exchange_transaction_id_in_cashier_payment_should_be_same_as_currency_exchange_transaction_id(
    client, booking_and_bill, cashier_session_repo
):
    bill_aggregate = booking_and_bill[1]
    cash_register_id, cashier_session_id = create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )
    currency_exchange_payload = create_currency_exchange_payload()
    url = (
        'v1/cashier/cash-registers/{0}/cashier-sessions/{1}/currency-exchanges'.format(
            cash_register_id, cashier_session_id
        )
    )
    with mock_template_service():
        response = client.post(
            url,
            data=json.dumps(currency_exchange_payload),
            content_type='application/json',
            headers={
                "X-User-Type": "super-admin",
                "X-Hotel-Id": bill_aggregate.vendor_id,
            },
        )
        assert response.status_code == 201

    response_data = response.json.get('data')
    cashier_session_aggregate: CashierSessionAggregate = cashier_session_repo.load(
        cashier_session_id
    )
    cashier_payments = [
        cp
        for cp in cashier_session_aggregate.payments
        if response_data.get('transaction_id') in cp.transaction_id
    ]
    assert len(cashier_payments) == 2
    transaction_id = response_data.get('transaction_id')
    assert all(
        p.payment_details.get("payment_source") == "currency_exchange"
        for p in cashier_payments
    )
    assert all(
        p.payment_details.get("currency_exchange_transaction_id") == transaction_id
        for p in cashier_payments
    )
