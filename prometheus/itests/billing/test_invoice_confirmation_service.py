import datetime
from collections import defaultdict

import pytest
from treebo_commons.money.constants import CurrencyType
from treebo_commons.money.money import Money
from treebo_commons.utils import dateutils

from object_registry import locate_instance
from prometheus import crs_context
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.core.globals import HotelContext
from prometheus.domain.billing.entities.bill import Bill
from prometheus.domain.billing.entities.charge import Charge
from prometheus.domain.billing.entities.charge_split import ChargeSplit
from prometheus.domain.billing.entities.payment import Payment
from prometheus.domain.billing.factories.invoice_factory import InvoiceFactory
from prometheus.domain.billing.services.einvoicing_service import EInvoicingService
from prometheus.domain.billing.services.invoice_service import InvoiceService
from prometheus.infrastructure.external_clients.cleartax_client import ClearTaxClient
from prometheus.tests.factories.aggregate_factories import (
    BillFactory,
    BookingAggregateFactory,
)
from prometheus.tests.mockers import mock_einvoicing_service
from prometheus.tests.mocks.catalog_service_client_mock import CatalogServiceClientMock
from ths_common.constants.billing_constants import (
    ChargeBillToTypes,
    ChargeStatus,
    ChargeTypes,
    InvoiceStatus,
    IssuedByType,
    IssuedToType,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
    TaxTypes,
)
from ths_common.exceptions import ValidationException
from ths_common.value_objects import (
    Address,
    BookingBillParentInfo,
    ChargeItem,
    GSTDetails,
    InvoiceBillToInfo,
    InvoiceIssuedByInfo,
    PhoneNumber,
    TaxDetail,
    VendorDetails,
)


@pytest.fixture
def address():
    return Address(
        field_1="House no 420",
        field_2="lane no 9211",
        city="Bangalore",
        state="Karnataka",
        country="India",
        pincode=34234234,
    )


@pytest.fixture
def gst_details(address):
    return GSTDetails(
        legal_name="Ram sharma", gstin_num="GST-12433243", address=address
    )


@pytest.fixture
def some_booking_and_bill():
    bill = BillFactory(bill__bill_id="BIL3")
    booking = BookingAggregateFactory(booking__bill_id=bill.bill.bill_id)
    return booking, bill


@pytest.fixture
def phone_no():
    return PhoneNumber(number="23299193922", country_code="001")


@pytest.fixture
def booking_bill_parent_info():
    return BookingBillParentInfo(
        booking_id='01-1243-23213-23232-2211',
        reference_number="TRB-2312312",
        creation_date=datetime.datetime.strptime('12Jan2018', '%d%b%Y'),
    )


@pytest.fixture
def payment_in_done_state():
    return Payment(
        payment_id=1,
        amount=Money('234.56', CurrencyType('INR')),
        date_of_payment=dateutils.isoformat_str_to_datetime(
            '2018-01-12 12:00:00+05:30'
        ),
        payment_mode=PaymentModes.CREDIT_CARD,
        payment_type=PaymentTypes.PAYMENT,
        payment_details=dict(),
        status=PaymentStatus.DONE,
        paid_to=PaymentReceiverTypes.TREEBO,
        payment_channel=21,
        payment_ref_id="sdfjadslkfjasd",
        paid_by=PaymentReceiverTypes.GUEST,
        comment='Test payment',
        amount_in_payment_currency=Money(20, CurrencyType('EUR')),
    )


@pytest.fixture
def payment_in_pending_state():
    return Payment(
        payment_id=2,
        amount=Money(100, CurrencyType('INR')),
        date_of_payment=dateutils.isoformat_str_to_datetime(
            '2018-01-12 12:00:00+05:30'
        ),
        payment_mode=PaymentModes.CREDIT_CARD,
        payment_type=PaymentTypes.PAYMENT,
        payment_details=dict(),
        status=PaymentStatus.PENDING,
        paid_to=PaymentReceiverTypes.TREEBO,
        payment_channel=21,
        payment_ref_id="sdfjadslkfjasd",
        paid_by=PaymentReceiverTypes.GUEST,
        comment='Test payment1',
        amount_in_payment_currency=Money(20, CurrencyType('EUR')),
    )


@pytest.fixture
def payment_in_pending_state_treebo():
    return Payment(
        payment_id=3,
        amount=Money(200, CurrencyType('INR')),
        date_of_payment=dateutils.isoformat_str_to_datetime(
            '2018-01-12 12:00:00+05:30'
        ),
        payment_mode=PaymentModes.CREDIT_CARD,
        payment_type=PaymentTypes.PAYMENT,
        payment_details=dict(),
        status=PaymentStatus.PENDING,
        paid_to=PaymentReceiverTypes.TREEBO,
        payment_channel=21,
        payment_ref_id="sdfjadslkfjasd",
        paid_by=PaymentReceiverTypes.CORPORATE,
        comment='Test payment 2',
        amount_in_payment_currency=Money(20, CurrencyType('EUR')),
    )


@pytest.fixture
def consumed_charge_non_credit():
    charge_split_1 = ChargeSplit(
        charge_split_id=1,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        percentage=50,
    )

    charge_split_2 = ChargeSplit(
        charge_split_id=2,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        percentage=50,
    )

    charge_splits = [charge_split_1, charge_split_2]

    return Charge(
        charge_id=1,
        charge_to=["CST-23232", "CST-23233"],
        pretax_amount=Money(100, CurrencyType('INR')),
        tax_amount=Money(10, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money(5, CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money(5, CurrencyType('INR'))),
        ],
        posttax_amount=Money(110, CurrencyType('INR')),
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        status=ChargeStatus.CONSUMED,
        recorded_time=dateutils.localize_datetime(
            datetime.datetime.strptime('11Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        applicable_date=dateutils.localize_datetime(
            datetime.datetime.strptime('12Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        comment="Charge done",
        created_by="AUTH-12121",
        charge_item=ChargeItem(
            name="RoomStay", sku_category_id="cat_1", details=dict()
        ),
        charge_splits=charge_splits,
    )


@pytest.fixture
def consumed_charge_credit_company():
    charge_split_1 = ChargeSplit(
        charge_split_id=1,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.CREDIT,
        bill_to_type=ChargeBillToTypes.COMPANY,
        percentage=50,
    )

    charge_split_2 = ChargeSplit(
        charge_split_id=2,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.CREDIT,
        bill_to_type=ChargeBillToTypes.COMPANY,
        percentage=50,
    )

    charge_splits = [charge_split_1, charge_split_2]

    return Charge(
        charge_id=2,
        charge_to=["CST-23232", "CST-23233"],
        pretax_amount=Money(100, CurrencyType('INR')),
        tax_amount=Money(10, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money(5, CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money(5, CurrencyType('INR'))),
        ],
        posttax_amount=Money(110, CurrencyType('INR')),
        charge_type=ChargeTypes.CREDIT,
        bill_to_type=ChargeBillToTypes.COMPANY,
        status=ChargeStatus.CONSUMED,
        recorded_time=dateutils.localize_datetime(
            datetime.datetime.strptime('11Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        applicable_date=dateutils.localize_datetime(
            datetime.datetime.strptime('12Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        comment="Charge done",
        created_by="AUTH-12121",
        charge_item=ChargeItem(
            name="RoomStay", sku_category_id="cat_1", details=dict()
        ),
        charge_splits=charge_splits,
    )


@pytest.fixture
def consumed_charge_non_credit_company():
    charge_split_1 = ChargeSplit(
        charge_split_id=1,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.COMPANY,
        percentage=50,
    )

    charge_split_2 = ChargeSplit(
        charge_split_id=2,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.COMPANY,
        percentage=50,
    )

    charge_splits = [charge_split_1, charge_split_2]

    return Charge(
        charge_id=3,
        charge_to=["CST-23232", "CST-23233"],
        pretax_amount=Money(100, CurrencyType('INR')),
        tax_amount=Money(10, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money(5, CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money(5, CurrencyType('INR'))),
        ],
        posttax_amount=Money(110, CurrencyType('INR')),
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.COMPANY,
        status=ChargeStatus.CONSUMED,
        recorded_time=dateutils.localize_datetime(
            datetime.datetime.strptime('11Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        applicable_date=dateutils.localize_datetime(
            datetime.datetime.strptime('12Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        comment="Charge done",
        created_by="AUTH-12121",
        charge_item=ChargeItem(
            name="RoomStay", sku_category_id="cat_1", details=dict()
        ),
        charge_splits=charge_splits,
    )


@pytest.fixture
def consumed_charge_non_credit_treebo():
    charge_split_1 = ChargeSplit(
        charge_split_id=1,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        percentage=50,
    )

    charge_split_2 = ChargeSplit(
        charge_split_id=2,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        percentage=50,
    )

    charge_splits = [charge_split_1, charge_split_2]

    return Charge(
        charge_id=4,
        charge_to=["CST-23232", "CST-23233"],
        pretax_amount=Money(100, CurrencyType('INR')),
        tax_amount=Money(10, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money(5, CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money(5, CurrencyType('INR'))),
        ],
        posttax_amount=Money(110, CurrencyType('INR')),
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        status=ChargeStatus.CONSUMED,
        recorded_time=dateutils.localize_datetime(
            datetime.datetime.strptime('11Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        applicable_date=dateutils.localize_datetime(
            datetime.datetime.strptime('12Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        comment="Charge done",
        created_by="AUTH-12121",
        charge_item=ChargeItem(
            name="RoomStay", sku_category_id="cat_1", details=dict()
        ),
        charge_splits=charge_splits,
    )


@pytest.fixture
def created_charge_non_credit_guest():
    charge_split_1 = ChargeSplit(
        charge_split_id=1,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        percentage=50,
    )

    charge_split_2 = ChargeSplit(
        charge_split_id=2,
        charge_to=None,
        pre_tax=Money(50, CurrencyType('INR')),
        tax=Money(5, CurrencyType('INR')),
        post_tax=Money(55, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money('2.5', CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money('2.5', CurrencyType('INR'))),
        ],
        invoice_id=None,
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        percentage=50,
    )

    charge_splits = [charge_split_1, charge_split_2]

    return Charge(
        charge_id=4,
        charge_to=["CST-23232", "CST-23233"],
        pretax_amount=Money(100, CurrencyType('INR')),
        tax_amount=Money(10, CurrencyType('INR')),
        tax_details=[
            TaxDetail(TaxTypes.CGST, 5, Money(5, CurrencyType('INR'))),
            TaxDetail(TaxTypes.SGST, 5, Money(5, CurrencyType('INR'))),
        ],
        posttax_amount=Money(110, CurrencyType('INR')),
        charge_type=ChargeTypes.NON_CREDIT,
        bill_to_type=ChargeBillToTypes.GUEST,
        status=ChargeStatus.CREATED,
        recorded_time=dateutils.localize_datetime(
            datetime.datetime.strptime('11Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        applicable_date=dateutils.localize_datetime(
            datetime.datetime.strptime('12Jan2018', '%d%b%Y'),
            dateutils.local_timezone(),
        ),
        comment="Charge done",
        created_by="AUTH-12121",
        charge_item=ChargeItem(
            name="RoomStay",
            sku_category_id="cat_1",
            details={"room_stay_id": 1},
        ),
        charge_splits=charge_splits,
    )


@pytest.fixture
def bill_aggregate(
    phone_no,
    payment_in_done_state,
    payment_in_pending_state,
    payment_in_pending_state_treebo,
    consumed_charge_non_credit,
    consumed_charge_credit_company,
    consumed_charge_non_credit_company,
    consumed_charge_non_credit_treebo,
    booking_bill_parent_info,
    gst_details,
):
    from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate

    bill = Bill(
        bill_id="BIL-12123232334",
        bill_date=datetime.datetime.strptime('11Jan2018', '%d%b%Y'),
        app_id="ths",
        parent_reference_number="19-22323232-2323",
        vendor_id="12345",
        vendor_details=VendorDetails(
            name="Treebo Elmas",
            hotel_id="12345",
            state_code="02",
            gst_details=gst_details,
            phone=phone_no,
            email="<EMAIL>",
            url="www.treebotest.com",
        ),
        version=1,
        parent_info=booking_bill_parent_info,
    )

    payments = [
        payment_in_done_state,
        payment_in_pending_state,
        payment_in_pending_state_treebo,
    ]
    charges = [
        consumed_charge_non_credit,
        consumed_charge_credit_company,
        consumed_charge_non_credit_company,
        consumed_charge_non_credit_treebo,
    ]

    return BillAggregate(bill, payments, charges)


@pytest.fixture
def invoice_aggregate(bill_aggregate, address, phone_no, active_hotel_aggregate):
    charge_split_map = {2: [1, 2], 3: [1]}

    generated_by = '2324231'
    generation_channel = '2'
    parent_info = BookingBillParentInfo(
        booking_id='***********',
        reference_number='TEST_BOOKING_REF_1',
        creation_date=dateutils.current_date(),
        checkin_date=datetime.datetime.strptime('21Jun2018', '%d%b%Y'),
        checkout_date=datetime.datetime.strptime('23Jun2018', '%d%b%Y'),
    )

    bill_aggregate.update_parent_info(parent_info)
    bill_to_type = ChargeBillToTypes.COMPANY
    bill_to = InvoiceBillToInfo(
        customer_id='CMR-1242342323',
        name="ram",
        address=address,
        gstin="GST-34343",
        phone=phone_no,
        email="<EMAIL>",
    )
    user_info_map = dict()
    user_info_map["CST-23232"] = dict(name="Ram")
    user_info_map["CST-23233"] = dict(name="Shyam")

    invoice_date = datetime.datetime.strptime('25Jun2018', '%d%b%Y').date()

    hotel_context = crs_context.set_hotel_context(active_hotel_aggregate)
    vendor_details = hotel_context.build_vendor_details()
    issued_by = InvoiceIssuedByInfo(
        gst_details=vendor_details.gst_details,
        phone=vendor_details.phone,
        email=vendor_details.email,
        url=vendor_details.url,
        legal_signature=None,
    )
    return InvoiceFactory.create_preview_invoice(
        bill_aggregate=bill_aggregate,
        charge_split_map=charge_split_map,
        bill_to=bill_to,
        bill_to_type=bill_to_type,
        generated_by=generated_by,
        invoice_date=invoice_date,
        user_info_map=user_info_map,
        generation_channel=generation_channel,
        issued_by_type=IssuedByType.HOTEL,
        issued_to_type=IssuedToType.CUSTOMER,
        issued_by=issued_by,
    )


@pytest.fixture
def tenant_settings():
    return locate_instance(TenantSettings)


@pytest.fixture
def cleartax_client(tenant_settings):
    return ClearTaxClient(tenant_settings)


@pytest.fixture
def einvoicing_service(cleartax_client, tenant_settings):
    from prometheus.domain.billing.repositories import (
        CreditNoteRepository,
        InvoiceRepository,
    )

    invoice_repository = InvoiceRepository()
    credit_note_repository = CreditNoteRepository()
    return EInvoicingService(
        cleartax_client, invoice_repository, credit_note_repository, None, None
    )


@pytest.fixture
def catalog_service_client():
    return CatalogServiceClientMock()


class TestInvoiceConfirmationService(object):
    def test_confirm_invoice(
        self,
        bill_aggregate,
        invoice_aggregate,
        invoice_repo,
        invoice_series_repo,
        sku_category_repo,
        catalog_service_client,
        active_hotel_aggregate,
        einvoicing_service,
    ):
        hotel_context = HotelContext.create_new(active_hotel_aggregate)

        # Test the invoice aggregate belongs to the given bill aggregate
        assert bill_aggregate.bill.bill_id == invoice_aggregate.invoice.bill_id
        # Test invoice is in preview state
        assert invoice_aggregate.invoice.status == InvoiceStatus.PREVIEW
        # Invoice number should be none
        assert invoice_aggregate.invoice.invoice_number is None

        for c in bill_aggregate.charges:
            c.item.details['room_stay_id'] = 1

        vendor_details = hotel_context.build_vendor_details()
        issued_by = InvoiceIssuedByInfo(
            gst_details=vendor_details.gst_details,
            phone=vendor_details.phone,
            email=vendor_details.email,
            url=vendor_details.url,
            legal_signature=vendor_details.legal_signature,
        )
        with mock_einvoicing_service():
            bill_aggregate, invoice_aggregate, hotel_invoice = InvoiceService(
                invoice_series_repo, einvoicing_service, tenant_settings
            ).confirm_invoice(
                bill_aggregate,
                invoice_aggregate,
                dateutils.current_datetime(),
                hotel_context,
                IssuedByType.HOTEL,
                issued_by,
            )

        invoice_id = invoice_aggregate.invoice.invoice_id

        # Invoice preview update
        assert invoice_aggregate.invoice.status == InvoiceStatus.GENERATED

        # Invoice no should be updated
        if invoice_aggregate.invoice.is_einvoice:
            assert invoice_aggregate.invoice.invoice_number is not None

        charge_split_map = defaultdict(list)

        # Bill should be updated with invoice id
        for invoice_charge in invoice_aggregate.invoice_charges:
            charge_split_map[invoice_charge.charge_id].extend(
                invoice_charge.charge_split_ids
            )

        for charge in bill_aggregate.charges:
            charge_split_ids = charge_split_map.get(charge.charge_id)
            if charge_split_ids:
                for charge_split in charge.charge_splits:
                    if charge_split.charge_split_id in charge_split_ids:
                        assert charge_split.invoice_id == invoice_id
                    else:
                        # currently assumption is that this is the only invoice
                        assert charge_split.invoice_id is None

        # Negative test
        # confirm again should fail
        with pytest.raises(ValidationException):
            InvoiceService(
                invoice_series_repo, einvoicing_service, tenant_settings
            ).confirm_invoice(
                bill_aggregate,
                invoice_aggregate,
                dateutils.current_datetime(),
                hotel_context,
                IssuedByType.HOTEL,
                issued_by,
            )
