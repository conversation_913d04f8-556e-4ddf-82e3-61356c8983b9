import datetime
import json

import pytest
from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import locate_instance
from prometheus.application.end_of_day.night_audit_service import NightAuditService
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    checkin_booking,
    checkout_booking,
    make_booking,
)
from prometheus.itests.api_wrappers.expense_wrappers import add_expenses_v2
from prometheus.itests.api_wrappers.invoice_wrappers import preview_invoice
from prometheus.itests.booking.test_patch_booking_api import make_booking_v2
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkin_payload,
    create_checkout_payload,
)
from prometheus.itests.payload_generators.expense_payload_generators import (
    create_expenses_v2_request,
)
from prometheus.itests.payload_generators.invoice_payload_generators import (
    create_preview_invoice_payload,
    create_preview_invoice_with_allowance_decision_payload,
    create_preview_invoice_with_charge_decision_payload,
)
from prometheus.reporting.finance_erp_reporting.finance_reporting_service import (
    FinanceReportingService,
)
from prometheus.tests.mockers import (
    mock_authn_service_client,
    mock_authz_service_client,
    mock_catalog_client,
    mock_communication_service_client,
    mock_refund_rules,
    mock_trial_balance_reporting_service,
)
from ths_common.constants.billing_constants import (
    InvoiceStatus,
    IssuedByType,
    PaymentInstruction,
    PaymentModes,
    PaymentTypes,
)
from ths_common.constants.booking_constants import ExpenseStatus


def modify_invoice_payload(
    invoice_aggregate,
    bill_aggregate,
    transfer_payment_to_new_invoice_account: bool = True,
    posttax_amount="500.0 INR",
    account_number=2,
    billed_entity_id=3,
):
    return {
        "data": {
            "modify_invoices": [
                {
                    "invoice_charge_modification": [
                        {
                            "billed_entity_account": {
                                "account_number": account_number,
                                "billed_entity_id": billed_entity_id,
                            },
                            "invoice_charge_id": ic.invoice_charge_id,
                            "posttax_amount": posttax_amount,
                        }
                        for ic in invoice_aggregate.invoice_charges
                    ],
                    "invoice_id": invoice_aggregate.invoice_id,
                    "invoice_version": invoice_aggregate.invoice.version,
                }
            ],
            "transfer_payment_to_new_invoice_account": transfer_payment_to_new_invoice_account,
            "comment": "Created a New Credit Note",
        },
        "resource_version": bill_aggregate.bill.version,
    }


def modify_invoice_api_call(payload, bill_id, client, hotel_in_posttax=False):
    with mock_catalog_client(hotel_in_posttax=hotel_in_posttax), mock_refund_rules():
        url = 'v1/bills/' + bill_id + '/invoices/locked-invoice-modifications'
        headers = {'X-User-Type': 'super-admin'}
        response = client.post(
            url,
            data=json.dumps(payload),
            content_type='application/json',
            headers=headers,
        )
        return response


def reissue_locked_reseller_invoice_for_non_financial_changes(payload, bill_id, client):
    url = (
        'v1/bills/'
        + bill_id
        + '/invoices/reissue-locked-reseller-invoice-for-non-financial-changes'
    )
    headers = {'X-User-Type': 'super-admin'}
    response = client.post(
        url, data=json.dumps(payload), content_type='application/json', headers=headers
    )
    return response


def marked_invoices_locked(
    active_hotel_aggregate,
    booking_id,
    booking_invoice_group_repo,
    booking_repo,
    client,
    invoice_repo,
):
    booking_aggregate = booking_repo.load(booking_id)
    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)
    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    action_id = checkout_booking(client, booking_id, checkout_payload)
    booking_invoice_group_aggregate = booking_invoice_group_repo.load(invoice_group_id)
    invoice_ids = booking_invoice_group_aggregate.booking_invoice_group.invoice_ids
    invoice_aggregates = invoice_repo.load_all(invoice_ids)
    for invoice_aggregate in invoice_aggregates:
        invoice_aggregate.invoice.status = InvoiceStatus.LOCKED
    invoice_repo.update_all(invoice_aggregates)
    return action_id


def create_locked_invoices(
    active_hotel_aggregate,
    booking_id,
    booking_invoice_group_repo,
    booking_repo,
    client,
    hotel_repo,
    invoice_repo,
    seller_repo,
):
    booking_aggregate = booking_repo.load(booking_id)
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)
    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)
    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    checkout_booking(client, booking_id, checkout_payload)
    booking_invoice_group_aggregate = booking_invoice_group_repo.load(invoice_group_id)
    invoice_ids = booking_invoice_group_aggregate.booking_invoice_group.invoice_ids
    # commented perform night audit
    night_audit_service = locate_instance(NightAuditService)
    seller_aggregates = seller_repo.load_for_hotel_id(
        hotel_id=booking_aggregate.hotel_id
    )
    hotel_aggregate = hotel_repo.load_for_update(booking_aggregate.hotel_id)
    night_audit_service.schedule_night_audit(booking_aggregate.hotel_id)
    with mock_catalog_client(), mock_trial_balance_reporting_service():
        with mock_communication_service_client(), mock_authz_service_client(), mock_authn_service_client():
            night_audit_service._post_past_charges_and_payments_and_freeze_invoices(
                hotel_aggregate, seller_aggregates
            )
    assert invoice_repo.load(invoice_ids[0]).invoice.status == InvoiceStatus.LOCKED
    invoice_aggregates = invoice_repo.load_all(invoice_ids)
    return invoice_aggregates


def create_locked_invoices_with_charge_having_allowances(
    active_hotel_aggregate,
    booking_id,
    booking_invoice_group_repo,
    booking_repo,
    client,
    hotel_repo,
    invoice_repo,
    seller_repo,
    bill_repo,
):
    booking_aggregate = booking_repo.load(booking_id)
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)
    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    bill_aggregate.consume_charges(
        charges=[bill_aggregate.get_charge(1)],
        business_date=active_hotel_aggregate.hotel.current_business_date,
    )
    bill_repo.update(bill_aggregate)
    bill_id = bill_aggregate.bill_id
    add_allowance_url = 'v1/bills/' + bill_id + '/charges/1/charge-splits/1/allowances'
    new_allowance = dict(
        data=dict(pretax_amount='50 INR', remarks='add new allowance'),
        resource_version=bill_aggregate.current_version(),
    )
    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        add_allowance_url,
        data=json.dumps(new_allowance),
        content_type='application/json',
        headers=headers,
    )

    preview_invoice_payload = create_preview_invoice_with_allowance_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)
    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    checkout_booking(client, booking_id, checkout_payload)
    booking_invoice_group_aggregate = booking_invoice_group_repo.load(invoice_group_id)
    invoice_ids = booking_invoice_group_aggregate.booking_invoice_group.invoice_ids
    # commented perform night audit
    night_audit_service = locate_instance(NightAuditService)
    seller_aggregates = seller_repo.load_for_hotel_id(
        hotel_id=booking_aggregate.hotel_id
    )
    hotel_aggregate = hotel_repo.load_for_update(booking_aggregate.hotel_id)
    night_audit_service.schedule_night_audit(booking_aggregate.hotel_id)
    with mock_catalog_client(), mock_trial_balance_reporting_service():
        with mock_communication_service_client(), mock_authz_service_client(), mock_authn_service_client():
            night_audit_service._post_past_charges_and_payments_and_freeze_invoices(
                hotel_aggregate, seller_aggregates
            )
    assert invoice_repo.load(invoice_ids[0]).invoice.status == InvoiceStatus.LOCKED
    invoice_aggregates = invoice_repo.load_all(invoice_ids)
    return invoice_aggregates


@pytest.fixture
def locked_invoices(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    return create_locked_invoices(
        active_hotel_aggregate,
        booking_id,
        booking_invoice_group_repo,
        booking_repo,
        client,
        hotel_repo,
        invoice_repo,
        seller_repo,
    )


@pytest.fixture
def reseller_locked_invoices(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
):
    create_booking_payload = json.loads(create_booking_payload)
    create_booking_payload['booking_owner']['profile_type'] = 'sme'
    booking_id = make_booking(client, {"data": create_booking_payload})
    return create_locked_invoices(
        active_hotel_aggregate,
        booking_id,
        booking_invoice_group_repo,
        booking_repo,
        client,
        hotel_repo,
        invoice_repo,
        seller_repo,
    )


@pytest.fixture
def reseller_locked_invoices_with_inclusion(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
):
    create_booking_payload = json.loads(create_booking_payload)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "345",
            "start_date": dateutils.current_datetime().isoformat(),
            "end_date": (dateutils.current_datetime()).isoformat(),
        }
    ]
    create_booking_payload['payments'][0]["amount"] = '500'
    create_booking_payload['booking_owner']['profile_type'] = 'sme'
    booking_id = make_booking_v2(client, {"data": create_booking_payload})
    return create_locked_invoices(
        active_hotel_aggregate,
        booking_id,
        booking_invoice_group_repo,
        booking_repo,
        client,
        hotel_repo,
        invoice_repo,
        seller_repo,
    )


@pytest.fixture
def reseller_locked_invoices_with_charge_having_allowances(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
    bill_repo,
):
    create_booking_payload = json.loads(create_booking_payload)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "posttax_amount": "0",
            "start_date": dateutils.current_datetime().isoformat(),
            "end_date": (dateutils.current_datetime()).isoformat(),
        }
    ]
    create_booking_payload['payments'][0]["amount"] = '500'
    create_booking_payload['booking_owner']['profile_type'] = 'sme'
    booking_id = make_booking_v2(client, {"data": create_booking_payload})
    return create_locked_invoices_with_charge_having_allowances(
        active_hotel_aggregate,
        booking_id,
        booking_invoice_group_repo,
        booking_repo,
        client,
        hotel_repo,
        invoice_repo,
        seller_repo,
        bill_repo,
    )


def test_modify_locked_invoices_generates_credit_note_with_same_billed_entity_and_different_account(
    locked_invoices, bill_repo, client
):
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=locked_invoices[0],
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )

    response = modify_invoice_api_call(
        modify_invoice_request, locked_invoices[0].bill_id, client
    )

    assert response.status_code == 200
    response_data = response.json
    invoice_billed_entity_account = locked_invoices[0].invoice.billed_entity_account
    credit_note_billed_entity_account = response_data['data']['credit_notes'][0][
        'billed_entity_account'
    ]
    assert (
        invoice_billed_entity_account.billed_entity_id
        == credit_note_billed_entity_account['billed_entity_id']
    )
    assert (
        invoice_billed_entity_account.account_number
        != credit_note_billed_entity_account['account_number']
    )


def test_modify_locked_invoices_generates_credit_note_equals_to_invoice_amount(
    locked_invoices, bill_repo, client, credit_note_repo
):
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=locked_invoices[0],
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )

    response = modify_invoice_api_call(
        modify_invoice_request, locked_invoices[0].bill_id, client
    )

    assert response.status_code == 200
    response_data = response.json
    credit_note_dict = response_data['data']['credit_notes'][0]
    credit_note_aggregate = credit_note_repo.load(
        credit_note_id=credit_note_dict['credit_note_id']
    )
    invoice = locked_invoices[0].invoice

    assert invoice.pretax_amount == credit_note_aggregate.credit_note.pretax_amount
    assert invoice.posttax_amount == credit_note_aggregate.credit_note.posttax_amount


def test_modify_locked_invoices_generates_credit_note_with_correct_line_items(
    locked_invoices, bill_repo, client, credit_note_repo, booking_repo
):
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=locked_invoices[0],
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )

    response = modify_invoice_api_call(
        modify_invoice_request, locked_invoices[0].bill_id, client
    )

    assert response.status_code == 200
    response_data = response.json
    credit_note_dict = response_data['data']['credit_notes'][0]
    credit_note_aggregate = credit_note_repo.load(
        credit_note_id=credit_note_dict['credit_note_id']
    )
    invoice_aggregate = locked_invoices[0]
    invoice_charges = sorted(
        invoice_aggregate.invoice_charges, key=lambda ic: ic.invoice_charge_id
    )
    credit_note_line_items = sorted(
        credit_note_aggregate.credit_note_line_items,
        key=lambda cl: cl.invoice_charge_id,
    )
    assert len(invoice_charges) == len(credit_note_line_items)
    assert all(
        line_item.invoice_id == invoice_aggregate.invoice_id
        for line_item in credit_note_line_items
    )

    assert all(
        ic.invoice_charge_id == cl.invoice_charge_id
        for ic, cl in zip(invoice_charges, credit_note_line_items)
    )

    assert all(
        ic.posttax_amount == cl.posttax_amount
        for ic, cl in zip(invoice_charges, credit_note_line_items)
    )


def test_modify_locked_invoices_updates_credit_note_amount_in_invoice_line_items(
    locked_invoices, bill_repo, client, credit_note_repo, invoice_repo
):
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=locked_invoices[0],
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )

    response = modify_invoice_api_call(
        modify_invoice_request, locked_invoices[0].bill_id, client
    )

    assert response.status_code == 200
    response_data = response.json
    credit_note_dict = response_data['data']['credit_notes'][0]
    invoice_aggregate = locked_invoices[0]
    invoice_aggregate = invoice_repo.load(invoice_id=invoice_aggregate.invoice_id)

    assert all(
        ic.posttax_amount == ic.credit_note_generated_amount
        for ic in invoice_aggregate.invoice_charges
    )


def test_should_not_allow_modification_of_already_nullified_invoice(
    locked_invoices, bill_repo, client, credit_note_repo, invoice_repo
):
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=locked_invoices[0],
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )

    response = modify_invoice_api_call(
        modify_invoice_request, locked_invoices[0].bill_id, client
    )

    assert response.status_code == 200
    invoice_aggregate = invoice_repo.load(invoice_id=locked_invoices[0].invoice_id)
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)
    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=invoice_aggregate,
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )
    response = modify_invoice_api_call(
        modify_invoice_request, locked_invoices[0].bill_id, client
    )
    assert response.status_code == 400


def test_modify_locked_invoices_adds_charge_to_new_account(
    locked_invoices, bill_repo, client, credit_note_repo
):
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=locked_invoices[0],
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )

    response = modify_invoice_api_call(
        modify_invoice_request,
        locked_invoices[0].bill_id,
        client,
        hotel_in_posttax=True,
    )

    assert response.status_code == 200
    new_bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)
    request_billed_entity_account = modify_invoice_request['data']['modify_invoices'][
        0
    ]['invoice_charge_modification'][0]['billed_entity_account']

    # Charges and posttax amount added for new billed entity account
    assert len(bill_aggregate.active_charges) + 1 == len(
        new_bill_aggregate.active_charges
    )

    added_charge = new_bill_aggregate.get_charge(2)
    added_charge_amount_posttax = modify_invoice_request['data']['modify_invoices'][0][
        'invoice_charge_modification'
    ][0]['posttax_amount']
    assert (
        added_charge.charge_splits[0].billed_entity_account.billed_entity_id
        == request_billed_entity_account['billed_entity_id']
    )
    assert (
        added_charge.charge_splits[0].billed_entity_account.account_number
        == request_billed_entity_account['account_number']
    )
    assert added_charge.charge_splits[0].post_tax == Money(added_charge_amount_posttax)


def test_modify_locked_invoices_adds_payment_using_credit_shell_generated_by_current_bill(
    locked_invoices, bill_repo, client, credit_note_repo, credit_shell_repo
):
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=locked_invoices[0],
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )

    response = modify_invoice_api_call(
        modify_invoice_request, locked_invoices[0].bill_id, client
    )
    assert response.status_code == 200
    new_bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

    # checking payments using credit shell by same bill id
    payments = new_bill_aggregate.payments

    credit_shell_id = None
    for payment in payments:
        if payment.payment_mode == 'credit_shell':
            credit_shell_id = payment.payment_ref_id

    credit_shell_aggregate = credit_shell_repo.load(credit_shell_id=credit_shell_id)
    assert credit_shell_aggregate.credit_shell.bill_id == bill_aggregate.bill_id


def test_modify_locked_invoices_check_balance_of_bill_after_payment_and_refund(
    locked_invoices, bill_repo, client, credit_note_repo, credit_shell_repo
):
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=locked_invoices[0],
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )

    response = modify_invoice_api_call(
        modify_invoice_request, locked_invoices[0].bill_id, client
    )
    assert response.status_code == 200
    new_bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

    assert (
        new_bill_aggregate.summary.credit_summary.total_credit
        == bill_aggregate.summary.credit_summary.total_credit
    )


def test_modify_locked_invoices_check_paid_by_and_paid_to_after_payment_and_refund(
    locked_invoices, bill_repo, client, credit_note_repo, credit_shell_repo
):
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)
    initial_payment = bill_aggregate.get_payment(1)
    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=locked_invoices[0],
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )

    response = modify_invoice_api_call(
        modify_invoice_request, locked_invoices[0].bill_id, client
    )
    assert response.status_code == 200
    new_bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

    refund_via_credit_shell = new_bill_aggregate.get_payment(2)
    payment_via_credit_shell = new_bill_aggregate.get_payment(3)
    assert refund_via_credit_shell.payment_mode == PaymentModes.CREDIT_SHELL
    assert payment_via_credit_shell.payment_mode == PaymentModes.CREDIT_SHELL
    assert refund_via_credit_shell.paid_to == initial_payment.paid_by
    assert refund_via_credit_shell.paid_by == initial_payment.paid_to
    assert payment_via_credit_shell.paid_by == initial_payment.paid_by
    assert payment_via_credit_shell.paid_to == initial_payment.paid_to


def test_modify_locked_invoices_adds_payment_using_credit_shell(
    locked_invoices, bill_repo, client, credit_note_repo, credit_shell_repo
):
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=locked_invoices[0],
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )

    response = modify_invoice_api_call(
        modify_invoice_request, locked_invoices[0].bill_id, client
    )
    assert response.status_code == 200
    new_bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)
    # check credit shell generated for the bill_id , payment & refund done by credit shell

    # checking payments
    payment_refund = None
    payment_done = None

    payments = new_bill_aggregate.payments

    for payment in payments:
        if payment.payment_mode == 'credit_shell':
            if payment.payment_type == PaymentTypes.PAYMENT:
                payment_done = payment
            elif payment.payment_type == PaymentTypes.REFUND:
                payment_refund = payment

    assert payment_refund
    assert payment_done


def test_modify_locked_invoices_adds_payment_on_basic_of_param(
    locked_invoices, bill_repo, client, credit_note_repo, credit_shell_repo
):
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=locked_invoices[0],
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=False,
    )

    response = modify_invoice_api_call(
        modify_invoice_request, locked_invoices[0].bill_id, client
    )
    assert response.status_code == 200
    credit_notes = credit_note_repo.load_for_bill_id(bill_id=locked_invoices[0].bill_id)
    assert all(
        c.comment is not None for c in credit_notes
    ), "Comment field must be non-None"
    new_bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

    # checking payments

    assert len(new_bill_aggregate.payments) == len(bill_aggregate.payments) + 1

    payment_refund = None
    payment_done = None

    payments = new_bill_aggregate.payments

    for payment in payments:
        if payment.payment_mode == 'credit_shell':
            if payment.payment_type == PaymentTypes.PAYMENT:
                payment_done = payment
            elif payment.payment_type == PaymentTypes.REFUND:
                payment_refund = payment

    assert payment_refund
    assert not payment_done

    # credit shell is_refundable_folio validation
    credit_shell = credit_shell_repo.load_for_bill_id(
        bill_id=locked_invoices[0].bill_id
    )
    for cs in credit_shell:
        assert cs.credit_shell.is_refundable_folio == True

    def test_preview_invoices(
        active_hotel_aggregate,
        create_booking_payload,
        client,
        booking_repo,
        hotel_repo,
        booking_invoice_group_repo,
        invoice_repo,
        seller_repo,
    ):
        booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
        booking_aggregate = booking_repo.load(booking_id)

        # Rollover Business Date
        roll_over_business_date(active_hotel_aggregate, hotel_repo)

        checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
        checkin_booking(client, booking_id, checkin_payload)
        booking_aggregate = booking_repo.load(booking_id)

        expense_v2_request = create_expenses_v2_request(
            payment_instruction=PaymentInstruction.PAY_AT_CHECKOUT,
            expense_status=ExpenseStatus.CREATED,
            single_charge_split=True,
            charge_to=['2'],
        )
        add_expenses_v2(client, booking_id, expense_v2_request)

        preview_invoice_payload = create_preview_invoice_payload(
            booking_aggregate.booking.version, active_hotel_aggregate
        )
        response = preview_invoice(
            booking_id, client, preview_invoice_payload, show_raw_response=True
        )
        preview_invoice_payload_with_decision = (
            create_preview_invoice_with_charge_decision_payload(
                booking_aggregate.booking.version, active_hotel_aggregate
            )
        )
        response = preview_invoice(
            booking_id,
            client,
            preview_invoice_payload_with_decision,
            show_raw_response=True,
        )

    def test_preview_invoices_after_adding_allowance(
        active_hotel_aggregate,
        create_booking_payload,
        client,
        booking_repo,
        hotel_repo,
        bill_repo,
        booking_invoice_group_repo,
        invoice_repo,
        seller_repo,
    ):
        booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
        booking_aggregate = booking_repo.load(booking_id)

        # Rollover Business Date
        roll_over_business_date(active_hotel_aggregate, hotel_repo)

        checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
        checkin_booking(client, booking_id, checkin_payload)
        booking_aggregate = booking_repo.load(booking_id)

        expense_v2_request = create_expenses_v2_request(
            payment_instruction=PaymentInstruction.PAY_AT_CHECKOUT,
            expense_status=ExpenseStatus.CREATED,
            single_charge_split=True,
        )
        response = add_expenses_v2(client, booking_id, expense_v2_request)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        bill_aggregate.consume_charges(
            charges=[
                bill_aggregate.get_charge(response.get('data')[0].get('charge_id'))
            ],
            business_date=active_hotel_aggregate.hotel.current_business_date,
        )
        bill_repo.update(bill_aggregate)
        bill_id, charge_id = bill_aggregate.bill_id, bill_aggregate.charges[0].charge_id
        charge_split_id = bill_aggregate.charges[0].charge_splits[0].charge_split_id

        add_allowance_url = (
            'v1/bills/'
            + bill_id
            + '/charges/'
            + str(response.get('data')[0].get('charge_id'))
            + '/charge-splits/1/allowances'
        )
        new_allowance = dict(
            data=dict(pretax_amount='50 INR', remarks='add new allowance'),
            resource_version=bill_aggregate.current_version(),
        )
        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            add_allowance_url,
            data=json.dumps(new_allowance),
            content_type='application/json',
            headers=headers,
        )

        preview_invoice_payload = create_preview_invoice_payload(
            booking_aggregate.booking.version, active_hotel_aggregate
        )
        response = preview_invoice(
            booking_id, client, preview_invoice_payload, show_raw_response=True
        )
        preview_invoice_payload_with_decision = (
            create_preview_invoice_with_charge_decision_payload(
                booking_aggregate.booking.version, active_hotel_aggregate
            )
        )
        response = preview_invoice(
            booking_id,
            client,
            preview_invoice_payload_with_decision,
            show_raw_response=True,
        )


def get_invoices(bill_id, client):
    url = f"v2/bills/{bill_id}/invoices"
    response = client.get(
        url,
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    assert response.status_code == 200
    return response.json


def get_credit_notes(bill_id, client):
    url = f"v2/bills/{bill_id}/credit-notes"
    response = client.get(
        url,
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    assert response.status_code == 200
    return response.json


def test_modify_locked_invoices_for_financial_transaction(
    reseller_locked_invoices, bill_repo, client, invoice_repo, credit_note_repo
):
    locked_invoices = reseller_locked_invoices
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)
    all_invoices = invoice_repo.load_for_bill_id(
        bill_id=locked_invoices[0].bill_id, exclude_issued_to_reseller=False
    )
    sell_side_invoice = None
    for invoice_agg in all_invoices:
        if invoice_agg.invoice.issued_by_type == IssuedByType.RESELLER:
            sell_side_invoice = invoice_agg
    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=sell_side_invoice,
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )

    response = modify_invoice_api_call(
        modify_invoice_request, sell_side_invoice.bill_id, client
    )
    assert response.status_code == 200
    response_data = response.json
    invoice_billed_entity_account = sell_side_invoice.invoice.billed_entity_account
    credit_note_billed_entity_account = response_data['data']['credit_notes'][0][
        'billed_entity_account'
    ]
    assert (
        invoice_billed_entity_account.billed_entity_id
        == credit_note_billed_entity_account['billed_entity_id']
    )
    assert (
        invoice_billed_entity_account.account_number
        != credit_note_billed_entity_account['account_number']
    )
    credit_notes = credit_note_repo.load_for_bill_id(
        bill_id=locked_invoices[0].bill_id, exclude_issued_to_reseller=False
    )
    all_invoices = invoice_repo.load_for_bill_id(
        bill_id=locked_invoices[0].bill_id, exclude_issued_to_reseller=False
    )
    buy_side_invoice_number = None
    buy_side_cn_number = None
    for invoice_agg in all_invoices:
        if invoice_agg.invoice.issued_by_type == IssuedByType.HOTEL:
            buy_side_invoice_number = invoice_agg.invoice_number
    for cn_agg in credit_notes:
        if cn_agg.credit_note.issued_by_type == IssuedByType.HOTEL:
            buy_side_cn_number = cn_agg.credit_note_number
    assert len(credit_notes) == 2
    invoice_response = get_invoices(bill_aggregate.bill_id, client)["data"]
    cn_response = get_credit_notes(bill_aggregate.bill_id, client)["data"]
    assert invoice_response[0]["hotel_invoice_number"] is not None
    assert invoice_response[0]["hotel_invoice_number"] == buy_side_invoice_number
    assert cn_response[0]["hotel_credit_note_number"] is not None
    assert cn_response[0]["hotel_credit_note_number"] == buy_side_cn_number


def update_company_details(booking_id, client):
    payload = {
        "data": {
            "company_details": {
                "legal_details": {
                    "legal_name": "Updated",
                    "email": "<EMAIL>",
                    "phone": {"country_code": "string", "number": "123123"},
                    "address": {
                        "city": "string",
                        "country": "string",
                        "field_1": "string",
                        "field_2": "string",
                        "pincode": "123123",
                        "state": "string",
                    },
                    "tin": "12ABCDE0000A1ZM",
                    "is_sez": True,
                    "has_lut": True,
                    "client_internal_code": "company-client-code",
                    "external_reference_id": "updated_external_code",
                }
            }
        },
        "resource_version": 1,
    }
    from prometheus.itests.api_wrappers.booking_wrappers import patch_booking

    patch_booking(client, booking_id, payload)


def test_modify_locked_invoices_after_corporate_update_cn_should_have_corp_code_of_invoice(
    reseller_locked_invoices,
    bill_repo,
    client,
    invoice_repo,
    credit_note_repo,
    booking_repo,
    hotel_repo,
    room_type_repo,
    sku_category_repo,
):
    locked_invoices = reseller_locked_invoices
    finance_reporting_service = FinanceReportingService(
        booking_repository=booking_repo,
        bill_repository=bill_repo,
        invoice_repository=invoice_repo,
        hotel_repository=hotel_repo,
        room_type_repository=room_type_repo,
        sku_category_repository=sku_category_repo,
        job_registry=None,
        credit_note_repository=credit_note_repo,
        expense_item_repository=None,
        job_scheduler_service=None,
        reporting_job_publisher=None,
        einvoicing_service=None,
        marvin_service_client=None,
        payment_service_client=None,
        finance_service_client=None,
        financial_data_reporting_service=None,
        invoice_report_repository=None,
        cn_report_repository=None,
        company_profile_service_client=None,
        tenant_settings=None,
    )
    date = dateutils.date_to_ymd_str(datetime.datetime.now().date())
    (
        reports_before_company_update,
        _,
    ) = finance_reporting_service._generate_invoice_report_aggregate_data(date)
    invoice_one_before_credit_note = reports_before_company_update[0]
    booking = booking_repo.load_booking_by_bill_id(locked_invoices[0].bill_id)
    update_company_details(booking.booking_id, client)
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)
    all_invoices = invoice_repo.load_for_bill_id(
        bill_id=locked_invoices[0].bill_id, exclude_issued_to_reseller=False
    )
    sell_side_invoice = None
    for invoice_agg in all_invoices:
        if invoice_agg.invoice.issued_by_type == IssuedByType.RESELLER:
            sell_side_invoice = invoice_agg
    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=sell_side_invoice,
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )
    response = modify_invoice_api_call(
        modify_invoice_request, sell_side_invoice.bill_id, client
    )
    assert response.status_code == 200
    (
        reports_after_company_update_and_reissue,
        _,
    ) = finance_reporting_service._generate_invoice_report_aggregate_data(date)
    invoice_one_after_reissue = [
        inv
        for inv in reports_after_company_update_and_reissue
        if inv.invoice_number == invoice_one_before_credit_note.invoice_number
    ][0]
    credit_note_one = [
        inv
        for inv in reports_after_company_update_and_reissue
        if inv.invoice_status == 'credit_note'
    ][0]
    assert (
        invoice_one_after_reissue.corporate_id
        == credit_note_one.corporate_id
        == invoice_one_before_credit_note.corporate_id
    )


def test_modify_reseller_locked_invoices_for_non_financial_transaction(
    reseller_locked_invoices, bill_repo, client, invoice_repo, credit_note_repo
):
    locked_invoices = reseller_locked_invoices
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)
    all_invoices = invoice_repo.load_for_bill_id(
        bill_id=locked_invoices[0].bill_id, exclude_issued_to_reseller=False
    )
    sell_side_invoice = None
    for invoice_agg in all_invoices:
        if invoice_agg.invoice.issued_by_type == IssuedByType.RESELLER:
            sell_side_invoice = invoice_agg
    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=sell_side_invoice,
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )

    response = reissue_locked_reseller_invoice_for_non_financial_changes(
        modify_invoice_request, sell_side_invoice.bill_id, client
    )
    assert response.status_code == 200
    response_data = response.json
    invoice_billed_entity_account = sell_side_invoice.invoice.billed_entity_account
    credit_note_billed_entity_account = response_data['data']['credit_notes'][0][
        'billed_entity_account'
    ]
    assert (
        invoice_billed_entity_account.billed_entity_id
        == credit_note_billed_entity_account['billed_entity_id']
    )
    assert (
        invoice_billed_entity_account.account_number
        != credit_note_billed_entity_account['account_number']
    )
    credit_notes = credit_note_repo.load_for_bill_id(
        bill_id=locked_invoices[0].bill_id, exclude_issued_to_reseller=False
    )
    assert len(credit_notes) == 1
    # only sell side is nullified  via credit note


def test_clubbed_inclusion_modify_reseller_locked_invoices_for_non_financial_transaction(
    reseller_locked_invoices_with_inclusion,
    bill_repo,
    client,
    invoice_repo,
    credit_note_repo,
):
    locked_invoices = reseller_locked_invoices_with_inclusion
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)
    all_invoices = invoice_repo.load_for_bill_id(
        bill_id=locked_invoices[0].bill_id, exclude_issued_to_reseller=False
    )
    sell_side_invoice = None
    for invoice_agg in all_invoices:
        if invoice_agg.invoice.issued_by_type == IssuedByType.RESELLER:
            sell_side_invoice = invoice_agg
    assert len(bill_aggregate.charges) == 2
    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=sell_side_invoice,
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )

    response = reissue_locked_reseller_invoice_for_non_financial_changes(
        modify_invoice_request, sell_side_invoice.bill_id, client
    )
    assert response.status_code == 200
    response_data = response.json
    invoice_billed_entity_account = sell_side_invoice.invoice.billed_entity_account
    credit_note_billed_entity_account = response_data['data']['credit_notes'][0][
        'billed_entity_account'
    ]
    assert (
        invoice_billed_entity_account.billed_entity_id
        == credit_note_billed_entity_account['billed_entity_id']
    )
    assert (
        invoice_billed_entity_account.account_number
        != credit_note_billed_entity_account['account_number']
    )
    credit_notes = credit_note_repo.load_for_bill_id(
        bill_id=locked_invoices[0].bill_id, exclude_issued_to_reseller=False
    )
    assert len(credit_notes) == 1
    bill_aggregate = bill_repo.load(locked_invoices[0].bill_id)
    assert len(bill_aggregate.charges) == 3

    # for refund reason and type
    assert bill_aggregate.get_payment(2).refund_reason is not None
