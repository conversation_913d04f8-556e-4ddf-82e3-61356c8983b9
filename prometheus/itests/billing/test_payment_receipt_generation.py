import json

import pytest
from treebo_commons.utils import dateutils

from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    checkin_booking,
    make_booking,
)
from prometheus.itests.billing.test_payment_api import create_cash_register_and_session
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.tests.mockers import (
    mock_aws_service_client,
    mock_rule_engine,
    mock_template_service,
)
from ths_common.constants.billing_constants import (
    CashRegisterNames,
    PaymentChannels,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.booking_constants import BookingChannels, BookingStatus


@pytest.fixture
def open_booking_and_bill(booking_repo, booking_and_bill):
    booking_aggregate = booking_and_bill[0]
    booking_aggregate.booking.status = BookingStatus.CONFIRMED
    booking_repo.update(booking_aggregate)
    return booking_and_bill


def test_create_payment_should_create_payment_receipt(
    client, open_booking_and_bill, bill_repo, payment_receipt_repo
):
    bill_aggregate = open_booking_and_bill[1]

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='8165.7700',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )

    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )

    headers = {"X-User-Type": "fdm"}
    with mock_rule_engine():
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200
    payment = response.json.get('data')

    payment_id = payment.get('payment_id')
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)

    payment_receipt_aggregate = payment_receipt_repo.load(
        payment_id, bill_aggregate.bill.bill_id
    )

    assert payment_receipt_aggregate is not None
    assert payment_receipt_aggregate.payment_receipt.payment_receipt_number is not None


def test_create_payment_in_sync_should_create_receipt_with_url(
    client, open_booking_and_bill, bill_repo, payment_receipt_repo
):
    bill_aggregate = open_booking_and_bill[1]

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='8165.7700',
            amount_in_payment_currency="444 USD",
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_ref_id="payment_ref",
            comment="remarks",
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
            show_receipt=True,
        ),
        resource_version=1,
    )

    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )

    headers = {"X-User-Type": "fdm"}
    sample_signed_url = "http://example.com/"
    with mock_rule_engine(), mock_template_service(), mock_aws_service_client(
        sample_signed_url
    ):
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200
    payment = response.json.get('data')
    payment_id = payment.get('payment_id')
    payment_receipt_aggregate = payment_receipt_repo.load(
        payment_id, bill_aggregate.bill.bill_id
    )

    assert payment_receipt_aggregate is not None
    assert payment_receipt_aggregate.payment_receipt.payment_receipt_number is not None
    assert (
        payment_receipt_aggregate.payment_receipt.payment_receipt_url
        == "mock_itest_url"
    )
    assert payment['payment_receipt_url'] == sample_signed_url


def test_create_refund_in_sync_should_create_receipt_with_url(
    client, open_booking_and_bill, bill_repo, payment_receipt_repo
):
    bill_aggregate = open_booking_and_bill[1]

    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    new_payment = dict(
        data=dict(
            amount='8165.7700',
            amount_in_payment_currency="444 USD",
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_ref_id="payment_ref",
            comment="remarks",
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            status=PaymentStatus.DONE.value,
            show_receipt=True,
        ),
        resource_version=1,
    )

    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )

    headers = {"X-User-Type": "fdm"}
    sample_signed_url = "http://example.com/"
    with mock_rule_engine(), mock_aws_service_client(
        sample_signed_url
    ), mock_template_service():
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )

    new_refund = dict(
        data=dict(
            amount='70.7700',
            amount_in_payment_currency="1 USD",
            paid_to=PaymentReceiverTypes.GUEST,
            paid_by=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_ref_id="payment_ref",
            comment="remarks",
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.REFUND.value,
            status=PaymentStatus.DONE.value,
            show_receipt=True,
        ),
        resource_version=1,
    )

    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )

    with mock_rule_engine(), mock_aws_service_client(
        sample_signed_url
    ), mock_template_service():
        response = client.post(
            url,
            data=json.dumps(new_refund),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200
    refund = response.json.get('data')
    refund_id = refund.get('payment_id')
    refund_receipt_aggregate = payment_receipt_repo.load(
        refund_id, bill_aggregate.bill.bill_id
    )

    assert refund_receipt_aggregate is not None
    assert refund_receipt_aggregate.payment_receipt.payment_receipt_number is not None
    assert (
        refund_receipt_aggregate.payment_receipt.payment_receipt_url == "mock_itest_url"
    )
    assert refund['payment_receipt_url'] == sample_signed_url


def create_checkin_payload_with_edit_guest_details(
    booking_version,
    checkin_datetime=None,
    room_id="15",
    guest_id="2",
    guest_stay_id="1",
):
    checkin_datetime = (
        dateutils.current_datetime() if checkin_datetime is None else checkin_datetime
    )
    return {
        "data": {
            "action_type": "checkin",
            "payload": {
                "checkin": {
                    "room_stays": [
                        {
                            "guest_stays": [
                                {
                                    "checkin_date": checkin_datetime.isoformat(),
                                    "guest_id": guest_id,
                                    "guest_stay_id": guest_stay_id,
                                }
                            ],
                            "room_allocation": {
                                "room_id": room_id,
                            },
                            "room_stay_id": "1",
                        }
                    ]
                }
            },
        },
        "resource_version": booking_version,
    }


def get_room_number(room_repo, booking_aggregate, room_id):
    room_aggregate = room_repo.load(
        room_id=room_id, hotel_id=booking_aggregate.booking.hotel_id
    )
    return room_aggregate.room.room_number


# room nos are recorded in payment receipt if room allocation is present for billed entity who made the payment
def test_create_payment_with_billed_entity_should_create_payment_receipt_with_affected_room_nos(
    client,
    booking_repo,
    bill_repo,
    payment_receipt_repo,
    room_repo,
    create_booking_payload,
    active_hotel_aggregate,
    hotel_repo,
):
    payload = {"data": json.loads(create_booking_payload)}
    booking_id = make_booking(client, payload)
    booking_aggregate = booking_repo.load(booking_id)
    bill_id = booking_aggregate.booking.bill_id
    bill_aggregate = bill_repo.load(bill_id)
    booking_id = booking_aggregate.booking.booking_id

    # do check_in to create room allocation
    roll_over_business_date(active_hotel_aggregate, hotel_repo)
    checkin_payload = create_checkin_payload_with_edit_guest_details(
        booking_aggregate.booking.version, room_id="15", guest_id="2", guest_stay_id="1"
    )
    checkin_booking(client, booking_id, checkin_payload)

    # add payment with split, room number of guest who has BE "2" will be recorded in receipt
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    payment_splits = [
        dict(
            amount='8165.7700',
            billed_entity_account=dict(account_number=1, billed_entity_id=2),
        )
    ]
    new_payment = dict(
        data=dict(
            amount='8165.7700',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            payment_splits=payment_splits,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )

    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )

    headers = {"X-User-Type": "fdm"}
    with mock_rule_engine():
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )

    payment_id = response.json.get('data').get('payment_id')
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)

    # get the room number using room id
    room_number = get_room_number(room_repo, booking_aggregate, room_id="15")
    payment_receipt_aggregate = payment_receipt_repo.load(
        payment_id, bill_aggregate.bill.bill_id
    )

    payment_receipt = payment_receipt_aggregate.payment_receipt

    assert room_number in payment_receipt.affected_room_nos


# This is for booking where guest name and owner name same
# room nos are recorded in payment receipt if room allocation is present and guest name
# matches with owner name + billed entity present for owner in payment split
def test_create_payment_with_billed_entity_should_create_payment_receipt_with_affected_room_nos_case2(
    client,
    booking_repo,
    bill_repo,
    payment_receipt_repo,
    room_repo,
    active_hotel_aggregate,
    hotel_repo,
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=0)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=1)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
        channel=BookingChannels.DIRECT.value,
        make_booking_owner_name_and_guest_name_same=True,
    )
    del create_booking_payload['booking_owner']['gst_details']
    payload = {"data": create_booking_payload}
    booking_id = make_booking(client, payload)
    booking_aggregate = booking_repo.load(booking_id)
    bill_id = booking_aggregate.booking.bill_id
    bill_aggregate = bill_repo.load(bill_id)
    booking_id = booking_aggregate.booking.booking_id

    # do check_in to create room allocation
    roll_over_business_date(active_hotel_aggregate, hotel_repo)
    checkin_payload = create_checkin_payload_with_edit_guest_details(
        booking_aggregate.booking.version, room_id="15", guest_id="1", guest_stay_id="1"
    )
    checkin_booking(client, booking_id, checkin_payload)

    # add payment split to owner BE "1"
    # since guest name matches with owner name, corresponding room no of guest
    # who's name matches with owner  will be recorded in receipt
    url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
    payment_splits = [
        dict(
            amount='8165.7700',
            billed_entity_account=dict(account_number=1, billed_entity_id=1),
        )
    ]
    new_payment = dict(
        data=dict(
            amount='8165.7700',
            paid_by=PaymentReceiverTypes.GUEST,
            paid_to=PaymentReceiverTypes.HOTEL,
            payment_channel=PaymentChannels.FRONT_DESK,
            payment_mode=PaymentModes.CASH,
            payment_type=PaymentTypes.PAYMENT.value,
            payment_splits=payment_splits,
            status=PaymentStatus.DONE.value,
        ),
        resource_version=1,
    )

    create_cash_register_and_session(
        client, bill_aggregate.vendor_id, CashRegisterNames.HOTEL_CASH_REGISTER
    )

    headers = {"X-User-Type": "fdm"}
    with mock_rule_engine():
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )

    payment_id = response.json.get('data').get('payment_id')
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)

    # get the room number using room id
    room_number = get_room_number(room_repo, booking_aggregate, room_id="15")
    payment_receipt_aggregate = payment_receipt_repo.load(
        payment_id, bill_aggregate.bill.bill_id
    )

    payment_receipt = payment_receipt_aggregate.payment_receipt

    assert room_number in payment_receipt.affected_room_nos
