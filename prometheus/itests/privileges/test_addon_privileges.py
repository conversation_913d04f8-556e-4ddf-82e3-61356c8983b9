import json
from datetime import timed<PERSON><PERSON>

import pytest
from treebo_commons.utils import dateutils

from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    checkin_booking,
    checkout_booking,
    make_booking,
)
from prometheus.itests.api_wrappers.invoice_wrappers import preview_invoice
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkin_payload,
    create_checkout_payload,
)
from prometheus.itests.payload_generators.invoice_payload_generators import (
    create_preview_invoice_payload,
)
from prometheus.tests.factories.aggregate_factories import (
    BillFactory,
    BookingAggregateFactory,
)
from prometheus.tests.mockers import mock_role_manager, mock_tax_calculator_service
from ths_common.constants.billing_constants import ChargeTypes
from ths_common.constants.booking_constants import BookingStatus
from ths_common.exceptions import AuthorizationError
from ths_common.value_objects import UserData


def setup_booking_bill_customer_expense_item(booking_repo, bill_repo):
    bill_aggregate = BillFactory()
    bill_repo.save(bill_aggregate)
    booking_aggregate = BookingAggregateFactory(booking__bill_id=bill_aggregate.bill_id)
    booking_repo.save(booking_aggregate)
    return booking_aggregate, bill_aggregate


@pytest.mark.skip("Fix tests around privileges")
def test_addon_create_policy_for_user_type_view_only(
    booking_repo, addon_service, checkin_addon_dict, bill_repo, lunch_item
):
    booking_aggregate, bill_aggregate = setup_booking_bill_customer_expense_item(
        booking_repo, bill_repo
    )
    booking = booking_aggregate.booking

    checkin_addon_dict['expense_item_id'] = lunch_item.expense_item_id
    with pytest.raises(AuthorizationError):
        user_data = UserData(user_type="view-only")
        with mock_role_manager():
            addon_service.add_new_addon(
                booking.booking_id, checkin_addon_dict, user_data
            )


@pytest.mark.skip("Fix tests around privileges")
def test_addon_create_policy_for_user_type_fdm_for_checkout_room_stay(
    booking_repo,
    addon_service,
    checkin_addon_dict,
    lunch_item,
    active_hotel_aggregate,
    client,
    two_day_booking_payload,
    hotel_repo,
):
    booking_id = make_booking(client, {"data": json.loads(two_day_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)

    # Rollover Business Date
    # roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
    )
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    preview_invoice_payload = create_preview_invoice_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)

    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version,
        active_hotel_aggregate,
        invoice_group_id,
        dateutils.current_datetime(),
    )
    action_id = checkout_booking(client, booking_id, checkout_payload)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CHECKED_OUT
    checkin_addon_dict['expense_item_id'] = lunch_item.expense_item_id
    with pytest.raises(AuthorizationError):
        user_data = UserData(user_type="fdm")
        addon_service.add_new_addon(
            booking_aggregate.booking.booking_id, checkin_addon_dict, user_data
        )


@pytest.mark.skip("Fix tests around privileges")
def test_past_dated_addon_create_policy_for_user_type_fdm_for_checked_in_room(
    booking_repo,
    addon_service,
    checkin_addon_dict_for_v2,
    lunch_item,
    client,
    two_day_booking_payload,
):
    booking_id = make_booking(client, {"data": json.loads(two_day_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CONFIRMED

    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
    )
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    checkin_addon_dict_for_v2['expense_item_id'] = lunch_item.expense_item_id

    with pytest.raises(AuthorizationError):
        user_data = UserData(user_type="fdm")
        addon_service.add_new_addon(
            booking_aggregate.booking.booking_id, checkin_addon_dict_for_v2, user_data
        )


@pytest.mark.skip("Fix tests around privileges")
def test_past_dated_addon_create_policy_for_addon_type_credit(
    booking_repo,
    addon_service,
    checkin_addon_dict_for_v2,
    lunch_item,
    client,
    two_day_booking_payload,
):
    booking_id = make_booking(client, {"data": json.loads(two_day_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CONFIRMED
    checkin_addon_dict_for_v2['expense_item_id'] = lunch_item.expense_item_id
    checkin_addon_dict_for_v2['charge_type'] = ChargeTypes.CREDIT

    with pytest.raises(AuthorizationError):
        user_data = UserData(user_type="fdm")
        addon_service.add_new_addon(
            booking_aggregate.booking.booking_id, checkin_addon_dict_for_v2, user_data
        )


@pytest.mark.skip("Fix tests around privileges")
def test_addon_create_policy_for_user_type_cr_for_checkout_room_stay(
    booking_repo,
    addon_service,
    checkin_addon_dict,
    lunch_item,
    active_hotel_aggregate,
    client,
    two_day_booking_payload,
    hotel_repo,
):
    booking_id = make_booking(client, {"data": json.loads(two_day_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version,
        booking_aggregate.booking.checkin_date,
    )
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    preview_invoice_payload = create_preview_invoice_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)

    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version,
        active_hotel_aggregate,
        invoice_group_id,
        dateutils.current_datetime(),
    )
    action_id = checkout_booking(client, booking_id, checkout_payload)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CHECKED_OUT
    checkin_addon_dict['expense_item_id'] = lunch_item.expense_item_id
    with pytest.raises(AuthorizationError):
        user_data = UserData(user_type="cr-team")
        addon_service.add_new_addon(
            booking_aggregate.booking.booking_id, checkin_addon_dict, user_data
        )


@pytest.mark.skip("Fix tests around privileges")
def test_addon_edit_policy_for_user_type_view_only(
    booking_repo,
    addon_service,
    all_days_addon_dict_v2,
    bill_repo,
    lunch_item,
    update_addon_date_dict,
):
    booking_aggregate, bill_aggregate = setup_booking_bill_customer_expense_item(
        booking_repo, bill_repo
    )
    booking = booking_aggregate.booking

    all_days_addon_dict_v2['expense_item_id'] = lunch_item.expense_item_id
    user_data = UserData(user_type="super-admin")
    with mock_role_manager():
        addon = addon_service.add_new_addon(
            booking.booking_id, all_days_addon_dict_v2, user_data
        )

    assert addon is not None
    with mock_role_manager():
        addons = addon_service.get_addons(
            booking.booking_id, linked_addons_required=True
        )

    updated_booking = booking_repo.load(booking.booking_id)
    assert (
        len(updated_booking.expenses)
        == (booking.checkout_date - booking.checkin_date).days + 1
    )

    new_checkout_date = booking.checkout_date + timedelta(days=3)
    updated_booking.booking.checkout_date = new_checkout_date
    updated_booking.room_stays[0].checkout_date = new_checkout_date
    updated_booking.room_stays[0].guest_stays[0].checkout_date = new_checkout_date

    with pytest.raises(AuthorizationError):
        user_data = UserData(user_type="view-only")
        addon_service.update_addon(
            updated_booking.booking.booking_id,
            addons[0].addon.addon_id,
            addons[0].addon.version,
            update_addon_date_dict,
            user_data,
        )


@pytest.mark.skip("Fix tests around privileges")
def test_addon_edit_policy_for_user_type_fdm_for_past_date_addon(
    booking_repo,
    addon_service,
    all_days_addon_dict_v2,
    bill_repo,
    lunch_item,
    update_addon_date_dict,
):
    bill_aggregate = BillFactory()
    bill_repo.save(bill_aggregate)

    booking_aggregate = BookingAggregateFactory(booking__bill_id=bill_aggregate.bill_id)
    booking_aggregate.room_stays[0].checkin_date = dateutils.subtract(
        booking_aggregate.room_stays[0].checkin_date, days=2
    )
    booking_aggregate.room_stays[0].checkout_date = dateutils.subtract(
        booking_aggregate.room_stays[0].checkout_date, days=2
    )
    booking_aggregate.room_stays[0].guest_stays[0].checkin_date = dateutils.subtract(
        booking_aggregate.room_stays[0].guest_stays[0].checkin_date, days=2
    )
    booking_aggregate.room_stays[0].guest_stays[0].checkout_date = dateutils.subtract(
        booking_aggregate.room_stays[0].guest_stays[0].checkout_date, days=2
    )
    booking_aggregate.booking.checkin_date = dateutils.subtract(
        booking_aggregate.booking.checkin_date, days=2
    )
    booking_aggregate.booking.checkout_date = dateutils.subtract(
        booking_aggregate.booking.checkout_date, days=2
    )
    booking_repo.save(booking_aggregate)

    booking = booking_aggregate.booking

    all_days_addon_dict_v2['expense_item_id'] = lunch_item.expense_item_id
    user_data = UserData(user_type="super-admin")
    with mock_tax_calculator_service():
        addon = addon_service.add_new_addon(
            booking.booking_id, all_days_addon_dict_v2, user_data
        )

    assert addon is not None

    addons = addon_service.get_addons(booking.booking_id, linked_addons_required=True)

    updated_booking = booking_repo.load(booking.booking_id)
    assert (
        len(updated_booking.expenses)
        == (booking.checkout_date - booking.checkin_date).days + 1
    )

    new_checkout_date = booking.checkout_date + timedelta(days=3)
    updated_booking.booking.checkout_date = new_checkout_date
    updated_booking.room_stays[0].checkout_date = new_checkout_date
    updated_booking.room_stays[0].guest_stays[0].checkout_date = new_checkout_date

    with pytest.raises(AuthorizationError):
        user_data = UserData(user_type="fdm")
        addon_service.update_addon(
            updated_booking.booking.booking_id,
            addons[0].addon.addon_id,
            addons[0].addon.version,
            update_addon_date_dict,
            user_data,
        )


@pytest.mark.skip("Fix tests around privileges")
def test_addon_delete_policy_for_user_type_fdm_for_past_date_addon(
    booking_repo,
    addon_service,
    all_days_addon_dict_v2,
    bill_repo,
    lunch_item,
    update_addon_date_dict,
):
    bill_aggregate = BillFactory()
    bill_repo.save(bill_aggregate)

    booking_aggregate = BookingAggregateFactory(booking__bill_id=bill_aggregate.bill_id)
    booking_aggregate.room_stays[0].checkin_date = dateutils.subtract(
        booking_aggregate.room_stays[0].checkin_date, days=2
    )
    booking_aggregate.room_stays[0].checkout_date = dateutils.subtract(
        booking_aggregate.room_stays[0].checkout_date, days=2
    )
    booking_aggregate.room_stays[0].guest_stays[0].checkin_date = dateutils.subtract(
        booking_aggregate.room_stays[0].guest_stays[0].checkin_date, days=2
    )
    booking_aggregate.room_stays[0].guest_stays[0].checkout_date = dateutils.subtract(
        booking_aggregate.room_stays[0].guest_stays[0].checkout_date, days=2
    )
    booking_aggregate.booking.checkin_date = dateutils.subtract(
        booking_aggregate.booking.checkin_date, days=2
    )
    booking_aggregate.booking.checkout_date = dateutils.subtract(
        booking_aggregate.booking.checkout_date, days=2
    )
    booking_repo.save(booking_aggregate)

    booking = booking_aggregate.booking

    all_days_addon_dict_v2['expense_item_id'] = lunch_item.expense_item_id
    user_data = UserData(user_type="super-admin")
    with mock_tax_calculator_service():
        addon = addon_service.add_new_addon(
            booking.booking_id, all_days_addon_dict_v2, user_data
        )

    assert addon is not None

    addons = addon_service.get_addons(booking.booking_id, linked_addons_required=True)
    updated_booking = booking_repo.load(booking.booking_id)

    with pytest.raises(AuthorizationError):
        user_data = UserData(user_type="fdm")
        addon_service.delete_addon(
            updated_booking.booking.booking_id, addons[0].addon.addon_id, user_data
        )
