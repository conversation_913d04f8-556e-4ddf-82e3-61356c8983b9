import json

from prometheus.itests.billing.test_invoice_modification import marked_invoices_locked
from prometheus.itests.helpers import roll_over_business_date
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_company_profile_service,
    mock_is_booking_funding_enabled,
    mock_rate_manager_client,
    mock_role_manager,
    mock_tenant_config,
)
from ths_common.constants.billing_constants import BilledEntityCategory
from ths_common.constants.funding_constants import FundingExpenseItem, FundingType


def update_booking_funding(
    client,
    booking_id,
    amount,
    funding_id=None,
    funding_type=FundingType.MANUAL_FUNDING.value,
    reason="For testing purpose",
    expected_status_code=200,
):
    """
    Helper to test PUT /v1/booking-funding/<booking_id> API.
    """
    with mock_catalog_client(), mock_role_manager(), mock_rate_manager_client():
        url = f"/v1/booking-funding/{booking_id}"
        payload = {
            "data": {
                "amount": amount,
                "funding_type": funding_type,
                "reason": reason,
            }
        }
        if funding_id:
            payload["funding_id"] = funding_id

        response = client.put(
            url,
            json=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )

        assert response.status_code == expected_status_code
        if expected_status_code != 200:
            return response

        return response.json["data"]


def test_add_funding_after_checkout_should_create_customer_if_not_exists(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
    bill_repo,
    credit_note_repo,
):
    """
    Validates funding behavior after checkout: adds customer and charge to franchiser.
    """
    from treebo_commons.utils import dateutils

    from prometheus.itests.booking.test_booking_v2 import make_booking

    # Setup booking payload
    create_booking_payload = json.loads(create_booking_payload)
    create_booking_payload["room_stays"][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "10",
            "start_date": dateutils.current_datetime().isoformat(),
            "end_date": dateutils.current_datetime().isoformat(),
        }
    ]

    booking_id = make_booking(client, {"data": create_booking_payload})

    roll_over_business_date(active_hotel_aggregate, hotel_repo)
    marked_invoices_locked(
        active_hotel_aggregate,
        booking_id,
        booking_invoice_group_repo,
        booking_repo,
        client,
        invoice_repo,
    )

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)

    with mock_is_booking_funding_enabled(), mock_company_profile_service(), mock_tenant_config(
        [
            {
                "config_name": "rate_config.hotel_uses_posttax_price",
                "config_value": "true",
                "value_type": "boolean",
            },
        ]
    ):
        update_booking_funding(client, booking_id, amount="1000 INR")

    updated_booking_aggregate = booking_repo.load(booking_id)
    updated_bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)

    # Assert new billed entity (Franchiser) is added
    assert (
        len(updated_bill_aggregate.billed_entities)
        == len(bill_aggregate.billed_entities) + 1
    )
    assert any(
        be.category == BilledEntityCategory.FRANCHISER
        for be in updated_bill_aggregate.billed_entities
    )

    # Assert new customer is added
    assert (
        len(updated_booking_aggregate.customers) == len(booking_aggregate.customers) + 1
    )

    # Assert funding charge applied to franchiser
    funding_charge = sum(
        c.posttax_amount
        for c in updated_bill_aggregate.charges
        if c.item.item_id == FundingExpenseItem.TREEBO_MANUAL_FUNDING.value
    )
    assert funding_charge.amount == 1120
