import datetime
import json
from contextlib import contextmanager

import pytest
from treebo_commons.utils import dateutils

from object_registry import locate_instance
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import make_booking
from prometheus.itests.billing.test_invoice_modification import create_locked_invoices
from prometheus.reporting.finance_erp_reporting.constants import FinanceReports
from prometheus.reporting.finance_erp_reporting.finance_reporting_service import (
    FinanceReportingService,
)
from ths_common.constants.booking_constants import BookingChannels
from ths_common.constants.reporting_constants import PurchaseInvoiceTypes


@pytest.fixture
def marketplace_locked_invoices(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
):
    create_booking_payload = json.loads(create_booking_payload)
    create_booking_payload["source"][
        "channel_code"
    ] = BookingChannels.TREEBO_INTERNAL.value
    create_booking_payload['booking_owner']['profile_type'] = 'corporate'
    create_booking_payload['booking_owner']['reference_id'] = 'rup_123'
    booking_id = make_booking(client, {"data": create_booking_payload})
    return create_locked_invoices(
        active_hotel_aggregate,
        booking_id,
        booking_invoice_group_repo,
        booking_repo,
        client,
        hotel_repo,
        invoice_repo,
        seller_repo,
    )


@contextmanager
def mocked_settings(branch_sh_codes):
    real_get_all_sh_company_codes_chain_manager = (
        FinanceReportingService.get_all_sh_company_codes_of_chain_manager
    )

    FinanceReportingService.get_all_sh_company_codes_of_chain_manager = (
        lambda *args, **kwargs: branch_sh_codes
    )

    yield
    FinanceReportingService.get_all_sh_company_codes_of_chain_manager = (
        real_get_all_sh_company_codes_chain_manager
    )


def test_market_place_purchase_invoice_of_proper_sub_entity_should_generate_report(
    client,
    marketplace_locked_invoices,
):
    finance_reporting_service = locate_instance(FinanceReportingService)
    date = dateutils.date_to_ymd_str(datetime.datetime.now().date())

    with mocked_settings(branch_sh_codes=['rup_123']):
        marketplace_purchase_invoice_reports = (
            finance_reporting_service._create_marketplace_purchase_invoice_reports(date)
        )
        assert len(marketplace_purchase_invoice_reports) == 1
        for report in marketplace_purchase_invoice_reports:
            assert report.purchase_type == PurchaseInvoiceTypes.TREEBO_STAY_INVOICE
            assert (
                report.report_category
                == FinanceReports.MARKETPLACE_PURCHASE_INVOICE_REPORT.value
            )


def test_market_place_purchase_invoice_of_non_branch_sub_entity_should_not_generate_report(
    client,
    marketplace_locked_invoices,
):
    finance_reporting_service = locate_instance(FinanceReportingService)
    date = dateutils.date_to_ymd_str(datetime.datetime.now().date())

    with mocked_settings(branch_sh_codes=['cop_123']):
        marketplace_purchase_invoice_reports = (
            finance_reporting_service._create_marketplace_purchase_invoice_reports(date)
        )
        assert len(marketplace_purchase_invoice_reports) == 0
