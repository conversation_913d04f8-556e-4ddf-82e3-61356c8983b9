from datetime import timedelta

from treebo_commons.utils import dateutils


def create_checkin_payload(
    booking_version,
    checkin_datetime=None,
    guest_stay_id=1,
    room_stay_id=1,
    room_id=15,
    guest_id=1,
    is_of_two_room=False,
):
    checkin_datetime = (
        dateutils.current_datetime() if checkin_datetime is None else checkin_datetime
    )
    payload = {
        "data": {
            "action_type": "checkin",
            "payload": {
                "checkin": {
                    "room_stays": [
                        {
                            "guest_stays": [
                                {
                                    "guest": {"first_name": "aneesh1"},
                                    "guest_id": str(guest_id),
                                    "guest_stay_id": guest_stay_id,
                                }
                            ],
                            "room_allocation": {
                                "room_id": str(room_id),
                            },
                            "room_stay_id": room_stay_id,
                        }
                    ]
                }
            },
        },
        "resource_version": booking_version,
    }
    if guest_id == 1:
        payload["data"]["payload"]["checkin"]["room_stays"][0]["guest_stays"][0].pop(
            "guest_id"
        )
        payload["data"]["payload"]["checkin"]["room_stays"][0]["guest_stays"][0][
            "guest_id_"
        ] = str(guest_id)
    if is_of_two_room:
        payload["data"]["payload"]["checkin"]["room_stays"].append(
            {
                "guest_stays": [
                    {
                        "guest": {"first_name": "Arun"},
                        "guest_id": str(guest_id + 1),
                        "guest_stay_id": guest_stay_id,
                    }
                ],
                "room_allocation": {
                    "room_id": str(160),
                },
                "room_stay_id": room_stay_id + 1,
            }
        )
    return payload


def create_checkin_payload_without_new_guest(
    booking_version,
    active_hotel_aggregate,
    checkin_datetime=None,
    room_stay_id=1,
    room_id="15",
):
    checkin_datetime = (
        dateutils.current_datetime() if checkin_datetime is None else checkin_datetime
    )
    return {
        "data": {
            "action_type": "checkin",
            "payload": {
                "checkin": {
                    "room_stays": [
                        {
                            "guest_stays": [{"guest_stay_id": 1}],
                            "room_allocation": {
                                "room_id": room_id,
                            },
                            "room_stay_id": room_stay_id,
                        }
                    ]
                }
            },
        },
        "resource_version": booking_version,
    }


def create_checkin_payload_multiple_room_stays_without_new_guest(
    booking_version,
    active_hotel_aggregate,
    checkin_datetime=None,
    room_stay_id=1,
    room_id="15",
):
    checkin_datetime = (
        dateutils.current_datetime() if checkin_datetime is None else checkin_datetime
    )
    return {
        "data": {
            "action_type": "checkin",
            "payload": {
                "checkin": {
                    "room_stays": [
                        {
                            "guest_stays": [{"guest_stay_id": 1}],
                            "room_allocation": {
                                "room_id": 15,
                            },
                            "room_stay_id": 1,
                        },
                        {
                            "guest_stays": [{"guest_stay_id": 2}],
                            "room_allocation": {
                                "room_id": 16,
                            },
                            "room_stay_id": 2,
                        },
                    ]
                }
            },
        },
        "resource_version": booking_version,
    }


def create_checkin_payload_with_edit_guest_details(
    booking_version,
    active_hotel_aggregate,
    checkin_datetime=None,
    guest_first_name=None,
    guest_last_name=None,
):
    checkin_datetime = (
        dateutils.current_datetime() if checkin_datetime is None else checkin_datetime
    )
    return {
        "data": {
            "action_type": "checkin",
            "payload": {
                "checkin": {
                    "room_stays": [
                        {
                            "guest_stays": [
                                {
                                    "checkin_date": checkin_datetime.isoformat(),
                                    "guest": {
                                        "first_name": guest_first_name
                                        if guest_first_name
                                        else "tyrion",
                                        "last_name": guest_last_name
                                        if guest_last_name
                                        else "lannister",
                                        "gst_details": {
                                            "legal_name": "halfman",
                                            "address": {
                                                "state": "king's landing",
                                                "country": "westeros",
                                            },
                                        },
                                    },
                                    "guest_id": "2",
                                    "guest_stay_id": "1",
                                }
                            ],
                            "room_allocation": {
                                "room_id": "15",
                            },
                            "room_stay_id": "1",
                        }
                    ]
                }
            },
        },
        "resource_version": booking_version,
    }


def create_checkout_payload(
    booking_version, active_hotel_aggregate, invoice_group_id, checkout_datetime=None
):
    checkout_datetime = (
        dateutils.current_datetime() + timedelta(days=1)
        if checkout_datetime is None
        else checkout_datetime
    )
    return {
        "data": {
            "action_type": "checkout",
            "payload": {
                "checkout": {
                    "invoice_group_id": invoice_group_id,
                    "should_generate_invoice": True,
                }
            },
        },
        "resource_version": booking_version,
    }


def create_complete_no_show_payload(booking_version):
    return {
        "data": {
            "action_type": "noshow",
            "payload": {"noshow": {"noshow_reason": "h"}},
        },
        "resource_version": booking_version,
    }


def create_partial_no_show_payload(booking_version, room_stay_id, guest_stay_ids):
    return {
        "data": {
            "action_type": "noshow",
            "payload": {
                "noshow": {
                    "room_stays": [
                        {"room_stay_id": room_stay_id, "guest_stay_ids": guest_stay_ids}
                    ]
                }
            },
        },
        "resource_version": booking_version,
    }


def create_partial_no_show_payload_with_one_complete_room_noshow(
    booking_version, room_stay_id, guest_stay_ids, room_stay_id_to_mark_room_noshow
):
    return {
        "data": {
            "action_type": "noshow",
            "payload": {
                "noshow": {
                    "room_stays": [
                        {
                            "room_stay_id": room_stay_id,
                            "guest_stay_ids": guest_stay_ids,
                        },
                        {"room_stay_id": room_stay_id_to_mark_room_noshow},
                    ]
                }
            },
        },
        "resource_version": booking_version,
    }


def create_room_no_show_payload(booking_version, room_stay_id):
    return {
        "data": {
            "action_type": "noshow",
            "payload": {"noshow": {"room_stays": [{"room_stay_id": room_stay_id}]}},
        },
        "resource_version": booking_version,
    }


def room_stay_cancellation_request(booking_version, room_stay_id=1):
    return {
        "data": {
            "action_type": "cancel",
            "payload": {
                "cancel": {
                    "cancellation_reason": "h",
                    "room_stays": [{"room_stay_id": room_stay_id}],
                }
            },
        },
        "resource_version": booking_version,
    }


def guest_stay_cancellation_request(
    booking_version, room_stay_id=1, guest_ids=None, prices=None
):
    return {
        "data": {
            "action_type": "cancel",
            "payload": {
                "cancel": {
                    "cancellation_reason": "h",
                    "room_stays": [
                        {
                            "room_stay_id": room_stay_id,
                            "guest_stay_ids": guest_ids,
                            "prices": prices,
                        }
                    ],
                }
            },
        },
        "resource_version": booking_version,
    }


def guest_stay_noshow_request(
    booking_version, room_stay_id=1, guest_ids=None, prices=None
):
    return {
        "data": {
            "action_type": "noshow",
            "payload": {
                "noshow": {
                    "noshow_reason": "g",
                    "room_stays": [
                        {
                            "room_stay_id": room_stay_id,
                            "guest_stay_ids": guest_ids,
                            "prices": prices,
                        }
                    ],
                }
            },
        },
        "resource_version": booking_version,
    }


def booking_cancellation_request(booking_version):
    return {
        "data": {
            "action_type": "cancel",
            "payload": {"cancel": {"cancellation_reason": "h"}},
        },
        "resource_version": booking_version,
    }


def create_confirm_payload(booking_version):
    return {
        "data": {"action_type": "confirm", "payload": {"confirm": {}}},
        "resource_version": booking_version,
    }


def create_update_customer_payload(customer_details, booking_version):
    payload = []
    for customer in customer_details:
        data = {
            "first_name": customer['first_name'],
            "last_name": customer['last_name'],
            "customer_id": customer['id'],
        }
        if 'is_booker' in customer:
            data['is_booker'] = customer['is_booker']
        if customer.get("gst_details"):
            data["gst_details"] = customer.get("gst_details")
        payload.append(data)
    return {"data": payload, "resource_version": booking_version}
