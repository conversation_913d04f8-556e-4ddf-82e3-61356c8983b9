import json
from datetime import time

import pytest

from prometheus.itests.api_wrappers.addon_wrappers import add_addon, delete_addon
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    checkin_booking,
    make_booking,
)
from prometheus.itests.payload_generators.addon_payload_generators import (
    addons_request as create_addon_request,
)
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkin_payload,
)
from prometheus.tests.factories.aggregate_factories import (
    BillFactory,
    BookingAggregateFactory,
)
from prometheus.tests.mockers import mock_tax_calculator_service
from prometheus.tests.test_utils import yesterday
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import ChargeStatus
from ths_common.constants.booking_constants import ExpenseStatus

super_admin_user_type = {'X-User-Type': 'super-admin'}


@pytest.fixture
def addons_request():
    return """
    {
        "data": {
                "room_stay_id" : 1,
                "expense_item_id" : "123",
                "name" : "Lunch",
                "pretax_price" : 100,
                "quantity" : 1,
                "charge_checkin" : true,
                "charge_checkout" : false,
                "charge_other_days" : false,
                "charge_type" : "credit",
                "bill_to_type" : "company"
        }
    }
"""


def test_addon_apis(
    client, addons_request, bill_repo, booking_repo, booking_audit_trail_repo
):
    headers = {'X-User-Type': 'super-admin'}
    bill_aggregate = BillFactory()
    for billed_entity in bill_aggregate.billed_entities:
        for account in billed_entity.accounts:
            bill_aggregate.add_folio_if_not_exists(
                billed_entity.billed_entity_id, account.account_number
            )
    bill_repo.save(bill_aggregate)

    booking_aggregate = BookingAggregateFactory(booking__bill_id=bill_aggregate.bill_id)
    booking_repo.save(booking_aggregate)

    # Create addon
    url = (
        'v1/bookings/'
        + booking_aggregate.booking.booking_id
        + '/addons'
        + '?linked=True'
    )
    response = client.post(
        url,
        data=addons_request,
        content_type='application/json',
        headers=super_admin_user_type,
    )
    assert response.status_code == 200
    assert response.json['data']['booking_id'] is not None
    assert response.json['data']['expense_ids'] is not None

    # Search addons for a booking
    get_url = (
        'v1/bookings/'
        + booking_aggregate.booking.booking_id
        + '/addons'
        + '?linked=True'
    )
    get_response = client.get(get_url, content_type='application/json', headers=headers)
    addon_response = json.loads(get_response.data.decode('utf-8'))
    assert len(addon_response['data']) == 1
    assert addon_response['data'][0]['booking_id'] is not None

    # Update addon
    addon_id = response.json['data']['addon_id']
    update_url = (
        'v1/bookings/' + booking_aggregate.booking.booking_id + '/addons/' + addon_id
    )
    addon_update_body = (
        """
    {
     "resource_version" : %s,
     "data": {
        "charge_type": "non-credit",
        "bill_to_type": "guest",
        "charge_other_days": "true",
        "charge_checkout": "true"
     }
    }
    """
        % response.json['data']['version']
    )
    update_response = client.patch(
        update_url,
        data=addon_update_body,
        content_type='application/json',
        headers=super_admin_user_type,
    )
    assert update_response.status_code == 200
    audit_trail = booking_audit_trail_repo.load_for_booking(
        booking_aggregate.booking.booking_id
    )
    assert audit_trail[0].audit_trail.audit_type == AuditType.ADDON_MODIFIED
    assert (
        audit_trail[0].audit_trail.audit_payload['domain_events'][0]['event_detail'][
            'details'
        ][0]['attribute']
        == 'charge_type'
    )
    assert (
        audit_trail[0].audit_trail.audit_payload['domain_events'][0]['event_detail'][
            'details'
        ][0]['new_value']
        == 'non-credit:guest'
    )
    assert (
        audit_trail[0].audit_trail.audit_payload['domain_events'][0]['event_detail'][
            'details'
        ][0]['old_value']
        == 'credit:company'
    )

    # Update addon
    addon_id = response.json['data']['addon_id']
    update_url = (
        'v1/bookings/' + booking_aggregate.booking.booking_id + '/addons/' + addon_id
    )
    addon_update_body = (
        """
    {
     "resource_version" : %s,
     "data": {
        "bill_to_type": "company",
        "charge_other_days": "true",
        "charge_checkout": "true"
     }
    }
    """
        % update_response.json['data']['version']
    )
    update_response = client.patch(
        update_url,
        data=addon_update_body,
        content_type='application/json',
        headers=super_admin_user_type,
    )
    assert update_response.status_code == 200
    audit_trail = booking_audit_trail_repo.load_for_booking(
        booking_aggregate.booking.booking_id
    )
    assert audit_trail[0].audit_trail.audit_type == AuditType.ADDON_MODIFIED
    assert (
        audit_trail[0].audit_trail.audit_payload['domain_events'][0]['event_detail'][
            'details'
        ][0]['attribute']
        == 'charge_type'
    )
    assert (
        audit_trail[0].audit_trail.audit_payload['domain_events'][0]['event_detail'][
            'details'
        ][0]['new_value']
        == 'non-credit:company'
    )
    assert (
        audit_trail[0].audit_trail.audit_payload['domain_events'][0]['event_detail'][
            'details'
        ][0]['old_value']
        == 'non-credit:guest'
    )

    # Delete addon
    addon_id = response.json['data']['addon_id']
    delete_url = (
        'v1/bookings/' + booking_aggregate.booking.booking_id + '/addons/' + addon_id
    )
    delete_response = client.delete(delete_url, headers=super_admin_user_type)
    assert delete_response.status_code == 200
    audit_trail = booking_audit_trail_repo.load_for_booking(
        booking_aggregate.booking.booking_id
    )
    assert audit_trail[0].audit_trail.audit_type == AuditType.ADDON_REMOVED

    # from prometheus import crs_context
    # crs_context.set_hotel_context(active_hotel_aggregate)

    updated_booking = booking_repo.load(booking_aggregate.booking.booking_id)
    assert all(
        map(lambda ex: ex.status == ExpenseStatus.CANCELLED, updated_booking.expenses)
    ), "All expenses are in not 'deleted' state"

    updated_bill = bill_repo.load(bill_aggregate.bill.bill_id)
    assert all(
        map(lambda ch: ch.status == ChargeStatus.CANCELLED, updated_bill.charges)
    ), "All charges are in not 'deleted' state"


def test_addon_should_create_addons_if_checkin_time_and_checkout_date_are_not_same(
    client,
    all_days_addon_request,
    bill_repo,
    booking_repo,
    booking_with_different_checkin_checkout_times,
):
    bill_aggregate = BillFactory()
    for billed_entity in bill_aggregate.billed_entities:
        for account in billed_entity.accounts:
            bill_aggregate.add_folio_if_not_exists(
                billed_entity.billed_entity_id, account.account_number
            )
    bill_repo.save(bill_aggregate)

    booking_repo.save(booking_with_different_checkin_checkout_times)

    url = (
        'v1/bookings/'
        + booking_with_different_checkin_checkout_times.booking.booking_id
        + '/addons'
    )
    response = client.post(
        url,
        data=all_days_addon_request,
        content_type='application/json',
        headers=super_admin_user_type,
    )
    assert response.status_code == 200
    assert len(response.json['data']['expense_ids']) == 4


def test_addon_expense_applicable_date_should_be_11_59_for_non_checkout_dates_and_switchover_time_for_checkout(
    client,
    all_days_addon_request,
    bill_repo,
    booking_repo,
    booking_with_different_checkin_checkout_times,
):
    bill_aggregate = BillFactory()
    for billed_entity in bill_aggregate.billed_entities:
        for account in billed_entity.accounts:
            bill_aggregate.add_folio_if_not_exists(
                billed_entity.billed_entity_id, account.account_number
            )
    bill_repo.save(bill_aggregate)

    booking_repo.save(booking_with_different_checkin_checkout_times)

    url = (
        'v1/bookings/'
        + booking_with_different_checkin_checkout_times.booking.booking_id
        + '/addons'
    )
    response = client.post(
        url,
        data=all_days_addon_request,
        content_type='application/json',
        headers=super_admin_user_type,
    )
    assert response.status_code == 200
    assert len(response.json['data']['expense_ids']) == 4
    checkout_date = booking_with_different_checkin_checkout_times.booking.checkout_date

    updated_booking_aggregate = booking_repo.load(
        booking_with_different_checkin_checkout_times.booking.booking_id
    )
    for expense in updated_booking_aggregate.expenses:
        if expense.applicable_date.date() != checkout_date.date():
            assert expense.applicable_date.time() == time(23, 59)
        else:
            assert expense.applicable_date.time() == time(6, 0)


@pytest.mark.usefixtures('setup_hotel')
def test_should_be_able_to_create_addons_on_a_roomstay_not_checked_in(
    client,
    new_booking_yesterday_payload_with_2_room_stays,
    booking_repo,
    booking_audit_trail_repo,
):
    booking_id = make_booking(
        client, {"data": json.loads(new_booking_yesterday_payload_with_2_room_stays)}
    )
    assert booking_id is not None

    booking_aggregate = booking_repo.load(booking_id)
    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version, checkin_datetime=yesterday()
    )
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    addon_id = add_addon(
        client, booking_id, create_addon_request(room_stay_id=2)
    )  # Should be added to reserved roomstay

    # should have addon created audit trail
    audit_trail = booking_audit_trail_repo.load_for_booking(booking_id)
    assert audit_trail[0].audit_trail.audit_type == AuditType.ADDON_CREATED

    # Should fail for checked in roomstay
    add_addon(
        client,
        booking_id,
        create_addon_request(room_stay_id=1),
        expected_response_code=200,
    )

    delete_addon(client, booking_id, addon_id, expected_response_code=200)
