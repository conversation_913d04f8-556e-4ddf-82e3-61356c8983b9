import json
from datetime import time, timedelta
from decimal import Decimal
from itertools import chain

import pytest

from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import make_booking
from prometheus.tests.mockers import mock_role_manager, mock_tax_calculator_service
from prometheus.tests.test_utils import cached_now
from ths_common.constants.billing_constants import ChargeStatus


def add_multiple_guest_stays():
    today = cached_now()
    payload = {
        "data": {
            "guest_stays": [
                {
                    "age_group": "adult",
                    "checkin_date": today.isoformat(),
                    "checkout_date": (today + timedelta(hours=24)).isoformat(),
                },
                {
                    "age_group": "adult",
                    "checkin_date": today.isoformat(),
                    "checkout_date": (today + timedelta(hours=24)).isoformat(),
                },
            ],
            "new_room_stay_prices": [
                {
                    "applicable_date": today.isoformat(),
                    "bill_to_type": "guest",
                    "type": "non-credit",
                    "pretax_amount": 4623,
                }
            ],
        },
        "resource_version": 1,
    }
    return json.dumps(payload)


def add_guest_stay_request():
    today = cached_now()
    payload = {
        "data": {
            "age_group": "adult",
            "checkin_date": today.isoformat(),
            "checkout_date": (today + timedelta(hours=24)).isoformat(),
            "new_room_stay_prices": [
                {
                    "applicable_date": today.isoformat(),
                    "bill_to_type": "guest",
                    "type": "non-credit",
                    "pretax_amount": 2623,
                }
            ],
        },
        "resource_version": 1,
    }
    return json.dumps(payload)


@pytest.mark.usefixtures('rt01_inventory')
def test_add_guest(
    active_hotel_aggregate,
    booking_repo,
    bill_repo,
    client,
    create_booking_payload,
    room_type_inventory_repo,
):
    print("===================>> Running Test")
    payload = {"data": json.loads(create_booking_payload)}
    booking_id = make_booking(client, payload)

    print("===================>> Create booking API Executed")

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)

    assert len(booking_aggregate.room_stays[0].guest_stays) == 1
    print("===================>> Loaded booking and bill from repo")

    booking_id = booking_aggregate.booking.booking_id

    req = add_guest_stay_request()
    url = f"/v1/bookings/{booking_id}/room-stays/1/guest-stays"
    with mock_role_manager():
        response = client.post(
            url,
            data=req,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
    assert response.status_code == 201
    assert response.json["data"]["guest_stay_id"] == 2
    assert response.json["data"]["age_group"] == "adult"
    print("===================>> Add Guest Stay API Completed")

    updated_booking = booking_repo.load(booking_id)
    updated_bill = bill_repo.load(bill_aggregate.bill.bill_id)
    print(
        "===================>> Loaded booking and bill from repo, after add guest stay"
    )
    assert (
        updated_booking.booking.version == booking_aggregate.booking.version + 1
    ), "Booking version is not updated"
    assert (
        updated_bill.bill.version == bill_aggregate.bill.version + 1
    ), "Bill version is not updated"
    assert (
        len(updated_booking.room_stays[0].guest_stays) == 2
    ), "Guest stay is not added"
    guest_stays = list(chain(*[rs.guest_stays for rs in updated_booking.room_stays]))
    assert all(
        str(gs.checkin_date.time()) == active_hotel_aggregate.hotel.checkin_time
        for gs in guest_stays
    )
    assert all(
        str(gs.checkout_date.time()) == active_hotel_aggregate.hotel.checkout_time
        for gs in guest_stays
    )
    # charges[0] will be cancelled. TODO may be filter by status?
    charges_not_cancelled = [
        charge
        for charge in updated_bill.charges
        if charge.status == ChargeStatus.CREATED
    ]
    assert (
        len(charges_not_cancelled) > 0
    ), "There should at least be one charge that is not cancelled"
    assert charges_not_cancelled[0].pretax_amount.amount == Decimal(
        "2623.00"
    ), "Price is not updated"
    assert charges_not_cancelled[0].applicable_date.time() == time(
        23, 59
    ), "Applicable time should be 23:59"
    print("===================>> Completed Test")


@pytest.mark.usefixtures('rt01_inventory')
def test_add_multiple_guests(
    booking_repo,
    bill_repo,
    client,
    create_booking_payload,
    room_type_inventory_repo,
):
    payload = {"data": json.loads(create_booking_payload)}
    booking_id = make_booking(client, payload)

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)

    assert len(booking_aggregate.room_stays[0].guest_stays) == 1

    booking_id = booking_aggregate.booking.booking_id

    room_stay = booking_aggregate.get_room_stay(1)
    assert len(room_stay.charge_ids) == 1, "Room Stay should have single charge"
    charge_id = room_stay.charge_ids[0]

    req = add_multiple_guest_stays()
    url = f"/v1/bookings/{booking_id}/room-stays/1/guest-stays-list"
    with mock_role_manager():
        response = client.post(
            url,
            data=req,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
    assert response.status_code == 201
    assert len(response.json["data"]["guest_stays"]) == 2

    updated_booking = booking_repo.load(booking_id)
    updated_bill = bill_repo.load(bill_aggregate.bill.bill_id)
    assert (
        updated_booking.booking.version == booking_aggregate.booking.version + 1
    ), "Booking version is not updated"
    assert (
        updated_bill.bill.version == bill_aggregate.bill.version + 1
    ), "Bill version is not updated"
    assert (
        len(updated_booking.room_stays[0].guest_stays) == 3
    ), "All guest stays are not added"
    assert len(updated_bill.charges) == 1, "No new charge should have been added"

    charge = updated_bill.get_charge(charge_id)
    assert charge.pretax_amount.amount == Decimal("4623.00"), "Price is not updated"
