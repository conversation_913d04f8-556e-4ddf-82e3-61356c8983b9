import copy
import datetime
import json

from treebo_commons.utils import dateutils

from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    checkin_booking,
    make_booking,
)
from prometheus.itests.api_wrappers.expense_wrappers import add_expenses_v2
from prometheus.itests.api_wrappers.invoice_wrappers import (
    get_proforma_invoice,
    preview_invoice,
)
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkin_payload,
)
from prometheus.itests.payload_generators.expense_payload_generators import (
    create_expenses_v2_request,
)
from prometheus.itests.payload_generators.invoice_payload_generators import (
    create_preview_invoice_payload,
    create_preview_invoice_with_charge_decision_payload,
)
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    PaymentInstruction,
)
from ths_common.constants.booking_constants import ExpenseStatus


def test_preview_invoices(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)

    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    expense_v2_request = create_expenses_v2_request(
        payment_instruction=PaymentInstruction.PAY_AT_CHECKOUT,
        expense_status=ExpenseStatus.CREATED,
        single_charge_split=True,
        charge_to=['2'],
    )
    add_expenses_v2(client, booking_id, expense_v2_request)

    preview_invoice_payload = create_preview_invoice_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    response = preview_invoice(
        booking_id, client, preview_invoice_payload, show_raw_response=True
    )
    preview_invoice_payload_with_decision = (
        create_preview_invoice_with_charge_decision_payload(
            booking_aggregate.booking.version, active_hotel_aggregate
        )
    )
    response = preview_invoice(
        booking_id,
        client,
        preview_invoice_payload_with_decision,
        show_raw_response=True,
    )


def test_preview_invoices_after_adding_allowance(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    bill_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)

    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    expense_v2_request = create_expenses_v2_request(
        payment_instruction=PaymentInstruction.PAY_AT_CHECKOUT,
        expense_status=ExpenseStatus.CREATED,
        single_charge_split=True,
    )
    response = add_expenses_v2(client, booking_id, expense_v2_request)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    bill_aggregate.consume_charges(
        charges=[bill_aggregate.get_charge(response.get('data')[0].get('charge_id'))],
        business_date=active_hotel_aggregate.hotel.current_business_date,
    )
    bill_repo.update(bill_aggregate)
    bill_id, charge_id = bill_aggregate.bill_id, bill_aggregate.charges[0].charge_id
    charge_split_id = bill_aggregate.charges[0].charge_splits[0].charge_split_id

    add_allowance_url = (
        'v1/bills/'
        + bill_id
        + '/charges/'
        + str(response.get('data')[0].get('charge_id'))
        + '/charge-splits/1/allowances'
    )
    new_allowance = dict(
        data=dict(pretax_amount='50 INR', remarks='add new allowance'),
        resource_version=bill_aggregate.current_version(),
    )
    headers = {"X-User-Type": "super-admin"}
    response = client.post(
        add_allowance_url,
        data=json.dumps(new_allowance),
        content_type='application/json',
        headers=headers,
    )

    preview_invoice_payload = create_preview_invoice_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    response = preview_invoice(
        booking_id, client, preview_invoice_payload, show_raw_response=True
    )
    preview_invoice_payload_with_decision = (
        create_preview_invoice_with_charge_decision_payload(
            booking_aggregate.booking.version, active_hotel_aggregate
        )
    )
    response = preview_invoice(
        booking_id,
        client,
        preview_invoice_payload_with_decision,
        show_raw_response=True,
    )


def create_preview_invoice_payload_for_multiple_room(
    booking_version, room_stay_guest_ids_map, booked_charges_to_post=None
):
    data = {
        "data": {
            "include_cancel_no_show_charges": False,
            "is_advanced": True,
            "room_wise_invoice_request": [
                {"guest_ids": guest_ids, "room_stay_id": room_stay_id}
                for (room_stay_id, guest_ids) in room_stay_guest_ids_map.items()
            ],
        },
        "resource_version": booking_version,
    }
    if booked_charges_to_post:
        data["data"]["booked_charges_to_post"] = booked_charges_to_post
    return data


def test_preview_invoice_should_show_room_level_checkout_date(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
):
    first_room_checkout = (
        dateutils.current_datetime() + datetime.timedelta(days=1)
    ).isoformat()
    second_room_checkout = (
        dateutils.current_datetime() + datetime.timedelta(days=2)
    ).isoformat()
    payload = json.loads(create_booking_payload)
    payload["room_stays"].append(copy.deepcopy(payload["room_stays"][0]))
    payload["room_stays"][1]["checkout_date"] = second_room_checkout
    payload["room_stays"][1]["room_type_id"] = "rt02"
    payload["room_stays"][1]["prices"].append(
        copy.deepcopy(payload["room_stays"][1]["prices"][0])
    )
    payload["room_stays"][1]["prices"][1]["applicable_date"] = first_room_checkout
    payload["default_billed_entity_category"] = BilledEntityCategory.PRIMARY_GUEST.value
    booking_id = make_booking(client, {"data": payload})
    booking_aggregate = booking_repo.load(booking_id)

    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(
        booking_aggregate.booking.version, is_of_two_room=True, guest_id=2
    )

    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    room_stay_guest_ids_map = {1: ["2"], 2: ["3"]}

    preview_invoice_payload = create_preview_invoice_payload_for_multiple_room(
        booking_aggregate.booking.version, room_stay_guest_ids_map
    )
    preview_invoice(booking_id, client, preview_invoice_payload, show_raw_response=True)
    preview_invoice_payload_with_decision = (
        create_preview_invoice_payload_for_multiple_room(
            booking_aggregate.booking.version,
            room_stay_guest_ids_map,
            booked_charges_to_post=[1, 2, 3],
        )
    )
    preview_invoice(
        booking_id,
        client,
        preview_invoice_payload_with_decision,
        show_raw_response=True,
    )
    invoice_templates = get_proforma_invoice(booking_id, client)["data"][
        "invoice_templates"
    ]
    first_room_inv_template, second_room_inv_template = invoice_templates
    first_room_inv_template_checkout_date = datetime.datetime.strptime(
        first_room_inv_template["booking"]["checkout_date"], "%Y-%m-%d %H:%M:%S%z"
    )
    assert (
        first_room_inv_template_checkout_date.date()
        == (dateutils.current_datetime() + datetime.timedelta(days=1)).date()
    )
    second_room_inv_template_checkout_date = datetime.datetime.strptime(
        second_room_inv_template["booking"]["checkout_date"], "%Y-%m-%d %H:%M:%S%z"
    )
    assert (
        second_room_inv_template_checkout_date.date()
        == (dateutils.current_datetime() + datetime.timedelta(days=2)).date()
    )
