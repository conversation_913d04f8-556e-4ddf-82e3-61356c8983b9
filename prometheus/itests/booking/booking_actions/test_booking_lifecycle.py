import json
from itertools import chain
from unittest.mock import patch

import pytest
from treebo_commons.utils import dateutils

from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.booking.entities import RoomStay
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import (
    cancel_booking,
    checkin_booking,
    checkout_booking,
    get_action,
    get_all_actions,
    make_booking,
    undo_checkin_booking,
    undo_checkout_booking,
)
from prometheus.itests.api_wrappers.invoice_wrappers import preview_invoice
from prometheus.itests.booking.test_booking_api import cancellation_request
from prometheus.itests.booking.test_roomstay_api import (
    create_room_change_payload,
    patch_room_stay,
)
from prometheus.itests.helpers import roll_over_business_date
from prometheus.itests.payload_generators.booking_action_payload_generators import (
    create_checkin_payload,
    create_checkout_payload,
)
from prometheus.itests.payload_generators.invoice_payload_generators import (
    create_preview_invoice_payload,
    create_preview_invoice_with_charge_decision_payload,
)
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import ChargeStatus, InvoiceStatus
from ths_common.constants.booking_constants import BookingStatus


def test_booking_create(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    bill_repo,
    booking_repo,
    booking_audit_trail_repo,
    room_type_inventory_repo,
    room_stay_overflow_repo,
):
    room_type_inventory = room_type_inventory_repo.load_multiple(
        active_hotel_aggregate.hotel.hotel_id,
        dateutils.current_date(),
        dateutils.add(dateutils.current_date(), days=1),
        room_type_ids=['rt01'],
    )
    room_type_inventory = room_type_inventory[0]
    availability = room_type_inventory.get_availability_for_date(
        dateutils.current_date()
    )
    availability.update_count(2)
    room_type_inventory_repo.update(room_type_inventory)

    original_count = availability.actual_count

    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    assert booking_id is not None

    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate is not None, "A booking aggregate should be created"
    assert len(booking_aggregate.room_stays) == 1, "A room stay should be created"
    assert (
        len(booking_aggregate.room_stays[0].guest_stays) == 1
    ), "A guest stay should be created"
    assert (
        booking_aggregate.booking.status == BookingStatus.CONFIRMED
    ), "Booking should be in confirmed state"
    assert len(booking_aggregate.customers) == 2, "2 customers should be created"

    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert bill_aggregate is not None, "A bill should be created"
    assert len(bill_aggregate.charges) == 1, "Bill should have a charge"

    audit_trail = booking_audit_trail_repo.load_for_booking(booking_id)
    assert (
        len(audit_trail) == 1
    ), "There should be an audit trail entry for booking create"

    room_type_inventory = room_type_inventory_repo.load_multiple(
        active_hotel_aggregate.hotel.hotel_id,
        dateutils.current_date(),
        dateutils.add(dateutils.current_date(), days=1),
        room_type_ids=['rt01'],
    )
    assert (
        room_type_inventory[0]
        .get_availability_for_date(dateutils.current_date())
        .actual_count
        == 1
    ), "Inventory availability should be reduced by 1"

    for i in range(2):
        payload = json.loads(create_booking_payload)
        payload['reference_number'] = 'REF-{0}'.format(i)
        payload['source']['channel_code'] = 'direct'
        booking_id = make_booking(client, {"data": payload})
        assert booking_id is not None

    room_type_inventory = room_type_inventory_repo.load_multiple(
        active_hotel_aggregate.hotel.hotel_id,
        dateutils.current_date(),
        dateutils.add(dateutils.current_date(), days=1),
        room_type_ids=['rt01'],
    )
    room_type_id = (
        room_type_inventory[0]
        .get_availability_for_date(dateutils.current_date())
        .room_type_id
    )
    assert (
        room_type_inventory[0]
        .get_availability_for_date(dateutils.current_date())
        .actual_count
        == -1
    ), "Inventory should be -1"

    assert (
        room_stay_overflow_repo.get_overflow_count_for_date(
            active_hotel_aggregate.hotel.hotel_id,
            dateutils.current_date(),
            room_type_id,
        )
        == 1
    ), "1 room stay should be marked as overflow"

    # Search for overbookings
    url = (
        'v1/bookings'
        + '?hotel_id='
        + str(active_hotel_aggregate.hotel.hotel_id)
        + '&overbookings=true'
    )
    response = client.get(url, headers={"X-User-Type": "cr-team"})
    assert response.status_code == 200
    bookings_response = json.loads(response.data.decode('utf-8'))
    rs_overflow_aggregates = room_stay_overflow_repo.get_overflowed_room_stays_for_date(
        active_hotel_aggregate.hotel.hotel_id, dateutils.current_date(), room_type_id
    )
    assert (
        len(bookings_response['data']['bookings']) == 1
    ), "1 overbooking should be marked overflow"
    assert (
        bookings_response['data']['bookings'][0]['booking_id']
        == rs_overflow_aggregates[0].room_stay_overflow.booking_id
    )
    assert bookings_response['data']['bookings'][0]['room_stays'][0][
        'is_overflow'
    ], "Room stay must be marked overflowed"


def test_booking_checkin(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    room_allotment_repo,
    hotel_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    assert booking_id is not None
    booking_aggregate = booking_repo.load(booking_id)
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)

    booking_aggregate = booking_repo.load(booking_id)
    assert (
        booking_aggregate.booking.status == BookingStatus.CHECKED_IN
    ), "Booking should move to checked-in state"
    assert all(
        rs.status == BookingStatus.CHECKED_IN for rs in booking_aggregate.room_stays
    ), "All roomstays should be checked in"
    guest_stays = chain(*[rs.guest_stays for rs in booking_aggregate.room_stays])
    assert all(
        gs.status == BookingStatus.CHECKED_IN for gs in guest_stays
    ), "All guest_stays should be checked in"

    room_allotment_aggregate = room_allotment_repo.load(
        active_hotel_aggregate.hotel.hotel_id, "15"
    )
    assert (
        len(room_allotment_aggregate.all_room_allotments) == 1
    ), "A room allotment should be created for room 15"


def test_room_stay_checkin_should_add_room_no_to_all_charges(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    bill_repo,
    hotel_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    assert booking_id is not None
    booking_aggregate = booking_repo.load(booking_id)
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    room_stay_to_checkin = checkin_payload['data']['payload']['checkin']['room_stays'][
        0
    ]['room_stay_id']
    checkin_booking(client, booking_id, checkin_payload)

    booking_aggregate = booking_repo.load(booking_id)
    room_stay = booking_aggregate.get_room_stay(room_stay_to_checkin)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)

    charges = bill_aggregate.filter_and_get_charges(
        room_stay.charge_ids, allowed_charge_status=[ChargeStatus.CREATED]
    )
    assert all(c.item.details.get('room_no') is not None for c in charges)
    assert all(c.item.details.get('room_type') is not None for c in charges)


@pytest.mark.parametrize(
    'create_booking_payload',
    [dict(checkin_date=dateutils.subtract(dateutils.current_datetime(), days=1))],
    indirect=True,
)
def test_past_dated_booking_checkin_when_business_date_is_1_day_less_than_calendar_date(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    room_allotment_repo,
    hotel_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    assert booking_id is not None
    booking_aggregate = booking_repo.load(booking_id)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    assert (
        booking_aggregate.booking.status == BookingStatus.CHECKED_IN
    ), "Booking should move to checked-in state"


@pytest.mark.parametrize(
    'create_booking_payload',
    [dict(checkin_date=dateutils.subtract(dateutils.current_datetime(), days=1))],
    indirect=True,
)
@patch('treebo_commons.utils.dateutils.current_datetime')
def test_yesterday_booking_checkin_should_work_current_calendar_datetime_is_less_than_switch_over_time_on_current_date(
    mocked_current_datetime,
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    room_allotment_repo,
    hotel_repo,
):
    """
    Hotel Aggregate current business date is of yesterday's
    Booking's checkin date is also yesterday.

    We mock the current datetime to move time before switch over time
    """
    import datetime

    from treebo_commons.request_tracing.context import get_app_timezone
    from treebo_commons.utils.dateutils import tzlocal

    tz = get_app_timezone() or tzlocal

    # Mock dateutils.current_datetime() return value as 1 minute before switch over time of current date

    mocked_current_datetime.return_value = dateutils.subtract(
        dateutils.datetime_at_given_time(
            datetime.datetime.now(tz),
            datetime.datetime.strptime(
                active_hotel_aggregate.hotel.switch_over_time, "%H:%M:%S"
            ).time(),
        ),
        minutes=1,
    )

    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    assert booking_id is not None
    booking_aggregate = booking_repo.load(booking_id)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    assert (
        booking_aggregate.booking.status == BookingStatus.CHECKED_IN
    ), "Booking should move to checked-in state"


def test_booking_undo_checkin(
    active_hotel_aggregate, create_booking_payload, client, booking_repo, hotel_repo
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    assert booking_id is not None
    booking_aggregate = booking_repo.load(booking_id)
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    action_id = checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CHECKED_IN

    undo_checkin_booking(client, booking_id, action_id)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CONFIRMED


def test_booking_preview_invoice(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    booking_invoice_group_repo,
    hotel_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_invoice_group_aggregate = booking_invoice_group_repo.load(invoice_group_id)
    assert booking_invoice_group_aggregate is not None


def test_booking_checkout(
    active_hotel_aggregate, create_booking_payload, client, booking_repo, hotel_repo
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)

    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    checkout_booking(client, booking_id, checkout_payload)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CHECKED_OUT


@pytest.mark.parametrize(
    'create_booking_payload',
    [dict(checkin_date=dateutils.subtract(dateutils.current_datetime(), days=1))],
    indirect=True,
)
@patch('treebo_commons.utils.dateutils.current_datetime')
def test_booking_checkout_works_when_calendar_date_is_more_than_business_date_with_time_less_than_switch_over(
    mocked_current_datetime,
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    bill_repo,
):
    import datetime

    from treebo_commons.request_tracing.context import get_app_timezone
    from treebo_commons.utils.dateutils import tzlocal

    tz = get_app_timezone() or tzlocal

    mocked_current_datetime.return_value = dateutils.subtract(
        dateutils.datetime_at_given_time(
            datetime.datetime.now(tz),
            datetime.datetime.strptime(
                active_hotel_aggregate.hotel.switch_over_time, "%H:%M:%S"
            ).time(),
        ),
        minutes=10,
    )

    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)

    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    checkout_booking(client, booking_id, checkout_payload)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CHECKED_OUT

    # Assert that 1 room night charge is consumed
    # Even though checkin and checkout is on same business date
    room_stay: RoomStay = booking_aggregate.get_room_stay(1)
    charge_id = room_stay.get_charge_for_date(
        active_hotel_aggregate.hotel.current_business_date
    )
    bill_aggregate: BillAggregate = bill_repo.load(booking_aggregate.bill_id)
    charge = bill_aggregate.get_charge(charge_id)
    assert charge.status == ChargeStatus.CONSUMED
    assert charge.is_completely_invoiced()


def test_booking_checkout_should_lock_billed_entity_account(
    client,
    active_hotel_aggregate,
    create_booking_payload,
    booking_repo,
    bill_repo,
    invoice_repo,
    booking_invoice_group_repo,
    hotel_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)

    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    checkout_booking(client, booking_id, checkout_payload)

    booking_invoice_group_aggregate = booking_invoice_group_repo.load(invoice_group_id)
    invoice_ids = booking_invoice_group_aggregate.booking_invoice_group.invoice_ids
    invoice_aggregates = invoice_repo.load_all(invoice_ids)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)

    for invoice_aggregate in invoice_aggregates:
        billed_entity_account = invoice_aggregate.invoice.billed_entity_account
        bill_aggregate = bill_repo.load(bill_aggregate.bill_id)
        assert (
            bill_aggregate.get_billed_entity(billed_entity_account.billed_entity_id)
            .get_account(billed_entity_account.account_number)
            .is_locked()
        )


def test_booking_undo_checkout(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    booking_audit_trail_repo,
    hotel_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    preview_invoice_payload = create_preview_invoice_with_charge_decision_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)

    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    action_id = checkout_booking(client, booking_id, checkout_payload)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CHECKED_OUT

    undo_checkout_booking(client, booking_id, action_id)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.status == BookingStatus.CHECKED_IN

    audit_trail = booking_audit_trail_repo.load_for_booking(booking_id)
    undo_cancel_trail = [
        audit
        for audit in audit_trail
        if audit.audit_trail.audit_type == AuditType.CHECKOUT_REVERSED
    ]
    assert (
        len(undo_cancel_trail) == 1
    ), "There should be exactly one audit trail event for checkout reversal"


@pytest.mark.skip
def test_booking_get_all_actions(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    booking_modification_item,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    checkin_payload = create_checkin_payload(booking_aggregate.booking.version)
    checkin_action_id = checkin_booking(client, booking_id, checkin_payload)
    booking_aggregate = booking_repo.load(booking_id)

    actions = get_all_actions(client, booking_id)
    checkin_action = [
        action for action in actions['data'] if action['action_id'] == checkin_action_id
    ][0]
    assert checkin_action['reversal'] == 'allowed', "Checkin should be reversible"
    checkin_action_get_response = get_action(client, booking_id, checkin_action_id)
    assert (
        checkin_action_get_response['data']['reversal'] == 'allowed'
    ), "Checkin should be reversible"

    preview_invoice_payload = create_preview_invoice_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)

    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    checkout_action_id = checkout_booking(client, booking_id, checkout_payload)

    actions = get_all_actions(client, booking_id)
    checkin_action = [
        action for action in actions['data'] if action['action_id'] == checkin_action_id
    ][0]
    checkout_action = [
        action
        for action in actions['data']
        if action['action_id'] == checkout_action_id
    ][0]
    assert checkin_action['reversal'] == 'disallowed', "Checkin should be disallowed"
    assert checkout_action['reversal'] == 'allowed', "Checkout should be reversible"
    checkin_action_get_response = get_action(client, booking_id, checkin_action_id)
    assert (
        checkin_action_get_response['data']['reversal'] == 'disallowed'
    ), "Checkin should be disallowed"
    checkout_action_get_reponse = get_action(client, booking_id, checkout_action_id)
    assert (
        checkout_action_get_reponse['data']['reversal'] == 'allowed'
    ), "Checkout should be reversible"

    undo_checkout_booking(client, booking_id, checkout_action_id)
    actions = get_all_actions(client, booking_id)
    checkin_action = [
        action for action in actions['data'] if action['action_id'] == checkin_action_id
    ][0]
    checkout_action = [
        action
        for action in actions['data']
        if action['action_id'] == checkout_action_id
    ][0]
    assert (
        checkin_action['reversal'] == 'disallowed'
    ), "Checkin should not be reversible"
    assert checkout_action['reversal'] == 'irreversible', "Checkin should be reversible"
    checkin_action_get_response = get_action(client, booking_id, checkin_action_id)
    assert (
        checkin_action_get_response['data']['reversal'] == 'disallowed'
    ), "Checkin should not be allowed"
    checkout_action_get_reponse = get_action(client, booking_id, checkout_action_id)
    assert (
        checkout_action_get_reponse['data']['reversal'] == 'irreversible'
    ), "Checkout should be irreversible"


def test_booking_reverse_checkout_is_adding_invoice_number_to_account_for_reuse_and_marking_account_as_uninvoiced_unlocked_and_invoice_as_cancelled(
    reverse_checkout_after_booking_and_checkout,
    booking_invoice_group_repo,
    invoice_repo,
    bill_repo,
):
    (
        booking_aggregate,
        invoice_group_id,
        action_id,
    ) = reverse_checkout_after_booking_and_checkout
    booking_invoice_group_aggregate = booking_invoice_group_repo.load(invoice_group_id)
    invoice_aggregates = invoice_repo.load_all(
        booking_invoice_group_aggregate.invoice_ids
    )
    bill_aggregates = bill_repo.load(booking_aggregate.bill_id)
    for invoice_aggregate in invoice_aggregates:
        billed_entity_account_vo = invoice_aggregate.invoice.billed_entity_account
        bea = bill_aggregates.get_billed_entity(
            billed_entity_account_vo.billed_entity_id
        ).get_account(billed_entity_account_vo.account_number)
        assert bea.invoice_numbers_available_for_use is not None
        assert len(bea.invoice_numbers_available_for_use['invoice_numbers']) > 0
        assert bea.is_invoiced() is False
        assert bea.is_locked() is False
        assert invoice_aggregate.invoice.status == InvoiceStatus.CANCELLED


def test_invoice_being_reused_at_checkout_after_reverse_checkout(
    reverse_checkout_after_booking_and_checkout,
    booking_invoice_group_repo,
    invoice_repo,
    bill_repo,
    active_hotel_aggregate,
    client,
    booking_repo,
):
    (
        booking_aggregate,
        invoice_group_id,
        action_id,
    ) = reverse_checkout_after_booking_and_checkout
    booking_invoice_group_aggregate = booking_invoice_group_repo.load(invoice_group_id)
    invoice_aggregates = invoice_repo.load_all(
        booking_invoice_group_aggregate.invoice_ids
    )
    bill_aggregates = bill_repo.load(booking_aggregate.bill_id)
    invoice_bea_list = []
    for invoice_aggregate in invoice_aggregates:
        billed_entity_account_vo = invoice_aggregate.invoice.billed_entity_account
        account = bill_aggregates.get_billed_entity(
            billed_entity_account_vo.billed_entity_id
        ).get_account(billed_entity_account_vo.account_number)
        inv_list = dict(
            billed_entity_account=invoice_aggregate.invoice.billed_entity_account,
            invoice_number=account.invoice_numbers_available_for_use['invoice_numbers'],
        )
        invoice_bea_list.append(inv_list)

    preview_invoice_payload = create_preview_invoice_payload(
        booking_aggregate.booking.version, active_hotel_aggregate
    )
    booking_id = booking_aggregate.booking_id
    invoice_group_id = preview_invoice(booking_id, client, preview_invoice_payload)
    booking_aggregate = booking_repo.load(booking_id)

    checkout_payload = create_checkout_payload(
        booking_aggregate.booking.version, active_hotel_aggregate, invoice_group_id
    )
    action_id = checkout_booking(client, booking_id, checkout_payload)
    booking_invoice_group_aggregate_new = booking_invoice_group_repo.load(
        invoice_group_id
    )
    invoice_aggregates_new = invoice_repo.load_all(
        booking_invoice_group_aggregate_new.invoice_ids
    )
    bill_aggregates_refreshed = bill_repo.load(booking_aggregate.bill_id)
    invoice_bea_list_new = []
    for invoice_aggregate_new in invoice_aggregates_new:
        inv_list = dict(
            billed_entity_account=invoice_aggregate_new.invoice.billed_entity_account,
            invoice_number=[invoice_aggregate_new.invoice_number],
        )
        invoice_bea_list_new.append(inv_list)
    assert invoice_bea_list == invoice_bea_list_new


def test_cancel_booking_marking_all_allotments_as_deleted(
    create_booking_payload,
    active_hotel_aggregate,
    booking_repo,
    client,
    hotel_repo,
    room_repo,
    temp_create_new_room,
    room_allotment_repo,
):
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    assert booking_id is not None

    booking_aggregate = booking_repo.load(booking_id)

    new_room_id = "15"
    room_change_payload = create_room_change_payload(
        booking_aggregate.booking.version, new_room_id, "rt01"
    )
    patch_room_stay(client, booking_id, room_change_payload)

    booking_aggregate = booking_repo.load(booking_id)

    new_room_id = "16"
    room_change_payload = create_room_change_payload(
        booking_aggregate.booking.version, new_room_id, "rt01"
    )
    patch_room_stay(client, booking_id, room_change_payload)

    booking_aggregate = booking_repo.load(booking_id)

    new_room_id = "15"
    room_change_payload = create_room_change_payload(
        booking_aggregate.booking.version, new_room_id, "rt01"
    )
    patch_room_stay(client, booking_id, room_change_payload)

    booking_aggregate = booking_repo.load(booking_id)

    cancel_payload = cancellation_request(booking_aggregate.booking.version)
    cancel_booking(client, booking_id, cancel_payload)
    booking_aggregate = booking_repo.load(booking_id)
    room_allotments = room_allotment_repo.load_multiple(
        booking_aggregate.booking.hotel_id
    )
    not_deleted_room_allotments = [
        allotment
        for x in room_allotments.values()
        for allotment in x.all_room_allotments
        if allotment.deleted is False
    ]
    assert len(not_deleted_room_allotments) == 0
