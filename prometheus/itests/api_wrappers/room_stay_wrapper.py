import datetime
import json

from treebo_commons.utils.dateutils import date_range

from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_rate_manager_client,
    mock_role_manager,
    mock_tenant_config,
)
from ths_common.constants.catalog_constants import SellerType


def get_room_stay_rate_plan_change_room_level_payload(
    checkin_date, checkout_date, resource_version=1
):
    price_applicable_dates = list(date_range(checkin_date, checkout_date))
    return json.dumps(
        {
            "data": {
                "prices": [
                    {
                        "applicable_date": applicable_date.isoformat(),
                        "bill_to_type": "company",
                        "pretax_amount": 118,
                        "type": "non-credit",
                    }
                    for applicable_date in price_applicable_dates
                ],
                "rate_plan": {"rate_plan_reference_id": "RoomLevelChanged"},
                "rate_plan_inclusions": [
                    {
                        "sku_id": "377",
                        "pretax_amount": "345",
                        "start_date": checkin_date.isoformat(),
                        "end_date": (
                            checkout_date - datetime.timedelta(days=1)
                        ).isoformat(),
                    }
                ],
            },
            "resource_version": resource_version,
        }
    )


def change_room_stay_rate_plan(
    client,
    booking_id,
    checkin_date,
    checkout_date,
    rate_manager_enabled=True,
    resource_version=1,
):
    with mock_role_manager() and mock_rate_manager_client() and mock_tenant_config(
        [
            {
                "config_name": "rate_manager_enabled",
                "config_value": "true" if rate_manager_enabled else "false",
                "value_type": "boolean",
            }
        ]
    ):
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            room_stay_url = 'v1/bookings/{0}/room-stays/{1}/update-rate-plan'.format(
                booking_id, 1
            )
            room_stay_rate_plan_change_payload = (
                get_room_stay_rate_plan_change_room_level_payload(
                    checkin_date, checkout_date, resource_version=resource_version
                )
            )
            response = client.post(
                room_stay_url,
                data=room_stay_rate_plan_change_payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            if rate_manager_enabled:
                assert response.status_code == 200
            else:
                assert response.status_code == 400
