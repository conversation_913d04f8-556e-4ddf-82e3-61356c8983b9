import json

from prometheus.tests.mockers import (
    mock_aws_service_client,
    mock_catalog_client,
    mock_role_manager,
    mock_tax_calculator_service,
    mock_template_service,
)


def preview_invoice(
    booking_id, client, payload, show_invoice_ids=False, show_raw_response=False
):
    with mock_role_manager():
        with mock_catalog_client():
            url = f"v1/bookings/{booking_id}/invoices"
            payload = json.dumps(payload)
            response = client.post(
                url,
                data=payload,
                content_type="application/json",
                headers={"X-User-Type": "super-admin"},
            )
            assert response.status_code == 200
            if show_invoice_ids:
                invoice_previews = response.json["data"]["invoice_previews"]
                assert invoice_previews
                invoice_ids = [ip['invoice_id'] for ip in invoice_previews]
    invoice_group_id = response.json["data"]["invoice_group_id"]
    if show_raw_response:
        return response.json
    return invoice_group_id if not show_invoice_ids else (invoice_group_id, invoice_ids)


def get_invoices(booking_id, client, show_raw=False):
    url = f"v1/bookings/{booking_id}/invoices"
    if show_raw:
        url = url + "?show_raw=true"
    response = client.get(
        url,
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    assert response.status_code == 200
    return response.json


def get_proforma_invoice(booking_id, client):
    with mock_template_service(), mock_aws_service_client():
        url = f"v1/bookings/{booking_id}/proforma-invoices"
        response = client.get(
            url,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert response.status_code == 200
        return response.json
