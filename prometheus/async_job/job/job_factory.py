import datetime

from prometheus.async_job.job.aggregates.job_aggregate import JobAggregate
from prometheus.async_job.job.dto.job_dto import AsyncJobD<PERSON>, ScheduledJobDTO
from prometheus.async_job.job.entities.job_entity import JobEntity
from ths_common.utils.id_generator_utils import random_id_generator


class JobFactory:
    @staticmethod
    def create_job(job_dto: AsyncJobDTO):
        entity = JobEntity(
            job_dto.job_name,
            job_dto.hotel_id,
            job_dto.data,
            None,
            job_id=random_id_generator('JOB'),
            seller_id=job_dto.seller_id,
        )
        if isinstance(job_dto, ScheduledJobDTO):
            entity.eta = job_dto.eta
        elif isinstance(job_dto, AsyncJobDTO):
            entity.eta = datetime.datetime.utcnow()
        else:
            raise TypeError('Unknown job type')
        return JobAggregate(entity)
