import datetime
import logging

from prometheus.common.decorators import timed
from prometheus.easyjoblite.constants import API_LOCAL
from ths_common.constants.scheduled_job_constants import JobStatus

logger = logging.getLogger(__name__)


class JobDomainService(object):
    def __init__(self, job_orchestrator):
        self.job_orchestrator = job_orchestrator

    @timed
    def schedule_for_execution(self, job_aggregate, handler_function):
        job_entity = job_aggregate.job_entity

        try:
            self.job_orchestrator.setup_entities()  # initializes the easy job orchestrator if not already done (lazy
            #  init)
            self.job_orchestrator.enqueue_job(
                api=handler_function,
                type=API_LOCAL,
                tag=job_entity.job_id,
                data=dict(
                    job_id=job_entity.job_id,
                    job_name=job_entity.job_name,
                    job_args=job_entity.data,
                ),
            )
            logger.info(
                'Job job_name: %s, id: %s has been enqueued.',
                job_aggregate.job_entity.job_name,
                job_aggregate.job_entity.job_id,
            )
            job_entity.status = JobStatus.PROCESSING
            job_entity.picked_at = datetime.datetime.utcnow()
            return job_aggregate
        except Exception as e:
            logger.exception(
                'Error while scheduling job with id: %s', job_entity.job_id
            )
            job_entity.status = JobStatus.FAILED
            job_entity.failure_message = str(e)
            return job_aggregate
