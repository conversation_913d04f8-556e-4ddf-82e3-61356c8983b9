# coding=utf-8
"""
payment API's
"""
import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.billing.command_handlers.add_allowance import (
    AddAllowancesCommandHandler,
)
from prometheus.application.billing.command_handlers.add_charge import (
    AddChargeCommandHandler,
)
from prometheus.application.billing.command_handlers.add_payments import (
    AddPaymentsCommandHandler,
)
from prometheus.application.billing.command_handlers.block_credit_note_sequence import (
    BlockCreditNoteSequenceCommandHandler,
)
from prometheus.application.billing.command_handlers.block_invoice_sequence import (
    BlockInvoiceSequenceCommandHandler,
)
from prometheus.application.billing.command_handlers.cancel_credit_note import (
    CancelCreditNoteCommandHandler,
)
from prometheus.application.billing.command_handlers.create_new_bill import (
    CreateNewBillCommandHandler,
)
from prometheus.application.billing.command_handlers.generate_credit_note import (
    GenerateCreditNoteCommandHandler,
)
from prometheus.application.billing.command_handlers.generate_credit_note_template import (
    GenerateCreditNoteTemplateCommandHandler,
)
from prometheus.application.billing.command_handlers.generate_hotel_to_reseller_credit_note import (
    GenerateHotelToResellerCreditNoteCommandHandler,
)
from prometheus.application.billing.command_handlers.generate_invoices import (
    GenerateInvoicesCommandHandler,
)
from prometheus.application.billing.command_handlers.modify_locked_invoices import (
    ModifyLockedInvoicesCommandHandler,
)
from prometheus.application.billing.command_handlers.pos.generate_invoice_template import (
    GeneratePosInvoiceTemplateCommandHandler,
)
from prometheus.application.billing.command_handlers.print_bill import (
    PrintBillCommandHandler,
)
from prometheus.application.billing.command_handlers.redistribute_payments import (
    RedistributePaymentsCommandHandler,
)
from prometheus.application.billing.command_handlers.refresh_tax_on_account import (
    RefreshTaxCommandHandler,
)
from prometheus.application.billing.command_handlers.reissue_locked_reseller_invoice_for_non_financial_changes import (
    ModifyLockedResellerInvoiceCommandHandler,
)
from prometheus.application.billing.command_handlers.settle_by_spot_credit import (
    SettleBySpotCreditCommandHandler,
)
from prometheus.application.billing.command_handlers.transfer_charge import (
    TransferChargeCommandHandler,
)
from prometheus.application.billing.command_handlers.update_allowance import (
    UpdateAllowanceCommandHandler,
)
from prometheus.application.billing.command_handlers.update_charges import (
    UpdateChargesCommandHandler,
)
from prometheus.application.billing.command_handlers.update_payments import (
    UpdatePaymentsCommandHandler,
)
from prometheus.application.billing.command_handlers.void_bill import (
    VoidBillCommandHandler,
)
from prometheus.application.billing.dtos.invoice_reissue_dto import (
    ModifyLockedInvoicesDto,
)
from prometheus.application.billing.dtos.new_bill_dto import NewBillDto
from prometheus.application.billing.dtos.spot_credit_settlement_dto import (
    SpotCreditSettlementData,
)
from prometheus.application.billing.einvoice_application_service import (
    EInvoiceApplicationService,
)
from prometheus.application.billing.query_handlers.get_bill import GetBillQueryHandler
from prometheus.application.billing.query_handlers.get_billed_entities import (
    GetBilledEntitiesQueryHandler,
)
from prometheus.application.billing.query_handlers.get_bills import GetBillsQueryHandler
from prometheus.application.billing.query_handlers.get_charge import (
    GetChargeQueryHandler,
)
from prometheus.application.billing.query_handlers.get_charges import (
    GetChargesQueryHandler,
)
from prometheus.application.billing.query_handlers.get_credit_note_by_id import (
    GetCreditNoteByIdQueryHandler,
)
from prometheus.application.billing.query_handlers.get_credit_note_template import (
    GetCreditNoteTemplateQueryHandler,
)
from prometheus.application.billing.query_handlers.get_credit_notes_for_bill import (
    GetCreditNotesQueryHandler,
)
from prometheus.application.billing.query_handlers.get_folio_summary import (
    GetFolioSummaryQueryHandler,
)
from prometheus.application.billing.query_handlers.get_folios_einvoice_eligibility import (
    GetFoliosEInvoiceEligibilityQueryHandler,
)
from prometheus.application.billing.query_handlers.get_invoice_by_id import (
    GetInvoiceByIdQueryHandler,
)
from prometheus.application.billing.query_handlers.get_invoices_by_ids import (
    GetInvoiceByIdsQueryHandler,
)
from prometheus.application.billing.query_handlers.get_invoices_for_bill import (
    GetInvoicesForBillQueryHandler,
)
from prometheus.application.billing.query_handlers.get_payment import (
    GetPaymentQueryHandler,
)
from prometheus.application.billing.query_handlers.get_payment_by_ref_id import (
    GetPaymentByRefQueryHandler,
)
from prometheus.application.billing.query_handlers.get_payments import (
    GetPaymentsQueryHandler,
)
from prometheus.application.billing.query_handlers.get_refund_details import (
    RefundDetailsQueryHandler,
)
from prometheus.application.billing.query_handlers.get_refund_mode import (
    GetRefundModeQueryHandler,
)
from prometheus.application.booking.command_handlers.invoice.generate_invoice_template import (
    GenerateInvoiceTemplateCommandHandler,
)
from prometheus.application.booking.command_handlers.invoice.invoice_accounts import (
    InvoiceAccountsCommandHandler,
)
from prometheus.common.api_response import ApiResponse
from prometheus.common.decorators import authorize_write_op
from prometheus.common.request_parsers import (
    read_application_from_request_header,
    read_last_version_from_header,
    read_user_data_from_request_header,
)
from prometheus.common.serializers.request.billing import (
    AllowanceSchema,
    AllowancesSchema,
    BillGetFieldFilterSchema,
    BillsGetSchema,
    ChargePaginationSchema,
    CreditNoteNumberBlockSchema,
    EditAllowanceSchema,
    EditChargeSchema,
    EditChargesSchema,
    EditPaymentSchema,
    EditPaymentsSchema,
    FolioSummaryRequestSchema,
    InvoiceAccountsSchema,
    InvoiceGetSchema,
    InvoiceNumberBlockSchema,
    InvoicesGetSchema,
    InvoicesModificationRequestSchema,
    IrnGenerationRequestSchema,
    NewBillSchema,
    NewChargeSchema,
    NewCreditNoteSchema,
    NewInvoiceSchema,
    NewPaymentsSchema,
    RedistributePaymentsSchema,
    RefundDetailsRequestSchema,
    RefundSummaryRequestSchema,
    SettleBySpotCreditsSchema,
    TaxRecalculationAccountsSchema,
    TransferChargeSchema,
    TransferChargesSchema,
    platform_fees,
)
from prometheus.common.serializers.request.booking import BookingInvoiceTemplateRequest
from prometheus.common.serializers.response.billing import (
    AllowanceResponseSchema,
    BilledEntityBalanceSchema,
    BilledEntitySchema,
    BillingCreditNoteTemplateResponse,
    BillSchema,
    BillSummarySchema,
    ChargeSchema,
    CreditNoteSchema,
    FolioEInvoiceEligibilityResponseSchema,
    FolioResponseSchema,
    FolioSummarySchema,
    NewPaymentSchema,
    PaymentDetailsSchema,
    PaymentFailureSchema,
    PaymentSchema,
    RefundDetailsResponseSchema,
    RefundSummaryResponseSchema,
    ShallowBillResponseSchema,
    TransferChargeResponseSchema,
    TransferChargesResponseSchema,
)
from prometheus.common.serializers.response.booking import (
    BookingInvoiceTemplateResponse,
)
from prometheus.common.serializers.response.invoice import (
    InvoiceRawSchema,
    InvoiceSchema,
    ModifyInvoicesResponseSchema,
    PosOrderInvoiceTemplateResponse,
)
from prometheus.core.api_docs import swag_route
from prometheus.domain.billing.dto.allowance_data import (
    AllowanceData,
    EditAllowanceData,
)
from prometheus.domain.billing.dto.charge_data import ChargeData
from prometheus.domain.billing.dto.chargesplit_data import ChargeSplitData
from prometheus.domain.billing.dto.edit_charge_data import EditChargeData
from prometheus.domain.billing.dto.payment_data import PaymentData
from prometheus.domain.billing.dto.refund_summary_dto import RefundSummaryDto
from schema_instances import get_schema_obj
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_and_version_parser,
    schema_wrapper_parser,
)
from ths_common.constants.billing_constants import (
    BillAppId,
    ChargeBillToTypes,
    ChargeSplitType,
    ChargeStatus,
    ChargeTypes,
)
from ths_common.exceptions import NotModifiedException, ValidationException
from ths_common.value_objects import BillParentInfo, NotAssigned

bp = Blueprint('Bill', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@swag_route
@bp.route('/bills', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(NewBillSchema)
@inject(command_handler=CreateNewBillCommandHandler)
def add_bill(command_handler: CreateNewBillCommandHandler, parsed_request):
    """Add bill
    ---
    operationId: add_bill
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    post:
        description: Add bill.
        tags:
            - Bills
        parameters:
            - in: body
              name: body
              description: The bill object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/NewBillSchema"
        responses:
            200:
                description: The created bill.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/BillSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    charge_dtos = []
    for charge in parsed_request.get('charges'):
        charge_split_datas = []
        if charge.get('charge_splits'):
            charge_split_datas = [
                ChargeSplitData(
                    charge_to=data['charge_to'], percentage=data.get('percentage')
                )
                for data in charge.get('charge_splits')
            ]
        charge_split_type = (
            ChargeSplitType(charge['charge_split_type'])
            if charge.get('charge_split_type')
            else None
        )

        charge_dtos.append(
            ChargeData(
                applicable_date=charge.get('applicable_date'),
                item=charge.get('item'),
                pretax_amount=charge.get('pretax_amount'),
                posttax_amount=charge.get('posttax_amount'),
                charge_type=ChargeTypes(charge.get('type')),
                bill_to_type=ChargeBillToTypes(charge.get('bill_to_type')),
                comment=charge.get('comment'),
                charge_split_type=charge_split_type,
                charge_splits=charge_split_datas,
                status=ChargeStatus(charge.get('status')),
            )
        )

    new_bill_dto = NewBillDto(
        bill_date=parsed_request.get('bill_date'),
        app_id=parsed_request.get('app_id'),
        parent_reference_number=parsed_request.get('parent_reference_number'),
        parent_info=BillParentInfo(parsed_request.get('parent_info')),
        vendor_id=parsed_request.get('vendor_id'),
        vendor_details=parsed_request.get('vendor_details'),
        charges=charge_dtos,
    )
    bill_aggregate = command_handler.handle(new_bill_dto)

    bill_schema = get_schema_obj(BillSchema)
    response = bill_schema.dump(bill_aggregate)
    return ApiResponse.build(data=response.data, status_code=200)


def generate_bill_response(bill_schema, bill_aggregate):
    response = bill_schema.dump(bill_aggregate)
    return response.data


@swag_route
@bp.route('/bills/<string:bill_id>', methods=['GET'])
@schema_wrapper_parser(BillGetFieldFilterSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetBillQueryHandler)
def get_bill(query_handler: GetBillQueryHandler, bill_id, parsed_request):
    """Get bill
    ---
    operationId: get_bill
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get bill details.
        parameters:
            - in: path
              name: bill_id
              description: The bill id which needs to be fetched
              required: True
              type: string
            - name: field_filter
              in: query
              required: false
              schema:
                $ref: "#/components/schemas/BillGetFieldFilterSchema"
        tags:
            - Bills
        responses:
            200:
                description: The fetched bill.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/ShallowBillResponseSchema"
                        data_non_shallow:
                            $ref: "#/components/schemas/BillSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    last_fetched_version = read_last_version_from_header()

    shallow_response = parsed_request.get("shallow_response")
    use_raw_query = parsed_request.get("use_raw_query")

    try:
        bill_aggregate = query_handler.handle(
            bill_id,
            last_fetched_version=last_fetched_version,
            use_raw_query=use_raw_query,
        )

    except NotModifiedException:
        return ApiResponse.build(data=None, status_code=304)

    if shallow_response:
        bill_schema = get_schema_obj(ShallowBillResponseSchema)
    else:
        bill_schema = get_schema_obj(BillSchema)

    response_data = generate_bill_response(bill_schema, bill_aggregate)
    return ApiResponse.build(data=response_data, status_code=200)


@swag_route
@bp.route('/bills', methods=['GET'])
@schema_wrapper_parser(BillsGetSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetBillsQueryHandler)
def get_bills(query_handler: GetBillsQueryHandler, parsed_request):
    """Get bills
    ---
    operationId: get_bills
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get bills details.
        parameters:
            - in: query
              name: bill_ids
              description: The bill ids which needs to be fetched
              required: True
              schema:
                $ref: "#/components/schemas/BillsGetSchema"
        tags:
            - Bills
        responses:
            200:
                description: The fetched bills.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/BillSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """

    bill_ids = parsed_request.get("bill_ids")
    booking_id = parsed_request.get("booking_id")

    bill_aggregates = query_handler.handle(bill_ids, booking_id=booking_id)
    if parsed_request.get('summary'):
        bill_response = BillSummarySchema(many=True).dump(bill_aggregates)
    else:
        bill_response = BillSchema(many=True).dump(bill_aggregates)
    return ApiResponse.build(data=dict(bills=bill_response.data), status_code=200)


@swag_route
@bp.route('/bills/<string:bill_id>/print', methods=['GET'])
@inject(command_handler=PrintBillCommandHandler)
def print_bill(command_handler: PrintBillCommandHandler, bill_id):
    """Print Bill
    ---
    operationId: print_bill
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Generates bill pdf url
        parameters:
            - in: path
              name: bill_id
              description: The bill id which needs to be fetched
              required: True
              type: string
        tags:
            - Bill url
        responses:
            200:
                description: Url of the fetched bill.
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                url:
                                    type: string
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    url = command_handler.handle(bill_id)
    return ApiResponse.build(data=dict(url=url), status_code=200)


@swag_route
@bp.route('/bills/<string:bill_id>/void', methods=['POST'])
@inject(command_handler=VoidBillCommandHandler)
def void_bill(command_handler: VoidBillCommandHandler, bill_id):
    """Void Bill
    ---
    operationId: void_bill
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Voids a bill
        parameters:
            - in: path
              name: bill_id
              description: The bill id which needs to be voided
              required: True
              type: string
        tags:
            - Bills
        responses:
            200:
                description: The voided bill.
    """
    command_handler.handle(bill_id)
    return ApiResponse.build(data={}, status_code=200)


@swag_route
@bp.route('/bills/<string:bill_id>/payments', methods=['POST'])
@authorize_write_op
@schema_wrapper_and_version_parser(NewPaymentSchema)
@inject(command_handler=AddPaymentsCommandHandler)
def add_payment(
    command_handler: AddPaymentsCommandHandler,
    bill_id,
    resource_version,
    parsed_request,
):
    """Add payment
    ---
    operationId: add_payment
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Add payment.
        tags:
            - Bills
        parameters:
            - in: path
              name: bill_id
              description: The bill id to which payment is to be added
              required: True
              type: string
            - in: body
              name: body
              description: The payment object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/components/schemas/NewPaymentSchema"
        responses:
            200:
                description: The created payment object.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/components/schemas/PaymentSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    payments, new_resource_version, payment_failures = command_handler.handle(
        bill_id, resource_version, [PaymentData.from_dict(parsed_request)], user_data
    )
    if not payments and payment_failures:
        return ApiResponse.build(
            errors=PaymentFailureSchema().dump(payment_failures, many=True).data,
            status_code=400,
        )
    payment_dict = PaymentSchema().dump(payments[0]).data if payments else None
    errors, status_code = (
        (PaymentFailureSchema().dump(payment_failures, many=True).data, 207)
        if payment_failures
        else (None, 200)
    )
    return ApiResponse.build(
        data=payment_dict,
        errors=errors,
        status_code=status_code,
        resource_version=new_resource_version,
    )


@swag_route
@bp.route('/bills/<string:bill_id>/payments', methods=['GET'])
@inject(query_handler=GetPaymentsQueryHandler)
def get_payments(query_handler: GetPaymentsQueryHandler, bill_id):
    """Get all the payments for a given bill
    ---
    parameters:
        - in: path
          name: bill_id
          description: The bill id for which payment info need to be fetched.
          required: True
          type: string
    operationId: get_payments
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: get all the payments for a given bill_id.
        tags:
            - Bills
        responses:
            200:
                description: an array of payment objects.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/PaymentSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    payments, resource_version = query_handler.handle(bill_id)
    response = PaymentSchema(many=True).dump(payments)

    return ApiResponse.build(
        data=response.data, status_code=200, resource_version=resource_version
    )


@swag_route
@bp.route('/bills/<string:bill_id>/payments/<int:payment_id>', methods=['GET'])
@inject(query_handler=GetPaymentQueryHandler)
def get_payment(query_handler: GetPaymentQueryHandler, bill_id, payment_id):
    """Get the payment information
    ---
    parameters:
        - in: path
          name: bill_id
          description: The bill id for which payment info need to be fetched.
          required: True
          type: string
        - in: path
          name: payment_id
          description: The payment id for which payment info need to be fetched.
          required: True
          type: integer
    operationId: get_payment
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: get the payment for a given bill_id and payment_id.
        tags:
            - Bills
        responses:
            200:
                description: The payment object.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/components/schemas/PaymentSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    payment, new_resource_version = query_handler.handle(bill_id, payment_id)
    response = PaymentSchema().dump(payment)

    return ApiResponse.build(
        data=response.data, status_code=200, resource_version=new_resource_version
    )


@swag_route
@bp.route('/payments/<string:payment_ref_id>', methods=['GET'])
@inject(query_handler=GetPaymentByRefQueryHandler)
def get_payments_by_ref_id(query_handler: GetPaymentByRefQueryHandler, payment_ref_id):
    """Get the payment information
    ---
    parameters:
        - in: path
          name: payment_ref_id
          description: The payment ref id for which payment info need to be fetched.
          required: True
          type: string
    operationId: get_payment
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: get the payment for a given payment_ref_id.
        tags:
            - Bills
        responses:
            200:
                description: The payment object.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/components/schemas/PaymentSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    payments = query_handler.handle(payment_ref_id)
    response = PaymentDetailsSchema(many=True).dump(payments)

    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bills/<string:bill_id>/payments/<int:payment_id>', methods=['PATCH'])
@authorize_write_op
@schema_wrapper_and_version_parser(EditPaymentSchema)
@inject(command_handler=UpdatePaymentsCommandHandler)
def update_payment(
    command_handler: UpdatePaymentsCommandHandler,
    bill_id,
    payment_id,
    resource_version,
    parsed_request,
):
    """update the payment information
    ---
    operationId: update_payment
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    patch:
        description: update the payment for a given bill_id and payment_id.
        parameters:
            - in: path
              name: bill_id
              description: The bill id to which payment to be added
              required: True
              type: string
            - in: path
              name: payment_id
              description: The payment id for which payment info need to be fetched.
              required: True
              type: integer
            - in: body
              name: body
              description: The payment object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/components/schemas/EditPaymentSchema"
        tags:
            - Bills
        responses:
            200:
                description: The payment object.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/components/schemas/PaymentSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    parsed_request['payment_id'] = payment_id
    payment_dtos = [PaymentData.from_dict(parsed_request)]
    payments, new_resource_version = command_handler.handle(
        bill_id, resource_version, payment_dtos, user_data
    )
    payment = payments[0] if payments else None
    response = PaymentSchema().dump(payment)

    return ApiResponse.build(
        data=response.data, status_code=200, resource_version=new_resource_version
    )


@swag_route
@bp.route('/bills/<string:bill_id>/payments', methods=['PATCH'])
@authorize_write_op
@schema_wrapper_and_version_parser(EditPaymentsSchema, many=True)
@inject(command_handler=UpdatePaymentsCommandHandler)
def update_payments(
    command_handler: UpdatePaymentsCommandHandler,
    bill_id,
    resource_version,
    parsed_request,
):
    """updates the payment information for multiple payments
    ---
    operationId: update_payments
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    patch:
        description: update the payments for a given bill_id and multiple payment_ids.
        parameters:
            - in: path
              name: bill_id
              description: The bill id to which payment to be updated
              required: True
              type: string
            - in: body
              name: body
              description: The payment objects which needs to be created
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/definitions/EditPaymentsSchema"
        tags:
            - Bills
        responses:
            200:
                description: The payment object.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/definitions/PaymentSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    payment_dtos = [
        PaymentData.from_dict(update_payment_dict)
        for update_payment_dict in parsed_request
    ]

    payments, new_resource_version = command_handler.handle(
        bill_id, resource_version, payment_dtos, user_data
    )
    response = PaymentSchema().dump(payments, many=True)

    return ApiResponse.build(
        data=response.data, status_code=200, resource_version=new_resource_version
    )


@swag_route
@bp.route('/bills/<string:bill_id>/charges', methods=['POST'])
@authorize_write_op
@schema_wrapper_and_version_parser(NewChargeSchema)
@inject(command_handler=AddChargeCommandHandler)
def add_charge(
    command_handler: AddChargeCommandHandler, bill_id, resource_version, parsed_request
):
    """Add charges (For internal use only)
    ---
    operationId: add_charge
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Add charges.
        tags:
            - Bills
        parameters:
            - in: path
              name: bill_id
              description: The bill id to which charge is to be added
              required: True
              type: string
            - in: body
              name: body
              description: The charge object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/components/schemas/NewChargeSchema"
        responses:
            200:
                description: The charge created
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/components/schemas/ChargeSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    charge_split_datas = []
    if parsed_request.get('charge_splits'):
        charge_split_datas = [
            ChargeSplitData(
                charge_to=data['charge_to'], percentage=data.get('percentage')
            )
            for data in parsed_request.get('charge_splits')
        ]
    charge_split_type = (
        ChargeSplitType(parsed_request['charge_split_type'])
        if parsed_request.get('charge_split_type')
        else None
    )

    charge_dto = ChargeData(
        pretax_amount=parsed_request.get('pretax_amount'),
        posttax_amount=parsed_request.get('posttax_amount'),
        charge_type=ChargeTypes(parsed_request['type']),
        bill_to_type=ChargeBillToTypes(parsed_request['bill_to_type']),
        applicable_date=parsed_request['applicable_date'],
        item=parsed_request.get('item'),
        status=ChargeStatus(parsed_request['status']),
        comment=parsed_request.get('comment'),
        charge_splits=charge_split_datas,
        charge_split_type=charge_split_type,
    )

    charge, new_resource_version = command_handler.handle(
        bill_id, resource_version, charge_dto
    )
    response = ChargeSchema().dump(charge)

    return ApiResponse.build(
        data=response.data, status_code=200, resource_version=new_resource_version
    )


@swag_route
@bp.route('/bills/<string:bill_id>/charges', methods=['GET'])
@schema_wrapper_parser(ChargePaginationSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetChargesQueryHandler)
def get_charges(query_handler: GetChargesQueryHandler, bill_id, parsed_request):
    """Get all the charges for a given bill
    ---
    operationId: get_charges
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: get the charges for a given bill_id.
        parameters:
            - in: path
              name: bill_id
              description: The bill id for which charges need to be fetched.
              required: True
              type: string
            - name: pagination_filter
              in: query
              required: false
              schema:
                $ref: "#/components/schemas/ChargePaginationSchema"
        tags:
            - Bills
        responses:
            200:
                description: The charge objects.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/ChargeSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    charge_id_gt, limit = None, None
    if parsed_request:
        charge_id_gt = parsed_request.get("charge_id_gt")
        limit = parsed_request.get("limit")

        charge_id_gt = charge_id_gt if charge_id_gt is not None else None
        limit = limit if limit is not None else None

    charges, resource_version, max_charge_id = query_handler.handle(
        bill_id,
        charge_id_gt=charge_id_gt,
        limit=limit,
        charge_ids=parsed_request.get('charge_ids'),
    )
    response = ChargeSchema(many=True).dump(charges)

    return ApiResponse.build(
        data=response.data,
        status_code=200,
        resource_version=resource_version,
        meta=dict(max_charge_id=max_charge_id) if max_charge_id else None,
    )


@swag_route
@bp.route('/bills/<string:bill_id>/charges/<int:charge_id>', methods=['GET'])
@inject(query_handler=GetChargeQueryHandler)
def get_charge(query_handler: GetChargeQueryHandler, bill_id, charge_id):
    """Get the payment information
    ---
    parameters:
        - in: path
          name: bill_id
          description: The bill id for which payment info need to be fetched.
          required: True
          type: string
        - in: path
          name: charge_id
          description: The charge id for which charge info need to be fetched.
          required: True
          type: integer
    operationId: get_charge
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: get the payment for a given bill_id and payment_id.
        tags:
            - Bills
        responses:
            200:
                description: The charge object.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/components/schemas/ChargeSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    charge, resource_version = query_handler.handle(bill_id, charge_id)
    response = ChargeSchema().dump(charge)

    return ApiResponse.build(
        data=response.data, status_code=200, resource_version=resource_version
    )


@swag_route
@bp.route('/bills/<string:bill_id>/charges/<int:charge_id>', methods=['PATCH'])
@authorize_write_op
@schema_wrapper_and_version_parser(EditChargeSchema)
@inject(command_handler=UpdateChargesCommandHandler)
def update_charge(
    command_handler: UpdateChargesCommandHandler,
    bill_id,
    charge_id,
    resource_version,
    parsed_request,
):
    """shallow update the charge information
    ---
    operationId: update_charge
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    patch:
        description: update the charge for a given bill_id and charge_id.
        tags:
            - Bills
        parameters:
            - in: path
              name: bill_id
              description: The bill id for which payment info need to be fetched.
              required: True
              type: string
            - in: path
              name: charge_id
              description: The charge id which needs to be updated.
              required: True
              type: integer
            - in: body
              name: charge
              description: The charge data to be updated.
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/components/schemas/EditChargeSchema"
        responses:
            200:
                description: The charge object.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/components/schemas/ChargeSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """

    parsed_request['charge_id'] = charge_id
    user_data = read_user_data_from_request_header()
    edit_charge_dtos = [
        EditChargeData(
            charge_id=parsed_request['charge_id'],
            price_data=parsed_request.get('price_data', NotAssigned),
            status=parsed_request.get('status', NotAssigned),
            charge_item=parsed_request.get('item', NotAssigned),
        )
    ]
    charges, new_resource_version = command_handler.handle(
        bill_id, resource_version, edit_charge_dtos, user_data
    )
    charge = charges[0] if charges else None
    response = ChargeSchema().dump(charge)
    return ApiResponse.build(
        data=response.data, status_code=200, resource_version=new_resource_version
    )


@swag_route
@bp.route('/bills/<string:bill_id>/charges', methods=['PATCH'])
@authorize_write_op
@schema_wrapper_and_version_parser(EditChargesSchema, many=True)
@inject(command_handler=UpdateChargesCommandHandler)
def update_charges(
    command_handler: UpdateChargesCommandHandler,
    bill_id,
    resource_version,
    parsed_request,
):
    """shallow update charge entities
    ---
    operationId: update_charges
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    patch:
        description: update the charges for the given charge entites.
        tags:
            - Bills
        parameters:
            - in: path
              name: bill_id
              description: The bill id for which charges need to be updated.
              required: True
              type: string
            - in: body
              name: charges
              description: The charges data to be updated.
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        type: array
                        items:
                            $ref: "#/components/schemas/EditChargesSchema"
        responses:
            200:
                description: The charge objects.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/ChargeSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    edit_charge_dtos = [
        EditChargeData(
            charge_id=edit_charge_dict['charge_id'],
            price_data=edit_charge_dict.get('price_data', NotAssigned),
            status=edit_charge_dict.get('status', NotAssigned),
            charge_item=edit_charge_dict.get('item', NotAssigned),
        )
        for edit_charge_dict in parsed_request
    ]
    charges, new_resource_version = command_handler.handle(
        bill_id, resource_version, edit_charge_dtos, user_data
    )
    response = ChargeSchema().dump(charges, many=True)
    return ApiResponse.build(
        data=response.data, status_code=200, resource_version=new_resource_version
    )


@swag_route
@bp.route('/invoices/<invoice_id>', methods=['GET'])
@schema_wrapper_parser(InvoiceGetSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetInvoiceByIdQueryHandler)
def get_invoice(query_handler: GetInvoiceByIdQueryHandler, invoice_id, parsed_request):
    """Get the invoice for a given invoice id
    ---
    operationId: get_invoice
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - in: path
              name: invoice_id
              description: The invoice id of the invoice which needs to be fetched.
              required: True
              type: string
            - in: query
              name: invoice_filter
              description: filter used to give extra details on invoice
              required: False
              schema:
                $ref: "#/components/schemas/InvoiceGetSchema"
        description: get the invoice for a given invoice_id.
        tags:
            - Invoices
        responses:
            200:
                description: The Invoice objects.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/InvoiceRawSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    invoice_aggregate = query_handler.handle(invoice_id)
    if parsed_request.get('show_raw'):
        invoice_response_schema = get_schema_obj(InvoiceRawSchema)
    else:
        invoice_response_schema = get_schema_obj(InvoiceSchema)
    response = invoice_response_schema.dump(invoice_aggregate)

    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bills/<bill_id>/invoices', methods=['GET'])
@inject(query_handler=GetInvoicesForBillQueryHandler)
def get_invoices_for_bill(query_handler: GetInvoicesForBillQueryHandler, bill_id):
    """Get the invoices for a given bill id
    ---
    operationId: get_invoices_for_bill
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - in: path
              name: bill_id
              description: The bill id of the bill from which invoices needed to be fetched.
              required: True
              type: string
        description: get the invoices for a given bill_id.
        tags:
            - Invoices
        responses:
            200:
                description: The Invoice objects.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/InvoiceSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    invoice_aggregates = query_handler.handle(bill_id)
    invoice_response_schema = InvoiceSchema(many=True)
    response = invoice_response_schema.dump(invoice_aggregates)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bills/<bill_id>/invoices', methods=['POST'])
@schema_wrapper_parser(NewInvoiceSchema)
@inject(command_handler=GenerateInvoicesCommandHandler)
def generate_invoices(
    command_handler: GenerateInvoicesCommandHandler, bill_id, parsed_request
):
    """Creates a new Invoice
    ---
    operationId: generate_invoice
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Create New Invoice
        tags:
            - Invoices
        parameters:
            - in: body
              name: body
              description: The data used to create invoice
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/NewInvoiceSchema"
        responses:
            200:
                description: The Invoice created
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/InvoiceSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    invoice_aggregates = command_handler.handle(bill_id, parsed_request)
    response = InvoiceSchema(many=True).dump(invoice_aggregates)

    return ApiResponse.build(data=response.data, status_code=201)


@swag_route
@bp.route('/bills/<bill_id>/credit-notes', methods=['POST'])
@schema_wrapper_parser(NewCreditNoteSchema)
@inject(command_handler=GenerateCreditNoteCommandHandler)
def generate_credit_note(
    command_handler: GenerateCreditNoteCommandHandler, bill_id, parsed_request
):
    """Creates a new Credit Note
    ---
    operationId: generate_credit_note
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Create New Credit Note
        tags:
            - Credit Notes
        parameters:
            - in: body
              name: body
              description: The data used to create credit note
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/NewCreditNoteSchema"
        responses:
            200:
                description: The credit note created
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CreditNoteSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    credit_note_aggregate = command_handler.handle(bill_id, parsed_request, user_data)
    response = CreditNoteSchema().dump(credit_note_aggregate)

    return ApiResponse.build(data=response.data, status_code=201)


@swag_route
@bp.route('/bills/<bill_id>/invoices/locked-invoice-modifications', methods=['POST'])
@schema_wrapper_and_version_parser(InvoicesModificationRequestSchema)
@inject(command_handler=ModifyLockedInvoicesCommandHandler)
def modify_locked_invoices(
    command_handler: ModifyLockedInvoicesCommandHandler,
    bill_id,
    resource_version,
    parsed_request,
):
    """Nullify invoice using credit note and transfer charges
    ---
    operationId: modify_locked_invoices
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Modify Locked Invoices
        tags:
            - Invoices
        parameters:
            - in: body
              name: body
              description: The data used to modify locked invoices
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/InvoicesModificationRequestSchema"
        responses:
            200:
                description: CreditNotes Generated
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/ModifyInvoicesResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    request_dto = ModifyLockedInvoicesDto(**parsed_request)
    user_data = read_user_data_from_request_header()

    job_result_dto = command_handler.handle(
        bill_id=bill_id,
        resource_version=resource_version,
        modify_locked_invoices_dto=request_dto,
        user_data=user_data,
    )
    credit_note_aggregates = job_result_dto.kwargs.get('credit_note_aggregates')
    accounts_used_for_modified_charges = job_result_dto.kwargs.get('accounts_summary')
    credit_notes = [
        credit_note_aggregate.credit_note
        for credit_note_aggregate in credit_note_aggregates
    ]
    response = ModifyInvoicesResponseSchema().dump(
        dict(
            credit_notes=credit_notes,
            accounts_used_for_modified_charges=accounts_used_for_modified_charges,
        )
    )
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route(
    '/bills/<bill_id>/invoices/reissue-locked-reseller-invoice-for-non-financial-changes',
    methods=['POST'],
)
@schema_wrapper_and_version_parser(InvoicesModificationRequestSchema)
@inject(command_handler=ModifyLockedResellerInvoiceCommandHandler)
def reissue_locked_reseller_invoice_for_non_financial_changes(
    command_handler: ModifyLockedResellerInvoiceCommandHandler,
    bill_id,
    resource_version,
    parsed_request,
):
    """Nullify invoice using credit note and transfer charges
    This API can only be used for reseller to customer invoice
    Charge amount can't be modified, Charge item can be moved to different BEA
    Buy side invoice (hotel to reseller) will remains unaffected
    New sell side invoice will be created, old one will be nullified via credit note.
    ---
    operationId: modify_locked_invoices
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Reissue Invoice For Non Financial changes
        tags:
            - Invoices
        parameters:
            - in: body
              name: body
              description: The data used to modify locked invoices
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/InvoicesModificationRequestSchema"
        responses:
            200:
                description: CreditNotes Generated
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/ModifyInvoicesResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    request_dto = ModifyLockedInvoicesDto(**parsed_request)
    user_data = read_user_data_from_request_header()
    job_result_dto = command_handler.handle(
        bill_id=bill_id,
        resource_version=resource_version,
        modify_locked_invoices_dto=request_dto,
        user_data=user_data,
    )
    credit_note_aggregates = job_result_dto.kwargs.get('credit_note_aggregates')
    accounts_used_for_modified_charges = job_result_dto.kwargs.get('accounts_summary')
    credit_notes = [
        credit_note_aggregate.credit_note
        for credit_note_aggregate in credit_note_aggregates
    ]
    response = ModifyInvoicesResponseSchema().dump(
        dict(
            credit_notes=credit_notes,
            accounts_used_for_modified_charges=accounts_used_for_modified_charges,
        )
    )
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bills/<bill_id>/credit-note-templates/<credit_note_id>', methods=['GET'])
@inject(query_handler=GetCreditNoteTemplateQueryHandler)
def generate_credit_note_template_data(
    query_handler: GetCreditNoteTemplateQueryHandler, bill_id, credit_note_id
):
    """Generates data for credit note template
    ---
    operationId: generate_credit_note_template_data
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Generate credit note template date
        tags:
            - Credit Notes
        parameters:
            - in: path
              name: bill_id
              description: The bill id of the bill to which the credit-note belongs
              required: True
              type: string
            - in: path
              name: credit_note_id
              description: The credit note id of the credit note for which template needs to be fetched
              required: True
              type: string
        responses:
            200:
                description: The credit note template response
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CreditNoteTemplateSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    template_json = query_handler.handle(bill_id, credit_note_id)
    return ApiResponse.build(data=template_json, status_code=200)


@swag_route
@bp.route('/bills/<bill_id>/credit-notes', methods=['GET'])
@inject(query_handler=GetCreditNotesQueryHandler)
def get_credit_notes(query_handler: GetCreditNotesQueryHandler, bill_id):
    """Get all credit notes for the given bill_id
    ---
    parameters:
        - in: path
          name: bill_id
          description: The bill id for this all credit notes needs to be fetched.
          required: True
          type: string
    operationId: get_credit_notes
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    externalDocs:
        description: Project documents
        url: https://docs.google.com/document/d/1VXOb29MK7-8wHeFL9QJ-0SJnoOogDHpi40eI15MpJNc/edit
    get:
        description: Get all credit notes for the given bill_id
        tags:
            - Credit Notes
        responses:
            200:
                description: The Credit Note objects.
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/CreditNoteSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    credit_note_aggregates = query_handler.handle(bill_id)
    response = CreditNoteSchema(many=True).dump(credit_note_aggregates)

    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/credit-notes/<credit_note_id>', methods=['GET'])
@inject(query_handler=GetCreditNoteByIdQueryHandler)
def get_credit_note(query_handler: GetCreditNoteByIdQueryHandler, credit_note_id):
    """Get the credit note for a given credit note id
    ---
    parameters:
        - in: path
          name: invoice_id
          description: The credit note id of the credit note which needs to be fetched.
          required: True
          type: string
    operationId: get_credit_note
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    externalDocs:
        description: Project documents
        url: https://docs.google.com/document/d/1VXOb29MK7-8wHeFL9QJ-0SJnoOogDHpi40eI15MpJNc/edit
    get:
        description: get the credit note for a given credit_note_id.
        tags:
            - Credit Notes
        responses:
            200:
                description: The Credit Note objects.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CreditNoteSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    credit_note_aggregate = query_handler.handle(credit_note_id)
    response = CreditNoteSchema().dump(credit_note_aggregate)

    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/credit-notes/<credit_note_id>', methods=['DELETE'])
@authorize_write_op
@inject(command_handler=CancelCreditNoteCommandHandler)
def cancel_credit_note(command_handler: CancelCreditNoteCommandHandler, credit_note_id):
    """Marks the credit note with the given credit_note_id as cancelled
    ---
    operationId: cancel_credit_note
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    delete:
        parameters:
            - name: bill_id
              in: path
              type: string
              required: true
            - name: credit_note_id
              in: path
              type: string
              required: true
        description: Marks the credit note with the given credit_note_id as cancelled
        tags:
            - Credit Notes
        responses:
            200:
                description: Cancelled Credit Note
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CreditNoteSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    credit_note_aggregate = command_handler.handle(credit_note_id, user_data)
    response = CreditNoteSchema().dump(credit_note_aggregate)
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route('/invoice-sequence/block', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(InvoiceNumberBlockSchema)
@inject(command_handler=BlockInvoiceSequenceCommandHandler)
def block_invoice_sequence(
    command_handler: BlockInvoiceSequenceCommandHandler, parsed_request
):
    """Blocks an invoice series and returns the valid invoice number.
    ---
    operationId: block_invoice_series
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Blocks the next valid invoice sequence number and returns
        tags:
            - Invoices
        parameters:
            - in: body
              name: body
              description: The data used to block the invoice series
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/InvoiceNumberBlockSchema"
        responses:
            200:
                description: Valid Invoice Number which can be used for the manual creation of Invoice Number
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                invoice_number:
                                    type: string
                                hotel_invoice_number:
                                    type: String
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    hotel_id = parsed_request['hotel_id']
    issued_by_type = parsed_request['issued_by_type']
    reason = parsed_request['reason']
    block_hotel_invoice_sequence = parsed_request.get('block_hotel_invoice_sequence')
    invoice_number, hotel_invoice_number = command_handler.handle(
        hotel_id,
        issued_by_type,
        reason,
        user_data,
        block_hotel_invoice_sequence=block_hotel_invoice_sequence,
    )
    if hotel_invoice_number:
        response = dict(
            invoice_number=invoice_number, hotel_invoice_number=hotel_invoice_number
        )
    else:
        response = dict(invoice_number=invoice_number)
    return ApiResponse.build(data=response, status_code=200)


@swag_route
@bp.route('/credit-note-sequence/block', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(CreditNoteNumberBlockSchema)
@inject(command_handler=BlockCreditNoteSequenceCommandHandler)
def block_credit_note_sequence(
    command_handler: BlockCreditNoteSequenceCommandHandler, parsed_request
):
    """Blocks a credit note series and returns the valid credit note number.
    ---
    operationId: block_credit_note_series
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Blocks the next valid credit note sequence number and returns
        tags:
            - Credit Notes
        parameters:
            - in: body
              name: body
              description: The data used to block the credit note series
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/CreditNoteNumberBlockSchema"
        responses:
            200:
                description: Valid Credit Note Number which can be used for the manual creation of credit note
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                credit_note_number:
                                    type: string
                                hotel_credit_note_number:
                                    type: string
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    hotel_id = parsed_request['hotel_id']
    issued_by_type = parsed_request['issued_by_type']
    reason = parsed_request['reason']
    block_hotel_credit_note_sequence = parsed_request.get(
        'block_hotel_credit_note_sequence'
    )
    (
        credit_note_number,
        hotel_credit_note_number,
    ) = command_handler.handle(
        hotel_id,
        issued_by_type,
        reason,
        user_data,
        block_hotel_credit_note_sequence=block_hotel_credit_note_sequence,
    )
    if hotel_credit_note_number:
        response = dict(
            credit_note_number=credit_note_number,
            hotel_credit_note_number=hotel_credit_note_number,
        )
    else:
        response = dict(credit_note_number=credit_note_number)
    return ApiResponse.build(data=response, status_code=200)


@swag_route
@bp.route(
    '/bills/<bill_id>/credit-notes/<string:credit_note_id>/hotel-credit-note',
    methods=['POST'],
)
@authorize_write_op
@inject(command_handler=GenerateHotelToResellerCreditNoteCommandHandler)
def generate_hotel_credit_note_to_reseller(
    command_handler: GenerateHotelToResellerCreditNoteCommandHandler,
    bill_id,
    credit_note_id,
):
    """create hotel to reseller credit-note for the existing issued to customer credit-note
    ---
    operationId: generate_hotel_to_reseller_credit_note
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: generate the hotel to reseller credit-note for an existing customer credit-note
        tags:
            - Credit Notes
        parameters:
            - in: path
              name: bill_id
              description: The bill id of the bill to which the credit-note belongs
              required: True
              type: string
            - in: path
              name: credit_note_id
              description: The credit-note id of the invoice for which the hotel invoice needs to be generated
              required: True
              type: string
        responses:
            200:
                description: The Credit Note objects.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CreditNoteSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    hotel_credit_note_aggregate = command_handler.handle(
        bill_id, credit_note_id, user_data
    )
    response = CreditNoteSchema().dump(hotel_credit_note_aggregate)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bills/<bill_id>/invoices/<invoice_id>/template', methods=['POST'])
@schema_wrapper_parser(BookingInvoiceTemplateRequest)
@inject(
    command_handler=GenerateInvoiceTemplateCommandHandler,
    pos_invoice_template_command_handler=GeneratePosInvoiceTemplateCommandHandler,
    get_bill_query_handler=GetBillQueryHandler,
)
def update_invoice_template_for_bill(
    command_handler: GenerateInvoiceTemplateCommandHandler,
    pos_invoice_template_command_handler: GeneratePosInvoiceTemplateCommandHandler,
    get_bill_query_handler: GetBillQueryHandler,
    bill_id,
    invoice_id,
    parsed_request,
):
    """create and upload the invoice template for a given invoice (FOR INTERNAL PURPOSE ONLY)
    ---
    operationId: update_invoice_template
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: get the invoice template for a given invoice and upload if needed
        tags:
            - Invoices
        parameters:
            - in: path
              name: bill_id
              description: The bill id of the bill to which the invoice belongs
              required: True
              type: string
            - in: path
              name: invoice_id
              description: The invoice id of the invoice for which template needs to be fetched
              required: True
              type: string
        responses:
            200:
                description: The schema of the invoice template.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/BookingInvoiceTemplateResponse"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    bill_aggregate = get_bill_query_handler.handle(bill_id)
    if bill_aggregate.bill.app_id == BillAppId.CRS_APP.value:
        job_result_dto = command_handler.handle(
            [invoice_id],
            bill_id=bill_id,
            should_upload=parsed_request['should_upload'],
            send_mail=False,
        )
    else:
        job_result_dto = pos_invoice_template_command_handler.handle(
            [invoice_id],
            bill_id=bill_id,
            should_upload=parsed_request['should_upload'],
        )

    invoice_url, invoice_signed_url = None, None
    invoice_aggregates, templates = job_result_dto.kwargs.get(
        'invoice_aggregates'
    ), job_result_dto.kwargs.get('templates')
    if invoice_aggregates:
        invoice_url = invoice_aggregates[0].invoice.invoice_url
        invoice_signed_url = invoice_aggregates[0].signed_url

    if bill_aggregate.bill.app_id == BillAppId.CRS_APP.value:
        invoice_template_response = BookingInvoiceTemplateResponse().dump(
            dict(
                template=templates[0],
                invoice_url=invoice_url,
                invoice_signed_url=invoice_signed_url,
            )
        )
    else:
        invoice_template_response = PosOrderInvoiceTemplateResponse().dump(
            dict(
                template=templates[0],
                invoice_url=invoice_url,
                invoice_signed_url=invoice_signed_url,
            )
        )
    return ApiResponse.build(data=invoice_template_response.data, status_code=200)


@swag_route
@bp.route('/bill/<bill_id>/credit-notes/<credit_note_id>/template', methods=['POST'])
@schema_wrapper_parser(BookingInvoiceTemplateRequest)
@inject(command_handler=GenerateCreditNoteTemplateCommandHandler)
def update_credit_note_template(
    command_handler: GenerateCreditNoteTemplateCommandHandler,
    bill_id,
    credit_note_id,
    parsed_request,
):
    """create and upload the credit-note template for a given credit note (FOR INTERNAL PURPOSE ONLY)
    ---
    operationId: update_credit_note_template
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: get the credit_note template for a given credit_note and upload if needed
        tags:
            - Credit Notes
        parameters:
            - in: path
              name: bill_id
              description: The bill id of the bill to which the credit-note belongs
              required: True
              type: string
            - in: path
              name: credit_note_id
              description: The credit note id of the credit_note for which template needs to be fetched
              required: True
              type: string
        responses:
            200:
                description: The schema of the credit note template.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/BillingCreditNoteTemplateResponse"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    job_result_dto = command_handler.handle(
        bill_id,
        credit_note_id,
        should_upload=parsed_request['should_upload'],
    )
    credit_note_aggregate, template = job_result_dto.kwargs.get(
        'credit_note_aggregate'
    ), job_result_dto.kwargs.get('template')
    if credit_note_aggregate:
        credit_note_url = credit_note_aggregate.credit_note.credit_note_url
        credit_note_signed_url = credit_note_aggregate.signed_url
    else:
        credit_note_url = None
        credit_note_signed_url = None

    credit_note_template_response = BillingCreditNoteTemplateResponse().dump(
        dict(
            template=template,
            credit_note_url=credit_note_url,
            credit_note_signed_url=credit_note_signed_url,
        )
    )
    return ApiResponse.build(data=credit_note_template_response.data, status_code=200)


@swag_route
@bp.route('/generate-irn', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(IrnGenerationRequestSchema)
@inject(einvoicing_app_service=EInvoiceApplicationService)
def generate_irn_for_invoices_and_credit_notes(einvoicing_app_service, parsed_request):
    invoice_ids = parsed_request.get('invoice_ids') or []
    credit_note_ids = parsed_request.get('credit_note_ids') or []
    bill_ids = parsed_request.get('bill_ids') or []

    if not (bill_ids or credit_note_ids or invoice_ids):
        raise ValidationException(
            description="Any one of Bill ids or credit note ids or invoice ids is required"
        )

    submission_data = einvoicing_app_service.generate_irn_and_report(
        invoice_ids, credit_note_ids, bill_ids
    )
    return ApiResponse.build(data=submission_data, status_code=200)


@swag_route
@bp.route('/bills/<string:bill_id>/billed-entities', methods=['GET'])
@inject(query_handler=GetBilledEntitiesQueryHandler)
def get_billed_entities(query_handler: GetBilledEntitiesQueryHandler, bill_id):
    """Get all the billed-entities for a given bill
    ---
    operationId: get_billed_entities
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: get the billed entities for a given bill_id.
        parameters:
            - in: path
              name: bill_id
              description: The bill id for which billed entities need to be fetched.
              required: True
              type: string
        tags:
            - Bills
        responses:
            200:
                description: The billed entity objects.
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/BilledEntitySchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    billed_entity_response_dtos = query_handler.handle(bill_id)
    response = BilledEntitySchema(many=True).dump(billed_entity_response_dtos)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route(
    '/bills/<string:bill_id>/charges/<int:charge_id>/charge-splits/<int:charge_split_id>/allowances',
    methods=['POST'],
)
@authorize_write_op
@schema_wrapper_and_version_parser(AllowanceSchema)
@inject(command_handler=AddAllowancesCommandHandler)
def add_allowance(
    command_handler: AddAllowancesCommandHandler,
    bill_id,
    resource_version,
    charge_id,
    charge_split_id,
    parsed_request,
):
    """Add allowance
    ---
    operationId: add_allowance
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Add allowance.
        tags:
            - Bills
        parameters:
            - in: path
              name: bill_id
              description: The bill id to which allowance is to be added
              required: True
              type: string
            - in: path
              name: charge_id
              description: The charge id to which allowance is to be added
              required: True
              type: string
            - in: path
              name: charge_split_id
              description: The charge split id to which allowance is to be added
              required: True
              type: string
            - in: body
              name: body
              description: The allowance object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/components/schemas/AllowanceSchema"
        responses:
            200:
                description: The allowance created
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/AllowanceResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """

    allowance_dto = AllowanceData(
        pretax_amount=parsed_request.get('pretax_amount'),
        posttax_amount=parsed_request.get('posttax_amount'),
        remarks=parsed_request['remarks'],
        charge_id=charge_id,
        charge_split_id=charge_split_id,
    )
    allowances, new_resource_version = command_handler.handle(
        bill_id, resource_version, [allowance_dto]
    )
    allowance = allowances[0] if allowances else None
    response = AllowanceResponseSchema().dump(allowance)
    return ApiResponse.build(
        data=response.data, status_code=200, resource_version=new_resource_version
    )


@swag_route
@bp.route('/bills/<string:bill_id>/allowances', methods=['POST'])
@authorize_write_op
@schema_wrapper_and_version_parser(AllowancesSchema, many=True)
@inject(command_handler=AddAllowancesCommandHandler)
def add_allowances(
    command_handler: AddAllowancesCommandHandler,
    bill_id,
    resource_version,
    parsed_request,
):
    """Add allowances
    ---
    operationId: add_allowances
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Add allowances.
        tags:
            - Bills
        parameters:
            - in: path
              name: bill_id
              description: The bill id to which allowance is to be added
              required: True
              type: string
            - in: body
              name: body
              description: The allowance objects which needs to be created
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/definitions/AllowancesSchema"
        responses:
            200:
                description: The allowances created
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/AllowanceResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    allowance_dtos = [
        AllowanceData(
            pretax_amount=allowance_dict.get('pretax_amount'),
            posttax_amount=allowance_dict.get('posttax_amount'),
            remarks=allowance_dict['remarks'],
            charge_id=allowance_dict['charge_id'],
            charge_split_id=allowance_dict['charge_split_id'],
        )
        for allowance_dict in parsed_request
    ]
    allowances, new_resource_version = command_handler.handle(
        bill_id, resource_version, allowance_dtos
    )
    response = AllowanceResponseSchema().dump(allowances, many=True)
    return ApiResponse.build(
        data=response.data, status_code=200, resource_version=new_resource_version
    )


@swag_route
@bp.route('/bills/<string:bill_id>/payment-list', methods=['POST'])
@authorize_write_op
@schema_wrapper_and_version_parser(NewPaymentsSchema)
@inject(command_handler=AddPaymentsCommandHandler)
def add_payments(
    command_handler: AddPaymentsCommandHandler,
    bill_id,
    resource_version,
    parsed_request,
):
    """Add payments
    ---
    operationId: add_payments
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Add payments.
        tags:
            - Bills
        parameters:
            - in: path
              name: bill_id
              description: The bill id to which payments are to be added
              required: True
              type: string
            - in: body
              name: body
              description: The payment objects which needs to be created
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/components/schemas/NewPaymentsSchema"
        responses:
            200:
                description: The created payment objects.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/components/schemas/PaymentSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    payments, _, payment_failures = command_handler.handle(
        bill_id,
        resource_version,
        [PaymentData.from_dict(payment) for payment in parsed_request['payments']],
        user_data,
    )
    if not payments and payment_failures:
        return ApiResponse.build(
            errors=PaymentFailureSchema().dump(payment_failures, many=True).data,
            status_code=400,
        )
    payment_dicts = PaymentSchema().dump(payments, many=True).data if payments else None
    errors, status_code = (
        (PaymentFailureSchema().dump(payment_failures, many=True).data, 207)
        if payment_failures
        else (None, 200)
    )
    return ApiResponse.build(data=payment_dicts, errors=errors, status_code=status_code)


@swag_route
@bp.route('/bills/<string:bill_id>/charges/<int:charge_id>/transfer', methods=['POST'])
@authorize_write_op
@schema_wrapper_and_version_parser(TransferChargeSchema)
@inject(command_handler=TransferChargeCommandHandler)
def transfer_charge(
    command_handler: TransferChargeCommandHandler,
    bill_id,
    resource_version,
    charge_id,
    parsed_request,
):
    """Transfer charge to different booking
    ---
    operationId: transfer_charge
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: transfer the charge of given bill_id and charge_id to different booking
        tags:
            - Bills
        parameters:
            - in: path
              name: bill_id
              description: The bill id of the charge that needs to be transferred.
              required: True
              type: string
            - in: path
              name: charge_id
              description: The charge id of the charge that needs to be transferred.
              required: True
              type: integer
            - in: body
              name: transfer charge schema
              description: The destination booking details to transfer the charge to.
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/TransferChargeSchema"
        responses:
            200:
                description: The charge object.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/components/schemas/ChargeSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    charges, destination_bill_aggregate = command_handler.handle(
        bill_id, resource_version, [charge_id], parsed_request, user_data
    )
    response = TransferChargeResponseSchema().dump(
        dict(destination_bill_id=destination_bill_aggregate.bill_id, charge=charges[0])
    )
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bills/<string:bill_id>/charges/transfer', methods=['POST'])
@authorize_write_op
@schema_wrapper_and_version_parser(TransferChargesSchema)
@inject(command_handler=TransferChargeCommandHandler)
def transfer_charges(
    command_handler: TransferChargeCommandHandler,
    bill_id,
    resource_version,
    parsed_request,
):
    """Transfer multiple charges to different booking
    ---
    operationId: transfer_charges
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: transfers the charge of given bill_id and charge_id to different booking
        tags:
            - Bills
        parameters:
            - in: path
              name: bill_id
              description: The bill id of the charge that needs to be transferred.
              required: True
              type: string
            - in: body
              name: transfer charges schema
              description: Charge ID's to be transferred & destination booking details to transfer the charge to.
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/TransferChargesSchema"
        responses:
            200:
                description: The charge object.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/components/schemas/ChargeSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    charges, destination_bill_aggregate = command_handler.handle(
        bill_id,
        resource_version,
        parsed_request['charge_ids'],
        parsed_request,
        user_data,
    )
    response = TransferChargesResponseSchema().dump(
        dict(destination_bill_id=destination_bill_aggregate.bill_id, charges=charges)
    )
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route(
    '/bills/<string:bill_id>/charges/<int:charge_id>/charge-splits/<int:charge_split_id>/allowances/<int:allowance_id>',
    methods=['PATCH'],
)
@authorize_write_op
@schema_wrapper_and_version_parser(EditAllowanceSchema)
@inject(command_handler=UpdateAllowanceCommandHandler)
def update_allowance(
    command_handler: UpdateAllowanceCommandHandler,
    bill_id,
    charge_id,
    charge_split_id,
    allowance_id,
    resource_version,
    parsed_request,
):
    """Update allowance
    ---
    operationId: update_allowance
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    patch:
        description: Update allowance.
        tags:
            - Bills
        parameters:
            - in: path
              name: bill_id
              description: The bill id of allowance which needs to be updated
              required: True
              type: string
            - in: path
              name: charge_id
              description: The charge id of allowance which needs to be updated
              required: True
              type: string
            - in: path
              name: charge_split_id
              description: The charge split id of allowance which needs to be updated
              required: True
              type: string
            - in: path
              name: allowance_id
              description: The allowance id of allowance which needs to be updated
              required: True
              type: string
            - in: body
              name: status
              description: The status to which allowance needs to be updated to
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/components/schemas/EditAllowanceSchema"
        responses:
            200:
                description: The allowance is updated
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/AllowanceResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    edit_allowance_dto = EditAllowanceData(parsed_request.get('status', NotAssigned))
    allowance = command_handler.handle(
        bill_id,
        resource_version,
        charge_id,
        charge_split_id,
        allowance_id,
        edit_allowance_dto,
    )
    response = AllowanceResponseSchema().dump(allowance)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bills/<string:bill_id>/invoice-accounts', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(InvoiceAccountsSchema)
@inject(command_handler=InvoiceAccountsCommandHandler)
def create_invoice_for_billed_entity_accounts(
    command_handler: InvoiceAccountsCommandHandler, bill_id, parsed_request
):
    """Creates invoice for each billed entity account
    ---
    operationId: create_invoice_for_billed_entity_accounts
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: creates invoice for given billed entity id and account number
        tags:
            - Bills
        parameters:
            - in: path
              name: bill_id
              description: The bill id
              required: True
              type: string
            - in: body
              name: invoice accounts schema
              description: The billed entity id and account number
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/InvoiceAccountsSchema"
        responses:
            200:
                description: The invoice object.
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/InvoiceSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    invoice_aggregates = command_handler.handle(bill_id, parsed_request, user_data)
    response = InvoiceSchema(many=True).dump(invoice_aggregates)
    return ApiResponse.build(data=response.data, status_code=201)


@bp.route(
    '/bills/<string:bill_id>/redistribute-payments-between-accounts', methods=['POST']
)
@authorize_write_op
@schema_wrapper_parser(RedistributePaymentsSchema)
@inject(command_handler=RedistributePaymentsCommandHandler)
def redistribute_payments_between_accounts(
    command_handler: RedistributePaymentsCommandHandler, bill_id, parsed_request
):
    """Redistribute the surplus/deficit amount across accounts for given Billed Entity to a single account.
    ---
    operationId: redistribute_payments_between_accounts
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Redistribute surplus/deficit amount across accounts for given Billed Entity to a single account
        tags:
            - Bills
        parameters:
            - in: path
              name: bill_id
              description: The bill id
              required: True
              type: string
            - in: body
              name: Redistribute Payment Schema
              description: List of billed entity id
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/RedistributePaymentsSchema"
        responses:
            201:
                 description: List of billed entity with redistributed account balance.
                 schema:
                     type: object
                     properties:
                         data:
                             type: array
                             billed_entity_wise_balance:
                                 $ref: "#/definitions/BilledEntityBalanceSchema"
                         meta:
                             type: object
                             additionalProperties: {}
                         errors:
                             type: array
                             items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    booking_invoice_group_id = parsed_request.get('booking_invoice_group_id')
    billed_entity_wise_balance = command_handler.handle(
        bill_id, booking_invoice_group_id=booking_invoice_group_id
    )
    response = BilledEntityBalanceSchema(many=True).dump(billed_entity_wise_balance)
    return ApiResponse.build(data=response.data, status_code=201)


@swag_route
@bp.route('/bills/<string:bill_id>/settle-by-spot-credit', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(SettleBySpotCreditsSchema)
@inject(command_handler=SettleBySpotCreditCommandHandler)
def settle_by_spot_credit(
    command_handler: SettleBySpotCreditCommandHandler, bill_id, parsed_request
):
    """Issue spot credit Booking level or Billed Entity Level
    ---
    parameters:
        - in: path
          name: bill_id
          description: The bill id for which spot credit need to be issues.
          required: True
          type: string
    operationId: settle_by_spot_credit
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Issue spot credit and transfer the credit charges to given BE's Credit Account
        tags:
            - Bills
        parameters:
            - in: path
              name: bill_id
              description: The bill id
              required: True
              type: string
            - in: body
              name: Settle Charges by Spot Credit Schema
              description: billed entity id and settlement type
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/SettleBySpotCreditsSchema"
        responses:
            201:
                 description: BIll summary schema.
                 schema:
                     type: object
                     properties:
                         data:
                             type: array
                             items:
                                 $ref: "#/components/schemas/FolioResponseSchema"
                         meta:
                             type: object
                             additionalProperties: {}
                         errors:
                             type: array
                             items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    spot_credit_settlement_dtos = [
        SpotCreditSettlementData.create_new(data)
        for data in parsed_request.get('settlement_data')
    ]
    user_data = read_user_data_from_request_header()
    folios = command_handler.handle(bill_id, spot_credit_settlement_dtos, user_data)
    response = FolioResponseSchema(many=True).dump(folios)
    return ApiResponse.build(
        data=response.data,
        status_code=200,
    )


@swag_route
@bp.route('/bills/<string:bill_id>/folio-summary/<int:folio_number>', methods=['GET'])
@schema_wrapper_parser(FolioSummaryRequestSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetFolioSummaryQueryHandler)
def get_folio_summary(
    query_handler: GetFolioSummaryQueryHandler, bill_id, folio_number, parsed_request
):
    """Get folio summary
    ---
    operationId: get_folio_summary
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get folio summary.
        parameters:
            - in: path
              name: bill_id
              description: The bill id which needs to be fetched
              required: True
              type: string
            - in: path
              name: folio_number
              description: The bill id which needs to be fetched
              required: True
              type: integer
            - name: invoice_group
              in: query
              required: false
              schema:
                $ref: "#/components/schemas/FolioSummaryRequestSchema"
        tags:
            - Bills
        responses:
            200:
                description: The folio summary object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/FolioSummarySchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    booking_invoice_group_id = parsed_request.get('booking_invoice_group_id')
    folio_summary = query_handler.handle(
        bill_id, folio_number, booking_invoice_group_id
    )
    response = FolioSummarySchema().dump(folio_summary)

    return ApiResponse.build(
        data=response.data,
        status_code=200,
    )


@swag_route
@bp.route('/invoices', methods=['GET'])
@schema_wrapper_parser(InvoicesGetSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetInvoiceByIdsQueryHandler)
def get_invoices(query_handler: GetInvoiceByIdsQueryHandler, parsed_request):
    """Get the invoices for a given invoice ids
    ---
    operationId: get_invoices
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - in: path
              name: invoice_ids
              description: The invoice ids of the invoice which needs to be fetched.
              required: True
              type: string
            - in: query
              name: invoice_filter
              description: filter used to give extra details on invoice
              required: False
              schema:
                $ref: "#/components/schemas/InvoiceGetSchema"
        description: get the invoices for a given invoice_ids.
        tags:
            - Invoices
        responses:
            200:
                description: The Invoice objects.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/InvoiceRawSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    invoice_ids = (
        parsed_request.get("invoice_ids").split(',')
        if parsed_request.get("invoice_ids")
        else []
    )
    generate_signed_url = (
        parsed_request.get("generate_signed_url")
        if parsed_request.get("generate_signed_url")
        else False
    )

    invoice_aggregates = query_handler.handle(
        invoice_ids, generate_signed_url=generate_signed_url
    )
    if parsed_request.get('show_raw'):
        invoice_response_schema = InvoiceRawSchema(many=True).dump(invoice_aggregates)
    else:
        invoice_response_schema = InvoiceSchema(many=True).dump(invoice_aggregates)

    return ApiResponse.build(data=invoice_response_schema.data, status_code=200)


@swag_route
@bp.route('/bills/<string:bill_id>/get-refund-mode', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(RefundSummaryRequestSchema, many=True)
@inject(query_handler=GetRefundModeQueryHandler)
def derive_refund_mode(
    query_handler: GetRefundModeQueryHandler,
    bill_id,
    parsed_request,
):
    """Derive Refund Mode
    ---
    operationId: derive_refund_mode
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Add payment.
        tags:
            - Bills
        parameters:
            - in: path
              name: bill_id
              description: The bill id for which refund mode derivation need to be done
              required: True
              type: string
            - in: body
              name: body
              description: Refund mode derivation request schema
              required: True
              schema:
                type: object
                properties:
                    data:
                        type: array
                        items:
                            $ref: "#/components/schemas/RefundSummaryRequestSchema"
        responses:
            200:
                description: Refund mode response.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/RefundSummaryRequestSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    refund_summary_request_dto = [RefundSummaryDto(**item) for item in parsed_request]
    refund_summary_dto = query_handler.handle(
        bill_id, refund_summary_request_dto, user_data
    )
    refund_summary_data = (
        RefundSummaryResponseSchema(many=True).dump(refund_summary_dto).data
    )
    return ApiResponse.build(data=refund_summary_data, status_code=200)


@swag_route
@bp.route('/bills/<string:bill_id>/recalculate-tax', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(TaxRecalculationAccountsSchema)
@inject(command_handler=RefreshTaxCommandHandler)
def recalculate_tax_billed_entity_accounts(
    command_handler: RefreshTaxCommandHandler, bill_id, parsed_request
):
    """Recalculate tax on each billed entity account
    ---
    operationId: recalculate_tax_billed_entity_accounts
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: recalculate tax for given billed entity id and account number
        tags:
            - Bills
        parameters:
            - in: path
              name: bill_id
              description: The bill id
              required: True
              type: string
            - in: body
              name: tax recalculate accounts schema
              description: The billed entity id and account number
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/TaxRecalculationAccountsSchema"
        responses:
            200:
                description: The bill object.
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/BillSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    bill_aggregate = command_handler.handle(
        bill_id, parsed_request['billed_entity_accounts']
    )
    bill_schema = get_schema_obj(BillSchema)
    response = bill_schema.dump(bill_aggregate)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bills/<string:bill_id>/refund-details', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(RefundDetailsRequestSchema)
@inject(query_handler=RefundDetailsQueryHandler)
def calculate_refund_details(
    query_handler: RefundDetailsQueryHandler, bill_id, parsed_request
):
    """
    Calculates refund details for a specific bill.

    This API the refund details for the specified bill ID. It delegates the task
    to the RefundDetailsQueryHandler to handle the calculation and retrieval of the details.

    Args:
        query_handler (RefundDetailsQueryHandler): An instance of the query handler responsible for fetching
        and calculating refund details.
        bill_id (str): The unique identifier of the bill for which refund details are being calculated.
        parsed_request (dict): The parsed request data containing necessary information for the calculation.

    Returns:
        ApiResponse: An API response containing the calculated refund details data and a status code.
    """
    refund_details = query_handler.handle(bill_id, parsed_request)
    response = RefundDetailsResponseSchema().dump(refund_details)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bills/<bill_id>/folios/einvoice-eligibility', methods=['POST'])
@schema_wrapper_parser(platform_fees)
@inject(query_handler=GetFoliosEInvoiceEligibilityQueryHandler)
def get_einvoice_eligibility(
    query_handler: GetFoliosEInvoiceEligibilityQueryHandler, bill_id, parsed_request
):
    """Check einvoice eligibility of folios for a given bill and folio_numbers
    ---
    operationId: get_einvoice_eligibility
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: check einvoice eligibility of folios for a given bill and folio_numbers.
        parameters:
            - in: path
              name: bill_id
              description: The bill id for which folios need to be checked.
              required: True
              type: string
            - in: body
              name: body
              description: The folios einvoice eligibility object which needs to be fetched
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/platform_fees"
        tags:
            - Bills
        responses:
            200:
                description: The folio eligibility objects.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/FolioEInvoiceEligibilityResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    folio_numbers = parsed_request.get("folio_numbers")
    folio_einvoice_eligibility_details = query_handler.handle(
        bill_id,
        folio_numbers,
    )
    response = FolioEInvoiceEligibilityResponseSchema(many=True).dump(
        folio_einvoice_eligibility_details
    )

    return ApiResponse.build(
        data=response.data,
        status_code=200,
    )
