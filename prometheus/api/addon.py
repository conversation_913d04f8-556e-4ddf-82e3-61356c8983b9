# coding=utf-8
import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.booking.command_handlers.create_addon import (
    CreateAddonCommandHandler,
)
from prometheus.application.booking.command_handlers.delete_addon import (
    DeleteAddonCommandHandler,
)
from prometheus.application.booking.command_handlers.patch_addon import (
    PatchAddonCommandHandler,
)
from prometheus.application.booking.query_handlers.get_addon import GetAddonQueryHandler
from prometheus.common.api_response import ApiResponse
from prometheus.common.decorators import authorize_write_op
from prometheus.common.request_parsers import read_user_data_from_request_header
from prometheus.common.serializers.request.addon import UpdateAddonSchema
from prometheus.common.serializers.response.addon import (
    AddonResponseSchema,
    AddonSchema,
)
from prometheus.core.api_docs import swag_route
from shared_kernel.api_helpers.request_parsers import (
    schema_wrapper_and_version_parser,
    schema_wrapper_parser,
)
from ths_common.constants.user_constants import UserType

bp = Blueprint('Addons', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@swag_route
@bp.route('/bookings/<booking_id>/addons', methods=['GET'])
@inject(query_handler=GetAddonQueryHandler)
def get_addons(query_handler: GetAddonQueryHandler, booking_id):
    """
    ---
    operationId: get_addons
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get Add-ons information for a given booking.
        tags:
            - Add-ons
        parameters:
            - in: path
              name: booking_id
              description: The booking_id to with Add-on belongs to
              required: True
              type: string
        responses:
            200:
                description: List of add-ons objects for a given booking
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/AddonResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    addons = query_handler.handle(booking_id, linked_addons_required=False)
    v1_addons = [
        addon_aggregate
        for addon_aggregate in addons
        if (
            addon_aggregate.addon.charge_checkin
            or addon_aggregate.addon.charge_checkout
            or addon_aggregate.addon.charge_other_days
        )
    ]
    addon_response_schema = AddonResponseSchema()
    response = addon_response_schema.dump(v1_addons, many=True)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bookings/<booking_id>/addons', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(AddonSchema)
@inject(command_handler=CreateAddonCommandHandler)
def apply_addon(command_handler: CreateAddonCommandHandler, booking_id, parsed_request):
    """
    ---
    operationId: apply_addons
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Apply one or more addons to the given booking
        tags:
            - Add-ons
        parameters:
            - in: path
              name: booking_id
              description: The booking_id to which Add-on belongs to
              required: True
              type: string
            - in: body
              name: body
              description: The Add-on object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/components/schemas/AddonSchema"
        responses:
            200:
                description: A shallow detail of the addons.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/components/schemas/AddonResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    addon_aggregate = command_handler.handle(booking_id, parsed_request, user_data)

    addon_response_schema = AddonResponseSchema()
    response = addon_response_schema.dump(addon_aggregate)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bookings/<string:booking_id>/addons/<string:addon_id>', methods=['DELETE'])
@authorize_write_op
@inject(command_handler=DeleteAddonCommandHandler)
def delete_addon(command_handler: DeleteAddonCommandHandler, booking_id, addon_id):
    """
    ---
    operationId: delete_addons
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    delete:
        description: Deletes an add-on from the given booking
        tags:
            - Add-ons
        parameters:
            - in: path
              name: booking_id
              description: The booking_id associated with Add-on which needs to be deleted
              required: True
              type: string
            - in: path
              name: addon_id
              description: The Add-on object which needs to be deleted
              required: True
              type: string
        responses:
            204:
                description: The add-on is deleted
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    command_handler.handle(booking_id, addon_id, user_data=user_data)
    return ApiResponse.build(status_code=200)


@swag_route
@bp.route('/bookings/<string:booking_id>/addons/<string:addon_id>', methods=['PATCH'])
@authorize_write_op
@schema_wrapper_and_version_parser(UpdateAddonSchema)
@inject(command_handler=PatchAddonCommandHandler)
def update_addon(
    command_handler: PatchAddonCommandHandler,
    booking_id,
    addon_id,
    resource_version,
    parsed_request,
):
    """
    ---
    operationId: update_addon
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    patch:
        description: Updates an add-on from the given booking
        tags:
            - Add-ons
        parameters:
            - in: path
              name: booking_id
              description: The booking_id associated with Add-on which needs to be updated
              required: True
              type: string
            - in: path
              name: addon_id
              description: The Add-on object which needs to be updated
              required: True
              type: string
            - in: body
              name: body
              description: The data to update AddOn
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/components/schemas/UpdateAddonSchema"
        responses:
            200:
                description: A shallow detail of the addons.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/AddonResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    addon_aggregate = command_handler.handle(
        booking_id, addon_id, resource_version, parsed_request, user_data=user_data
    )
    addon_response_schema = AddonResponseSchema()
    response = addon_response_schema.dump(addon_aggregate)
    return ApiResponse.build(data=response.data, status_code=200)
