import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.cashiering.command_handlers.create_cash_register import (
    CreateCashRegisterCommandHandler,
)
from prometheus.application.cashiering.command_handlers.create_cashier_session import (
    CreateCashierSessionCommandHandler,
)
from prometheus.application.cashiering.command_handlers.create_cashier_session_payment import (
    CreateCashierSessionPaymentCommandHandler,
)
from prometheus.application.cashiering.command_handlers.create_currency_exchange import (
    CreateCurrencyExchangeCommandHandler,
)
from prometheus.application.cashiering.command_handlers.patch_cash_register import (
    PatchCashRegisterCommandHandler,
)
from prometheus.application.cashiering.command_handlers.patch_cashier_session import (
    PatchCashierSessionCommandHandler,
)
from prometheus.application.cashiering.command_handlers.patch_cashier_session_payment import (
    PatchCashierSessionPaymentCommandHandler,
)
from prometheus.application.cashiering.dtos.new_currency_exchange_dto import (
    NewCurrencyExchangeDto,
)
from prometheus.application.cashiering.query_handlers.count_cashier_session import (
    CountCashierSessionQueryHandler,
)
from prometheus.application.cashiering.query_handlers.get_cash_register import (
    GetCashRegisterQueryHandler,
)
from prometheus.application.cashiering.query_handlers.get_cashier_session_by_id import (
    GetCashierSessionByIdQueryHandler,
)
from prometheus.application.cashiering.query_handlers.get_cashier_session_payments import (
    GetCashierSessionPaymentsQueryHandler,
)
from prometheus.application.cashiering.query_handlers.get_cashier_session_summary import (
    GetCashierSessionSummaryQueryHandler,
)
from prometheus.application.cashiering.query_handlers.get_open_cashier_sessions import (
    GetOpenCashierSessionsQueryHandler,
)
from prometheus.application.cashiering.query_handlers.search_cash_register import (
    SearchCashRegisterQueryHandler,
)
from prometheus.application.cashiering.query_handlers.search_cashier_session import (
    SearchCashierSessionQueryHandler,
)
from prometheus.common.api_response import ApiResponse
from prometheus.common.request_parsers import read_user_data_from_request_header
from prometheus.common.serializers.request.cashiering import (
    CashCounterNewPaymentSchema,
    CashCounterUpdatePaymentSchema,
    CashierSessionNewSchema,
    CashierSessionSearchSchema,
    CashierSessionUpdateSchema,
    CashRegisterNewSchema,
    CashRegisterSearchSchema,
    NewCurrencyExchangeSchema,
    UpdateCashRegisterSchema,
)
from prometheus.common.serializers.response.cashiering import (
    CashierPaymentResponseSchema,
    CashierSessionResponseSchema,
    CashierSessionSearchResponseSchema,
    CashierSessionSummaryResponseSchema,
    CashRegisterResponseSchema,
    CashRegisterSearchResponseSchema,
    CurrencyExchangeResponseSchema,
)
from prometheus.core.api_docs import swag_route
from prometheus.domain.billing.dto.cash_counter_payment_data import CashierPaymentData
from prometheus.domain.billing.dto.cashier_session_search_query import (
    CashierSessionSearchQuery,
)
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_parser,
)
from ths_common.constants.billing_constants import CashierPaymentStatus
from ths_common.constants.user_constants import UserType

bp = Blueprint('Cashier', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@swag_route
@bp.route('/cashier/cash-registers', methods=['POST'])
@schema_wrapper_parser(CashRegisterNewSchema)
@inject(command_handler=CreateCashRegisterCommandHandler)
def create_new_cash_register(
    command_handler: CreateCashRegisterCommandHandler, parsed_request
):
    """Create new Cash Register
    ---
    operationId: add_cash_register
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    post:
        description: Add cash register.
        tags:
            - Cashiering
        parameters:
            - in: body
              name: body
              description: The cash register object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/CashRegisterNewSchema"
        responses:
            200:
                description: The created cash register.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CashRegisterResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    cash_register_aggregate = command_handler.handle(parsed_request, user_data)

    cash_register_response_schema = CashRegisterResponseSchema()
    cash_register_response_schema.context['extra_data'] = dict(
        cash_register_aggregate=cash_register_aggregate
    )
    response = cash_register_response_schema.dump(cash_register_aggregate.cash_register)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/cashier/cash-registers/<string:cash_register_id>', methods=['GET'])
@inject(query_handler=GetCashRegisterQueryHandler)
def get_cash_register(query_handler: GetCashRegisterQueryHandler, cash_register_id):
    """Get cash register
    ---
    operationId: get_cash_register
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get cash register details.
        parameters:
            - in: path
              name: cash_register_id
              description: The cash register id which needs to be fetched
              required: True
              type: string
        tags:
            - Cashiering
        responses:
            200:
                description: The fetched cash register.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CashRegisterResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    cash_register_aggregate = query_handler.handle(
        cash_register_id, user_data=user_data
    )
    cash_register_response_schema = CashRegisterResponseSchema()
    cash_register_response_schema.context['extra_data'] = dict(
        cash_register_aggregate=cash_register_aggregate
    )
    response = cash_register_response_schema.dump(cash_register_aggregate.cash_register)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/cashier/cash-registers/search', methods=['GET'])
@schema_wrapper_parser(CashRegisterSearchSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=SearchCashRegisterQueryHandler)
def search_cash_register(query_handler: SearchCashRegisterQueryHandler, parsed_request):
    """Search cash register
    ---
    operationId: search_cash_register
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get cash register details.
        parameters:
            - in: body
              name: body
              description: Query to search the cash register
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/CashRegisterSearchSchema"
        tags:
            - Cashiering
        responses:
            200:
                description: The fetched cash register.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CashRegisterSearchResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    cash_register_aggregates = query_handler.handle(parsed_request)
    serialized_cash_registers = []
    for cash_register_aggregate in cash_register_aggregates:
        cash_register_response_schema = CashRegisterResponseSchema()
        cash_register_response_schema.context['extra_data'] = dict(
            cash_register_aggregate=cash_register_aggregate
        )
        serialized_cash_registers.append(
            cash_register_response_schema.dump(cash_register_aggregate.cash_register)
        )

    response_data = {'cash_registers': serialized_cash_registers}
    search_response_schema = CashRegisterSearchResponseSchema()
    search_response_schema.context['extra_data'] = dict(
        serialized_cash_registers=serialized_cash_registers
    )
    response = search_response_schema.dump(response_data)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/cashier/cash-registers/<string:cash_register_id>', methods=['PATCH'])
@schema_wrapper_parser(UpdateCashRegisterSchema)
@inject(command_handler=PatchCashRegisterCommandHandler)
def update_cash_register(
    command_handler: PatchCashRegisterCommandHandler, cash_register_id, parsed_request
):
    """Update Cash Register
    ---
    operationId: update_cash_register
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    patch:
        description: Update cash register.
        tags:
            - Cashiering
        parameters:
            - in: path
              name: cash_register_id
              description: The cash register id which needs to be updated
              required: True
              type: string

            - in: body
              name: body
              description: The cash register object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/UpdateCashRegisterSchema"
        responses:
            200:
                description: The created cash register.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CashRegisterResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    cash_register_aggregate = command_handler.handle(
        cash_register_id, parsed_request, user_data
    )

    cash_register_response_schema = CashRegisterResponseSchema()
    cash_register_response_schema.context['extra_data'] = dict(
        cash_register_aggregate=cash_register_aggregate
    )
    response = cash_register_response_schema.dump(cash_register_aggregate.cash_register)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route(
    '/cashier/cash-registers/<string:cash_register_id>/cashier-sessions',
    methods=['POST'],
)
@schema_wrapper_parser(CashierSessionNewSchema)
@inject(command_handler=CreateCashierSessionCommandHandler)
def add_cashier_session(
    command_handler: CreateCashierSessionCommandHandler,
    cash_register_id,
    parsed_request,
):
    """Add Cashier Session
    ---
    operationId: add_cashier_session
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    post:
        description: Add cashier session.
        tags:
            - Cashiering
        parameters:
            - in: path
              name: cash_register_id
              description: The cash register id which needs to be fetched
              required: True
              type: string
            - in: body
              name: body
              description: The cashier session object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/CashierSessionNewSchema"
        responses:
            200:
                description: The created cashier session.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CashierSessionResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """

    user_data = read_user_data_from_request_header()
    cashier_session_aggregate = command_handler.handle(
        parsed_request, cash_register_id, user_data
    )

    cashier_response_schema = CashierSessionResponseSchema()
    cashier_response_schema.context['extra_data'] = dict(
        cashier_session_aggregate=cashier_session_aggregate
    )
    response = cashier_response_schema.dump(cashier_session_aggregate.cashier_session)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route(
    '/cashier/cash-registers/<string:cash_register_id>/cashier-sessions/<string:cashier_session_id>',
    methods=['PATCH'],
)
@schema_wrapper_parser(CashierSessionUpdateSchema)
@inject(command_handler=PatchCashierSessionCommandHandler)
def update_cashier_session(
    command_handler: PatchCashierSessionCommandHandler,
    cash_register_id,
    cashier_session_id,
    parsed_request,
):
    """Update Cashier Session
    ---
    operationId: update_cashier_session
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    patch:
        description: Update cashier session.
        tags:
            - Cashiering
        parameters:
            - in: path
              name: cash_register_id
              description: The cash register id which needs to be fetched
              required: True
              type: string
            - in: path
              name: cashier_session_id
              description: The cashier session id which needs to be fetched
              required: True
              type: string
            - in: body
              name: body
              description: The cashier session object which needs to be updated
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/CashierSessionUpdateSchema"
        responses:
            200:
                description: The updated cashier session.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CashierSessionResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """

    user_data = read_user_data_from_request_header()
    cashier_session_aggregate = command_handler.handle(
        cash_register_id, cashier_session_id, parsed_request, user_data
    )

    cashier_response_schema = CashierSessionResponseSchema()
    cashier_response_schema.context['extra_data'] = dict(
        cashier_session_aggregate=cashier_session_aggregate
    )
    response = cashier_response_schema.dump(cashier_session_aggregate.cashier_session)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route(
    '/cashier/cash-registers/<string:cash_register_id>/cashier-sessions/<string:cashier_session_id>',
    methods=['GET'],
)
@inject(query_handler=GetCashierSessionByIdQueryHandler)
def get_cashier_session(
    query_handler: GetCashierSessionByIdQueryHandler,
    cash_register_id,
    cashier_session_id,
):
    """Get cashier session
    ---
    operationId: get_cashier_session
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get cashier session.
        parameters:
            - in: path
              name: cash_register_id
              description: The cash register id which needs to be fetched
              required: True
              type: string
            - in: path
              name: cashier_session_id
              description: The cashier session id which needs to be fetched
              required: True
              type: string
        tags:
            - Cashiering
        responses:
            200:
                description: The fetched cashier session.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CashierSessionResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    cashier_session_aggregate = query_handler.handle(
        cashier_session_id, user_data=user_data
    )
    cashier_response_schema = CashierSessionResponseSchema()
    cashier_response_schema.context['extra_data'] = dict(
        cashier_session_aggregate=cashier_session_aggregate
    )
    response = cashier_response_schema.dump(cashier_session_aggregate.cashier_session)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route(
    '/cashier/cash-registers/<string:cash_register_id>/cashier-sessions/<string:cashier_session_id>/summary',
    methods=['GET'],
)
@inject(query_handler=GetCashierSessionSummaryQueryHandler)
def get_cashier_session_summary(
    query_handler: GetCashierSessionSummaryQueryHandler,
    cash_register_id,
    cashier_session_id,
):
    """Get cashier session summary
    ---
    operationId: get_cashier_session_summary
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get cashier session.
        parameters:
            - in: path
              name: cash_register_id
              description: The cash register id which needs to be fetched
              required: True
              type: string
            - in: path
              name: cashier_session_id
              description: The cashier session id which needs to be fetched
              required: True
              type: string
        tags:
            - Cashiering
        responses:
            200:
                description: The fetched cashier session.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CashierSessionSummaryResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    cashier_session_summary_data = query_handler.handle(
        cashier_session_id, user_data=user_data
    )
    cashier_response_summary_schema = CashierSessionSummaryResponseSchema(many=True)
    response = cashier_response_summary_schema.dump(cashier_session_summary_data)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route(
    '/cashier/cash-registers/<string:cash_register_id>/cashier-sessions/<string:cashier_session_id>/payments',
    methods=['POST'],
)
@schema_wrapper_parser(CashCounterNewPaymentSchema)
@inject(command_handler=CreateCashierSessionPaymentCommandHandler)
def add_cashier_session_payments(
    command_handler: CreateCashierSessionPaymentCommandHandler,
    cash_register_id,
    cashier_session_id,
    parsed_request,
):
    """Add Cash counter payments
    ---
    operationId: add_cashier_session_payments
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Add cash counter payments.
        tags:
            - Cashiering
        parameters:
            - in: path
              name: cash_register_id
              description: The cash register id to which payment is to be added
              required: True
              type: string
            - in: path
              name: cashier_session_id
              description: The cashier session id to which payment is to be added
              required: True
              type: string
            - in: body
              name: body
              description: The payment object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/CashCounterNewPaymentSchema"
        responses:
            200:
                description: The created payment object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CashierPaymentResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    payment_dto = CashierPaymentData.from_dict(parsed_request)
    payment = command_handler.handle(cashier_session_id, payment_dto, user_data)
    payment = CashierPaymentResponseSchema().dump(payment).data
    return ApiResponse.build(data=payment, status_code=200)


@swag_route
@bp.route(
    '/cashier/cash-registers/<string:cash_register_id>/'
    'cashier-sessions/<string:cashier_session_id>/payments/<int:payment_id>',
    methods=['PATCH'],
)
@schema_wrapper_parser(CashCounterUpdatePaymentSchema)
@inject(command_handler=PatchCashierSessionPaymentCommandHandler)
def update_cashier_session_payments(
    command_handler: PatchCashierSessionPaymentCommandHandler,
    cash_register_id,
    cashier_session_id,
    payment_id,
    parsed_request,
):
    """Update Cash counter payments
    ---
    operationId: update_cashier_session_payments
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    patch:
        description: Add cash counter payments.
        tags:
            - Cashiering
        parameters:
            - in: path
              name: cash_register_id
              description: The cash register id to which payment is to be added
              required: True
              type: string
            - in: path
              name: cashier_session_id
              description: The cashier session id to which payment is to be added
              required: True
              type: string
            - in: path
              name: payment_id
              description: The cashier session payment id to which payment is to be updated
              required: True
              type: integer
            - in: body
              name: body
              description: The payment object which needs to be updated
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/CashCounterUpdatePaymentSchema"
        responses:
            200:
                description: The created payment object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CashierPaymentResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    status = (
        CashierPaymentStatus(parsed_request.get('status'))
        if parsed_request.get('status')
        else None
    )
    payment_dto = CashierPaymentData(
        date_of_payment=None,
        payment_mode=None,
        payment_mode_sub_type=None,
        payment_type=None,
        payment_details={},
        status=status,
        paid_to=None,
        payment_ref_id=None,
        comment=None,
        amount=None,
        amount_in_payment_currency=None,
        booking_id=None,
        voucher_number=None,
    )
    payment = command_handler.handle(
        cashier_session_id, payment_id, payment_dto, user_data
    )
    payment = CashierPaymentResponseSchema().dump(payment).data
    return ApiResponse.build(data=payment, status_code=200)


@swag_route
@bp.route(
    '/cashier/cash-registers/<string:cash_register_id>/cashier-sessions/<string:cashier_session_id>/payments',
    methods=['GET'],
)
@inject(query_handler=GetCashierSessionPaymentsQueryHandler)
def get_cashier_payments(
    query_handler: GetCashierSessionPaymentsQueryHandler,
    cash_register_id,
    cashier_session_id,
):
    """Get all the payments for a given cashier session
    ---
    parameters:
        - in: path
          name: cash_register_id
          description: The cash register id for which payment info need to be fetched.
          required: True
          type: string
    parameters:
        - in: path
          name: cashier_session_id
          description: The cashier session id for which payment info need to be fetched.
          required: True
          type: string
    operationId: get_cashier_payments
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: get all the payments for a given cashier session id.
        tags:
            - Cashiering
        responses:
            200:
                description: an array of payment objects.
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/CashierPaymentResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    payments = query_handler.handle(cashier_session_id, user_data=user_data)
    response = CashierPaymentResponseSchema(many=True).dump(payments)

    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route(
    '/cashier/cash-registers/<string:cash_register_id>/cashier-sessions',
    methods=['GET'],
)
@schema_wrapper_parser(CashierSessionSearchSchema, param_type=RequestTypes.ARGS)
@inject(
    search_query_handler=SearchCashierSessionQueryHandler,
    counter_query_handler=CountCashierSessionQueryHandler,
)
def search_cashier_sessions(
    search_query_handler: SearchCashierSessionQueryHandler,
    counter_query_handler: CountCashierSessionQueryHandler,
    cash_register_id,
    parsed_request,
):
    """Search Cashier Sessions
    ---
    operationId: search_cashier_sessions
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - in: path
              name: cash_register_id
              description: cash_register_id for which search should happen
              required: True
              type: string
            - in: query
              name: search_criteria
              required: true
              schema:
                $ref: "#/components/schemas/CashierSessionSearchSchema"
        description: Get list of cashier session details which match the search criteria.
        tags:
            - Cashiering
        responses:
            200:
                description: an array of cashier session objects.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CashierSessionSearchResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    query = CashierSessionSearchQuery(**parsed_request)
    cashier_session_aggregates = search_query_handler.handle(
        cash_register_id, query, user_data
    )
    total_count = counter_query_handler.handle(cash_register_id, query)

    serialized_cashier_sessions = dict()
    for cashier_session_aggregate in cashier_session_aggregates:
        cashier_session_response_schema = CashierSessionResponseSchema()
        cashier_session_response_schema.context['extra_data'] = dict(
            cashier_session_aggregate=cashier_session_aggregate
        )
        serialized_cashier_sessions[
            cashier_session_aggregate.cashier_session.cashier_session_id
        ] = cashier_session_response_schema.dump(
            cashier_session_aggregate.cashier_session
        )

    search_response = {
        'cashier_sessions': [
            cashier_session_aggregate
            for cashier_session_aggregate in cashier_session_aggregates
        ],
        'offset': query.offset,
        'limit': query.limit,
        'total': total_count,
    }

    if not query.include_payments:
        search_response_schema = CashierSessionSearchResponseSchema(
            exclude=['cashier_sessions.payments', 'cashier_sessions.session_summary']
        )
    else:
        search_response_schema = CashierSessionSearchResponseSchema()

    search_response_schema.context['extra_data'] = dict(
        serialized_cashier_sessions=serialized_cashier_sessions
    )
    response = search_response_schema.dump(search_response)
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route(
    '/cashier/cash-registers/<string:cash_register_id>/cashier-sessions/<string:cashier_session_id>/currency'
    '-exchanges',
    methods=['POST'],
)
@schema_wrapper_parser(NewCurrencyExchangeSchema)
@inject(command_handler=CreateCurrencyExchangeCommandHandler)
def record_new_currency_exchange(
    command_handler: CreateCurrencyExchangeCommandHandler,
    cash_register_id,
    cashier_session_id,
    parsed_request,
):
    """Record new currency exchange
    ---
    operationId: record_new_currency_exchange
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Records new currency exchange
        tags:
            - Cashiering
        parameters:
            - in: path
              name: cash_register_id
              description: The cash register id to which payment is to be added
              required: True
              type: string
            - in: path
              name: cashier_session_id
              description: The cashier session id to which payment is to be added
              required: True
              type: string
            - in: body
              name: body
              description: The currency exchange payload
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/NewCurrencyExchangeSchema"
        responses:
            201:
                description: Currency exchange entity recorded
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CurrencyExchangeResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    data = parsed_request
    request_dto = NewCurrencyExchangeDto(
        data['currency_seller_detail'],
        data['amount_in_foreign_currency'],
        data['foreign_currency_payment_mode'],
        data['amount_in_base_currency'],
        data['taxable_amount'],
        data['tax_amount'],
        data.get('tax_details'),
        data['round_off'],
        data['total_payable_in_base_currency'],
        data['exchange_rate'],
        data.get('transaction_date'),
        remarks=data.get('remarks'),
    )
    user_data = read_user_data_from_request_header()
    currency_exchange_dto = command_handler.handle(
        cash_register_id, cashier_session_id, request_dto, user_data
    )
    response = CurrencyExchangeResponseSchema().dump(currency_exchange_dto).data
    return ApiResponse.build(data=response, status_code=201)


@swag_route
@bp.route('/cashier/open-cashier-session', methods=['GET'])
@schema_wrapper_parser(CashRegisterSearchSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetOpenCashierSessionsQueryHandler)
def get_open_cashier_session(
    query_handler: GetOpenCashierSessionsQueryHandler, parsed_request
):
    """Get Open Cashier Session in given vendor_id
    ---
    operationId: get_open_cashier_session
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - in: query
              name: search_criteria
              required: true
              schema:
                $ref: "#/components/schemas/CashRegisterSearchSchema"
        description: Get open cashier session for provided vendor_id
        tags:
            - Cashiering
        responses:
            200:
                description: Open cashier session object
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CashierSessionResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    cashier_session_aggregate = query_handler.handle(
        parsed_request.get('vendor_id'), user_data
    )

    if not cashier_session_aggregate:
        return ApiResponse.build(data=None, status_code=200)

    cashier_session_response_schema = CashierSessionResponseSchema()
    cashier_session_response_schema.context['extra_data'] = dict(
        cashier_session_aggregate=cashier_session_aggregate
    )
    response = cashier_session_response_schema.dump(
        cashier_session_aggregate.cashier_session
    )
    return ApiResponse.build(status_code=200, data=response.data)
