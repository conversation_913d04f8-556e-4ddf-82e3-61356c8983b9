import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.billing.command_handlers.credit_shell.create_credit_shell_refund import (
    CreditShellRefundCommandHandler,
)
from prometheus.application.billing.dtos.credit_shell_refund_dto import (
    CreditShellRefundDto,
)
from prometheus.application.billing.query_handlers.credit_shell.get_credit_shells import (
    GetCreditShellsQueryHandler,
)
from prometheus.application.billing.query_handlers.credit_shell.get_transaction_log import (
    GetCreditShellTransactionLogQueryHandler,
)
from prometheus.common.api_response import ApiResponse
from prometheus.common.request_parsers import (
    read_application_from_request_header,
    read_user_data_from_request_header,
)
from prometheus.common.serializers.request import (
    CreditShellFilterSchema,
    NewCreditShellSchema,
)
from prometheus.common.serializers.response.credit_shell import (
    CreditShellRefundResponseSchema,
    CreditShellResponseSchema,
    CreditShellTransactionLogSchema,
)
from prometheus.core.api_docs import swag_route
from schema_instances import get_schema_obj
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_parser,
)

bp = Blueprint('CreditShell', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@swag_route
@bp.route('/credit-shells', methods=['GET'])
@schema_wrapper_parser(CreditShellFilterSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetCreditShellsQueryHandler)
def get_credit_shells(query_handler: GetCreditShellsQueryHandler, parsed_request):
    """Get all credit shells
    ---
    operationId: get_credit_shells
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get all credit shells
        tags:
            - CreditShell
        parameters:
            - name: credit_shell_filter
              in: query
              required: false
              schema:
                $ref: "#/components/schemas/CreditShellFilterSchema"
        responses:
            200:
                description: A list of all credit shell
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/CreditShellResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """

    booking_id = parsed_request.get('booking_id')
    credit_shell_id = parsed_request.get('credit_shell_id')
    bill_id = parsed_request.get('bill_id')
    credit_shell_aggregates = query_handler.handle(
        booking_id=booking_id, credit_shell_id=credit_shell_id, bill_id=bill_id
    )
    response = CreditShellResponseSchema(many=True).dump(
        [aggregate.credit_shell for aggregate in credit_shell_aggregates]
    )
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/credit-shells/<string:credit_shell_id>/transaction-log', methods=['GET'])
@inject(query_handler=GetCreditShellTransactionLogQueryHandler)
def get_transaction_logs(
    query_handler: GetCreditShellTransactionLogQueryHandler, credit_shell_id
):
    """Get Credit Shell Transaction Logs
    ---
    parameters:
          - name: credit_shell_id
            in: path
            type: string
            required: true
    operationId: get_transaction_logs
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get transaction logs for given credit shell
        tags:
            - AuditTrail
        responses:
            200:
                description: A list of transaction for the credit shell
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/CreditShellTransactionLogSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    transaction_logs = query_handler.handle(credit_shell_id)
    response = (
        get_schema_obj(CreditShellTransactionLogSchema, many=True)
        .dump(transaction_logs)
        .data
    )
    return ApiResponse.build(data=response, status_code=200)


@swag_route
@bp.route('/credit-shells', methods=['POST'])
@schema_wrapper_parser(NewCreditShellSchema)
@inject(command_handler=CreditShellRefundCommandHandler)
def credit_shell_refund(
    command_handler: CreditShellRefundCommandHandler, parsed_request
):
    """Redeem credit shell refund
    ---
    operationId: credit_shell_refund
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Credit Shell refund
        tags:
            - CreditShell
        parameters:
            - in: body
              name: body
              description: The credit shell refund objects which needs to be created
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/NewCreditShellSchema"
        responses:
            200:
                description: A credit shell refund
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/CreditShellRefundResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    credit_shell_aggregate = command_handler.handle(
        CreditShellRefundDto.create_new(parsed_request), user_data
    )[0]
    response = CreditShellRefundResponseSchema().dump(
        credit_shell_aggregate.credit_shell_refund
    )
    return ApiResponse.build(data=response.data, status_code=200)
