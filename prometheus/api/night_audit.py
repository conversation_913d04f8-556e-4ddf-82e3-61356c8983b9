"""
API for scheduling night audits for all hotels
"""
import logging

from flask import Blueprint
from treebo_commons.utils import dateutils

from object_registry import inject
from prometheus.application.end_of_day.night_audit_service import NightAuditService
from prometheus.common.api_response import ApiResponse
from prometheus.common.decorators import authorize_write_op
from prometheus.common.serializers.request.night_audit import ReScheduleNightAuditSchema
from prometheus.common.serializers.request.pending_night_audit_hotels import (
    PendingNightAuditHotelsSchema,
)
from prometheus.common.serializers.response.night_audit import (
    ScheduledNightAuditResponseSchema,
)
from prometheus.common.serializers.response.pending_night_audit_hotels import (
    PendingNightAuditHotelsResponseSchema,
)
from prometheus.core.api_docs import swag_route
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_parser,
)

bp = Blueprint('NightAudit', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@bp.route('/hotels/<hotel_id>/night-audit/<booking_id>', methods=['POST'])
@inject(night_audit_service=NightAuditService)
def consume_charges_for_booking(night_audit_service, hotel_id, booking_id):
    night_audit_service.perform_night_audit_for_booking(hotel_id, booking_id)
    return ApiResponse.build(status_code=200)


@swag_route
@bp.route('/hotels/<hotel_id>/scheduled-night-audit', methods=['GET'])
@inject(night_audit_service=NightAuditService)
def get_scheduled_night_audit(night_audit_service, hotel_id):
    """Get next scheduled Night Audit for the given hotel
    ---
    operationId: get_scheduled_night_audit
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - in: header
              name: X-Tenant-Id
              required: true
              type: string
            - in: header
              name: X-Hotel-Id
              required: false
              type: string
            - in: header
              name: X-User-Type
              required: false
              type: string
        description: Get next scheduled Night Audit for the given hotel
        tags:
            - Night Audit
        responses:
            200:
                description: Night audit job scheduled to run next
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/ScheduledNightAuditResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    night_audit_dto = night_audit_service.get_next_scheduled_night_audit_job(hotel_id)
    if not night_audit_dto:
        return ApiResponse.build(
            status_code=200,
            data=dict(message="Next night audit not scheduled for this hotel"),
        )
    response = ScheduledNightAuditResponseSchema().dump(night_audit_dto).data
    return ApiResponse.build(status_code=200, data=response)


@swag_route
@bp.route('/hotels/<hotel_id>/reschedule-night-audit', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(ReScheduleNightAuditSchema, param_type=RequestTypes.JSON)
@inject(night_audit_service=NightAuditService)
def reschedule_night_audit(night_audit_service, hotel_id, parsed_request):
    """Re-Schedule Night Audit for the given hotel, to given ETA
    ---
    operationId: reschedule_night_audit
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Re-Schedule Night Audit for the given hotel, to given ETA
        tags:
            - Night Audit
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - in: header
              name: X-Tenant-Id
              required: true
              type: string
            - in: header
              name: X-Hotel-Id
              required: false
              type: string
            - in: header
              name: X-User-Type
              required: false
              type: string
            - in: body
              name: body
              description: Time to reschedule night audit
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/ReScheduleNightAuditSchema"
        responses:
            200:
                description: Updated night audit job, with new eta
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/ScheduledNightAuditResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    eta = (
        parsed_request.get('eta')
        if not parsed_request.get('run_now')
        else dateutils.current_datetime()
    )
    night_audit_dto = night_audit_service.reschedule_night_audit(
        hotel_id, parsed_request.get('eta'), run_now=parsed_request.get('run_now')
    )
    response = ScheduledNightAuditResponseSchema().dump(night_audit_dto).data
    return ApiResponse.build(status_code=200, data=response)


@swag_route
@bp.route('/hotels/<hotel_id>/last-completed-night-audit', methods=['GET'])
@inject(night_audit_service=NightAuditService)
def get_last_completed_night_audit(night_audit_service, hotel_id):
    """Get last successfully completed Night Audit for the given hotel
    ---
    operationId: get_last_completed_night_audit
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - in: header
              name: X-Tenant-Id
              required: true
              type: string
            - in: header
              name: X-Hotel-Id
              required: false
              type: string
            - in: header
              name: X-User-Type
              required: false
              type: string
        description: Get last successfully completed Night Audit for the given hotel
        tags:
            - Night Audit
        responses:
            200:
                description: Night audit detail about last completion
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/ScheduledNightAuditResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    night_audit_dto = night_audit_service.get_last_completed_night_audit(hotel_id)
    if not night_audit_dto:
        return ApiResponse.build(
            status_code=200,
            data=dict(
                message="No successful night audit run till now for this" " hotel"
            ),
        )

    response = ScheduledNightAuditResponseSchema().dump(night_audit_dto).data
    return ApiResponse.build(status_code=200, data=response)


@swag_route
@bp.route("/pending-night-audit-hotels/", methods=["GET"])
@schema_wrapper_parser(PendingNightAuditHotelsSchema, param_type=RequestTypes.ARGS)
@inject(night_audit_service=NightAuditService)
def get_pending_night_audit_hotels(night_audit_service, parsed_request):
    """Search Pending Night Audit Hotels
    ---
    operationId: get_pending_night_audit_hotels
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: pending_days
              in: query
              required: false
              schema:
                $ref: "#/components/schemas/PendingNightAuditHotelsSchema"
        description: Get hotels whose business date is less than current date
        tags:
            - NightAudit
        responses:
            200:
                description: List of pending hotels with hotel id as key
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/PendingNightAuditHotelsResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """

    hotels_details = night_audit_service.get_hotels_with_pending_night_audit(
        parsed_request.get('pending_days')
    )
    pending_night_audit_hotel_response_schema = PendingNightAuditHotelsResponseSchema()
    response = pending_night_audit_hotel_response_schema.dump(
        {"hotels": hotels_details}
    )
    return ApiResponse.build(status_code=200, data=response)
