# -*- coding: utf-8 -*-

import logging

import requests
from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request

from ths_common.constants.user_constants import UserType
from thsc.crs import context, ths_config, version
from thsc.crs.decorators import request_pre_check
from thsc.crs.errors import THSCErrors
from thsc.crs.exceptions import CRSAPIException, THSCException


class RequestExecutor(object):
    def __init__(self):
        self.session = requests.Session()
        self.session.mount(
            'http://',
            requests.adapters.HTTPAdapter(
                pool_connections=50, pool_maxsize=5, max_retries=ths_config.max_tries
            ),
        )

        self._req_mappings = dict(
            GET=self.session.get,
            POST=self.session.post,
            DELETE=self.session.delete,
            PUT=self.session.put,
            PATCH=self.session.patch,
        )

    def build_url(self, api_request):
        uri = api_request.get_uri()
        return self.get_host() + uri

    def build_headers(self, api_request):
        headers = api_request.get_headers()
        headers_from_context = self.get_headers_from_context()
        headers.update(headers_from_context)
        return headers

    @request_pre_check()
    def execute(self, api_request, *args, **kwargs):
        """
        execute an API request_templates and handle the response with appropriate response handler
        :param api_request: APIRequest object
        :return: response (status_code and response dict/obj)
        """
        logger = logging.getLogger(self.__class__.__name__)
        request_failed = True

        url = self.build_url(api_request)
        headers = self.build_headers(api_request)
        tenant_id_to_override = kwargs.get('tenant_id')
        if tenant_id_to_override:
            headers['X-Tenant-ID'] = tenant_id_to_override
        requests_method = self._req_mappings[api_request.http_method]
        data = api_request.to_dict() if api_request.payload else dict()

        try:
            logger.info(
                "API request={0}, method={1}, data={2}, headers={3}".format(
                    url, api_request.http_method, data, headers
                )
            )
            if requests_method == self.session.get:
                response = requests_method(url=url, params=data, headers=headers)
            else:
                response = requests_method(url=url, json=data, headers=headers)
        except Exception as exc:
            logger.exception(
                "Exception occurred in firing API request. request_url: %s, %s: request payload: %s",
                api_request.http_method,
                url,
                data,
            )
            raise THSCException(message=str(exc))

        if 200 <= response.status_code < 300:
            request_failed = False

        json_response = {}
        try:
            if response.status_code != 204:  # No content
                json_response = response.json()
        except Exception as e:
            logger.info(
                "Failed to parse response for json due to reason: {}".format(str(e))
            )

        if request_failed:
            if json_response.get('errors'):
                errors = json_response.get('errors')
                logger.error('Error from CRS: %s', errors)
                message = errors[0].get("message") if errors else None
                raise CRSAPIException(
                    extra_payload=errors,
                    message=message,
                    http_status_code=response.status_code,
                    errors=errors,
                )
            raise CRSAPIException(
                THSCErrors.API_CALLED_FAILED,
                description="Api call failed with response Status Code: {0}".format(
                    response.status_code
                ),
                http_status_code=response.status_code,
            )

        if api_request.parse_response:
            data = json_response.get('data', {})
            resource_version = json_response.get('resource_version', None)
            if data:
                data = api_request.from_dict(response=data)

            if resource_version:
                return data, resource_version
            else:
                return data

    @staticmethod
    def get_headers_from_context():
        headers = dict()
        enrich_outgoing_request(headers)
        # Override headers set in enrich_outgoing_request if value is explicitly set in context
        if context.user_type is not None:
            if not headers.get('X-User-Type'):
                # Override user_type, if user_type is not already there
                headers['X-User-Type'] = str(context.user_type)
        if context.user is not None:
            headers['X-User'] = str(context.user)
        if context.application is not None:
            headers['X-Application'] = str(context.application)
        if context.auth_id is not None:
            headers['X-Auth-Id'] = str(context.auth_id)
        headers['X-THSC-Version'] = version.VERSION
        if context.hotel_id is not None:
            headers['X-Hotel-Id'] = context.hotel_id
        return headers

    @staticmethod
    def get_host():
        host = ths_config.crs_host
        if context.read_only:
            host = ths_config.read_only_crs_host
        return host
