from thsc.crs.convertors.inventory_convertors import (
    RoomTypeInventoryAvailabilityConvertor,
    RoomTypeInventoryAvailabilityGetQueryConvertor,
)
from thsc.crs.request_templates.request import Request


class GetRoomTypeInventoryAvailabilities(Request):
    _payload = True
    _convertor = RoomTypeInventoryAvailabilityGetQueryConvertor
    _response_convertor = RoomTypeInventoryAvailabilityConvertor

    def __init__(self, hotel_id, data=None):
        super(GetRoomTypeInventoryAvailabilities, self).__init__(
            "/hotels/{hotel_id}/room-type-inventories".format(hotel_id=hotel_id),
            'GET',
            data=data,
        )

    def get_uri(self):
        return super(GetRoomTypeInventoryAvailabilities, self).get_uri()

    def to_dict(self):
        request = self._convertor().to_dict(self.data, many=self._many)
        return request

    def from_dict(self, response):
        resp = self._response_convertor().from_dict(response, many=True)
        return resp
