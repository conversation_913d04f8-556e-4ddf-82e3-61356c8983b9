from thsc.crs.convertors.billing_convertors import (
    GenerateInvoicesQueryConverter,
    InvoiceConvertor,
)
from thsc.crs.entities.billing import GenerateInvoicesQuery
from thsc.crs.request_templates.request import Request


class GenerateInvoices(Request):
    _payload = True
    _convertor = GenerateInvoicesQueryConverter
    _response_convertor = InvoiceConvertor

    def __init__(
        self,
        bill_id,
        version,
        charge_ids,
        bill_to_info,
        user_info_map,
        status,
        issued_to_type,
        issued_by_type,
        allow_mixed_charge_type_in_invoice,
    ):
        super(GenerateInvoices, self).__init__(
            '/bills/{0}/invoices'.format(bill_id),
            'POST',
            data=GenerateInvoicesQuery(
                charge_ids,
                bill_to_info,
                user_info_map,
                status,
                issued_to_type,
                issued_by_type,
                allow_mixed_charge_type_in_invoice,
            ),
        )
        self.bill_id = bill_id
        self.resource_version = version

    def url(self):
        return super(GenerateInvoices, self).uri()

    def from_dict(self, response):
        resp = self._response_convertor().from_dict(response, many=True)
        return resp

    def to_dict(self):
        response = super(GenerateInvoices, self).to_dict()
        response['resource_version'] = self.resource_version
        return response
