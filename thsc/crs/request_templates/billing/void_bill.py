from thsc.crs.convertors.billing_convertors import BillConvertor
from thsc.crs.request_templates.request import Request


class VoidBill(Request):
    _response_convertor = BillConvertor
    _payload = False

    def __init__(self, bill_id):
        super(VoidBill, self).__init__('/bills/{0}/void'.format(bill_id), 'POST')
        self.bill_id = bill_id

    def from_dict(self, response):
        return self._response_convertor().from_dict(response, many=False)
