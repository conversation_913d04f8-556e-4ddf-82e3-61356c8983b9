from thsc.crs.convertors.billing_convertors import UploadedInvoiceTemplateConvertor
from thsc.crs.request_templates.request import Request


class GetInvoiceTemplate(Request):
    _payload = True
    _response_convertor = UploadedInvoiceTemplateConvertor

    def __init__(self, booking_id, invoice_id):
        super(GetInvoiceTemplate, self).__init__(
            '/bookings/{0}/invoices/{1}/template'.format(booking_id, invoice_id),
            request_method='POST',
        )

    def url(self):
        return super(GetInvoiceTemplate, self).uri()

    def to_dict(self):
        return {}

    def from_dict(self, response):
        resp = self._response_convertor().from_dict(response, False)
        return resp
