from ths_common.constants.hotel_constants import ManagedBy
from ths_common.value_objects import NotAssigned
from thsc.crs import hotel_live_cache
from thsc.crs.exceptions import CRSException


class Hotel(object):
    @staticmethod
    def is_managed_by_crs(hotel_id):
        """

        Args:
            hotel_id (str):

        Returns (ManagedBy):

        """
        from thsc.crs.executor_service import hotel_executor_service

        try:
            hotel_config = hotel_executor_service.get_hotel_config(hotel_id)
            hotel_live_cache[hotel_config.hotel_id] = hotel_config
        except CRSException:
            hotel_config = hotel_live_cache.get(hotel_id)
            if not hotel_config:
                return False
        return hotel_config.managed_by in (
            ManagedBy.CRS,
            ManagedBy.MIGRATION_IN_PROGRESS,
        )

    def __eq__(self, other):
        return self.__dict__ == other.__dict__


class HotelConfig(object):
    def __init__(
        self,
        hotel_id=NotAssigned,
        migration_start_date=NotAssigned,
        migration_end_date=NotAssigned,
        live_date=NotAssigned,
        managed_by=NotAssigned,
    ):
        self.hotel_id = hotel_id
        self.migration_start_date = migration_start_date
        self.migration_end_date = migration_end_date
        self.live_date = live_date
        self.managed_by = managed_by

    @staticmethod
    def create_empty_instance():
        return HotelConfig()
