from ths_common.value_objects import NotAssigned


class InclusionFrequency(object):
    def __init__(
        self,
        count=NotAssigned,
        day_of_serving=NotAssigned,
        frequency_type=NotAssigned,
    ):
        self.count = count
        self.day_of_serving = day_of_serving
        self.frequency_type = frequency_type


class InclusionOffering(object):
    def __init__(
        self,
        offered_quantity=NotAssigned,
        offering_type=NotAssigned,
    ):
        self.offered_quantity = offered_quantity
        self.offering_type = offering_type


class PackageInclusion(object):
    def __init__(
        self,
        frequency=NotAssigned,
        name=NotAssigned,
        offering=NotAssigned,
        sku_id=NotAssigned,
    ):
        self.frequency = frequency
        self.name = name
        self.offering = offering
        self.sku_id = sku_id


class CommissionDetail(object):
    def __init__(
        self,
        commission_percent=NotAssigned,
        commission_type=NotAssigned,
    ):
        self.commission_percent = commission_percent
        self.commission_type = commission_type


class RatePlanPackage(object):
    def __init__(
        self,
        inclusions=NotAssigned,
        package_id=NotAssigned,
        package_name=NotAssigned,
    ):
        self.inclusions = inclusions
        self.package_id = package_id
        self.package_name = package_name


class CancellationPolicy(object):
    def __init__(
        self,
        cancellation_charge_unit=NotAssigned,
        cancellation_charge_value=NotAssigned,
        cancellation_duration_before_checkin_end=NotAssigned,
        cancellation_duration_before_checkin_start=NotAssigned,
    ):
        self.cancellation_charge_unit = cancellation_charge_unit
        self.cancellation_charge_value = cancellation_charge_value
        self.cancellation_duration_before_checkin_end = (
            cancellation_duration_before_checkin_end
        )
        self.cancellation_duration_before_checkin_start = (
            cancellation_duration_before_checkin_start
        )


class ChildPolicy(object):
    def __init__(
        self,
        charge_per_child=NotAssigned,
        child_allowed=NotAssigned,
        unit_of_charge=NotAssigned,
    ):
        self.charge_per_child = charge_per_child
        self.child_allowed = child_allowed
        self.unit_of_charge = unit_of_charge


class PaymentPolicy(object):
    def __init__(
        self,
        advance_payment_percentage=NotAssigned,
        days_before_checkin_to_make_payment=NotAssigned,
        occupancy_percentage=NotAssigned,
        unit_of_payment_percentage=NotAssigned,
    ):
        self.advance_payment_percentage = advance_payment_percentage
        self.days_before_checkin_to_make_payment = days_before_checkin_to_make_payment
        self.occupancy_percentage = occupancy_percentage
        self.unit_of_payment_percentage = unit_of_payment_percentage


class RatePlanPolicy(object):
    def __init__(
        self,
        cancellation_policies=NotAssigned,
        child_policy=NotAssigned,
        payment_policies=NotAssigned,
    ):
        self.cancellation_policies = cancellation_policies
        self.child_policy = child_policy
        self.payment_policies = payment_policies


class RatePlanRestriction(object):
    def __init__(
        self,
        maximum_los=NotAssigned,
        minimum_abw=NotAssigned,
        minimum_los=NotAssigned,
    ):
        self.maximum_los = maximum_los
        self.minimum_abw = minimum_abw
        self.minimum_los = minimum_los


class RatePlan(object):
    def __init__(
        self,
        commission_details=NotAssigned,
        is_flexi=NotAssigned,
        name=NotAssigned,
        package=NotAssigned,
        policies=NotAssigned,
        print_rate=NotAssigned,
        rate_plan_code=NotAssigned,
        rate_plan_id=NotAssigned,
        rate_plan_reference_id=NotAssigned,
        restrictions=NotAssigned,
        suppress_rate=NotAssigned,
    ):
        self.commission_details = commission_details
        self.is_flexi = is_flexi
        self.name = name
        self.package = package
        self.policies = policies
        self.print_rate = print_rate
        self.rate_plan_code = rate_plan_code
        self.rate_plan_id = rate_plan_id
        self.rate_plan_reference_id = rate_plan_reference_id
        self.restrictions = restrictions
        self.suppress_rate = suppress_rate


class BookingRatePlans(object):
    def __init__(
        self,
        rate_plans=NotAssigned,
    ):
        self.rate_plans = rate_plans

    @staticmethod
    def get(booking_id):
        from thsc.crs.executor_service import rate_plan_executor_service

        return rate_plan_executor_service.get_rate_plans(booking_id)
