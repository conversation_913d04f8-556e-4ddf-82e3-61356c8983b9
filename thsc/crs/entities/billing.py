from datetime import datetime

from treebo_commons.money import Money

from ths_common.constants.billing_constants import (
    ChargeBillToTypes,
    ChargeStatus,
    ChargeTypes,
    PaymentChannels,
    PaymentModes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.expense_constants import ItemCodeTypes
from ths_common.value_objects import NotAssigned, TaxDetail
from thsc.crs.utils import get_money_if_assigned


class Bill(object):
    def __init__(
        self,
        bill_date=NotAssigned,
        bill_id=NotAssigned,
        version=NotAssigned,
        charges=NotAssigned,
        app_id=NotAssigned,
        parent_reference_number=NotAssigned,
        payments=NotAssigned,
        invoice_ids=NotAssigned,
        grace_period=NotAssigned,
        gstin=NotAssigned,
        vendor_details=NotAssigned,
        net_payable=NotAssigned,
        net_paid_amount=NotAssigned,
        total_credit_posttax_amount=NotAssigned,
        total_non_credit_invoiced_amount=NotAssigned,
        total_posttax_amount=NotAssigned,
        total_pretax_amount=NotAssigned,
        total_tax_amount=NotAssigned,
        vendor_id=NotAssigned,
        parent_info=NotAssigned,
        status=NotAssigned,
    ):
        self.bill_date = bill_date
        self.bill_id = bill_id
        self.app_id = app_id
        self.parent_reference_number = parent_reference_number
        self.version = version
        self.charges = charges
        self.payments = payments
        self.invoice_ids = invoice_ids
        self.grace_period = grace_period
        self.gstin = gstin
        self.vendor_id = vendor_id
        self.vendor_details = vendor_details
        self.net_payable = net_payable
        self.net_paid_amount = net_paid_amount
        self.total_credit_posttax_amount = total_credit_posttax_amount
        self.total_non_credit_invoiced_amount = total_non_credit_invoiced_amount
        self.total_posttax_amount = total_posttax_amount
        self.total_pretax_amount = total_pretax_amount
        self.total_tax_amount = total_tax_amount
        self.parent_info = parent_info
        self.status = status

    def __eq__(self, other):
        return self.__dict__ == other.__dict__

    def create(self):
        """
        Creates new bill
        Returns:
            Bill

        """
        from thsc.crs.executor_service import billing_executor_service

        bill = billing_executor_service.create_bill(self)
        self.replace(bill)
        return bill

    def replace(self, bill):
        self.bill_date = bill.bill_date
        self.parent_reference_number = bill.parent_reference_number
        self.app_id = bill.app_id
        self.version = bill.version
        self.invoice_ids = bill.invoice_ids
        self.grace_period = bill.grace_period
        self.gstin = bill.gstin
        self.vendor_details = bill.vendor_details
        self.charges = bill.charges
        self.payments = bill.payments
        self.net_payable = bill.net_payable
        self.net_paid_amount = bill.net_paid_amount
        self.total_credit_posttax_amount = bill.total_credit_posttax_amount
        self.total_non_credit_invoiced_amount = bill.total_non_credit_invoiced_amount
        self.total_posttax_amount = bill.total_posttax_amount
        self.total_pretax_amount = bill.total_pretax_amount
        self.total_tax_amount = bill.total_tax_amount
        self.vendor_id = bill.vendor_id
        self.status = bill.status

    @staticmethod
    def get(bill_id):
        from thsc.crs.executor_service import billing_executor_service

        return billing_executor_service.get_bill(bill_id)

    @staticmethod
    def get_v2(bill_id):
        from thsc.crs.executor_service import billing_executor_service

        return billing_executor_service.get_bill_v2(bill_id)

    @staticmethod
    def get_bills(bill_ids):
        from thsc.crs.executor_service import billing_executor_service

        bill_ids = ','.join(bill_ids)
        return billing_executor_service.get_bills(bill_ids)

    @staticmethod
    def create_for_update(bill_id, version):
        return Bill(bill_id=bill_id, version=version)

    def reset_bill(self):
        bill = self.get(self.bill_id)
        self.replace(bill)

    def void(self):
        from thsc.crs.executor_service import billing_executor_service

        return billing_executor_service.void_bill(self.bill_id)

    def add_payment(self, payment):
        '''
        Add payment
        Args:
            payment (Payment): List of payment

        Returns:
            Payment
        '''
        from thsc.crs.executor_service import billing_executor_service

        payment, resource_version = billing_executor_service.add_payment(
            self.bill_id, self.version, payment
        )
        self.reset_bill()
        return payment

    def add_charge(self, charge):
        '''
        Add charge
        :param charge:
        :return:
        '''
        from thsc.crs.executor_service import billing_executor_service

        charge, resource_version = billing_executor_service.add_charge(
            self.bill_id, self.version, charge
        )
        self.reset_bill()
        return charge

    def update_payment(self, payment):
        '''
        Add payments
        Args:
            payment (Payment): List of payments

        Returns:
            [Payment]
        '''
        from thsc.crs.executor_service import billing_executor_service

        payment, resource_version = billing_executor_service.update_payment(
            self.bill_id, self.version, payment
        )
        self.reset_bill()
        return payment

    def update_payments(self, payments):
        '''
        Add payments
        Args:
            payments (Payment): List of payments

        Returns:
            [Payments]
        '''
        from thsc.crs.executor_service import billing_executor_service

        payments, resource_version = billing_executor_service.update_payments(
            self.bill_id, self.version, payments
        )
        self.reset_bill()
        return payments

    def update_charge(self, charge):
        '''
        update charges
        Args:
            charges ([Charge]): List of charges

        Returns:
            [Charge]
        '''
        from thsc.crs.executor_service import billing_executor_service

        charges, resource_version = billing_executor_service.update_charge(
            self.bill_id, self.version, charge
        )
        self.reset_bill()
        return charges

    def update_charges(self, charges):
        '''
        update charges
        Args:
            charges ([Charge]): List of charges

        Returns:
            [Charge]
        '''
        from thsc.crs.executor_service import billing_executor_service

        charges, resource_version = billing_executor_service.update_charges(
            self.bill_id, self.version, charges
        )
        self.reset_bill()
        return charges

    def get_payments(self):
        """
        :return: [thsc.crs.entities.billing.Payment]
        """
        from thsc.crs.executor_service import billing_executor_service

        payments, resource_version = billing_executor_service.get_payments(self.bill_id)
        return payments

    def get_credit_notes(self):
        """

        :return: [thsc.crs.entities.billing.CreditNote]
        """
        from thsc.crs.executor_service import billing_executor_service

        return billing_executor_service.get_credit_notes(self.bill_id)

    def get_credit_note_template(self, credit_note_id):
        """

        :param credit_note_id:
        :return: thsc.crs.entities.billing.CreditNoteTemplate
        """
        from thsc.crs.executor_service import billing_executor_service

        return billing_executor_service.get_credit_note_template(
            self.bill_id, credit_note_id
        )

    def generate_invoices(
        self,
        charge_ids,
        bill_to_info,
        user_info_map,
        status,
        issued_to_type,
        issued_by_type,
        allow_mixed_charge_type_in_invoice=None,
    ):
        """

        :param charge_ids:
        :param bill_to_info
        :param user_info_map
        :param status
        :param issued_to_type
        :param issued_by_type
        :param allow_mixed_charge_type_in_invoice
        :return: thsc.crs.entities.billing.GenerateInvoices
        """
        from thsc.crs.executor_service import billing_executor_service

        return billing_executor_service.generate_invoices(
            self.bill_id,
            self.version,
            charge_ids,
            bill_to_info,
            user_info_map,
            status,
            issued_to_type,
            issued_by_type,
            allow_mixed_charge_type_in_invoice,
        )

    @staticmethod
    def print_bill(bill_id):
        """
        :param bill_id
        :return: thsc.crs.entities.billing.GenerateInvoices
        """
        from thsc.crs.executor_service import billing_executor_service

        return billing_executor_service.print_bill(bill_id)

    @staticmethod
    def create_instance(
        bill_date,
        app_id,
        parent_reference_number,
        vendor_id,
        vendor_details,
        charges,
        parent_info=NotAssigned,
    ):
        bill = Bill(
            bill_date=bill_date,
            app_id=app_id,
            parent_reference_number=parent_reference_number,
            vendor_id=vendor_id,
            vendor_details=vendor_details,
            charges=charges,
            parent_info=parent_info,
        )
        return bill

    @staticmethod
    def get_invoices(invoice_ids):
        from thsc.crs.executor_service import billing_executor_service

        return billing_executor_service.get_invoices(invoice_ids)


class Bills(object):
    def __init__(
        self,
        bills=NotAssigned,
        offset=NotAssigned,
        limit=NotAssigned,
        total=NotAssigned,
    ):
        self.bills = bills


class PrintBillUrl(object):
    def __init__(self, url):
        """

        :param  url:
        """
        self.url = url

    @classmethod
    def create_empty_instance(cls):
        return cls(url=NotAssigned)


class Payment(object):
    def __init__(
        self,
        amount=NotAssigned,
        amount_in_payment_currency=NotAssigned,
        date_of_payment=NotAssigned,
        paid_to=NotAssigned,
        payment_channel=NotAssigned,
        payment_mode=NotAssigned,
        payment_type=NotAssigned,
        status=NotAssigned,
        paid_by=NotAssigned,
        payment_details=NotAssigned,
        payment_ref_id=NotAssigned,
        payment_mode_sub_type=NotAssigned,
        payment_id=NotAssigned,
        comment=NotAssigned,
    ):
        """
        Args:
            amount (Money):
            paid_to (prometheus.constants.billing_constants.PaymentReceiverTypes):
            payment_channel (PaymentChannels):
            payment_mode (PaymentModes):
            payment_mode_sub_type (PaymentModeSubType):
            payment_type (PaymentTypes):
            status (PaymentStatus):
            date_of_payment (datetime):
            paid_by (PaidByTypes): Unique Identifier for the Customer making the payment
            payment_details (dict): Any details regarding the payment
            payment_ref_id (str): Payment reference ID as given by the payment gateway
            comment (str): Optional comment for the payment
        """
        self.payment_id = payment_id
        self.amount = get_money_if_assigned(amount)
        self.payment_mode = payment_mode
        self.payment_mode_sub_type = payment_mode_sub_type
        self.payment_type = payment_type
        self.status = status
        self.payment_details = payment_details
        self.paid_by = paid_by
        self.paid_to = paid_to
        self.payment_channel = payment_channel
        self.payment_ref_id = payment_ref_id
        self.date_of_payment = date_of_payment
        self.amount_in_payment_currency = amount_in_payment_currency
        self.comment = comment

    @staticmethod
    def create_instance(
        amount,
        amount_in_payment_currency,
        date_of_payment,
        paid_to,
        payment_channel,
        payment_mode,
        payment_type,
        status,
        paid_by,
        payment_details=NotAssigned,
        payment_ref_id=NotAssigned,
        payment_mode_sub_type=NotAssigned,
        comment=NotAssigned,
    ):
        """
        Args:
            amount (Money):
            paid_to (prometheus.constants.billing_constants.PaymentReceiverTypes):
            payment_channel (PaymentChannels):
            payment_mode (PaymentModes):
            payment_type (PaymentTypes):
            status (PaymentStatus):
            date_of_payment (datetime):
            paid_by (PaidByTypes): Unique Identifier for the Customer making the payment
            payment_mode_sub_type (PaymentModeSubType): the sub type of the payment mode
            payment_details (dict): Any details regarding the payment
            payment_ref_id (str): Payment reference ID as given by the payment gateway
            comment (str): Optional comment for the payment
        """
        return Payment(
            amount,
            amount_in_payment_currency,
            date_of_payment,
            paid_to,
            payment_channel,
            payment_mode,
            payment_type,
            status,
            paid_by,
            payment_details,
            payment_ref_id,
            payment_mode_sub_type,
            comment=comment,
        )

    @staticmethod
    def create_instance_for_update(payment_id):
        return Payment(payment_id=payment_id)

    def __eq__(self, other):
        return self.__dict__ == other.__dict__


class ChargeSplit(object):
    def __init__(
        self,
        charge_to=NotAssigned,
        percentage=NotAssigned,
        charge_type=NotAssigned,
        bill_to_type=NotAssigned,
        pre_tax=NotAssigned,
        post_tax=NotAssigned,
        posttax_amount_post_allowance=NotAssigned,
        pretax_amount_post_allowance=NotAssigned,
        tax_amount_post_allowance=NotAssigned,
        tax_details_post_allowance=NotAssigned,
        billed_entity_id=NotAssigned,
        billed_entity_account_number=NotAssigned,
        invoice_id=NotAssigned,
    ):
        self.charge_to = charge_to
        self.percentage = percentage
        self.charge_type = charge_type
        self.bill_to_type = bill_to_type
        self.pre_tax = pre_tax
        self.post_tax = post_tax
        self.posttax_amount_post_allowance = posttax_amount_post_allowance
        self.pretax_amount_post_allowance = pretax_amount_post_allowance
        self.tax_amount_post_allowance = tax_amount_post_allowance
        self.tax_details_post_allowance = tax_details_post_allowance
        self.billed_entity_id = billed_entity_id
        self.billed_entity_account_number = billed_entity_account_number
        self.invoice_id = invoice_id

    @staticmethod
    def create_empty_instance():
        return ChargeSplit(charge_to=NotAssigned, percentage=NotAssigned)


class Charge(object):
    def __init__(
        self,
        bill_to_type=NotAssigned,
        applicable_date=NotAssigned,
        type=NotAssigned,
        status=NotAssigned,
        created_at=NotAssigned,
        pretax_amount=NotAssigned,
        posttax_amount=NotAssigned,
        comments=NotAssigned,
        charge_to=NotAssigned,
        item=NotAssigned,
        charge_id=NotAssigned,
        charge_split_type=NotAssigned,
        charge_splits=NotAssigned,
        posttax_amount_post_allowance=NotAssigned,
        pretax_amount_post_allowance=NotAssigned,
        tax_amount_post_allowance=NotAssigned,
        tax_details_post_allowance=NotAssigned,
        is_inclusion_charge=NotAssigned,
        inclusion_charge_ids=NotAssigned,
    ):
        """
        Args:
            applicable_date (datetime): Date of consumption of the charge
            pretax_amount (Money): The Charge amount exclusive of tax
            type (ChargeTypes): credit - Guest can check-out without paying. The expenses will be billed to the company
                                non-credit - Guest has to clear the dues at the time of check-out
            status (ChargeStatus): Status of the charge
            posttax_amount (Money): The charge amount inclusive of tax
            comments (str): Any User/Client comments regarding the Charge
            charge_to (List[str]):
            bill_to_type (ChargeBillToTypes): Whom should the expenses be billed to
            item (ChargeItem):
        """
        # self.creation_channel = creation_channel
        self.applicable_date = applicable_date
        self.pretax_amount = get_money_if_assigned(pretax_amount)
        self.posttax_amount = get_money_if_assigned(posttax_amount)
        self.tax_amount = NotAssigned
        self.tax_details = NotAssigned
        self.type = type
        self.status = status
        self.comments = comments
        self.charge_to = charge_to
        self.bill_to_type = bill_to_type
        self.charge_id = charge_id
        self.charge_splits = charge_splits
        self.charge_split_type = charge_split_type
        self.item = item
        self.created_at = created_at
        self.bill_version_id = NotAssigned
        self.posttax_amount_post_allowance = posttax_amount_post_allowance
        self.pretax_amount_post_allowance = pretax_amount_post_allowance
        self.tax_amount_post_allowance = tax_amount_post_allowance
        self.tax_details_post_allowance = tax_details_post_allowance
        self.is_inclusion_charge = is_inclusion_charge
        self.inclusion_charge_ids = inclusion_charge_ids

    @staticmethod
    def create_instance(
        bill_to_type,
        applicable_date,
        type,
        status,
        created_at,
        recorded_time=NotAssigned,
        pretax_amount=NotAssigned,
        posttax_amount=NotAssigned,
        comments=NotAssigned,
        charge_to=NotAssigned,
        item=NotAssigned,
        charge_split_type=NotAssigned,
        charge_splits=NotAssigned,
        is_inclusion_charge=NotAssigned,
        inclusion_charge_ids=NotAssigned,
    ):
        return Charge(
            bill_to_type=bill_to_type,
            applicable_date=applicable_date,
            type=type,
            status=status,
            created_at=created_at,
            pretax_amount=pretax_amount,
            posttax_amount=posttax_amount,
            comments=comments,
            charge_to=charge_to,
            item=item,
            charge_split_type=charge_split_type,
            charge_splits=charge_splits,
            is_inclusion_charge=is_inclusion_charge,
            inclusion_charge_ids=inclusion_charge_ids,
        )

    @staticmethod
    def create_instance_for_update(charge_id):
        return Charge(charge_id=charge_id)

    @staticmethod
    def create_instance_from_existing_instance(charge):
        return Charge(
            charge_id=charge.charge_id,
            bill_to_type=charge.bill_to_type,
            applicable_date=charge.applicable_date,
            type=charge.type,
            status=charge.status,
            created_at=charge.created_at,
            pretax_amount=charge.pretax_amount,
            posttax_amount=charge.posttax_amount,
            comments=charge.comments,
            charge_to=charge.charge_to,
            item=charge.item,
            charge_split_type=charge.charge_split_type,
            charge_splits=charge.charge_splits,
            is_inclusion_charge=charge.is_inclusion_charge,
            inclusion_charge_ids=charge.inclusion_charge_ids,
        )

    def __eq__(self, other):
        return self.__dict__ == other.__dict__


class InvoiceCharge(object):
    def __init__(
        self,
        invoice_charge_id,
        charge_id,
        charge_split_ids,
        pretax_amount,
        tax_amount,
        tax_details,
        posttax_amount,
        charge_type,
        bill_to_type,
        charge_status,
        recorded_time,
        applicable_date,
        comment,
        created_by,
        charge_item,
        charge_to_ids,
        deleted,
        credit_note_generated_amount,
    ):
        self.invoice_charge_id = invoice_charge_id
        self.charge_id = charge_id
        self.charge_split_ids = charge_split_ids
        self.pretax_amount = get_money_if_assigned(pretax_amount)
        self.tax_details = tax_details if tax_details else []
        self.tax_amount = get_money_if_assigned(tax_amount)
        self.posttax_amount = get_money_if_assigned(posttax_amount)
        self.charge_type = charge_type
        self.bill_to_type = bill_to_type
        self.charge_status = charge_status
        self.recorded_time = recorded_time
        self.applicable_date = applicable_date
        self.comment = comment
        self.created_by = created_by
        self.charge_item = charge_item
        self.charge_to_ids = charge_to_ids
        self.deleted = deleted
        self.credit_note_generated_amount = (
            credit_note_generated_amount if credit_note_generated_amount else Money(0)
        )

    @staticmethod
    def create_empty_instance():
        invoice_charge = InvoiceCharge(
            invoice_charge_id=NotAssigned,
            charge_id=NotAssigned,
            charge_split_ids=NotAssigned,
            pretax_amount=NotAssigned,
            tax_amount=NotAssigned,
            tax_details=NotAssigned,
            posttax_amount=NotAssigned,
            charge_type=NotAssigned,
            bill_to_type=NotAssigned,
            charge_status=NotAssigned,
            recorded_time=NotAssigned,
            applicable_date=NotAssigned,
            comment=NotAssigned,
            created_by=NotAssigned,
            charge_item=NotAssigned,
            charge_to_ids=NotAssigned,
            deleted=NotAssigned,
            credit_note_generated_amount=NotAssigned,
        )
        return invoice_charge


class PaymentSplit(object):
    def __init__(
        self, payment_id, payment_split_id, amount, payment_type, payment_mode
    ):
        self.payment_id = payment_id
        self.payment_split_id = payment_split_id
        self.amount = amount
        self.payment_type = payment_type
        self.payment_mode = payment_mode

    @staticmethod
    def create_empty_instance():
        payment_split = PaymentSplit(
            payment_id=NotAssigned,
            payment_split_id=NotAssigned,
            amount=NotAssigned,
            payment_type=NotAssigned,
            payment_mode=NotAssigned,
        )
        return payment_split


class Invoice(object):
    def __init__(
        self,
        invoice_id,
        bill_id,
        invoice_number,
        pretax_amount,
        tax_amount,
        posttax_amount,
        invoice_date,
        invoice_due_date,
        bill_to,
        bill_to_type,
        allowed_charge_types,
        allowed_charge_tos,
        invoice_url,
        tax_details_breakup,
        status,
        vendor_id=None,
        vendor_details=None,
        generated_by=None,
        generation_channel=None,
        version=None,
        deleted=False,
        issued_to_type=None,
        issued_by_type=None,
        hotel_invoice_id=None,
        issued_by=None,
        created_at=None,
        modified_at=None,
        invoice_charges=None,
        payment_splits=None,
        irn=None,
        qr_code=None,
        is_einvoice=None,
        parent_info=None,
        signed_url=None,
        is_spot_credit=None,
    ):
        """
        Args:
            invoice_id : unique id of the invoice
            bill_id : the bill id of the bill to which the invoice belongs
            invoice_number : Invoice number in series compliant with the GST Council rules
            pretax_amount : Total amount exclusive of tax
            tax_amount : Total applicable tax
            posttax_amount : Total amount inclusive of tax
            invoice_date : Date of invoice generation
            invoice_due_date : The due date for payment of pending amounts
            bill_to : the bill_to details
            bill_to_type: the charges in this invoice can only belong to this type
            allowed_charge_types : the invoice can contain charges of the given type only
            allowed_charge_tos : if present then the charge contains charges assigned only to the given customers
            invoice_url : the url of the invoice template
            tax_details_breakup : the dict containing the tax type level breakup dict(cgst=10.0, sgst=11.0, igst=0.0)
            status : the status of the invoice
        """
        self.invoice_id = invoice_id
        self.bill_id = bill_id
        self.invoice_number = invoice_number
        self.pretax_amount = get_money_if_assigned(pretax_amount)
        self.tax_amount = get_money_if_assigned(tax_amount)
        self.posttax_amount = get_money_if_assigned(posttax_amount)
        self.tax_details_breakup = dict()
        if tax_details_breakup:
            for tax_type, amount in tax_details_breakup:
                self.tax_details_breakup[tax_type] = get_money_if_assigned(amount)
        self.invoice_date = invoice_date
        self.invoice_due_date = invoice_due_date
        self.status = status
        self.bill_to = bill_to
        self.bill_to_type = bill_to_type
        self.allowed_charge_types = allowed_charge_types
        self.allowed_charge_tos = allowed_charge_tos
        self.invoice_url = invoice_url

        self.created_at = created_at
        self.modified_at = modified_at
        self.generated_by = generated_by
        self.generation_channel = generation_channel
        self.vendor_details = vendor_details
        self.version = version
        self.deleted = deleted
        self.vendor_id = vendor_id
        self.issued_to_type = issued_to_type
        self.issued_by_type = issued_by_type
        self.issued_by = issued_by
        self.hotel_invoice_id = hotel_invoice_id
        self.invoice_charges = invoice_charges
        self.payment_splits = payment_splits
        self.irn = irn
        self.qr_code = qr_code
        self.is_einvoice = is_einvoice
        self.parent_info = parent_info
        self.signed_url = signed_url
        self.is_spot_credit = is_spot_credit

    @staticmethod
    def get(invoice_id, with_invoice_charges=False):
        from thsc.crs.executor_service import billing_executor_service

        return billing_executor_service.get_invoice(
            invoice_id, with_invoice_charges=with_invoice_charges
        )

    @staticmethod
    def create_empty_instance():
        return Invoice(
            invoice_id=NotAssigned,
            bill_id=NotAssigned,
            invoice_number=NotAssigned,
            pretax_amount=NotAssigned,
            tax_amount=NotAssigned,
            posttax_amount=NotAssigned,
            invoice_date=NotAssigned,
            invoice_due_date=NotAssigned,
            bill_to=NotAssigned,
            bill_to_type=NotAssigned,
            allowed_charge_types=NotAssigned,
            allowed_charge_tos=NotAssigned,
            invoice_url=NotAssigned,
            tax_details_breakup=NotAssigned,
            status=NotAssigned,
            payment_splits=NotAssigned,
            irn=NotAssigned,
            qr_code=NotAssigned,
            is_einvoice=NotAssigned,
            parent_info=NotAssigned,
        )

    def __eq__(self, other):
        return self.__dict__ == other.__dict__


class ChargeItem(object):
    def __init__(self, name, sku_category_id, details, item_code=None):
        """
        Args:
            name (str): Category of the item
            sku_category_id (str):
            details (dict): Details about the Charge item in JSON
            item_code (str): HSN/SAC Code for this charge item
        """
        self.details = details
        self.name = name
        self.sku_category_id = sku_category_id
        self.item_code = item_code

    def __eq__(self, other):
        return self.__dict__ == other.__dict__

    @staticmethod
    def create_empty_instance():
        return ChargeItem(
            name=NotAssigned,
            sku_category_id=NotAssigned,
            details=NotAssigned,
            item_code=NotAssigned,
        )


class InvoiceChargeToInfo(object):
    def __init__(self, customer_id, name, is_booking_owner=False):
        """
        Args:
            customer_id:
            name:
            is_booking_owner:
        """
        self.customer_id = customer_id
        self.name = name
        self.is_booking_owner = is_booking_owner

    @staticmethod
    def create_empty_instance():
        return InvoiceChargeToInfo(
            customer_id=NotAssigned, name=NotAssigned, is_booking_owner=NotAssigned
        )

    def __eq__(self, other):
        return self.__dict__ == other.__dict__


class ItemCode(object):
    def __init__(self, code_type, value):
        """
        Args:
            code_type (ItemCodeTypes): GST Code type
            value (str): HSN/SAC Code of the item
        """
        self.code_type = code_type
        self.value = value

    @staticmethod
    def create_empty_instance():
        return ItemCode(code_type=NotAssigned, value=NotAssigned)

    def __eq__(self, other):
        return self.__dict__ == other.__dict__


class InvoiceGetQuery(object):
    def __init__(self, only_confirmed=NotAssigned, show_raw=NotAssigned):
        """

        Args:
            only_confirmed:
        """
        self.only_confirmed = only_confirmed
        self.show_raw = show_raw

    def __eq__(self, other):
        return self.__dict__ == other.__dict__

    @staticmethod
    def create_empty_instance():
        return InvoiceGetQuery(only_confirmed=NotAssigned)


class InvoicesGetQuery(object):
    def __init__(
        self,
        only_confirmed=NotAssigned,
        show_raw=NotAssigned,
        invoice_ids=NotAssigned,
        generate_signed_url=NotAssigned,
    ):
        """

        Args:
            only_confirmed:
        """
        self.only_confirmed = only_confirmed
        self.show_raw = show_raw
        self.invoice_ids = invoice_ids
        self.generate_signed_url = generate_signed_url

    def __eq__(self, other):
        return self.__dict__ == other.__dict__

    @staticmethod
    def create_empty_instance():
        return InvoicesGetQuery(only_confirmed=NotAssigned, invoice_ids=NotAssigned)


class InvoiceTemplateBookingDetails(object):
    def __init__(
        self, booking_id, checkin_date, checkout_date, creation_date, reference_number
    ):
        self.booking_id = booking_id
        self.checkin_date = checkin_date
        self.checkout_date = checkout_date
        self.creation_date = creation_date
        self.reference_number = reference_number

    @classmethod
    def create_empty_instance(cls):
        return cls(
            booking_id=NotAssigned,
            checkin_date=NotAssigned,
            checkout_date=NotAssigned,
            creation_date=NotAssigned,
            reference_number=NotAssigned,
        )


class BookingInvoiceChargeAmount(object):
    def __init__(
        self, applicable_date, item_code, posttax_amount, pretax_amount, services, tax
    ):
        """

        :param applicable_date:
        :param ItemCode item_code:
        :param posttax_amount:
        :param pretax_amount:
        :param services:
        :param dict tax: dict of {tax_type: {'amount': 123, 'percentage': 12}}
        """
        self.applicable_date = applicable_date
        self.item_code = item_code
        self.posttax_amount = posttax_amount
        self.pretax_amount = pretax_amount
        self.services = services
        self.tax = tax

    @property
    def tax_details(self):
        tax_details = []
        for tax_type, tax_amount in self.tax.items():
            tax_details.append(
                TaxDetail(
                    tax_type=tax_type,
                    percentage=tax_amount['percentage'],
                    amount=Money(str(tax_amount['amount'])),
                )
            )
        return tax_details

    @classmethod
    def create_empty_instance(cls):
        return cls(
            applicable_date=NotAssigned,
            item_code=NotAssigned,
            posttax_amount=NotAssigned,
            pretax_amount=NotAssigned,
            services=NotAssigned,
            tax=NotAssigned,
        )


class BookingInvoiceChargeTo(object):
    def __init__(self, customer_id, name):
        """

        :param customer_id:
        :param Name name:
        """
        self.customer_id = customer_id
        self.name = name

    @classmethod
    def create_empty_instance(cls):
        return cls(customer_id=NotAssigned, name=NotAssigned)


class BookingInvoiceRoomInfo(object):
    def __init__(self, occupancy, room_no, room_type):
        self.occupancy = occupancy
        self.room_no = room_no
        self.room_type = room_type

    @classmethod
    def create_empty_instance(cls):
        return cls(occupancy=NotAssigned, room_no=NotAssigned, room_type=NotAssigned)


class BookingInvoiceChargeItem(object):
    def __init__(self, charges, guests, room_info):
        """

        :param BookingInvoiceChargeAmount charges: List of BookingInvoiceChargeAmount
        :param BookingInvoiceChargeTo guests: List of BookingInvoiceChargeTo
        :param BookingInvoiceRoomInfo room_info:
        """
        self.charges = charges
        self.guests = guests
        self.room_info = room_info

    @classmethod
    def create_empty_instance(cls):
        return cls(charges=NotAssigned, guests=NotAssigned, room_info=NotAssigned)


class InvoiceTemplate(object):
    def __init__(self, booking_details, charge_items, invoice, payments):
        """

        :param InvoiceTemplateBookingDetails booking_details:
        :param BookingInvoiceChargeItem charge_items: List of BookingInvoiceChargeItem
        :param Invoice invoice: Invoice without invoice_charges
        :param Payment payments: List of Payment
        """
        self.booking_details = booking_details
        self.charge_items = charge_items
        self.invoice = invoice
        self.payments = payments

    @classmethod
    def create_empty_instance(cls):
        return cls(
            booking_details=NotAssigned,
            charge_items=NotAssigned,
            invoice=NotAssigned,
            payments=NotAssigned,
        )


class UploadedInvoiceTemplate(object):
    def __init__(self, invoice_url, template):
        """

        :param str invoice_url:
        :param InvoiceTemplate template:
        """
        self.invoice_url = invoice_url
        self.template = template

    @classmethod
    def create_empty_instance(cls):
        return cls(invoice_url=NotAssigned, template=NotAssigned)


class GenerateInvoicesQuery(object):
    def __init__(
        self,
        charge_ids,
        bill_to_info,
        user_info_map,
        status,
        issued_to_type,
        issued_by_type,
        allow_mixed_charge_type_in_invoice,
    ):
        """

        Args:
            charge_ids
            bill_to_info
            user_info_map
            status
            issued_to_type
            issued_by_type
            allow_mixed_charge_type_in_invoice
        """
        self.charge_ids = charge_ids
        self.bill_to_info = bill_to_info
        self.user_info_map = user_info_map
        self.status = status
        self.issued_to_type = issued_to_type
        self.issued_by_type = issued_by_type
        self.allow_mixed_charge_type_in_invoice = allow_mixed_charge_type_in_invoice

    def __eq__(self, other):
        return self.__dict__ == other.__dict__

    @staticmethod
    def create_empty_instance():
        return GenerateInvoicesQuery(
            charge_ids=NotAssigned,
            bill_to_info=NotAssigned,
            user_info_map=NotAssigned,
            status=NotAssigned,
            issued_to_type=NotAssigned,
            issued_by_type=NotAssigned,
            allow_mixed_charge_type_in_invoice=NotAssigned,
        )


class CreditNoteLineItem(object):
    def __init__(
        self,
        credit_note_line_item_id,
        invoice_id,
        invoice_charge_id,
        applicable_date,
        pretax_amount,
        posttax_amount,
        tax_amount,
        tax_details=None,
        deleted=False,
        item_name=None,
        item_code=None,
        charge_item_detail=None,
        sku_category_id=None,
    ):
        self.credit_note_line_item_id = credit_note_line_item_id
        self.invoice_id = invoice_id
        self.invoice_charge_id = invoice_charge_id
        self.applicable_date = applicable_date
        self.pretax_amount = pretax_amount
        self.posttax_amount = posttax_amount
        self.tax_amount = tax_amount
        self.tax_details = tax_details
        self.deleted = deleted
        self.item_name = item_name
        self.item_code = item_code
        self.charge_item_detail = charge_item_detail
        self.sku_category_id = sku_category_id

    @classmethod
    def create_empty_instance(cls):
        return cls(
            credit_note_line_item_id=NotAssigned,
            invoice_id=NotAssigned,
            invoice_charge_id=NotAssigned,
            applicable_date=NotAssigned,
            pretax_amount=NotAssigned,
            posttax_amount=NotAssigned,
            tax_amount=NotAssigned,
            tax_details=NotAssigned,
            deleted=NotAssigned,
            item_name=NotAssigned,
            item_code=NotAssigned,
            charge_item_detail=NotAssigned,
            sku_category_id=NotAssigned,
        )


class CreditNote(object):
    def __init__(
        self,
        bill_id,
        credit_note_id,
        credit_note_number,
        credit_note_date,
        vendor_details,
        vendor_id,
        issued_to,
        issued_by,
        status,
        comment,
        issued_to_type,
        issued_by_type,
        tax_details,
        posttax_amount,
        pretax_amount,
        tax_amount,
        hotel_credit_note_id,
        version,
        credit_note_line_items,
        credit_note_url=None,
        created_at=None,
        modified_at=None,
        irn=None,
        qr_code=None,
        is_einvoice=None,
        signed_url=None,
    ):
        self.bill_id = bill_id
        self.credit_note_id = credit_note_id
        self.credit_note_number = credit_note_number
        self.credit_note_date = credit_note_date
        self.vendor_details = vendor_details
        self.vendor_id = vendor_id
        self.issued_to = issued_to
        self.issued_by = issued_by
        self.status = status
        self.comment = comment
        self.issued_to_type = issued_to_type
        self.issued_by_type = issued_by_type
        self.tax_details = tax_details
        self.posttax_amount = posttax_amount
        self.pretax_amount = pretax_amount
        self.tax_amount = tax_amount
        self.hotel_credit_note_id = hotel_credit_note_id
        self.version = version
        self.credit_note_url = credit_note_url
        self.created_at = created_at
        self.modified_at = modified_at
        self.credit_note_line_items = credit_note_line_items
        self.irn = irn
        self.qr_code = qr_code
        self.is_einvoice = is_einvoice
        self.signed_url = signed_url

    @staticmethod
    def get(credit_note_id):
        """

        :param credit_note_id:
        :return: thsc.crs.entities.billing.CreditNote
        """
        from thsc.crs.executor_service import billing_executor_service

        return billing_executor_service.get_credit_note(credit_note_id)

    @classmethod
    def create_empty_instance(cls):
        return cls(
            bill_id=NotAssigned,
            credit_note_id=NotAssigned,
            credit_note_number=NotAssigned,
            credit_note_date=NotAssigned,
            vendor_details=NotAssigned,
            vendor_id=NotAssigned,
            issued_to=NotAssigned,
            issued_by=NotAssigned,
            status=NotAssigned,
            comment=NotAssigned,
            issued_to_type=NotAssigned,
            issued_by_type=NotAssigned,
            tax_details=NotAssigned,
            posttax_amount=NotAssigned,
            pretax_amount=NotAssigned,
            tax_amount=NotAssigned,
            hotel_credit_note_id=NotAssigned,
            version=NotAssigned,
            credit_note_line_items=NotAssigned,
            credit_note_url=NotAssigned,
            created_at=NotAssigned,
            modified_at=NotAssigned,
            irn=NotAssigned,
            qr_code=NotAssigned,
            is_einvoice=NotAssigned,
        )


class CreditNoteInvoiceDetail(object):
    def __init__(self, invoice_date, invoice_number):
        self.invoice_date = invoice_date
        self.invoice_number = invoice_number

    @classmethod
    def create_empty_instance(cls):
        return cls(invoice_date=NotAssigned, invoice_number=NotAssigned)


class CreditNoteTemplate(object):
    def __init__(self, booking_details, credit_note, invoice_details):
        """

        :param InvoiceTemplateBookingDetails booking_details:
        :param CreditNote credit_note: Credit Note
        :param CreditNoteInvoiceDetail invoice_details: List of CreditNoteInvoiceDetail
        """
        self.booking_details = booking_details
        self.credit_note = credit_note
        self.invoice_details = invoice_details

    @classmethod
    def create_empty_instance(cls):
        return cls(
            booking_details=NotAssigned,
            credit_note=NotAssigned,
            invoice_details=NotAssigned,
        )
