from ths_common.value_objects import NotAssigned


class DNRSearchQuery(object):
    def __init__(self, to_date, from_date=NotAssigned, source=NotAssigned):
        self.from_date = from_date
        self.to_date = to_date
        self.source = source


class DNR(object):
    def __init__(
        self,
        hotel_id=NotAssigned,
        room_id=NotAssigned,
        from_date=NotAssigned,
        to_date=NotAssigned,
        type=NotAssigned,
        subtype=NotAssigned,
        comments=NotAssigned,
        source=NotAssigned,
        assigned_by=NotAssigned,
        date_inactivated=NotAssigned,
        status=NotAssigned,
        dnr_id=NotAssigned,
        version=NotAssigned,
    ):
        """

        :param hotel_id:
        :param room_id:
        :param from_date:
        :param to_date:
        :param type (prometheus.constants.inventory_constants.DNRType):
        :param subtype (prometheus.constants.inventory_constants.DNRSubType):
        :param comments:
        :param source (prometheus.constants.inventory_constants.DNRSource):
        :param assigned_by:
        :param date_inactivated:
        :param status:
        :param dnr_id:
        :param version:
        """
        self.dnr_id = dnr_id
        self.from_date = from_date
        self.to_date = to_date
        self.hotel_id = hotel_id
        self.room_id = room_id
        self.type = type
        self.subtype = subtype
        self.comments = comments
        self.status = status
        self.source = source
        self.assigned_by = assigned_by
        self.date_inactivated = date_inactivated
        self.version = version

    def create(self):
        """
        Creates booking
        Returns:
            Booking

        """
        from thsc.crs.executor_service import inventory_executor_service

        return inventory_executor_service.mark_dnr(self)

    def update(self):
        """
        Updates DNR
        Returns:
            DNR

        """
        from thsc.crs.executor_service import inventory_executor_service

        dnr = inventory_executor_service.update_dnr(self, self.version)
        self.refresh_dnr(dnr)

    def remove(self):
        """
        Marks DNR Inactive
        Returns:
            DNR

        """
        from thsc.crs.executor_service import inventory_executor_service

        inventory_executor_service.remove_dnr(self, self.version)
        dnr = inventory_executor_service.get_dnr(self.hotel_id, self.dnr_id)
        self.refresh_dnr(dnr)

    @staticmethod
    def get(hotel_id, dnr_id):
        """
        Args:
            hotel_id (str)
            dnr_id (str):

        Returns:
            DNR

        """
        from thsc.crs.executor_service import inventory_executor_service

        return inventory_executor_service.get_dnr(hotel_id, dnr_id)

    @staticmethod
    def search(hotel_id, to_date, source=NotAssigned, from_date=NotAssigned):
        """

        :param hotel_id:
        :param to_date: (inclusive)
        :param source:
        :param from_date: (inclusive) - defaults to current date
        :return:
        """
        from thsc.crs.executor_service import inventory_executor_service

        dnr_search_query = DNRSearchQuery(to_date, from_date, source)
        dnrs = inventory_executor_service.search_dnr(hotel_id, dnr_search_query)
        return dnrs

    @staticmethod
    def create_instance(
        hotel_id,
        room_id,
        from_date,
        to_date,
        type,
        subtype,
        source,
        assigned_by,
        comments=NotAssigned,
    ):
        return DNR(
            hotel_id=hotel_id,
            room_id=room_id,
            from_date=from_date,
            to_date=to_date,
            type=type,
            subtype=subtype,
            source=source,
            assigned_by=assigned_by,
            comments=comments,
        )

    @staticmethod
    def create_instance_for_update(hotel_id, dnr_id, dnr_version):
        return DNR(hotel_id=hotel_id, dnr_id=dnr_id, version=dnr_version)

    @staticmethod
    def create_empty_instance():
        return DNR()

    def refresh_dnr(self, dnr):
        self.dnr_id = dnr.dnr_id
        self.from_date = dnr.from_date
        self.to_date = dnr.to_date
        self.hotel_id = dnr.hotel_id
        self.room_id = dnr.room_id
        self.type = dnr.type
        self.subtype = dnr.subtype
        self.comments = dnr.comments
        self.status = dnr.status
        self.source = dnr.source
        self.assigned_by = dnr.assigned_by
        self.date_inactivated = dnr.date_inactivated
        self.version = dnr.version
