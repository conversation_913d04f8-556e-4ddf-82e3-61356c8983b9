from ths_common.value_objects import NotAssigned


class CashierSession:
    def __init__(
        self,
        cashier_session_id=NotAssigned,
        session_number=NotAssigned,
        start_datetime=NotAssigned,
        end_datetime=NotAssigned,
        vendor_id=NotAssigned,
        status=NotAssigned,
        opened_by=NotAssigned,
        closed_by=NotAssigned,
        cash_register_id=NotAssigned,
    ):
        self.cashier_session_id = cashier_session_id
        self.start_datetime = start_datetime
        self.end_datetime = end_datetime
        self.status = status
        self.opened_by = opened_by
        self.closed_by = closed_by
        self.session_number = session_number
        self.cash_register_id = cash_register_id
        self.vendor_id = vendor_id

    @staticmethod
    def search(cash_register_id, cashier_session_query):
        from thsc.crs.executor_service import cashier_executor_service

        cashier_session = cashier_executor_service.search_cashier_session(
            cash_register_id, cashier_session_query
        )
        return cashier_session

    def add_cashier_payment(self, payment):
        '''
        Add payment
        Args:
            payment (Payment): List of payment

        Returns:
            Payment
        '''
        from thsc.crs.executor_service import cashier_executor_service

        payment = cashier_executor_service.add_cashier_payment(
            self.cash_register_id, self.cashier_session_id, payment
        )
        return payment


class CashierPayment:
    def __init__(
        self,
        payment_id=NotAssigned,
        date_of_payment=NotAssigned,
        payment_mode=NotAssigned,
        payment_type=NotAssigned,
        payment_details=NotAssigned,
        status=NotAssigned,
        paid_to=NotAssigned,
        comment=NotAssigned,
        amount=NotAssigned,
        added_by=NotAssigned,
        amount_in_payment_currency=NotAssigned,
        current_session_balance=NotAssigned,
        booking_id=NotAssigned,
        booking_owner_name=NotAssigned,
        bill_id=NotAssigned,
        payment_mode_sub_type=NotAssigned,
        voucher_url=NotAssigned,
        pos_order_id=NotAssigned,
        bill_payment_id=NotAssigned,
    ):
        self.payment_id = payment_id
        self.amount = amount
        self.date_of_payment = date_of_payment
        self.amount_in_payment_currency = amount_in_payment_currency
        self.payment_mode = payment_mode
        self.payment_type = payment_type
        self.paid_to = paid_to
        self.added_by = added_by
        self.status = status
        self.payment_details = payment_details
        self.comment = comment
        self.booking_id = booking_id
        self.pos_order_id = pos_order_id
        self.payment_mode_sub_type = payment_mode_sub_type
        self.voucher_url = voucher_url
        self.current_session_balance = current_session_balance
        self.booking_owner_name = booking_owner_name
        self.bill_id = bill_id
        self.bill_payment_id = bill_payment_id

    @staticmethod
    def create_instance(
        date_of_payment,
        payment_mode,
        payment_type,
        payment_details,
        status,
        paid_to,
        comment,
        amount,
        amount_in_payment_currency,
        payment_mode_sub_type,
    ):
        return CashierPayment(
            date_of_payment=date_of_payment,
            payment_mode=payment_mode,
            payment_type=payment_type,
            payment_details=payment_details,
            status=status,
            paid_to=paid_to,
            comment=comment,
            amount=amount,
            amount_in_payment_currency=amount_in_payment_currency,
            payment_mode_sub_type=payment_mode_sub_type,
        )


class CashRegisterSearchQuery:
    def __init__(self, vendor_id=NotAssigned):
        self.vendor_id = vendor_id

    @staticmethod
    def create_instance(vendor_id):
        return CashRegisterSearchQuery(vendor_id=vendor_id)


class CashierSessionSearchQuery:
    def __init__(self, sort_by=NotAssigned, limit=NotAssigned, vendor_id=NotAssigned):
        self.sort_by = sort_by
        self.limit = limit
        self.vendor_id = vendor_id

    @staticmethod
    def create_instance(sort_by, limit, vendor_id):
        return CashierSessionSearchQuery(
            sort_by=sort_by, limit=limit, vendor_id=vendor_id
        )


class CashRegister:
    def __init__(
        self,
        cash_register_id=NotAssigned,
        cash_register_name=NotAssigned,
        start_datetime=NotAssigned,
        end_datetime=NotAssigned,
        vendor_id=NotAssigned,
    ):
        self.cash_register_id = cash_register_id
        self.cash_register_name = cash_register_name
        self.start_datetime = start_datetime
        self.end_datetime = end_datetime
        self.vendor_id = vendor_id

    @staticmethod
    def search(cash_register_search_query):
        from thsc.crs.executor_service import cashier_executor_service

        cash_registers = cashier_executor_service.search_cash_register(
            cash_register_search_query
        )
        return cash_registers


class CashRegisterSearchResponse(object):
    def __init__(self, cash_registers=NotAssigned):
        self.cash_registers = cash_registers


class CashierSessionSearchResponse(object):
    def __init__(self, cashier_sessions=NotAssigned):
        self.cashier_sessions = cashier_sessions


class CashierSessionDetailsResponse(object):
    def __init__(self, cashier_sessions=NotAssigned, payments=NotAssigned):
        self.cashier_session = cashier_sessions
        self.payments = payments
