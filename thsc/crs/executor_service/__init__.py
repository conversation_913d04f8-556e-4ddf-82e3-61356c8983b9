from thsc.crs.executor_service.billing_executor_service import BillingExecutorService
from thsc.crs.executor_service.booking_executor_service import BookingExecutorService
from thsc.crs.executor_service.cashier_executor_service import CashierExecutorService
from thsc.crs.executor_service.hotel_executor_service import HotelExecutorService
from thsc.crs.executor_service.inventory_executor_service import (
    InventoryExecutorService,
)
from thsc.crs.executor_service.rate_plan_executor_service import RatePlanExecutorService
from thsc.crs.request_executor import RequestExecutor

executor = RequestExecutor()
booking_executor_service = BookingExecutorService(executor)
billing_executor_service = BillingExecutorService(executor)
hotel_executor_service = HotelExecutorService(executor)
inventory_executor_service = InventoryExecutorService(executor)
cashier_executor_service = CashierExecutorService(executor)
rate_plan_executor_service = RatePlanExecutorService(executor)
