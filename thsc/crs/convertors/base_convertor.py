import logging

from ths_common.exceptions import ValidationException
from ths_common.value_objects import NotAssigned
from thsc.crs.errors import THSCErrors
from thsc.crs.exceptions import THSCException

logger = logging.getLogger('thsc.log')


def get_attr_dict(data):
    """
    Utility function to retrieve __dict__ values or deriving the same from __slots__, for python classes,
    which has defined __slots__.

    For python classes with __slots__, __dict__ attribute is not defined, which might result in failure, if directly
    accessing __dict__ everywhere

    :param data:
    :return:
    """
    if hasattr(data, '__slots__'):
        attr_dict = {
            attr: getattr(data, attr) for attr in data.__slots__ if hasattr(data, attr)
        }
    else:
        attr_dict = data.__dict__
    return attr_dict


class BaseConvertor(object):
    convertor = {}
    reverse_convertor = {}
    object_class = None

    def to_dict(self, data, many=False):
        try:
            if not many:
                assert type(data) == self.object_class
                response_dict = self.__convertor(data)
                return self._transform(response_dict, data)
            else:
                for data_item in data:
                    assert type(data_item) == self.object_class
                return [
                    self._transform(self.__convertor(data_item), data_item)
                    for data_item in data
                ]
        except AssertionError as ae:
            raise ValidationException(
                THSCErrors.API_REQUEST_PAYLOAD_DATA_ERROR,
                description=str(data)
                + "of type "
                + str(type(data))
                + " should be of type "
                + str(self.object_class),
            )
        except Exception as e:
            attr_dict = get_attr_dict(data)
            logger.exception(
                "Error while converting to dict for data={0} using class={1}".format(
                    attr_dict, self.__class__.__name__
                )
            )
            raise THSCException(
                THSCErrors.API_REQUEST_DATA_CONVERSION_FAILED,
                description="Error while converting to dict for data={0} "
                "using class={1} with error={2}".format(
                    attr_dict, self.__class__.__name__, e.__str__()
                ),
            )

    def __convertor(self, data):
        response_dict = dict()
        attr_dict = get_attr_dict(data)

        for attribute, value in attr_dict.items():
            field_convertor = self.convertor.get(attribute, None)
            if not field_convertor or value == NotAssigned:
                continue

            convertor_class = field_convertor[0]
            attr = field_convertor[1]

            if convertor_class and value is not None:
                if isinstance(convertor_class(), BaseScalarConvertor):
                    if type(value) != list:
                        response_dict[attr] = convertor_class().serialize(value)
                    else:
                        response_dict[attr] = [
                            convertor_class().deserialize(x) for x in value
                        ]
                else:
                    if type(value) != list:
                        response_dict[attr] = convertor_class().to_dict(value)
                    else:
                        response_dict[attr] = [
                            convertor_class().to_dict(x) for x in value
                        ]
            else:
                response_dict[attr] = value
        return response_dict

    def _transform(self, request, data):
        return request

    def from_dict(self, data_dict, many=False):
        try:
            if not many:
                response_object = self.__reverse_convertor(data_dict)
                self._remove_na(response_object)
                return self._reverse_transform(response_object, data_dict)
            return [self.from_dict(x) for x in data_dict]
        except Exception as e:
            logger.exception(
                "Error while reverse converting dict to entity for data={0} using class={1}".format(
                    data_dict, self.__class__.__name__
                )
            )
            raise THSCException(
                THSCErrors.API_RESPONSE_DATA_CONVERSION_FAILED,
                description="Error while reverse converting dict "
                "to entity for data={0} using class={1}".format(
                    data_dict, self.__class__.__name__, e.__str__()
                ),
            )

    def __reverse_convertor(self, data):
        logger.debug(
            "Converting data={0} using class={1}".format(data, self.__class__.__name__)
        )
        if hasattr(self.object_class, 'create_empty_instance'):
            response_object = self.object_class.create_empty_instance()
        else:
            response_object = self.object_class()

        for key, value in data.items():
            field_convertor = self.reverse_convertor.get(key, None)
            if not field_convertor:
                continue

            convertor_class = field_convertor[0]
            attr = field_convertor[1]

            if convertor_class and value is not None:
                if isinstance(convertor_class(), BaseScalarConvertor):
                    if type(value) != list:
                        setattr(
                            response_object, attr, convertor_class().deserialize(value)
                        )
                    else:
                        setattr(
                            response_object,
                            attr,
                            [convertor_class().deserialize(x) for x in value],
                        )
                else:
                    if type(value) != list:
                        setattr(
                            response_object, attr, convertor_class().from_dict(value)
                        )
                    else:
                        setattr(
                            response_object,
                            attr,
                            [convertor_class().from_dict(x) for x in value],
                        )
            else:
                setattr(response_object, attr, value)
        return response_object

    def _reverse_transform(self, response_object, data_dict):
        return response_object

    def _remove_na(self, response_object):
        attr_dict = get_attr_dict(response_object)
        for attribute, value in attr_dict.items():
            if value == NotAssigned:
                setattr(response_object, attribute, None)


class BaseScalarConvertor(BaseConvertor):
    def serialize(self, value):
        pass

    def deserialize(self, value):
        pass
