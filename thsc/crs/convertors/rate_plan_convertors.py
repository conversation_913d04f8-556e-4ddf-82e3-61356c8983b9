from thsc.crs.convertors.base_convertor import BaseConvertor
from thsc.crs.entities.rate_plan import (
    BookingRatePlans,
    CancellationPolicy,
    ChildPolicy,
    CommissionDetail,
    InclusionFrequency,
    InclusionOffering,
    PackageInclusion,
    PaymentPolicy,
    RatePlan,
    RatePlanPackage,
    RatePlanPolicy,
    RatePlanRestriction,
)


class InclusionFrequencyConvertor(BaseConvertor):
    reverse_convertor = {
        "count": (None, "count"),
        "day_of_serving": (None, "day_of_serving"),
        "frequency_type": (None, "frequency_type"),
    }

    object_class = InclusionFrequency


class InclusionOfferingConvertor(BaseConvertor):
    reverse_convertor = {
        "offered_quantity": (None, "offered_quantity"),
        "offering_type": (None, "offering_type"),
    }

    object_class = InclusionOffering


class PackageInclusionConvertor(BaseConvertor):
    reverse_convertor = {
        "frequency": (InclusionFrequencyConvertor, "frequency"),
        "name": (None, "name"),
        "offering": (InclusionOfferingConvertor, "offering"),
        "sku_id": (None, "sku_id"),
    }

    object_class = PackageInclusion


class CommissionDetailConvertor(BaseConvertor):
    reverse_convertor = {
        "commission_percent": (None, "commission_percent"),
        "commission_type": (None, "commission_type"),
    }

    object_class = CommissionDetail


class RatePlanPackageConvertor(BaseConvertor):
    reverse_convertor = {
        "inclusions": (PackageInclusionConvertor, "inclusions"),
        "package_id": (None, "package_id"),
        "package_name": (None, "package_name"),
    }

    object_class = RatePlanPackage


class CancellationPolicyConvertor(BaseConvertor):
    reverse_convertor = {
        "cancellation_charge_unit": (None, "cancellation_charge_unit"),
        "cancellation_charge_value": (None, "cancellation_charge_value"),
        "cancellation_duration_before_checkin_end": (
            None,
            "cancellation_duration_before_checkin_end",
        ),
        "cancellation_duration_before_checkin_start": (
            None,
            "cancellation_duration_before_checkin_start",
        ),
    }

    object_class = CancellationPolicy


class ChildPolicyConvertor(BaseConvertor):
    reverse_convertor = {
        "charge_per_child": (None, "charge_per_child"),
        "child_allowed": (None, "child_allowed"),
        "unit_of_charge": (None, "unit_of_charge"),
    }

    object_class = ChildPolicy


class PaymentPolicyConvertor(BaseConvertor):
    reverse_convertor = {
        "advance_payment_percentage": (None, "advance_payment_percentage"),
        "days_before_checkin_to_make_payment": (
            None,
            "days_before_checkin_to_make_payment",
        ),
        "occupancy_percentage": (None, "occupancy_percentage"),
        "unit_of_payment_percentage": (None, "unit_of_payment_percentage"),
    }

    object_class = PaymentPolicy


class RatePlanPolicyConvertor(BaseConvertor):
    reverse_convertor = {
        "cancellation_policies": (CancellationPolicyConvertor, "cancellation_policies"),
        "child_policy": (ChildPolicyConvertor, "child_policy"),
        "payment_policies": (PaymentPolicyConvertor, "payment_policies"),
    }

    object_class = RatePlanPolicy


class RatePlanRestrictionConvertor(BaseConvertor):
    reverse_convertor = {
        "maximum_los": (None, "maximum_los"),
        "minimum_abw": (None, "minimum_abw"),
        "minimum_los": (None, "minimum_los"),
    }

    object_class = RatePlanRestriction


class RatePlanConvertor(BaseConvertor):
    reverse_convertor = {
        "commission_details": (CommissionDetailConvertor, "commission_details"),
        "is_flexi": (None, "is_flexi"),
        "name": (None, "name"),
        "package": (RatePlanPackageConvertor, "package"),
        "policies": (RatePlanPolicyConvertor, "policies"),
        "print_rate": (None, "print_rate"),
        "rate_plan_code": (None, "rate_plan_code"),
        "rate_plan_id": (None, "rate_plan_id"),
        "rate_plan_reference_id": (None, "rate_plan_reference_id"),
        "restrictions": (RatePlanRestrictionConvertor, "restrictions"),
        "suppress_rate": (None, "suppress_rate"),
    }

    object_class = RatePlan


class BookingRatePlanResponseConvertor(BaseConvertor):
    reverse_convertor = {
        "rate_plans": (RatePlanConvertor, "rate_plans"),
    }

    object_class = BookingRatePlans
