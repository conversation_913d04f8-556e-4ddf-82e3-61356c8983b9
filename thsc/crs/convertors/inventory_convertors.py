from thsc.crs.convertors.base_convertor import BaseConvertor
from thsc.crs.convertors.scalar_type_convertors import DateConvertor, DateTimeConvertor
from thsc.crs.entities.inventory.dnr import DNR, DNRSearchQuery
from thsc.crs.entities.inventory.inventory import (
    RoomTypeInventoryAvailability,
    RoomTypeInventoryAvailabilityGetQuery,
)


class RoomTypeInventoryAvailabilityGetQueryConvertor(BaseConvertor):
    object_class = RoomTypeInventoryAvailabilityGetQuery
    convertor = {
        'from_date': (DateConvertor, 'from_date'),
        'to_date': (DateConvertor, 'to_date'),
        'room_type_id': (None, 'room_type_id'),
    }


class RoomTypeInventoryAvailabilityConvertor(BaseConvertor):
    object_class = RoomTypeInventoryAvailability
    convertor = {
        'room_type_id': (None, 'room_type_id'),
        'date': (None, 'date'),
        'actual_count': (None, 'actual_count'),
    }
    reverse_convertor = convertor


class DNRConvertor(BaseConvertor):
    object_class = DNR
    convertor = {
        "assigned_by": (None, "assigned_by"),
        "comments": (None, "comments"),
        "from_date": (DateConvertor, "from_date"),
        "room_id": (None, "room_id"),
        "source": (None, "source"),
        "subtype": (None, "subtype"),
        "to_date": (DateConvertor, "to_date"),
        "type": (None, "type"),
    }
    reverse_convertor = {
        "assigned_by": (None, "assigned_by"),
        "comments": (None, "comments"),
        "from_date": (DateConvertor, "from_date"),
        "hotel_id": (None, "hotel_id"),
        "room_id": (None, "room_id"),
        "source": (None, "source"),
        "subtype": (None, "subtype"),
        "to_date": (DateConvertor, "to_date"),
        "type": (None, "type"),
        "dnr_id": (None, "dnr_id"),
        "date_inactivated": (DateTimeConvertor, "date_inactivated"),
        "version": (None, "version"),
        "status": (None, "status"),
    }


class UpdateDNRConvertor(DNRConvertor):
    convertor = {
        "comments": (None, "comments"),
        "from_date": (DateConvertor, "from_date"),
        "subtype": (None, "subtype"),
        "to_date": (DateConvertor, "to_date"),
        "type": (None, "type"),
    }
    reverse_convertor = {
        "assigned_by": (None, "assigned_by"),
        "comments": (None, "comments"),
        "from_date": (DateConvertor, "from_date"),
        "hotel_id": (None, "hotel_id"),
        "room_id": (None, "room_id"),
        "source": (None, "source"),
        "subtype": (None, "subtype"),
        "to_date": (DateConvertor, "to_date"),
        "type": (None, "type"),
        "dnr_id": (None, "dnr_id"),
        "date_inactivated": (DateTimeConvertor, "date_inactivated"),
        "version": (None, "version"),
        "status": (None, "status"),
    }


class DNRSearchQueryConvertor(BaseConvertor):
    object_class = DNRSearchQuery
    convertor = {
        "hotel_id": (None, "hotel_id"),
        "from_date": (DateConvertor, "from_date"),
        "to_date": (DateConvertor, "to_date"),
        "source": (None, "source"),
    }
