import random
import string

import factory
from treebo_commons.money import Money

from ths_common.constants.billing_constants import ChargeBillToTypes, ChargeTypes
from ths_common.constants.booking_constants import (
    AddonRelativeDate,
    AgeGroup,
    AttachmentFileType,
    AttachmentGroup,
    BookingStatus,
    ProfileTypes,
)
from ths_common.value_objects import Address, BookingSource, GSTDetails, NotAssigned
from thsc.crs.entities.booking import AddOn, Attachment, Booking, GuestStay, Price, Room
from thsc.crs.entities.customer import Customer
from thsc.tests.test_config import TEST_HOTEL_ID
from thsc.tests.utils import today_plus_days


class SourceFactory(factory.Factory):
    channel_code = "b2b"
    subchannel_code = "athena"
    application_code = "12345"

    class Meta:
        model = BookingSource


class AddressFactory(factory.Factory):
    country = "Westeros"
    state = "Crownlands"
    field_1 = "field 1"
    field_2 = "field 2"
    city = "city"
    pincode = "420024"

    class Meta:
        model = Address


class PriceFactory(factory.Factory):
    pretax_amount = None
    posttax_amount = Money("11 INR")
    applicable_date = today_plus_days(days=1)
    bill_to_type = ChargeBillToTypes.COMPANY
    type = ChargeTypes.CREDIT

    class Meta:
        model = Price


class GSTDetailsFactory(factory.Factory):
    legal_name = "Tyrion Lannister"
    gstin_num = "GSTIN1234"
    address = factory.SubFactory(AddressFactory)

    class Meta:
        model = GSTDetails


class CustomerFactory(factory.Factory):
    gst_details = factory.SubFactory(GSTDetailsFactory)
    address = factory.SubFactory(AddressFactory)
    email = "<EMAIL>"
    first_name = "Tyrion"
    last_name = "Lannister"
    phone_number = "1234567890"
    profile_type = ProfileTypes.INDIVIDUAL
    reference_id = "TRL"
    country_code = "WR"

    class Meta:
        model = Customer


class GuestStayFactory(factory.Factory):
    age_group = AgeGroup.ADULT
    guest = factory.SubFactory(CustomerFactory)

    class Meta:
        model = GuestStay


class RoomFactory(factory.Factory):
    prices = factory.List([factory.SubFactory(PriceFactory)])
    checkin_date = today_plus_days(days=1)
    checkout_date = today_plus_days(days=2)
    room_type_id = 'RT01'
    guests = factory.List([factory.SubFactory(GuestStayFactory)])

    # actual_checkin_date = NotAssigned
    # actual_checkout_date = NotAssigned
    # booking_id = NotAssigned
    # type = RoomStays.NIGHT
    # adults = NotAssigned
    # child = NotAssigned
    # id = NotAssigned
    # status = NotAssigned
    # room_no = NotAssigned

    # booking_version_id = NotAssigned
    # charge_ids = NotAssigned

    class Meta:
        model = Room


def id_generator(size=14, chars=string.ascii_uppercase + string.digits):
    return ''.join(random.choice(chars) for _ in range(size))


class BookingFactory(factory.Factory):
    hotel_id = TEST_HOTEL_ID
    booking_owner = factory.SubFactory(CustomerFactory)
    reference_number = "WLK-{0}".format(id_generator())
    hold_till = NotAssigned
    rooms = list()
    status = BookingStatus.CONFIRMED
    comments = "Some test booking"
    source = factory.SubFactory(SourceFactory)

    class Meta:
        model = Booking


class AddonFactory(factory.Factory):
    start_relative = AddonRelativeDate.CHECKIN
    end_relative = AddonRelativeDate.CHECKOUT
    name = "Some addon"
    expense_item_id = "lunch"
    posttax_price = Money("10 INR")
    pretax_price = NotAssigned
    tax = NotAssigned
    quantity = 1
    room_stay_id = 1
    charge_type = ChargeTypes.CREDIT
    bill_to_type = ChargeBillToTypes.COMPANY
    linked = False

    class Meta:
        model = AddOn


class AttachmentFactory(factory.Factory):
    attachment_group = AttachmentGroup.ID_PROOF
    display_name = "Test Attachment"
    file_type = AttachmentFileType.PDF
    original_url = "url.to.s3"

    class Meta:
        model = Attachment
