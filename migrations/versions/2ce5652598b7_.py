"""empty message

Revision ID: 2ce5652598b7
Revises: 36737e980c7a
Create Date: 2018-07-13 15:58:38.476556

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '2ce5652598b7'
down_revision = '36737e980c7a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('customer')
    op.drop_index('idx_booking_guests', table_name='booking')
    op.drop_index('ix_booking_guests', table_name='booking')
    op.drop_column('booking', 'guests')
    op.alter_column('invoice', 'invoice_date',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.Date(),
               existing_nullable=False)
    op.alter_column('invoice', 'invoice_due_date',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.Date(),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('invoice', 'invoice_due_date',
               existing_type=sa.Date(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=False)
    op.alter_column('invoice', 'invoice_date',
               existing_type=sa.Date(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=False)
    op.add_column('booking', sa.Column('guests', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True))
    op.create_index('ix_booking_guests', 'booking', ['guests'], unique=False)
    op.create_index('idx_booking_guests', 'booking', ['guests'], unique=False)
    op.create_table('customer',
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('modified_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('deleted', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('customer_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('external_ref_id', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('profile_type', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('first_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('last_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('gender', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('age', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('nationality', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('addr_field1', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('addr_field2', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('addr_city', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('addr_state', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('addr_country', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('pincode', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('country_code', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('phone', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('email', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('image_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('id_proof_type', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('id_kyc_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('id_number', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('legal_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('gstin_num', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('gst_addr_city', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('gst_addr_country', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('gst_addr_field1', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('gst_addr_field2', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('gst_addr_state', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('gst_pincode', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('id_proof_country_code', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('customer_id', name='customer_pkey')
    )
    # ### end Alembic commands ###
