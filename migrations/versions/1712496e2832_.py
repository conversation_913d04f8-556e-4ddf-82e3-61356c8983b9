"""empty message

Revision ID: 1712496e2832
Revises: 3f1860b4f0a9
Create Date: 2018-05-23 21:49:16.554038

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '1712496e2832'
down_revision = '3f1860b4f0a9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('booking', sa.Column('cancellation_datetime', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('booking', 'cancellation_datetime')
    # ### end Alembic commands ###
