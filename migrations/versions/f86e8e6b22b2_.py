"""empty message

Revision ID: f86e8e6b22b2
Revises: 8803fcb4f67c
Create Date: 2019-06-11 11:54:53.683641

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'f86e8e6b22b2'
down_revision = '8803fcb4f67c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('payment_split', sa.Column('payment_mode', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('payment_split', 'payment_mode')
    # ### end Alembic commands ###
