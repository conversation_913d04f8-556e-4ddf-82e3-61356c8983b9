"""empty message

Revision ID: 1c97d2f4f600
Revises: 889174805c2f
Create Date: 2020-03-13 15:13:51.040074

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '1c97d2f4f600'
down_revision = '889174805c2f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('credit_note', sa.Column('irn', sa.String(), nullable=True))
    op.add_column('credit_note', sa.Column('irp_ack_date', sa.Date(), nullable=True))
    op.add_column(
        'credit_note', sa.Column('irp_ack_number', sa.String(), nullable=True)
    )
    op.add_column(
        'credit_note',
        sa.Column('is_einvoice', sa.<PERSON>(), server_default='f', nullable=True),
    )
    op.add_column('credit_note', sa.Column('qr_code', sa.String(), nullable=True))
    op.add_column(
        'credit_note', sa.Column('signed_invoice', sa.String(), nullable=True)
    )
    op.add_column(
        'invoice',
        sa.Column('is_einvoice', sa.Boolean(), server_default='f', nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('invoice', 'is_einvoice')
    op.drop_column('credit_note', 'signed_invoice')
    op.drop_column('credit_note', 'qr_code')
    op.drop_column('credit_note', 'is_einvoice')
    op.drop_column('credit_note', 'irp_ack_number')
    op.drop_column('credit_note', 'irp_ack_date')
    op.drop_column('credit_note', 'irn')
    # ### end Alembic commands ###
