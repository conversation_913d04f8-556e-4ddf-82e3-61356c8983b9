"""empty message

Revision ID: a8169d3596bc
Revises: 2368e0fa360b
Create Date: 2018-09-22 13:20:42.799989

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'a8169d3596bc'
down_revision = '2368e0fa360b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('invoice', sa.Column('allowed_charge_to_ids', sa.ARRAY(sa.String()), nullable=True))
    op.drop_column('invoice', 'charge_to_ids')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('invoice', sa.Column('charge_to_ids', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True))
    op.drop_column('invoice', 'allowed_charge_to_ids')
    # ### end Alembic commands ###
