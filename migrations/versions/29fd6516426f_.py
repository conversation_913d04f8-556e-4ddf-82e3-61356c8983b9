"""This migration is a type change on a postgres array and is hand written.
<PERSON>embic could not detect the type change automatically

Revision ID: 29fd6516426f
Revises: 19fd6516426f
Create Date: 2018-06-21 16:49:30.800299

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '29fd6516426f'
down_revision = '19fd6516426f'
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column('addon', 'expenses',
                    existing_type=sa.ARRAY(sa.String()),
                    type_=sa.ARRAY(sa.Integer()),
                    existing_nullable=True,
                    postgresql_using='expenses::integer[]')


def downgrade():
    op.alter_column('addon', 'expenses',
                    existing_type=sa.ARRAY(sa.Integer()),
                    type_=sa.ARRAY(sa.String()),
                    existing_nullable=True)
