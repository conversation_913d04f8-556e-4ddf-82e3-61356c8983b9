"""empty message

Revision ID: 7a99fb4e0a92
Revises: 7d38d620800b
Create Date: 2020-05-28 17:20:45.183748

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '7a99fb4e0a92'
down_revision = '7d38d620800b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'booking_attachment',
        sa.Column(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('deleted', sa.Boolean(), nullable=True),
        sa.Column('attachment_id', sa.String(), nullable=False),
        sa.Column('booking_id', sa.String(), nullable=False),
        sa.Column('signed_url', sa.String(), nullable=False),
        sa.Column('display_name', sa.String(), nullable=False),
        sa.Column('file_type', sa.String(), nullable=False),
        sa.Column('attachment_group', sa.String(), nullable=False),
        sa.Column('expiry', sa.Date, nullable=False),
        sa.Column('source', sa.String(), nullable=False),
        sa.Column('uploaded_by', sa.String(), nullable=False),
        sa.PrimaryKeyConstraint('attachment_id'),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('booking_attachment')
    # ### end Alembic commands ###
