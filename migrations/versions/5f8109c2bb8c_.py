"""empty message

Revision ID: 5f8109c2bb8c
Revises: 82a37323e5ad
Create Date: 2019-08-06 12:55:59.973460

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '5f8109c2bb8c'
down_revision = '82a37323e5ad'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('room_inventory', 'current_status',
               existing_type=sa.VARCHAR(),
               nullable=True,
               existing_server_default=sa.text("'vacant'::character varying"))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('room_inventory', 'current_status',
               existing_type=sa.VARCHAR(),
               nullable=False,
               existing_server_default=sa.text("'vacant'::character varying"))
    # ### end Alembic commands ###
