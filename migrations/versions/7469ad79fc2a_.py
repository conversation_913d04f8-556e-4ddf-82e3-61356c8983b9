"""empty message

Revision ID: 7469ad79fc2a
Revises: 40eb0b7d4343
Create Date: 2019-01-22 14:52:45.096089

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '7469ad79fc2a'
down_revision = '40eb0b7d4343'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('dnr_audit_trail',
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('audit_id', sa.String(), nullable=False),
    sa.Column('user', sa.String(), nullable=True),
    sa.Column('user_type', sa.String(), nullable=True),
    sa.Column('application', sa.String(), nullable=True),
    sa.Column('request_id', sa.String(), nullable=True),
    sa.Column('dnr_id', sa.String(), nullable=True),
    sa.Column('audit_type', sa.String(), nullable=True),
    sa.Column('audit_payload', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('audit_id')
    )
    op.create_index(op.f('ix_dnr_audit_trail_dnr_id'), 'dnr_audit_trail', ['dnr_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_dnr_audit_trail_dnr_id'), table_name='dnr_audit_trail')
    op.drop_table('dnr_audit_trail')
    # ### end Alembic commands ###
