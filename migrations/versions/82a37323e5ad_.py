"""empty message

Revision ID: 82a37323e5ad
Revises: 76ceb0bcbd35
Create Date: 2019-07-23 14:47:13.808769

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '82a37323e5ad'
down_revision = '76ceb0bcbd35'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('booking_action', sa.Column('previous_state', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('booking_action', 'previous_state')
    # ### end Alembic commands ###
