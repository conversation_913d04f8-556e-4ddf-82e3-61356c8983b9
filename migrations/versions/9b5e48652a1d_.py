"""empty message

Revision ID: 9b5e48652a1d
Revises: 5a2db76bc292
Create Date: 2018-10-31 15:06:56.466952

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '9b5e48652a1d'
down_revision = '5a2db76bc292'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_status_not_published', 'integration_event', ['status'], unique=False, postgresql_where=sa.text("status != 'published'"))
    op.create_index('ordering_generated_at_asc', 'integration_event', [sa.text('generated_at ASC')], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ordering_generated_at_asc', table_name='integration_event')
    op.drop_index('idx_status_not_published', table_name='integration_event')
    # ### end Alembic commands ###
