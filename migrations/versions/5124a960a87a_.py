"""empty message

Revision ID: 5124a960a87a
Revises: 36b76a946af5
Create Date: 2019-12-10 17:24:05.393087

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '5124a960a87a'
down_revision = '36b76a946af5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'invoice_charges', sa.Column('item_hsn_code', sa.String(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('invoice_charges', 'item_hsn_code')
    # ### end Alembic commands ###
