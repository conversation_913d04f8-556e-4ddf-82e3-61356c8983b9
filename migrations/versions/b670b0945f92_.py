"""empty message

Revision ID: b670b0945f92
Revises: de564286b257
Create Date: 2020-06-08 17:34:26.019651

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b670b0945f92'
down_revision = 'de564286b257'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'credit_note_line_item', sa.Column('charge_id', sa.Integer(), nullable=True)
    )
    op.add_column(
        'charge_split', sa.Column('credit_note_id', sa.String(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('credit_note_line_item', 'charge_id')
    op.drop_column('charge_split', 'credit_note_id')
    # ### end Alembic commands ###
