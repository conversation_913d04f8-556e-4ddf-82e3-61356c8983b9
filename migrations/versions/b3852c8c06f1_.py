"""empty message

Revision ID: b3852c8c06f1
Revises: 7d925af7c422
Create Date: 2018-08-24 12:23:30.964216

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b3852c8c06f1'
down_revision = '7d925af7c422'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('addon', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('addon', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('addon', 'modified_at_')
    op.drop_column('addon', 'created_at_')
    op.add_column('bill', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('bill', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('bill', 'modified_at_')
    op.drop_column('bill', 'created_at_')
    op.add_column('booking', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('booking', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('booking', 'modified_at_')
    op.drop_column('booking', 'created_at_')
    op.add_column('booking_action', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('booking_action', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('booking_action', 'modified_at_')
    op.drop_column('booking_action', 'created_at_')
    op.add_column('booking_customer', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('booking_customer', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('booking_customer', 'modified_at_')
    op.drop_column('booking_customer', 'created_at_')
    op.add_column('booking_invoice_group', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('booking_invoice_group', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('booking_invoice_group', 'modified_at_')
    op.drop_column('booking_invoice_group', 'created_at_')
    op.add_column('charge', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('charge', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('charge', 'modified_at_')
    op.drop_column('charge', 'created_at_')
    op.add_column('charge_split', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('charge_split', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('charge_split', 'modified_at_')
    op.drop_column('charge_split', 'created_at_')
    op.add_column('dnr', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('dnr', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('dnr', 'modified_at_')
    op.drop_column('dnr', 'created_at_')
    op.add_column('expense', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('expense', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('expense', 'modified_at_')
    op.drop_column('expense', 'created_at_')
    op.add_column('expense_item', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('expense_item', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('expense_item', 'modified_at_')
    op.drop_column('expense_item', 'created_at_')
    op.add_column('guest_allocation', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('guest_allocation', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('guest_allocation', 'modified_at_')
    op.drop_column('guest_allocation', 'created_at_')
    op.add_column('guest_stay', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('guest_stay', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('guest_stay', 'modified_at_')
    op.drop_column('guest_stay', 'created_at_')
    op.add_column('hotel', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('hotel', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('hotel', 'modified_at_')
    op.drop_column('hotel', 'created_at_')
    op.add_column('hotel_config', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('hotel_config', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('hotel_config', 'modified_at_')
    op.drop_column('hotel_config', 'created_at_')
    op.add_column('hotel_room_type_config', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('hotel_room_type_config', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('hotel_room_type_config', 'modified_at_')
    op.drop_column('hotel_room_type_config', 'created_at_')
    op.add_column('integration_event', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('integration_event', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('integration_event', 'modified_at_')
    op.drop_column('integration_event', 'created_at_')
    op.add_column('invoice', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('invoice', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('invoice', 'modified_at_')
    op.drop_column('invoice', 'created_at_')
    op.add_column('invoice_charges', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('invoice_charges', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('invoice_charges', 'modified_at_')
    op.drop_column('invoice_charges', 'created_at_')
    op.add_column('invoice_number_count', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('invoice_number_count', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('invoice_number_count', 'modified_at_')
    op.drop_column('invoice_number_count', 'created_at_')
    op.add_column('job', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('job', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('job', 'modified_at_')
    op.drop_column('job', 'created_at_')
    op.add_column('payment', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('payment', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('payment', 'modified_at_')
    op.drop_column('payment', 'created_at_')
    op.add_column('room', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('room', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('room', 'modified_at_')
    op.drop_column('room', 'created_at_')
    op.add_column('room_allocation', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('room_allocation', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('room_allocation', 'modified_at_')
    op.drop_column('room_allocation', 'created_at_')
    op.add_column('room_inventory_availability', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('room_inventory_availability', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('room_inventory_availability', 'modified_at_')
    op.drop_column('room_inventory_availability', 'created_at_')
    op.add_column('room_inventory_current_status', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('room_inventory_current_status', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('room_inventory_current_status', 'modified_at_')
    op.drop_column('room_inventory_current_status', 'created_at_')
    op.add_column('room_stay', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('room_stay', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('room_stay', 'modified_at_')
    op.drop_column('room_stay', 'created_at_')
    op.add_column('room_type', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('room_type', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('room_type', 'modified_at_')
    op.drop_column('room_type', 'created_at_')
    op.add_column('room_type_inventory_availability', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('room_type_inventory_availability', sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.drop_column('room_type_inventory_availability', 'modified_at_')
    op.drop_column('room_type_inventory_availability', 'created_at_')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('room_type_inventory_availability', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('room_type_inventory_availability', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('room_type_inventory_availability', 'modified_at')
    op.drop_column('room_type_inventory_availability', 'created_at')
    op.add_column('room_type', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('room_type', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('room_type', 'modified_at')
    op.drop_column('room_type', 'created_at')
    op.add_column('room_stay', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('room_stay', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('room_stay', 'modified_at')
    op.drop_column('room_stay', 'created_at')
    op.add_column('room_inventory_current_status', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('room_inventory_current_status', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('room_inventory_current_status', 'modified_at')
    op.drop_column('room_inventory_current_status', 'created_at')
    op.add_column('room_inventory_availability', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('room_inventory_availability', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('room_inventory_availability', 'modified_at')
    op.drop_column('room_inventory_availability', 'created_at')
    op.add_column('room_allocation', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('room_allocation', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('room_allocation', 'modified_at')
    op.drop_column('room_allocation', 'created_at')
    op.add_column('room', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('room', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('room', 'modified_at')
    op.drop_column('room', 'created_at')
    op.add_column('payment', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('payment', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('payment', 'modified_at')
    op.drop_column('payment', 'created_at')
    op.add_column('job', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('job', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('job', 'modified_at')
    op.drop_column('job', 'created_at')
    op.add_column('invoice_number_count', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('invoice_number_count', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('invoice_number_count', 'modified_at')
    op.drop_column('invoice_number_count', 'created_at')
    op.add_column('invoice_charges', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('invoice_charges', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('invoice_charges', 'modified_at')
    op.drop_column('invoice_charges', 'created_at')
    op.add_column('invoice', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('invoice', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('invoice', 'modified_at')
    op.drop_column('invoice', 'created_at')
    op.add_column('integration_event', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('integration_event', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('integration_event', 'modified_at')
    op.drop_column('integration_event', 'created_at')
    op.add_column('hotel_room_type_config', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('hotel_room_type_config', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('hotel_room_type_config', 'modified_at')
    op.drop_column('hotel_room_type_config', 'created_at')
    op.add_column('hotel_config', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('hotel_config', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('hotel_config', 'modified_at')
    op.drop_column('hotel_config', 'created_at')
    op.add_column('hotel', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('hotel', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('hotel', 'modified_at')
    op.drop_column('hotel', 'created_at')
    op.add_column('guest_stay', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('guest_stay', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('guest_stay', 'modified_at')
    op.drop_column('guest_stay', 'created_at')
    op.add_column('guest_allocation', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('guest_allocation', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('guest_allocation', 'modified_at')
    op.drop_column('guest_allocation', 'created_at')
    op.add_column('expense_item', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('expense_item', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('expense_item', 'modified_at')
    op.drop_column('expense_item', 'created_at')
    op.add_column('expense', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('expense', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('expense', 'modified_at')
    op.drop_column('expense', 'created_at')
    op.add_column('dnr', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('dnr', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('dnr', 'modified_at')
    op.drop_column('dnr', 'created_at')
    op.add_column('charge_split', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('charge_split', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('charge_split', 'modified_at')
    op.drop_column('charge_split', 'created_at')
    op.add_column('charge', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('charge', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('charge', 'modified_at')
    op.drop_column('charge', 'created_at')
    op.add_column('booking_invoice_group', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('booking_invoice_group', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('booking_invoice_group', 'modified_at')
    op.drop_column('booking_invoice_group', 'created_at')
    op.add_column('booking_customer', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('booking_customer', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('booking_customer', 'modified_at')
    op.drop_column('booking_customer', 'created_at')
    op.add_column('booking_action', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('booking_action', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('booking_action', 'modified_at')
    op.drop_column('booking_action', 'created_at')
    op.add_column('booking', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('booking', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('booking', 'modified_at')
    op.drop_column('booking', 'created_at')
    op.add_column('bill', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('bill', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('bill', 'modified_at')
    op.drop_column('bill', 'created_at')
    op.add_column('addon', sa.Column('created_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('addon', sa.Column('modified_at_', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_column('addon', 'modified_at')
    op.drop_column('addon', 'created_at')
    # ### end Alembic commands ###
