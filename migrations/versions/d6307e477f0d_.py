"""empty message

Revision ID: d6307e477f0d
Revises: a9e3aff5dc1b
Create Date: 2018-07-08 19:13:37.235120

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'd6307e477f0d'
down_revision = 'a9e3aff5dc1b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('invoice', sa.Column('tax_details_breakup', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('invoice', 'tax_details_breakup')
    # ### end Alembic commands ###
