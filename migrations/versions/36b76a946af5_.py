"""empty message

Revision ID: 36b76a946af5
Revises: a86434b09a17
Create Date: 2019-12-04 17:16:00.097644

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '36b76a946af5'
down_revision = 'a86434b09a17'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('pos_order', 'seller_category_id')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'pos_order',
        sa.Column(
            'seller_category_id', sa.VARCHAR(), autoincrement=False, nullable=False
        ),
    )
    # ### end Alembic commands ###
