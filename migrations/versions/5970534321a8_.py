"""empty message

Revision ID: 5970534321a8
Revises: d4e707acf777
Create Date: 2019-10-22 14:58:34.047601

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '5970534321a8'
down_revision = 'd4e707acf777'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'order_number_seq',
        sa.<PERSON>umn(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('deleted', sa.<PERSON>(), nullable=True),
        sa.Column('sequence_id', sa.Integer(), nullable=False),
        sa.Column('seller_id', sa.String(), nullable=False),
        sa.Column('order_date', sa.Date(), nullable=False),
        sa.Column('last_order_number', sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint('sequence_id'),
        sa.UniqueConstraint('seller_id', 'order_date'),
    )
    op.create_table(
        'pos_order',
        sa.Column(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('order_id', sa.String(), nullable=False),
        sa.Column('order_number', sa.Integer(), nullable=False),
        sa.Column('order_date', sa.Date(), nullable=False),
        sa.Column('order_datetime', sa.DateTime(timezone=True), nullable=False),
        sa.Column('seller_category_id', sa.String(), nullable=False),
        sa.Column('seller_id', sa.String(), nullable=False),
        sa.Column('seller_details', sa.JSON(), nullable=True),
        sa.Column('order_type', sa.String(), nullable=True),
        sa.Column('bill_id', sa.Text(), nullable=False),
        sa.Column('bill_to', sa.Text(), nullable=True),
        sa.Column('settlement_method', sa.String(), nullable=True),
        sa.Column('crs_booking_id', sa.Text(), nullable=True),
        sa.Column('room_number', sa.String(), nullable=True),
        sa.Column('table_number', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=False),
        sa.Column('remarks', sa.Text(), nullable=True),
        sa.Column('version', sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint('order_id'),
    )
    op.create_index(
        op.f('ix_pos_order_crs_booking_id'),
        'pos_order',
        ['crs_booking_id'],
        unique=False,
    )
    op.create_index(
        op.f('ix_pos_order_order_date'), 'pos_order', ['order_date'], unique=False
    )
    op.create_index(
        op.f('ix_pos_order_order_number'), 'pos_order', ['order_number'], unique=False
    )
    op.create_index(
        op.f('ix_pos_order_seller_id'), 'pos_order', ['seller_id'], unique=False
    )
    op.create_index(op.f('ix_pos_order_status'), 'pos_order', ['status'], unique=False)
    op.create_table(
        'pos_order_item',
        sa.Column(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('deleted', sa.Boolean(), nullable=True),
        sa.Column('order_id', sa.String(), nullable=False),
        sa.Column('order_item_id', sa.Integer(), nullable=False),
        sa.Column('sku_id', sa.String(), nullable=True),
        sa.Column('sku_category_code', sa.String(), nullable=False),
        sa.Column('item_name', sa.String(), nullable=True),
        sa.Column('quantity', sa.Integer(), nullable=False),
        sa.Column(
            'unit_price_pretax', sa.DECIMAL(precision=15, scale=4), nullable=True
        ),
        sa.Column(
            'unit_price_posttax', sa.DECIMAL(precision=15, scale=4), nullable=True
        ),
        sa.Column('charge_id', sa.Integer(), nullable=False),
        sa.Column('charge_to', sa.ARRAY(sa.String()), nullable=True),
        sa.Column('bill_to_type', sa.String(), nullable=True),
        sa.Column('charge_type', sa.String(), nullable=True),
        sa.Column('item_detail', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('order_id', 'order_item_id'),
    )

    op.create_table(
        'pos_customer',
        sa.Column(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('deleted', sa.Boolean(), nullable=True),
        sa.Column('order_id', sa.String(), nullable=False),
        sa.Column('customer_id', sa.Integer(), nullable=False),
        sa.Column('first_name', sa.String(), nullable=True),
        sa.Column('last_name', sa.String(), nullable=True),
        sa.Column('country_code', sa.String(), nullable=True),
        sa.Column('phone_number', sa.String(), nullable=True),
        sa.Column('email', sa.String(), nullable=True),
        sa.Column('gstin_num', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('order_id', 'customer_id'),
    )

    op.create_table(
        'seller',
        sa.Column(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('deleted', sa.Boolean(), nullable=True),
        sa.Column('seller_id', sa.String(), nullable=False),
        sa.Column('seller_category_id', sa.Integer(), nullable=True),
        sa.Column('name', sa.String(), nullable=True),
        sa.Column('state_id', sa.Integer(), nullable=True),
        sa.Column('state_name', sa.String(), nullable=True),
        sa.Column('city_id', sa.Integer(), nullable=True),
        sa.Column('city_name', sa.String(), nullable=True),
        sa.Column('pincode', sa.String(), nullable=True),
        sa.Column('legal_name', sa.String(), nullable=True),
        sa.Column('legal_address', sa.String(), nullable=True),
        sa.Column('gstin_num', sa.String(), nullable=True),
        sa.Column('legal_signature', sa.String(), nullable=True),
        sa.Column('legal_city_id', sa.Integer(), nullable=True),
        sa.Column('legal_city_name', sa.String(), nullable=True),
        sa.Column('legal_state_id', sa.Integer(), nullable=True),
        sa.Column('legal_state_name', sa.String(), nullable=True),
        sa.Column('legal_pincode', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('seller_id'),
    )
    op.create_table(
        'seller_category',
        sa.Column(
            'created_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'modified_at',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('deleted', sa.Boolean(), nullable=True),
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name'),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('seller_category')
    op.drop_table('seller')
    op.drop_table('pos_customer')
    op.drop_table('pos_order_item')
    op.drop_index(op.f('ix_pos_order_status'), table_name='pos_order')
    op.drop_index(op.f('ix_pos_order_seller_id'), table_name='pos_order')
    op.drop_index(op.f('ix_pos_order_order_number'), table_name='pos_order')
    op.drop_index(op.f('ix_pos_order_order_date'), table_name='pos_order')
    op.drop_index(op.f('ix_pos_order_deleted'), table_name='pos_order')
    op.drop_index(op.f('ix_pos_order_crs_booking_id'), table_name='pos_order')
    op.drop_table('pos_order')
    op.drop_table('order_number_seq')
    # ### end Alembic commands ###
