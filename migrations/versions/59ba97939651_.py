"""empty message

Revision ID: 59ba97939651
Revises: 88aa5a8238cd
Create Date: 2019-04-05 13:35:44.313098

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '59ba97939651'
down_revision = '88aa5a8238cd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('room_allocation', sa.Column('overridden', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('room_allocation', 'overridden')
    # ### end Alembic commands ###
