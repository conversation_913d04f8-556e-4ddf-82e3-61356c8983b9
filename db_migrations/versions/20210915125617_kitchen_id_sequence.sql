-- revision: '20210915125617_kitchen_id_sequence'
-- down_revision: '20210825192658_add_kitchen_references_order_item_and_kot'

-- upgrade
CREATE TABLE kot_sequence (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    sequence_id integer NOT NULL,
    last_kot_id integer NOT NULL,
    deleted boolean,
    seller_id character varying NOT NULL
);

ALTER TABLE ONLY kot_sequence
    ADD CONSTRAINT kot_seq_pkey PRIMARY KEY (sequence_id);

CREATE SEQUENCE kot_sequence_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE kot_sequence_id_seq OWNED BY kot_sequence.sequence_id;

ALTER TABLE ONLY kot_sequence ALTER COLUMN sequence_id SET DEFAULT nextval('kot_sequence_id_seq'::regclass);

ALTER TABLE pos_customer ADD COLUMN customer_type character varying;

-- downgrade
ALTER TABLE kot_sequence ALTER COLUMN sequence_id DROP DEFAULT;
DROP TABLE kot_sequence;
ALTER TABLE pos_customer DROP COLUMN customer_type;