-- revision: '20211027132625_housekeeping_audit_trail_table'
-- down_revision: '20211027093049_new_field_cancellation_date_in_room_stay'

-- upgrade
CREATE TABLE housekeeping_audit_trail (
	created_at timestamptz NOT NULL DEFAULT now(),
	modified_at timestamptz NOT NULL DEFAULT now(),
	housekeeping_audit_trail_id varchar NOT NULL,
	hotel_id varchar NOT NULL,
	room_id int4 NOT NULL,
	action_performed varchar,
	action_datetime TIMESTAMP WITH TIME ZONE,
	old_housekeeping_status varchar,
	new_housekeeping_status varchar,
	old_room_status varchar,
	new_room_status varchar,
	"user" varchar NOT NULL,
	auth_id varchar,
	application varchar,
	request_id varchar,
	remarks varchar,
	CONSTRAINT housekeeping_audit_trail_pkey PRIMARY KEY (housekeeping_audit_trail_id)
);

CREATE INDEX ix_hk_audit_trail_hotel_room_action ON housekeeping_audit_trail(hotel_id, room_id, action_performed, created_at);

-- downgrade
DROP TABLE housekeeping_audit_trail;
