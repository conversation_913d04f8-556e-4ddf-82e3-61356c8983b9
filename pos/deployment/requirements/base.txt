werkzeug==0.16.0
Flask==1.0
Flask-Admin==1.4.2
SQLAlchemy==1.3.2
Flask-SQLAlchemy==2.1
Flask-Migrate==2.0.2
flasgger==0.8.0
flask-swagger-ui==3.6.0
git-pylint-commit-hook
requests==2.23.0
easyjoblite==0.7.3
transitions==0.6.8
aenum==2.1.2
python-dateutil==2.8.0
click==6.7

aiohttp==3.7.4.post0

# Generating GraphViz dot files
sadisplay==0.4.6
# Convert dot files to png file
graphviz==0.5.2

#Testing
coverage==4.4
pylint==1.8.4
pre-commit==0.12.2
flake8==3.3.0
invoke==0.17.0
pytest==3.0.7
factory-boy==2.10.0

psycopg2==2.7.5
gevent==21.12.0
gunicorn==19.8.0
psycogreen==1.0

# Documentation
Sphinx==1.5.2
sphinxcontrib-httpdomain==1.5.0
sphinx-rtd-theme

logstash-formatter==0.5.16

newrelic==4.20.1.121

#Utils
attrs==17.2.0
deepdiff==3.3.0
apispec==1.3.3
apispec-webframeworks==0.5.2
jsonpickle==0.9.6
simplejson==3.15.0
jsonschema==2.6.0

# Devops
flaskhealthcheck==1.4.3

# Reporting
boto3==1.9.171

# Google's phonenumber library
phonenumbers==8.10.17

# Segment analytics
analytics-python==1.2.3

# Sentry sdk
sentry-sdk[flask]==1.4.3

Jinja2==2.11.3
MarkupSafe==1.1.1
PyYAML>=5.4.1
