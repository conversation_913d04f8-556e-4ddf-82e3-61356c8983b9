from object_registry import register_instance
from pos.infrastructure.database.base_repository import BaseRepository
from pos.infrastructure.database.repositories.order.db_adaptors.kot_adaptor import (
    KOTAdaptor,
)
from pos.infrastructure.database.repositories.order.models import KOTModel, OrderModel
from pos.pos_restaurant.aggregates.order_kot_aggregate import OrderKotAggregate
from pos.pos_restaurant.entities.kot import KOT


@register_instance()
class KotRepository(BaseRepository):
    kot_adaptor = KOTAdaptor()

    def from_aggregate(self, aggregate: OrderKotAggregate = None):
        kots = aggregate.kots
        kot_models = (
            [self.kot_adaptor.to_db_entity(kot) for kot in kots] if kots else []
        )
        return kot_models

    def to_aggregate(self, **kwargs):
        kot_models = kwargs.get('kot_models')
        kots = [
            self.kot_adaptor.to_domain_entity(kot_model) for kot_model in kot_models
        ]
        return OrderKotAggregate(kots)

    def load(self, order_id):
        kot_models = self.query(KOTModel).filter(KOTModel.order_id == order_id).all()
        return self.to_aggregate(kot_models=kot_models)

    def update(self, aggregate: OrderKotAggregate):
        kot_models = self.from_aggregate(aggregate)
        self._update_all(kot_models)
        self.flush_session()
