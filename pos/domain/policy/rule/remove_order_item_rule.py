from pos.domain.order.errors import OrderError
from pos.domain.policy.facts.facts import Facts
from pos.domain.policy.rule.base import BaseRule
from ths_common.exceptions import PolicyAuthException


class RemoveOrderItemRule(BaseRule):
    def allow(self, facts: Facts):
        if facts.is_seller_type_restaurant() and facts.is_order_item_cancellable():
            raise PolicyAuthException(
                error=OrderError.ORDER_ITEMS_SENT_TO_KITCHEN_CANNOT_BE_REMOVED
            )
        return True
