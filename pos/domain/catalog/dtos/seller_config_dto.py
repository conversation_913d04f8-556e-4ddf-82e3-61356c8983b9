class SellerConfigDto:
    def __init__(
        self,
        acceptance_time=None,
        delivery_time=None,
        settlement_time=None,
        priority_multiplier=None,
        order_item_cancellation_time=None,
        generate_kot_only_when_item_is_preparing=None,
    ):
        self.acceptance_time = acceptance_time if acceptance_time else "00:05:00"
        self.delivery_time = delivery_time if delivery_time else "00:05:00"
        self.settlement_time = settlement_time if settlement_time else "00:30:00"
        self.priority_multiplier = priority_multiplier if priority_multiplier else 1.25
        self.order_item_cancellation_time = (
            order_item_cancellation_time if order_item_cancellation_time else "00:05:00"
        )
        self.generate_kot_only_when_item_is_preparing = (
            generate_kot_only_when_item_is_preparing
            if generate_kot_only_when_item_is_preparing
            else False
        )
