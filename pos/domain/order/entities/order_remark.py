class OrderRemark(object):
    def __init__(
        self,
        remark_id,
        order_id,
        order_item_id,
        action,
        remark,
        deleted=False,
        created_at=None,
        modified_at=None,
    ):
        self.remark_id = remark_id
        self.order_id = order_id
        self.order_item_id = order_item_id
        self.action = action
        self.remark = remark
        self.deleted = deleted
        self.created_at = created_at
        self.modified_at = modified_at

    def update_remark(self, remark):
        self.remark = remark
