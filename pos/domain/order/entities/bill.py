from treebo_commons.utils import dateutils

from ths_common.pos.constants.order_constants import PosBillStatus


class SplitBill(object):
    def __init__(
        self,
        bill_id,
        status,
        split_type,
        crs_booking_id=None,
        room_stay_id=None,
        guest_ids=None,
        bill_to=None,
        split_id=None,
        settlement_method=None,
        discounts=None,
        total_price_pretax=None,
        total_price_posttax=None,
        settled_by=None,
        deleted=False,
        bill_number=None,
        settlement_datetime=None,
        extra_information=None,
        created_at=None,
        modified_at=None,
    ):
        self.split_id = split_id
        self.bill_id = bill_id
        self.status = status
        self.split_type = split_type
        self.crs_booking_id = crs_booking_id
        self.room_stay_id = room_stay_id
        self.guest_ids = guest_ids
        self.bill_to = bill_to
        self.settlement_method = settlement_method
        self.deleted = deleted
        self.discounts = discounts if discounts else []
        self.total_price_pretax = total_price_pretax
        self.total_price_posttax = total_price_posttax
        self.settled_by = settled_by
        self.bill_number = bill_number
        self.settlement_datetime = settlement_datetime
        self.extra_information = extra_information
        self.created_at = created_at if created_at else dateutils.current_datetime()
        self.modified_at = modified_at if modified_at else dateutils.current_datetime()

    def settle(self, settlement_method):
        self.status = PosBillStatus.SETTLED
        self.settlement_method = settlement_method

    def update_split_type(self, split_type):
        self.split_type = split_type

    def set_room_booking_details(self, crs_booking_id, room_stay_id, guest_ids):
        self.crs_booking_id = crs_booking_id
        self.room_stay_id = room_stay_id
        self.guest_ids = guest_ids

    def update_bill_to(self, bill_to):
        self.bill_to = bill_to

    def update_settled_by(self, settled_by):
        self.settled_by = settled_by

    def update(self, bill):
        self.status = bill.status
        self.split_type = bill.split_type
        self.crs_booking_id = bill.crs_booking_id
        self.room_stay_id = bill.room_stay_id
        self.guest_ids = bill.guest_ids
        self.bill_to = bill.bill_to
        self.settlement_method = bill.settlement_method
        self.total_price_pretax = bill.total_price_pretax
        self.total_price_posttax = bill.total_price_posttax

    def update_pretax_and_posttax(self, total_price_pretax, total_price_posttax):
        self.total_price_posttax = total_price_posttax
        self.total_price_pretax = total_price_pretax

    def update_bill_id(self, bill_id):
        self.bill_id = bill_id

    def get_discount(self, order_item_id):
        for discount in self.discounts:
            if discount.order_item_id == order_item_id:
                return discount

    def update_discounts(self, order_item, discounts=None):
        for discount in self.discounts:
            if discount.order_item_id == order_item.order_item_id:
                if discounts:
                    discount.discounts = discounts
                else:
                    discount.discounts = order_item.discounts

    def remove_discount(self, order_item):
        for discount in self.discounts:
            if discount.order_item_id == order_item.order_item_id:
                self.discounts.remove(discount)

    def update_settlement_datetime(self, settlement_datetime):
        self.settlement_datetime = settlement_datetime

    def update_extra_information(self, extra_information):
        self.extra_information = extra_information
