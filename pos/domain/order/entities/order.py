from datetime import datetime

from treebo_commons.utils import dateutils

from pos.domain.order.errors import OrderError
from pos.domain.order.value_objects.seller import SellerVO
from ths_common.exceptions import ValidationException
from ths_common.pos.constants.order_constants import PosOrderPriorities, PosOrderStatus


class Order(object):
    def __init__(
        self,
        order_id,
        order_number,
        order_datetime,
        seller_vo: SellerVO,
        order_type,
        seller_type,
        status,
        settlement_method,
        total_price_pretax,
        source_of_customer,
        scheduled_datetime=None,
        room_booking_details=None,
        table_id=None,
        bill_id=None,
        remarks=None,
        bill_to=None,
        version=1,
        scheduled_order_number=None,
        created_at=None,
        modified_at=None,
        base_currency=None,
        status_updated_at: datetime = None,
        order_date=None,
        scheduled_order_date=None,
        guest_count=None,
        created_by=None,
        auth_id=None,
        deleted=False,
    ):
        self.order_id = order_id
        self.order_number = order_number
        self.order_datetime = order_datetime
        self.seller_vo = seller_vo
        self.order_type = order_type
        self.seller_type = seller_type
        self.status = status
        self.settlement_method = settlement_method
        self.guest_count = guest_count
        self.room_booking_details = room_booking_details
        self.table_id = table_id
        self.source_of_customer = source_of_customer
        self.bill_id = bill_id
        self.remarks = remarks
        self.bill_to = bill_to
        self.version = version
        self.created_at = created_at
        self.modified_at = modified_at
        self.order_date = order_date
        self.base_currency = base_currency
        self.scheduled_datetime = scheduled_datetime
        self.total_price_pretax = total_price_pretax
        self.status_updated_at = status_updated_at
        self.scheduled_order_number = scheduled_order_number
        self.scheduled_order_date = scheduled_order_date
        self.created_by = created_by
        self.priority = None
        self.auth_id = auth_id
        self.deleted = deleted

    @property
    def crs_booking_id(self):
        return (
            self.room_booking_details.crs_booking_id
            if self.room_booking_details
            else None
        )

    @crs_booking_id.setter
    def crs_booking_id(self, crs_booking_id):
        self.room_booking_details.crs_booking_id = crs_booking_id

    @property
    def room_number(self):
        return (
            self.room_booking_details.room_number if self.room_booking_details else None
        )

    @property
    def room_stay_id(self):
        return (
            self.room_booking_details.room_stay_id
            if self.room_booking_details
            else None
        )

    @room_stay_id.setter
    def room_stay_id(self, room_stay_id):
        self.room_booking_details.room_stay_id = room_stay_id

    @property
    def guest_ids(self):
        return (
            self.room_booking_details.guest_ids if self.room_booking_details else None
        )

    @guest_ids.setter
    def guest_ids(self, guest_ids):
        self.room_booking_details.guest_ids = guest_ids

    def update_bill_id(self, bill_id):
        self.bill_id = bill_id

    def set_bill_to(self, bill_to):
        self.bill_to = bill_to

    def update_room_booking_details(self, room_booking_details):
        self.room_booking_details = room_booking_details

    def mark_settled(self):
        if self.status == PosOrderStatus.SETTLED:
            raise ValidationException(error=OrderError.ORDER_ALREADY_SETTLED)
        elif self.status == PosOrderStatus.CANCELLED:
            raise ValidationException(error=OrderError.CANNOT_SETTLE_CANCELLED_ORDER)
        elif self.status == PosOrderStatus.VOIDED:
            raise ValidationException(error=OrderError.CANNOT_SETTLE_VOIDED_ORDER)
        self.status = PosOrderStatus.SETTLED

    def mark_cancelled(self):
        if self.status == PosOrderStatus.CANCELLED:
            raise ValidationException(error=OrderError.CANNOT_CANCEL_CANCELLED_ORDER)
        elif self.status == PosOrderStatus.SETTLED:
            raise ValidationException(error=OrderError.CANNOT_CANCEL_SETTLED_ORDER)
        elif self.status == PosOrderStatus.VOIDED:
            raise ValidationException(error=OrderError.CANNOT_CANCEL_VOIDED_ORDER)
        self.status = PosOrderStatus.CANCELLED

    def mark_voided(self):
        if self.status == PosOrderStatus.CANCELLED:
            raise ValidationException(error=OrderError.CANNOT_VOID_CANCELLED_ORDER)
        elif self.status == PosOrderStatus.SETTLED:
            raise ValidationException(error=OrderError.CANNOT_VOID_SETTLED_ORDER)
        elif self.status == PosOrderStatus.VOIDED:
            raise ValidationException(error=OrderError.CANNOT_VOID_VOIDED_ORDER)
        self.status = PosOrderStatus.VOIDED

    def mark_preparing(self):
        self.status = PosOrderStatus.PREPARING
        self.status_updated_at = dateutils.current_datetime()

    def set_priority(
        self,
        current_time,
        acceptance_timedelta,
        acceptance_time_upper_limit,
        settlement_timedelta,
        settlement_time_upper_limit,
        order_has_delayed_item,
        order_has_extremely_delayed_item,
    ):
        self.priority = PosOrderPriorities.ON_TRACK
        if self.status == PosOrderStatus.CREATED and self.status_updated_at:
            if self.status_updated_at + acceptance_timedelta >= current_time:
                self.priority = PosOrderPriorities.ON_TRACK
            elif self.status_updated_at + acceptance_time_upper_limit >= current_time:
                self.priority = PosOrderPriorities.DELAYED
            elif self.status_updated_at + acceptance_time_upper_limit < current_time:
                self.priority = PosOrderPriorities.EXTREMELY_DELAYED
        elif self.status == PosOrderStatus.DELIVERED and self.status_updated_at:
            if self.status_updated_at + settlement_timedelta >= current_time:
                self.priority = PosOrderPriorities.ON_TRACK
            elif self.status_updated_at + settlement_time_upper_limit >= current_time:
                self.priority = PosOrderPriorities.DELAYED
            elif self.status_updated_at + settlement_time_upper_limit < current_time:
                self.priority = PosOrderPriorities.EXTREMELY_DELAYED
        else:
            self.priority = PosOrderPriorities.ON_TRACK

        if self.status == PosOrderStatus.CANCELLED:
            self.priority = PosOrderPriorities.ON_TRACK
        elif self.status == PosOrderStatus.SETTLED:
            self.priority = PosOrderPriorities.ON_TRACK
        elif order_has_extremely_delayed_item:
            self.priority = PosOrderPriorities.EXTREMELY_DELAYED
        elif order_has_delayed_item:
            self.priority = PosOrderPriorities.DELAYED
