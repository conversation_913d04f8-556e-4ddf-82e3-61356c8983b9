from flask import request

from object_registry import inject
from pos.api import pos_bp
from pos.api.serializers.request.order import CreateNewOrderSchema
from pos.api.serializers.response.order import OrderResponseSchema
from pos.application.services.order_application_service import OrderApplicationService
from pos.common.request_parsers import (
    read_user_data_from_request_header_and_set_context,
)
from pos.core.api_docs import swag_route
from pos.core.globals import pos_context
from shared_kernel.api_helpers.request_parsers import schema_wrapper_parser
from shared_kernel.api_response import ApiResponse


@swag_route
@pos_bp.route('/orders', methods=['POST'])
@schema_wrapper_parser(CreateNewOrderSchema)
@inject(order_app_service=OrderApplicationService)
def create_new_order(order_app_service, parsed_request):
    """Create new POS Order
    ---
    operationId: create_new_order
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Create new POS Order
        tags:
            - Order
        parameters:
            - in: body
              name: new_order
              description: The order object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/CreateNewOrderSchema"
        responses:
            200:
                description: A newly created POS Order object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/OrderResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header_and_set_context(
        default="backend-system"
    )
    order_aggregate = order_app_service.create_new_order(parsed_request, user_data)
    order_response_schema = OrderResponseSchema()
    response = order_response_schema.dump(order_aggregate)
    return ApiResponse.build(data=response.data, status_code=201)
