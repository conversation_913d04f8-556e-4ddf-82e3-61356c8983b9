from object_registry import inject
from pos.api import pos_bp
from pos.api.serializers.response.order import OrderResponseSchema
from pos.application.services.order_application_service import OrderApplicationService
from pos.core.api_docs import swag_route
from shared_kernel.api_response import ApiResponse


@swag_route
@pos_bp.route('/orders/<string:order_id>', methods=['GET'])
@inject(order_app_service=OrderApplicationService)
def get_order(order_app_service, order_id):
    """Returns the given POS Order
    ---
    operationId: get_order
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Gets POS order
        tags:
            - Order
        parameters:
            - in: path
              name: order_id
              description: The order_id of the pos order that needs to be fetched
              required: True
              type: string
        responses:
            200:
                description: POS Order object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/OrderResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    order_aggregate = order_app_service.get_order(order_id)
    order_response_schema = OrderResponseSchema()
    response = order_response_schema.dump(order_aggregate)
    return ApiResponse.build(data=response.data, status_code=200)
