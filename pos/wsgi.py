#!/usr/bin/env python
# coding=utf-8
"""
Manage.py commands
"""
import sys

from treebo_commons.multitenancy.sqlalchemy import db_engine

from object_registry import finalize_app_initialization
from pos.app import create_app

# noinspection PyUnresolvedReferences
from pos.models import *

# noinspection PyUnresolvedReferences
from prometheus.models import *

db_engine.setup_tenant_sessions()
app = create_app()

if 'db' not in sys.argv:
    finalize_app_initialization()
