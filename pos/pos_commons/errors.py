from ths_common.exceptions import CRSError


class ApplicationErrors(CRSError):
    EDIT_ORDER_RESOURCE_VERSION_MISMATCH = (
        "9001",
        "Edit order resource version should match existing order",
    )
    CANNOT_EDIT_ORDER_AFTER_SETTLEMENT = (
        "9002",
        "Order cannot be edited after settlement",
    )
    SETTLE_ORDER_RESOURCE_VERSION_MISMATCH = (
        "9003",
        "Settle order resource version should match existing order",
    )
    CANNOT_SETTLE_ORDER_UNLESS_BILLS_ARE_SETTLED = (
        "9004",
        "Cannot settle order until all bills are settled",
    )
    CANNOT_SETTLE_PRIMARY_BILL_UNLESS_OTHER_BILLS_ARE_SETTLED = (
        "9005",
        "Cannot settle primary bill until all other bills are settled",
    )
    CANNOT_SETTLE_ALREADY_SETTLED_BILL = "9006", "Cannot settle already settled bill"
    INVALID_SETTLE_BILL_METHOD = "9007", "Settlement method is invalid"
