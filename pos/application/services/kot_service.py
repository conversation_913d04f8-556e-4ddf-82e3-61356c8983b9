from collections import defaultdict

from sqlalchemy.orm.exc import NoResultFound
from treebo_commons.utils import dateutils

from object_registry import register_instance
from pos.application.decorators import read_seller_id_from_headers, session_manager
from pos.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from pos.domain.constants import KotMessage
from pos.domain.policy.engine import RuleEngine
from pos.domain.policy.facts.facts import Facts
from pos.infrastructure.database.repositories.order.kot_id_sequence_repository import (
    KOTIdSequenceRepository,
)
from pos.infrastructure.database.repositories.order.order_number_repository import (
    OrderNumberRepository,
)
from pos.infrastructure.database.repositories.order.order_repository import (
    OrderRepository,
)
from pos.pos_restaurant.repositories.kot_repository import KotRepository
from ths_common.exceptions import AuthorizationError, ResourceNotFound


@register_instance(
    dependencies=[
        KotRepository,
        OrderNumberRepository,
        OrderRepository,
        KOTIdSequenceRepository,
    ]
)
class KotService(object):
    def __init__(
        self,
        kot_repository,
        order_number_repository,
        order_repository,
        kot_id_sequence_repository,
    ):
        self.kot_repository = kot_repository
        self.order_number_repository = order_number_repository
        self.order_repository = order_repository
        self.kot_id_sequence_repository = kot_id_sequence_repository

    @session_manager(commit=False)
    def get_kots_for_order(self, order_id):
        return self.kot_repository.load(order_id)

    def create_kots(self, order_aggregate):
        kot_aggregate = self.kot_repository.load(order_aggregate.order_id)
        items_sent_to_kitchen_dict = defaultdict(int)

        for kot in kot_aggregate.kots:
            for order_item in kot.order_items:
                items_sent_to_kitchen_dict[order_item["order_item_id"]] += order_item[
                    "quantity"
                ]

        order_items_not_sent_to_kitchen = []
        for order_item in order_aggregate.order_items:
            if order_item.order_item_id not in items_sent_to_kitchen_dict:
                order_items_not_sent_to_kitchen.append(
                    {**order_item.to_json(), **{"quantity": order_item.quantity}}
                )
            elif (order_item.order_item_id in items_sent_to_kitchen_dict) and (
                order_item.quantity
                > items_sent_to_kitchen_dict[order_item.order_item_id]
            ):
                order_items_not_sent_to_kitchen.append(
                    {
                        **order_item.to_json(),
                        **{
                            "quantity": order_item.quantity
                            - items_sent_to_kitchen_dict[order_item.order_item_id]
                        },
                    }
                )

        kot_entities = []
        # bifurcate order_items to kitchens
        if len(order_items_not_sent_to_kitchen) > 0:
            kot_entities = self._generate_kitchen_wise_kots(
                order_items_not_sent_to_kitchen,
                order_aggregate.order_id,
                order_aggregate.seller_id,
                kot_aggregate,
            )

            order_aggregate.mark_order_preparing()
            order_items_to_be_prepared = [
                order_aggregate.order_item_dict[order_item["order_item_id"]]
                for order_item in order_items_not_sent_to_kitchen
            ]
            order_aggregate.mark_order_items_preparing(order_items_to_be_prepared)
            self.kot_repository.update(kot_aggregate)

        kot_entity_ids = [kot_entity.kot_id for kot_entity in kot_entities]
        kot_aggregate = self.kot_repository.load(order_aggregate.order_id)
        return [kot for kot in kot_aggregate.kots if kot.kot_id in kot_entity_ids]

    def create_kots_for_cancelled_items(self, cancelled_order_items, order_aggregate):
        kot_aggregate = self.kot_repository.load(order_aggregate.order_id)
        kot_entities = self._generate_kitchen_wise_kots(
            cancelled_order_items,
            order_aggregate.order_id,
            order_aggregate.seller_id,
            kot_aggregate,
            message=KotMessage.ORDER_ITEM_CANCELLED.value,
        )
        self.kot_repository.update(kot_aggregate)
        kot_entity_ids = [kot_entity.kot_id for kot_entity in kot_entities]
        kot_aggregate = self.kot_repository.load(order_aggregate.order_id)
        return [kot for kot in kot_aggregate.kots if kot.kot_id in kot_entity_ids]

    def _generate_kitchen_wise_kots(
        self,
        order_items_not_sent_to_kitchen,
        order_id,
        seller_id,
        kot_aggregate,
        message=None,
    ):
        kitchen_order_item_map = defaultdict(list)

        kot_entities = []
        for order_item in order_items_not_sent_to_kitchen:
            kitchen_ids = order_item["kitchen_ids"]
            if kitchen_ids:
                for kitchen_id in kitchen_ids:
                    kitchen_order_item_map[kitchen_id].append(order_item)
            else:
                kitchen_order_item_map[None].append(order_item)

        for kitchen_id, order_items in kitchen_order_item_map.items():
            kot_id = self.kot_id_sequence_repository.get_next_kot_id(seller_id)
            new_kot = kot_aggregate.create_kot(
                order_id, kot_id, kitchen_id, order_items, message
            )
            kot_entities.append(new_kot)
        return kot_entities

    @session_manager(commit=True)
    @read_seller_id_from_headers()
    def send_to_kitchen(self, order_id, user_data, seller_id=None):
        RuleEngine.action_allowed(
            action='create_order',
            facts=Facts(user_type=user_data.user_type),
            fail_on_error=True,
        )
        try:
            order_aggregate = self.order_repository.load_for_update(order_id, seller_id)
        except NoResultFound:
            raise ResourceNotFound(
                "Order",
                "order_id: {0} , seller_id {1} not found".format(order_id, seller_id),
            )
        if not order_aggregate.order_number and order_aggregate.scheduled_datetime:
            order_date = dateutils.to_date(order_aggregate.scheduled_datetime)
            order_number = self.order_number_repository.get_next_order_number(
                order_aggregate.seller_id, order_date
            )
            order_aggregate.update_order_number(order_number)
        kot_entities = self.create_kots(order_aggregate)
        self.order_repository.update(order_aggregate)

        IntegrationEventApplicationService.send_to_kitchen_event(
            order_aggregate, user_action="order_sent_to_kitchen"
        )

        return kot_entities

    @session_manager(commit=True)
    def get_kots(self, order_id):
        kot_aggregate = self.kot_repository.load(order_id)
        order_aggregate = self.order_repository.load(order_id)
        kot_entities = []

        for kot in kot_aggregate.kots:
            # skip cancelled/voided kots
            if kot.message in [
                KotMessage.ORDER_ITEM_CANCELLED.value,
                KotMessage.ORDER_VOIDED.value,
            ]:
                continue

            non_removed_order_items = [
                order_aggregate.get_order_item_by_id(order_item["order_item_id"])
                for order_item in kot.order_items
                if order_aggregate.get_order_item_by_id(order_item["order_item_id"])
            ]
            if non_removed_order_items:
                kot.order_items = [
                    order_item.to_json() for order_item in non_removed_order_items
                ]
                kot_entities.append(kot)
        return kot_entities
