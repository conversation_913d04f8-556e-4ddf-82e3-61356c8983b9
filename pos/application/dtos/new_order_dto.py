from typing import List

from treebo_commons.utils import dateutils


class OrderItemDto(object):
    def __init__(
        self,
        sku_id,
        sku_category_code,
        item_name,
        quantity,
        unit_price_pretax,
        unit_price_posttax,
        item_detail,
        charge_type,
        bill_to_type,
        remarks,
        discounts,
        kitchen_ids,
        is_complimentary,
        complimentary_details,
    ):
        self.sku_id = sku_id
        self.sku_category_code = sku_category_code
        self.item_name = item_name
        self.quantity = quantity
        self.unit_price_pretax = unit_price_pretax
        self.unit_price_posttax = unit_price_posttax
        self.item_detail = item_detail
        self.charge_type = charge_type
        self.bill_to_type = bill_to_type
        self.charge_id = None
        self.remarks = remarks
        self.charge_to = None
        self.discounts = discounts
        self.kitchen_ids = kitchen_ids
        self.is_complimentary = is_complimentary
        self.complimentary_details = complimentary_details

    @property
    def total_price_pretax(self):
        return (
            self.unit_price_pretax * self.quantity
            if self.unit_price_pretax is not None
            else None
        )

    @property
    def total_price_posttax(self):
        return (
            self.unit_price_posttax * self.quantity
            if self.unit_price_posttax is not None
            else None
        )

    def update_added_by_in_discounts(self, added_by):
        if not self.discounts:
            return
        for discount in self.discounts:
            discount.update_added_by(added_by)


class NewPosCustomerDto(object):
    def __init__(
        self,
        first_name,
        last_name,
        phone,
        email,
        gstin_num,
        company_profile_id=None,
        room_booking_details=None,
    ):
        self.first_name = first_name
        self.last_name = last_name
        self.phone = phone
        self.email = email
        self.gstin_num = gstin_num
        self.company_profile_id = company_profile_id
        self.room_booking_details = room_booking_details


class NewOrderDto(object):
    def __init__(
        self,
        seller_id,
        order_items: List[OrderItemDto],
        order_type,
        seller_type,
        source_of_customer,
        settlement_method,
        room_booking_details=None,
        table_id=None,
        remarks=None,
        extra_information=None,
        customers: List[NewPosCustomerDto] = None,
        bill_to: NewPosCustomerDto = None,
        discounts=None,
        order_datetime=None,
        scheduled_datetime=None,
        guest_count=None,
    ):
        self.order_datetime = order_datetime
        self.seller_id = seller_id
        self.order_type = order_type
        self.seller_type = seller_type
        self.settlement_method = settlement_method
        self.room_booking_details = room_booking_details
        self.table_id = table_id
        self.remarks = remarks
        self.extra_information = extra_information
        self.guest_count = guest_count
        self.bill_to = bill_to
        self.bill_id = None
        self.discounts = discounts
        self.customers = customers
        self.source_of_customer = source_of_customer
        self.scheduled_datetime = scheduled_datetime
        self.order_items = order_items
        self.order_date = None

    @property
    def scheduled_order_date(self):
        if self.scheduled_datetime:
            return dateutils.to_date(self.scheduled_datetime)
        return None

    def update_added_by_in_order_item_discounts(self, added_by):
        for order_item in self.order_items:
            order_item.update_added_by_in_discounts(added_by)
