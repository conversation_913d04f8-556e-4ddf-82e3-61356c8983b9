from ths_common.exceptions import CRSException


class PosException(CRSException):
    def __init__(self, error=None, description=None, extra_payload=None):
        if error:
            self.error_code = error.error_code
            self.message = error.message
        else:
            self.error_code = self.error.error_code
            self.message = self.error.message
        super().__init__(description=description, extra_payload=extra_payload)
