from ths_common.exceptions import CRSError


class ApplicationErrors(CRSError):
    EDIT_ORDER_RESOURCE_VERSION_MISMATCH = (
        "9001",
        "Edit order resource version should match existing order",
    )
    INTEGRATION_EVENT_DTO_FAILURE_NO_DATA_PASSED = (
        "9002",
        "Required data not passed for generating integration event",
    )


class PosConfigurationError(CRSError):
    NO_RMQ_CONFIG_FOR_TENANT = "9003", "RMQ producer not configured"
