from flask import current_app
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient


class CRSEventQueueConfig(object):
    """
    CRS Booking Queue Configuration
    """

    def __init__(self):
        self.rabbitmq_url = current_app.config.get('RABBITMQ_HOST')
        self.exchange_name = 'cs_exchange'
        self.exchange_type = 'topic'
        self.queue_name = 'catalog-event-queue'
        self.routing_keys = ['booking']
        self.exclusive = False


class InterfaceExchangeConsumerConfig(object):
    """
    Interface ExchangeConfig Jobs Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = 'erp_reports'
        self.exchange_type = 'topic'
        self.queue_name = 'interface-event-pos-queue'
        self.routing_keys = [
            'interface_exchange.erp.pos',
        ]
        self.exclusive = False


class InterfaceExchangePublisherConfig(object):
    """
    Interface ExchangeConfig Jobs Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = 'erp_reports'
        self.exchange_type = 'topic'
        self.queue_name = 'interface-event-pos-queue'
        self.routing_keys = [
            'pos.erp.interface_exchange',
        ]
        self.exclusive = False
