from treebo_commons.utils import dateutils

from pos.infrastructure.database.base_db_to_domain_entity_adaptor import BaseAdaptor
from pos.infrastructure.database.repositories.order.models import KOTModel
from pos.pos_restaurant.entities.kot import KOT


class KOTAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: KOT, **kwargs):
        return KOTModel(
            order_id=domain_entity.order_id,
            kot_id=domain_entity.kot_id,
            kitchen_id=domain_entity.kitchen_id,
            order_items=domain_entity.order_items,
            message=domain_entity.message,
        )

    def to_domain_entity(self, db_entity: KOTModel, **kwargs):
        return KOT(
            order_id=db_entity.order_id,
            kot_id=db_entity.kot_id,
            order_items=db_entity.order_items,
            kitchen_id=db_entity.kitchen_id,
            message=db_entity.message,
            created_at=dateutils.localize_datetime(db_entity.created_at),
            modified_at=dateutils.localize_datetime(db_entity.modified_at),
        )
