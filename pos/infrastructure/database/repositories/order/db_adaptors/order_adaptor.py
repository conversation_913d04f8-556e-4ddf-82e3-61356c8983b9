from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from pos.domain.order.entities.order import Order
from pos.domain.order.value_objects.discount import Discount
from pos.domain.order.value_objects.room_booking_detail import RoomBookingDetail
from pos.domain.order.value_objects.seller import SellerVO
from pos.infrastructure.database.base_db_to_domain_entity_adaptor import BaseAdaptor
from pos.models import OrderModel
from shared_kernel.value_objects import SellerDetails
from ths_common.pos.constants.order_constants import (
    PosOrderSettlementMethod,
    PosOrderStatus,
    PosOrderType,
    PosSourceOfCustomer,
    SellerType,
)


class OrderAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: Order, **kwargs):
        # noinspection PyArgumentList
        return OrderModel(
            order_id=domain_entity.order_id,
            order_number=domain_entity.order_number,
            scheduled_order_number=domain_entity.scheduled_order_number,
            order_datetime=domain_entity.order_datetime,
            order_date=domain_entity.order_date,
            seller_details=domain_entity.seller_vo.seller_details.to_json(),
            seller_id=domain_entity.seller_vo.seller_id,
            order_type=domain_entity.order_type.value,
            seller_type=domain_entity.seller_type,
            settlement_method=domain_entity.settlement_method.value,
            bill_id=domain_entity.bill_id,
            crs_booking_id=domain_entity.crs_booking_id,
            source_of_customer=domain_entity.source_of_customer.value
            if domain_entity.source_of_customer
            else None,
            room_number=domain_entity.room_number,
            room_stay_id=domain_entity.room_stay_id,
            guest_ids=domain_entity.guest_ids,
            table_id=domain_entity.table_id,
            total_price_pretax=domain_entity.total_price_pretax.amount
            if domain_entity.total_price_pretax
            else None,
            scheduled_datetime=domain_entity.scheduled_datetime,
            guest_count=domain_entity.guest_count,
            scheduled_order_date=domain_entity.scheduled_order_date,
            status_updated_at=domain_entity.status_updated_at,
            status=domain_entity.status.value,
            remarks=domain_entity.remarks,
            bill_to=domain_entity.bill_to,
            version=domain_entity.version,
            created_by=domain_entity.created_by,
            auth_id=domain_entity.auth_id,
            base_currency=domain_entity.base_currency.value,
            deleted=domain_entity.deleted,
        )

    def to_domain_entity(self, db_entity: OrderModel, **kwargs):
        room_booking_details = RoomBookingDetail(
            db_entity.crs_booking_id,
            db_entity.room_number,
            room_stay_id=db_entity.room_stay_id,
            guest_ids=db_entity.guest_ids,
        )
        currency = (
            CurrencyType(db_entity.base_currency)
            if db_entity.base_currency
            else CurrencyType.INR
        )

        return Order(
            order_id=db_entity.order_id,
            order_number=db_entity.order_number,
            scheduled_order_number=db_entity.scheduled_order_number,
            order_datetime=dateutils.localize_datetime(db_entity.order_datetime),
            seller_vo=SellerVO(
                db_entity.seller_id,
                SellerDetails.from_json(db_entity.seller_details)
                if db_entity.seller_details
                else None,
            ),
            order_type=PosOrderType(db_entity.order_type),
            seller_type=db_entity.seller_type,
            status=PosOrderStatus(db_entity.status),
            settlement_method=PosOrderSettlementMethod(db_entity.settlement_method),
            source_of_customer=PosSourceOfCustomer(db_entity.source_of_customer)
            if db_entity.source_of_customer
            else None,
            room_booking_details=room_booking_details,
            guest_count=db_entity.guest_count,
            table_id=db_entity.table_id,
            order_date=db_entity.order_date,
            status_updated_at=db_entity.status_updated_at,
            total_price_pretax=Money(db_entity.total_price_pretax, currency)
            if db_entity.total_price_pretax
            else None,
            scheduled_datetime=dateutils.localize_datetime(db_entity.scheduled_datetime)
            if db_entity.scheduled_datetime
            else None,
            scheduled_order_date=db_entity.scheduled_order_date,
            bill_id=db_entity.bill_id,
            remarks=db_entity.remarks,
            bill_to=db_entity.bill_to,
            created_by=db_entity.created_by,
            auth_id=db_entity.auth_id,
            version=db_entity.version,
            created_at=dateutils.localize_datetime(db_entity.created_at),
            modified_at=dateutils.localize_datetime(db_entity.modified_at),
            base_currency=currency,
            deleted=db_entity.deleted,
        )
