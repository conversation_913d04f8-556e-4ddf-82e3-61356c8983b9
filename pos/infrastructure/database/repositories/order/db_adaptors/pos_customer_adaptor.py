from pos.domain.order.entities.pos_customer import PosCustomer
from pos.domain.order.value_objects.room_booking_detail import (
    PosCustomerRoomBookingDetail,
)
from pos.infrastructure.database.base_db_to_domain_entity_adaptor import BaseAdaptor
from pos.infrastructure.database.repositories.order.models import PosCustomerModel
from ths_common.value_objects import PhoneNumber


class PosCustomerAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: PosCustomer, **kwargs):
        # noinspection PyArgumentList
        return PosCustomerModel(
            order_id=kwargs.get('order_id'),
            customer_id=int(domain_entity.customer_id),
            first_name=domain_entity.first_name,
            last_name=domain_entity.last_name,
            email=domain_entity.email,
            gstin_num=domain_entity.gstin_num,
            phone_number=domain_entity.phone.number if domain_entity.phone else None,
            country_code=domain_entity.phone.country_code
            if domain_entity.phone
            else None,
            company_profile_id=domain_entity.company_profile_id
            if domain_entity.company_profile_id
            else None,
            room_booking_details=domain_entity.room_booking_details.to_json()
            if domain_entity.room_booking_details
            else None,
            deleted=domain_entity.deleted,
        )

    def to_domain_entity(self, db_entity: PosCustomerModel, **kwargs):
        phone = None
        if db_entity.phone_number and db_entity.country_code:
            phone = PhoneNumber(
                number=db_entity.phone_number, country_code=db_entity.country_code
            )
        return PosCustomer(
            customer_id=str(db_entity.customer_id),
            first_name=db_entity.first_name,
            last_name=db_entity.last_name,
            phone=phone,
            email=db_entity.email,
            company_profile_id=db_entity.company_profile_id,
            room_booking_details=PosCustomerRoomBookingDetail.from_json(
                db_entity.room_booking_details
            )
            if db_entity.room_booking_details
            else None,
            gstin_num=db_entity.gstin_num,
            deleted=db_entity.deleted,
        )
