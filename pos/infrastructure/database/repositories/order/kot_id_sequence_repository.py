from object_registry import register_instance
from pos.infrastructure.database.base_repository import BaseRepository
from pos.models import KOTIdSequenceModel


@register_instance()
class KOTIdSequenceRepository(BaseRepository):
    def from_aggregate(self, aggregate=None):
        pass

    def to_aggregate(self, **kwargs):
        pass

    def get_next_kot_id(self, seller_id):
        sequence = self.get_for_update(KOTIdSequenceModel, seller_id=seller_id)

        if not sequence:
            kot_id = 1
            new_sequence = KOTIdSequenceModel(seller_id=seller_id, last_kot_id=kot_id)
            self._save(new_sequence)
        else:
            kot_id = sequence.last_kot_id + 1
            sequence.last_kot_id = kot_id
            self._update(sequence)

        self.flush_session()
        return kot_id
