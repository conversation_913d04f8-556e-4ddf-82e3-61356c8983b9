class ERPOrderDetailsDTO:
    def __init__(self, erp_order_details):
        self.order_id = erp_order_details[0]
        self.bill_id = erp_order_details[1]
        self.seller_id = erp_order_details[2]


class ERPSellerDetailsDTO:
    def __init__(self, erp_seller_details):
        self.seller_id = erp_seller_details[0]
        self.name = erp_seller_details[1]
        self.state = erp_seller_details[2]
