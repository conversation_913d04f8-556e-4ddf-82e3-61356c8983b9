import sqlalchemy
from sqlalchemy.orm.exc import FlushError

from ths_common.exceptions import (
    BookingReferenceIdCollision,
    DatabaseError,
    DatabaseLockError,
    PrimaryKeyCollision,
)


def handle_integrity_error(exception):
    # TODO There could be other integrity errors. Check for only collisions
    if (
        "reference_number".upper() in exception.__str__().upper()
        and "DUPLICATE KEY VALUE VIOLATES UNIQUE CONSTRAINT".upper()
        in exception.__str__().upper()
    ):
        raise BookingReferenceIdCollision(description=exception.__str__())
    raise PrimaryKeyCollision(description=exception.__str__())


def handle_database_exception(exception):
    if isinstance(exception, sqlalchemy.exc.IntegrityError) or isinstance(
        exception, FlushError
    ):
        handle_integrity_error(exception)

    if isinstance(exception, sqlalchemy.exc.OperationalError):
        if "could not obtain lock".upper() in exception.__str__().upper():
            raise DatabaseLockError(description=exception.__str__())

    raise DatabaseError(exception.__str__())
