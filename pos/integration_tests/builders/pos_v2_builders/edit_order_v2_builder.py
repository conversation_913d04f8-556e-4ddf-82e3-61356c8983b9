import json

from pos.integration_tests.builders.common_request_builder import (
    Customer,
    Discounts,
    OrderItems,
)
from pos.integration_tests.config.sheet_names import *
from pos.integration_tests.utilities import excel_utils
from pos.integration_tests.utilities.common_utils import sanitize_test_data


class EditOrderRequestV2(object):
    def __init__(self, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = EditOrderData(test_data).__dict__
        self.resource_version = sanitize_test_data(int(test_data['resource_version']))


class EditOrderData(object):
    def __init__(self, test_data):
        if sanitize_test_data(test_data['Customer_details']):
            self.customers = []
            customer_type = test_data['Customer_details'].split('#')
            for ct in customer_type:
                self.customers.append(
                    sanitize_test_data(Customer(json.loads(ct)).__dict__)
                )
        if sanitize_test_data(test_data['bill_to']):
            self.bill_to = sanitize_test_data(
                Customer(json.loads(test_data['bill_to'])).__dict__
            )
        if sanitize_test_data(test_data['discounts']):
            self.discounts = []
            discount_type = test_data['discounts'].split('#')
            for dt in discount_type:
                self.discounts.append(
                    sanitize_test_data(Discounts(json.loads(dt)).__dict__)
                )
        self.scheduled_datetime = sanitize_test_data(test_data['scheduled_datetime'])
        self.extra_information = sanitize_test_data(test_data['extra_information'])
        if sanitize_test_data(test_data['order_items']):
            self.order_items = []
            order_items_type = test_data['order_items'].split("#")
            for order_type in order_items_type:
                order_data = excel_utils.get_test_case_data(
                    ORDER_ITEMS_SHEET_NAME, order_type
                )[0]
                self.order_items.append(
                    sanitize_test_data(OrderItems(order_data).__dict__)
                )
        self.order_type = sanitize_test_data(test_data['order_type'])
        self.remarks = sanitize_test_data(test_data['remarks'])
        self.room_booking_details = sanitize_test_data(
            test_data['room_booking_details']
        )
        self.table_number = sanitize_test_data(str(test_data['table_number']))
