import json

from pos.integration_tests.builders.common_request_builder import Customer
from pos.integration_tests.config import common_config
from pos.integration_tests.config.sheet_names import *
from pos.integration_tests.utilities import excel_utils
from pos.integration_tests.utilities.common_utils import (
    increment_date,
    sanitize_test_data,
)


class SettleOrderRequest(object):
    def __init__(self, sheet_name, test_case_id, booking_id=None):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = SettleOrderData(test_data, booking_id).__dict__
        self.resource_version = sanitize_test_data(int(test_data['resource_version']))


class SettleOrderData(object):
    def __init__(self, test_data, booking_id=None):
        if test_data['settlement_method']:
            self.settlement_method = sanitize_test_data(
                str(test_data['settlement_method'])
            )
            self.room_booking_details = sanitize_test_data(
                RoomBookingData(
                    json.loads(test_data['booking_details']), booking_id
                ).__dict__
            )
            return

        if sanitize_test_data(test_data['Customer_details']) is not None:
            self.bill_to = sanitize_test_data(
                Customer(json.loads(test_data['Customer_details'])).__dict__
            )
        self.payments = []
        if sanitize_test_data(test_data['payment_mode_type']) is not None:
            payment_mode_type = test_data['payment_mode_type'].split(",")
            for payment_type in payment_mode_type:
                payment_data = excel_utils.get_test_case_data(
                    PAYMENTS_MODE_SHEET_NAME, payment_type
                )[0]
                self.payments.append(sanitize_test_data(Payment(payment_data).__dict__))


class Payment(object):
    def __init__(self, payment_data):
        self.amount = sanitize_test_data(str(payment_data['amount']))
        self.amount_in_payment_currency = sanitize_test_data(
            str(payment_data['amount_in_payment_currency'])
        )
        self.comment = sanitize_test_data(str(payment_data['comment']))
        self.payment_mode = sanitize_test_data(str(payment_data['payment_mode']))
        self.payment_mode_sub_type = sanitize_test_data(
            str(payment_data['payment_mode_sub_type'])
        )
        self.status = sanitize_test_data(str(payment_data['status']))


class RoomBookingData(object):
    def __init__(self, booking_data, booking_id):
        self.crs_booking_id = booking_id
        self.room_stay_id = sanitize_test_data(booking_data['room_stay_id'])
        self.assigned_to = []
        self.assigned_to.append(self.AssignedToValues(booking_data).__dict__)

    class AssignedToValues(object):
        def __init__(self, booking_data):
            self.actual_checkin_date = sanitize_test_data(
                increment_date(
                    int(booking_data['actual_checkin_date']),
                    common_config.CHECK_IN_TIME_ZONE,
                )
            )
            self.actual_checkout_date_ = sanitize_test_data(
                increment_date(
                    (booking_data['actual_checkout_date_']),
                    common_config.CHECK_IN_TIME_ZONE,
                )
            )
            self.guest_id = sanitize_test_data(str(booking_data['guest_id']))
