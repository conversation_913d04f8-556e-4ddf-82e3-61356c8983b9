################# USER TYPES ###############
# USER TYPE KEY
USER_TYPE_KEY = 'X-User-Type'

# USER TYPES
SUPER_ADMIN = 'super-admin'

# ERROR CODES
ERROR_CODES = [400, 403]

# SUCCESS CODES
SUCCESS_CODES = [200, 201]

CREATE_ORDER_ONE_ORDERITEM = {'id': "CreateOrder_01", 'type': 'create_order'}
CREATE_ORDER_V2_ONE_ORDERITEM = {'id': "CreateOrderV2_01", 'type': 'create_order'}

CREATE_ORDER_FOR_BOOKING_WITH_TWO_ITEM = [
    {'id': "Booking_01", 'type': 'create_booking'},
    {'id': "checkinPost_01", 'type': 'checkin_booking'},
    {'id': "CreateOrderV2_01", 'type': 'create_order_for_booking'},
]

CREATE_ORDER_FOR_BOOKING_WITH_THREE_ITEM = [
    {'id': "Booking_01", 'type': 'create_booking'},
    {'id': "checkinPost_01", 'type': 'checkin_booking'},
    {'id': "CreateOrderV2_01", 'type': 'create_order_for_booking'},
]

CREATE_ORDER_TWO_ORDERITEM = {'id': "CreateOrder_02", 'type': 'create_order'}
CREATE_ORDER_V2_TWO_ORDERITEM = {'id': "CreateOrderV2_43", 'type': 'create_order'}
CREATE_ORDER_V2_THREE_ORDERITEM = {'id': "CreateOrderV2_44", 'type': 'create_order'}
CREATE_ORDER_V2_MULTIPLE_ORDERITEM = {'id': "CreateOrderV2_45", 'type': 'create_order'}

CREATE_A_CHECKEDIN_BOOKING = [
    {'id': "Booking_01", 'type': 'create_booking'},
    {'id': "checkinPost_01", 'type': 'checkin_booking'},
]

CREATE_RESERVATION = {'id': "CreateReservation_01", 'type': 'create_reservation'}

ROOM_TYPE_MAP = {'acacia': 'RT01', 'oak': 'RT02', 'maple': 'RT03', 'mahogany': 'RT04'}

# HOTEL AND ROOM DETAILS FOR CREATE BOOKING
HOTEL_ID = '0016581'
ROOM_TYPE = 'RT03'

CHECK_IN_TIME_ZONE = '%Y-%m-%dT12:00:00+05:30'
CHECKOUT_TIME_ZONE = '%Y-%m-%dT11:00:00+05:30'
DEFAULT = '%Y-%m-%dT12:00:00+05:30'

POS_DOMAIN = 'http://pos.treebo.be'
CRS_DOMAIN = 'http://crs.treebo.be'

SELLER_ID = ['1', '10002']
SELLER_CURRENCY_MAP = {'1': 'INR', '10002': 'EUR'}
