import json

import requests
from treebo_commons.utils.dateutils import today, tomorrow

from pos.integration_tests.config import common_config
from pos.integration_tests.config.common_config import CRS_DOMAIN
from pos.integration_tests.config.request_uris import *


class CrsClient:
    headers = {'X-User-Type': 'super-admin'}

    def create_booking(self, payload):
        create_booking_api = CRS_DOMAIN + CREATE_BOOKING_URI

        return self.__make_call(
            method="POST",
            api=create_booking_api,
            headers=self.headers,
            data=json.loads(payload),
        )

    def booking_action(self, payload, booking_id):
        booking_action_api = CRS_DOMAIN + BOOKING_ACTION_URI.format(booking_id)

        return self.__make_call(
            method="POST",
            api=booking_action_api,
            headers=self.headers,
            data=json.loads(payload),
        )

    def reverse_booking_action(self, booking_id, action_id):
        reverse_booking_action_api = CRS_DOMAIN + REVERSE_BOOKING_ACTION_URI.format(
            booking_id, action_id
        )

        return self.__make_call(
            method="DELETE", api=reverse_booking_action_api, headers=self.headers
        )

    def get_available_rooms(self, room_type_id):
        get_room_availability_slots_uri = CRS_DOMAIN + GET_AVAILABLE_ROOMS_URI.format(
            common_config.HOTEL_ID, today(), tomorrow(), room_type_id
        )

        return self.__make_call(
            method="GET", api=get_room_availability_slots_uri, headers=self.headers
        )

    @staticmethod
    def __make_call(method, api, headers, data=None, params=None):
        response = requests.request(
            method, api, params=params, headers=headers, json=data, allow_redirects=True
        )
        return response
