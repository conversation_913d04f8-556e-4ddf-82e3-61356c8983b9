from pos.integration_tests.config.error_messages import ErrorMessage
from pos.integration_tests.requests.booking_request import BookingRequests
from pos.integration_tests.requests.order_request import OrderRequests
from pos.integration_tests.requests.reservation_request import ReservationsRequest
from pos.integration_tests.requests.v2_requests.v2_order_request import OrderRequestsV2
from pos.integration_tests.utilities.common_utils import assert_


class BaseTest(object):
    order_request = OrderRequests()
    order_request_v2 = OrderRequestsV2()
    booking_request = BookingRequests()
    reservation_request = ReservationsRequest()

    def common_request_caller(
        self, client, test_case_id_plus_action_to_be_performed_list, seller_id='1'
    ):
        for action in test_case_id_plus_action_to_be_performed_list:
            test_case_id = action['id'] if 'id' in action else None
            action_type = action['type']
            user_type = action['user_type'] if 'user_type' in action else None
            if action_type == 'create_order':
                self.order_request_v2.create_order_request(
                    client, test_case_id, 201, user_type, seller_id, booking_id=None
                )
            elif action_type == 'create_order_for_booking':
                self.order_request_v2.create_order_request(
                    client,
                    test_case_id,
                    201,
                    user_type,
                    seller_id,
                    self.booking_request.booking_id,
                )
            elif action_type == 'edit_order':
                self.order_request_v2.edit_order_request(
                    client, test_case_id, 201, user_type
                )
            elif action_type == 'settle_order':
                self.order_request.settle_order_request(
                    client, test_case_id, 200, user_type
                )
            elif action_type == 'settle_order':
                self.order_request.settle_order_request(
                    client, test_case_id, 200, user_type
                )
            elif action_type == 'settle_order_v2':
                self.order_request_v2.settle_order_request(
                    client,
                    test_case_id,
                    200,
                    self.order_request_v2.order_id,
                    self.order_request_v2.bill_id,
                    user_type,
                )
            elif action_type == 'cancel_order':
                self.order_request.cancel_order_request(
                    client, test_case_id, 201, user_type
                )
            elif action_type == 'void_order':
                self.order_request_v2.void_order_request(
                    client, test_case_id, 200, user_type
                )
            elif action_type == 'create_order_for_booking':
                self.order_request.new_order_for_booking_request(
                    test_case_id, seller_id
                )
            elif action_type == 'edit_order_for_booking':
                self.order_request.edit_order_for_booking_request(test_case_id)
            elif action_type == 'create_booking':
                self.booking_request.create_booking_request(test_case_id)
            elif action_type == 'checkin_booking':
                self.booking_request.checkin_booking_request(
                    test_case_id, self.booking_request.booking_id
                )
            elif action_type == 'reverse_booking_action':
                self.booking_request.reverse_action_request(
                    self.booking_request.booking_id, self.booking_request.action_id
                )
            elif action_type == 'create_reservation':
                self.reservation_request.create_reservation_request(
                    client, test_case_id, 200, user_type, seller_id
                )
            else:
                raise ValueError(
                    action['id'] + ' is not handled in Common request caller'
                )

    @staticmethod
    def response_validation_negative_cases(response, test_case_id):
        code = None
        dev_message = None
        extra_payload = None
        for error_message_index in range(len(response['errors'])):
            assert_(
                response['errors'][error_message_index]['message']
                in ErrorMessage[test_case_id].value,
                True,
                "actual error message is not correct",
            )
        if code:
            assert_(response['errors'][0]['code'], code)
        if dev_message:
            assert_(response['errors'][0]['developer_message'], dev_message)
        if extra_payload:
            assert_(response['errors'][0]['extra_payload'], extra_payload)
