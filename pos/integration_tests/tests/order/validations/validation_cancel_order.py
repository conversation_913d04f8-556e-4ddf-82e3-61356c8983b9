from pos.integration_tests.config import sheet_names
from pos.integration_tests.utilities.common_utils import assert_
from pos.integration_tests.utilities.excel_utils import get_test_case_data
from thsc.crs.entities.billing import Bill


class ValidationCancelOrder:
    def __init__(self, test_case_id, response, bill_id):
        self.test_data = get_test_case_data(
            sheet_names.CANCEL_ORDER_SHEET_NAME, test_case_id
        )[0]
        self.response = response
        self.bill_id = bill_id

    def validate_response(self):
        assert_(self.test_data['pos_status'], self.response['data']['status'])
        order_items_type = self.test_data['order_items'].split(",")
        crs_bill_obj = Bill.get(self.bill_id)
        for order_type_index, order_type in enumerate(order_items_type):
            assert_(
                str(crs_bill_obj.charges[order_type_index].status),
                self.test_data['bill_status'],
            )
