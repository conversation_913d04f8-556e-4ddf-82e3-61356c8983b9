import datetime

from treebo_commons.utils.dateutils import (
    add,
    current_date,
    localize_datetime,
    next_month_number,
    next_year_number,
    subtract,
)


def get_settlement_date(current_time, next_month=False):
    # TODO Remove hard-coding of 2/mm/yyyy 15:00:00.000
    # TODO Clean this up
    # TODO Add unit tests
    # Got this data from https://treebo.atlassian.net/browse/PROM-1428
    if next_month:
        return localize_datetime(
            datetime.datetime(
                year=next_year_number(current_time.year, current_time.month),
                month=next_month_number(current_time.month),
                day=2,
                hour=15,
            )
        )
    return localize_datetime(
        datetime.datetime(
            year=current_time.year, month=current_time.month, day=2, hour=15
        )
    )


def format_date_human_readable(d):
    return d.strftime("%a, %d %b %Y")


def format_datetime_human_readable(d):
    return d.strftime('%B %d,%Y' ' %I:%M %p')


def last_date_of_month(date):
    date_at_next_month = add(date, months=1)
    date_at_start_of_next_month = datetime.date(
        date_at_next_month.year, date_at_next_month.month, 1
    )
    return subtract(date_at_start_of_next_month, days=1)


def time_str_to_time(time_str):
    if not time_str:
        return None
    return datetime.datetime.strptime(time_str, "%H:%M:%S").time()


def get_current_fiscal_year(short_format=False, business_date=None):
    _current_date = current_date() if business_date is None else business_date
    year = _current_date.year - 1 if _current_date.month <= 3 else _current_date.year
    return year % 100 if short_format else year
