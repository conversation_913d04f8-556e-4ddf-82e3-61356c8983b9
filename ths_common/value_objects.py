import abc
from datetime import datetime
from decimal import Decimal

from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import date_range

from ths_common.constants.billing_constants import BilledEntityCategory
from ths_common.constants.booking_constants import (
    GuaranteeTypes,
    Salutation,
    ServiceTypes,
    TACommissionTypes,
)
from ths_common.constants.catalog_constants import SellerType
from ths_common.constants.expense_constants import ItemCodeTypes
from ths_common.constants.inventory_constants import (
    DNRInactiveRoomSubType,
    DNRMaintenanceSubType,
    DNRNonMaintenanceSubType,
    DNRSubType,
    DNRType,
)
from ths_common.exceptions import ValidationException
from ths_common.utils.common_utils import safe_strip


class Unassigned(object):
    def __init__(self):
        pass

    def __bool__(self):
        return False

    def __nonzero__(self):
        return False


NotAssigned = Unassigned()


class Name(object):
    __slots__ = ('salutation', 'first_name', 'middle_name', 'last_name', 'full_name')

    def __init__(self, first_name, middle_name=None, last_name=None, salutation=None):
        """

        :param str first_name:
        :param str middle_name:
        :param str last_name:
        :param ths_common.constants.booking_constants.Salutation salutation:
        """
        self.salutation = salutation if salutation else None
        self.first_name = first_name.strip() if first_name else None
        self.middle_name = middle_name.strip() if middle_name else None
        self.last_name = last_name.strip() if last_name else None
        self.full_name = ''.join(
            [
                "{} ".format(self.salutation.value) if self.salutation else '',
                "{}".format(self.first_name) if self.first_name else '',
                " {}".format(self.middle_name) if self.middle_name else '',
                " {}".format(self.last_name) if self.last_name else '',
            ]
        )

    def __eq__(self, other):
        if isinstance(self, other.__class__):
            return (
                self.salutation == other.salutation
                and self.first_name == other.first_name
                and self.middle_name == other.middle_name
                and self.last_name == other.last_name
            )
        return False

    def to_json(self):
        return {
            "salutation": self.salutation.value if self.salutation else "",
            "first_name": self.first_name,
            "middle_name": self.middle_name,
            "last_name": self.last_name,
        }

    @staticmethod
    def from_json(json):
        # TODO: Fix NoneType parsing of salutation
        if isinstance(json.get('salutation'), list):
            salutation_value = None
        else:
            salutation_value = json.get('salutation')
        return Name(
            first_name=json.get('first_name'),
            middle_name=json.get('middle_name'),
            last_name=json.get('last_name'),
            salutation=Salutation(salutation_value) if salutation_value else None,
        )

    @property
    def full_name_with_first_and_last_name(self):
        return ''.join(
            [
                "{}".format(self.first_name) if self.first_name else '',
                " {}".format(self.last_name) if self.last_name else '',
            ]
        )

    def __str__(self):
        return str(self.full_name)

    def __hash__(self):
        return hash(str(self))

    def has_case_insensitive_match(self, other):
        return self.full_name.lower() == other.full_name.lower()

    @staticmethod
    def create_empty_instance():
        return Name(
            first_name=NotAssigned,
            middle_name=NotAssigned,
            last_name=NotAssigned,
            salutation=NotAssigned,
        )


class PhoneNumber(object):
    """
    Phone number with country code
    """

    __slots__ = ('number', 'country_code')

    def __init__(self, number, country_code=None):
        """

        :param str number:
        :param str country_code:
        """
        self.number = safe_strip(number)
        self.country_code = safe_strip(country_code) if country_code else "+91"

    @classmethod
    def create_empty_instance(cls):
        return cls(number=NotAssigned, country_code=NotAssigned)

    def __eq__(self, other):
        if isinstance(self, other.__class__):
            return (
                self.number == other.number and self.country_code == other.country_code
            )
        return False

    def __hash__(self):
        return hash("{}{}".format(self.country_code, self.number))

    def to_json(self):
        return {"number": self.number, "country_code": self.country_code}

    @staticmethod
    def from_json(json):
        return PhoneNumber(
            number=json.get('number'), country_code=json.get('country_code')
        )

    def __str__(self):
        return self.number


class IDProof(object):
    __slots__ = (
        'id_proof_type',
        'id_number',
        'id_kyc_url',
        'id_proof_country_code',
        'issued_date',
        'issued_place',
        'attachment_id',
    )

    def __init__(
        self,
        id_proof_type,
        id_number,
        id_kyc_url=NotAssigned,
        id_proof_country_code=NotAssigned,
        issued_date=NotAssigned,
        issued_place=NotAssigned,
        attachment_id=NotAssigned,
    ):
        """

        :param id_proof_type:
        :param id_number:
        :param issued_date:
        :param issued_place:
        :param id_kyc_url:
        :param id_proof_country_code:
        :param attachment_id
        """
        self.id_proof_type = id_proof_type
        self.id_number = safe_strip(id_number)
        self.id_kyc_url = safe_strip(id_kyc_url)
        self.id_proof_country_code = id_proof_country_code
        self.issued_date = issued_date
        self.issued_place = issued_place
        self.attachment_id = attachment_id

    def __eq__(self, other):
        return (
            isinstance(other, IDProof)
            and self.id_proof_type == other.id_proof_type
            and self.id_number == other.id_number
            and self.id_kyc_url == other.id_kyc_url
            and self.id_proof_country_code == other.id_proof_country_code
            and self.issued_date == other.issued_date
            and self.issued_place == other.issued_place
            and self.attachment_id == other.attachment_id
        )

    def __str__(self):
        return (
            "ID Proof Type: {0}, ID Number: {1}, ID Proof Country Code: {2} ID Proof Issued date {3} ID Proof "
            "Issued place {4} Attachment id {5}".format(
                self.id_proof_type,
                self.id_number,
                self.id_proof_country_code,
                self.issued_date,
                self.issued_place,
                self.attachment_id,
            )
        )

    def to_json(self):
        return {
            "id_proof_type": self.id_proof_type,
            "id_number": self.id_number,
            "id_kyc_url": self.id_kyc_url if self.id_kyc_url else None,
            "id_proof_country_code": self.id_proof_country_code,
            "issued_date": str(self.issued_date) if self.issued_date else None,
            "issued_place": self.issued_place,
            "attachment_id": self.attachment_id,
        }

    @staticmethod
    def from_json(json):
        return IDProof(
            id_proof_type=json.get('id_proof_type'),
            id_number=json.get('id_number'),
            id_kyc_url=json.get('id_kyc_url'),
            id_proof_country_code=json.get('id_proof_country_code'),
            issued_date=dateutils.isoformat_str_to_datetime(json.get('issued_date'))
            if json.get('issued_date')
            else None,
            issued_place=json.get('issued_place'),
            attachment_id=json.get('attachment_id'),
        )

    @staticmethod
    def create_empty_instance():
        gst_details = IDProof(id_proof_type=NotAssigned, id_number=NotAssigned)
        return gst_details


class Address(object):
    """
    An Address value object
    """

    __slots__ = ('field_1', 'field_2', 'city', 'state', 'country', 'pincode')

    def __init__(self, field_1, field_2, city, state, country, pincode):
        """

        :param str field_1:
        :param str field_2:
        :param str city:
        :param str state:
        :param str country:
        :param str pincode:
        """
        self.field_1 = safe_strip(field_1)
        self.field_2 = safe_strip(field_2)
        self.city = city
        self.state = state
        self.country = country
        self.pincode = pincode

    def __eq__(self, other):
        if isinstance(self, other.__class__):
            return (
                self.field_1 == other.field_1
                and self.field_2 == other.field_2
                and self.city == other.city
                and self.state == other.state
                and self.country == other.country
                and self.pincode == other.pincode
            )
        return False

    @property
    def field1(self):
        return self.field_1

    @property
    def field2(self):
        return self.field_2

    def copy(self):
        return Address(
            field_1=self.field_1,
            field_2=self.field_2,
            city=self.city,
            state=self.state,
            country=self.country,
            pincode=self.pincode,
        )

    def to_json(self):
        return {
            "city": self.city,
            "state": self.state,
            "country": self.country,
            "pincode": self.pincode,
            "field_1": self.field_1,
            "field_2": self.field_2,
        }

    @staticmethod
    def from_json(json):
        return Address(
            field_1=json.get('field_1'),
            field_2=json.get('field_2'),
            city=json.get('city'),
            state=json.get('state'),
            country=json.get('country'),
            pincode=json.get('pincode'),
        )

    @property
    def address(self):
        return ''.join(
            [
                "{},".format(self.field_1) if self.field_1 else '',
                " {},".format(self.field_2) if self.field_1 else '',
                "city: {},".format(self.city) if self.city else '',
                "state: {},".format(self.state) if self.state else '',
                "country: {},".format(self.country) if self.country else '',
                "pincode: {}".format(self.pincode) if self.pincode else '',
            ]
        )

    def replace(self, obj):
        self.city = obj.city
        self.country = obj.country
        self.field_1 = obj.field1
        self.field_2 = obj.field2
        self.pincode = obj.pincode
        self.state = obj.state

    def __str__(self):
        return self.address

    @staticmethod
    def create_empty_instance():
        return Address(
            field_1=NotAssigned,
            field_2=NotAssigned,
            city=NotAssigned,
            state=NotAssigned,
            country=NotAssigned,
            pincode=NotAssigned,
        )


class GSTDetails(object):
    """
    GST Details
    """

    __slots__ = ('legal_name', 'gstin_num', 'address', 'is_sez', 'has_lut')

    def __init__(
        self, address, legal_name, gstin_num=NotAssigned, is_sez=False, has_lut=False
    ):
        """

        :param str legal_name:
        :param str gstin_num:
        :param ths_common.value_objects.Address address:
        :param bool is_sez:
        :param bool has_lut:
        """
        self.legal_name = safe_strip(legal_name)
        self.gstin_num = safe_strip(gstin_num)
        self.address = address
        self.is_sez = is_sez
        self.has_lut = has_lut

    def replace(self, obj):
        self.gstin_num = obj.gstin_num
        self.legal_name = obj.legal_name
        self.address.replace(obj.address)
        self.is_sez = obj.is_sez
        self.has_lut = obj.has_lut

    def to_json(self):
        return {
            "legal_name": self.legal_name,
            "gstin_num": self.gstin_num,
            "address": self.address.to_json() if self.address else None,
            "is_sez": self.is_sez,
            "has_lut": self.has_lut,
        }

    @staticmethod
    def from_json(json):
        return GSTDetails(
            legal_name=json.get('legal_name'),
            gstin_num=json.get('gstin_num'),
            address=Address.from_json(json.get('address'))
            if json.get('address')
            else None,
            is_sez=json.get('is_sez', False),
            has_lut=json.get('has_lut', False),
        )

    def __str__(self):
        return (
            "GSTIN: {0} | Address: {1} | Legal Name: {2} | SEZ: {3} | LUT: {4}".format(
                self.gstin_num,
                str(self.address),
                self.legal_name,
                self.is_sez,
                self.has_lut,
            )
        )

    @staticmethod
    def create_empty_instance():
        gst_details = GSTDetails(
            legal_name=NotAssigned, gstin_num=NotAssigned, address=NotAssigned
        )
        return gst_details

    @staticmethod
    def create_from_legal_details(legal_details):
        return GSTDetails(
            legal_name=legal_details.legal_name,
            address=legal_details.address,
            gstin_num=legal_details.tin,
            is_sez=legal_details.is_sez,
            has_lut=legal_details.has_lut,
        )

    def __eq__(self, other):
        return (
            isinstance(other, GSTDetails)
            and self.legal_name == other.legal_name
            and self.gstin_num == other.gstin_num
            and self.address == other.address
            and self.is_sez == other.is_sez
            and self.has_lut == other.has_lut
        )

    def has_same_tax_determiners(self, other):
        if isinstance(other, GSTDetails):
            return bool(self.is_sez) == bool(other.is_sez) and bool(
                self.has_lut
            ) == bool(other.has_lut)
        if other is None:
            return not (self.is_sez or self.has_lut)
        return False


class BookingSource(object):
    def __init__(self, channel_code, application_code, subchannel_code):
        """

        :param str channel_code:
        :param str subchannel_code:
        :param str application_code:
        """
        self.channel_code = safe_strip(channel_code)
        self.subchannel_code = safe_strip(subchannel_code)
        self.application_code = safe_strip(application_code)

    @property
    def channel_id(self):
        return self.channel_code

    @property
    def sub_channel_id(self):
        return self.subchannel_code

    @property
    def application_id(self):
        return self.application_code

    @staticmethod
    def create_empty_instance():
        source = BookingSource(
            channel_code=NotAssigned,
            application_code=NotAssigned,
            subchannel_code=NotAssigned,
        )
        return source

    def is_walk_in_channel(self):
        return self.subchannel_code == 'walk-in'

    def __eq__(self, other):
        return self.__dict__ == other.__dict__


class TaxDetail(object):
    __slots__ = ('tax_type', 'percentage', 'amount')

    def __init__(self, tax_type, percentage, amount):
        """
        NOTE: In Tax Service, tax_type can be percent/flat. In CRS, we used `tax_type` as `cgst/sgst`,
        which has moved to `tax_code` key in tax response.

        If tax_type was flat, we'll keep percentage as `None` in `TaxDetail`. So, `None` value for `percentage`
        will mean that `TaxType` is FLAT (This information is no where needed to be stored in CRS)

        We keep storing `tax_code` (from new tax api) in `tax_type`, without having to introduce `tax_code` column in
        CRS, and breaking all client

        :param str tax_type:
        :param Decimal percentage:
        :param treebo_commons.money.Money amount:
        """
        self.tax_type = tax_type
        if (
            percentage == NotAssigned
            or percentage is None
            or isinstance(percentage, Decimal)
        ):
            self.percentage = percentage
        else:
            self.percentage = Decimal(str(percentage))
        self.amount = (
            amount
            if amount == NotAssigned or isinstance(amount, Money)
            else Money(amount)
        )

    @property
    def tax_amount(self):
        return self.amount

    @classmethod
    def from_string(cls, string):
        parameters = string.split(':')
        currency = (
            CurrencyType(parameters[3]) if len(parameters) == 4 else CurrencyType.INR
        )
        return TaxDetail(
            tax_type=parameters[0],
            percentage=Decimal(parameters[1]) if parameters[1] != 'NA' else None,
            amount=Money(parameters[2], currency),
        )

    def __str__(self):
        return "{}:{}:{}:{}".format(
            self.tax_type,
            self.percentage if self.percentage is not None else 'NA',
            self.amount.amount,
            self.amount.currency.value,
        )

    def is_same_type(self, tax_detail):
        return (
            self.tax_type == tax_detail.tax_type
            and self.percentage == tax_detail.percentage
        )

    def is_same_tax_type(self, tax_detail):
        return self.tax_type == tax_detail.tax_type

    def to_json(self):
        return dict(
            tax_type=self.tax_type,
            percentage=self.percentage if self.percentage is not None else 'NA',
            amount=self.amount.amount,
        )

    def negate(self):
        return TaxDetail(self.tax_type, self.percentage, Money(-self.amount))

    def __copy__(self):
        return TaxDetail(self.tax_type, self.percentage, Money(self.amount))

    def __eq__(self, other):
        return (
            self.tax_type == other.tax_type
            and self.percentage == other.percentage
            and self.amount == other.amount
        )

    @staticmethod
    def create_empty_instance():
        return TaxDetail(
            tax_type=NotAssigned, percentage=NotAssigned, amount=NotAssigned
        )


class ItemCode(object):
    __slots__ = ('code_type', 'value')

    def __init__(self, code_type, value):
        """

        :param ItemCodeTypes code_type:
        :param str value:
        """
        if code_type not in ItemCodeTypes.all_options():
            raise AttributeError(
                "ItemCodeDetails::Invalid ItemCode Type: {}".format(code_type)
            )

        self.code_type = code_type
        self.value = safe_strip(value)

    @classmethod
    def from_string(cls, string):
        parameters = string.split(':')
        return ItemCode(ItemCodeTypes(parameters[0]), parameters[1])

    def __str__(self):
        return "{}:{}".format(self.code_type.value, self.value)


class ChargeItemDetails(object):
    __slots__ = ('details',)

    def __init__(self, details):
        """

        :param dict details:
        """
        self.details = details if details else dict()

    def dict(self):
        return self.details


class RoomChargeItemDetails(ChargeItemDetails):
    __slots__ = (
        'room_type_code',
        'occupancy',
        'room_type',
        'room_no',
        'charged_entity_id',
        'rate_plan_reference_id',
        'rate_plan_name',
        'rate_plan_code',
        'room_stay_id',
    )

    def __init__(
        self,
        room_type_code,
        occupancy,
        room_type=None,
        room_no=None,
        charged_entity_id=None,
        rate_plan_reference_id=None,
        rate_plan_name=None,
        rate_plan_code=None,
        room_stay_id=None,
    ):
        """

        :param str room_type_code:
        :param int occupancy:
        :param str room_type:
        :param str room_no:
        :param int room_stay_id:
        """
        self.occupancy = occupancy
        self.room_type_code = room_type_code
        self.room_type = room_type
        self.room_no = room_no
        self.charged_entity_id = charged_entity_id
        self.rate_plan_reference_id = rate_plan_reference_id
        self.rate_plan_name = rate_plan_name
        self.rate_plan_code = rate_plan_code
        self.room_stay_id = room_stay_id

    def update_room_no(self, room_no):
        self.room_no = room_no

    def update_occupancy(self, number_of_checked_in_guests):
        self.occupancy = number_of_checked_in_guests

    def update_room_type(self, room_type):
        self.room_type = room_type

    def update_room_stay_id(self, room_stay_id):
        self.room_stay_id = room_stay_id

    def update_charged_entity_id(self, charged_entity_id):
        self.charged_entity_id = charged_entity_id

    def update_rate_plan_name(self, rate_plan_name):
        self.rate_plan_name = rate_plan_name

    def update_rate_plan_code(self, rate_plan_code):
        self.rate_plan_code = rate_plan_code

    def update_rate_plan_reference_id(self, rate_plan_reference_id):
        self.rate_plan_reference_id = rate_plan_reference_id

    def dict(self):
        return dict(
            occupancy=self.occupancy,
            room_type=self.room_type,
            room_no=self.room_no,
            room_type_code=self.room_type_code,
            charged_entity_id=self.charged_entity_id,
            rate_plan_reference_id=self.rate_plan_reference_id,
            rate_plan_name=self.rate_plan_name,
            rate_plan_code=self.rate_plan_code,
            room_stay_id=self.room_stay_id,
        )

    @classmethod
    def from_dict(cls, dict_val):
        return cls(
            occupancy=dict_val.get('occupancy'),
            room_type=dict_val.get('room_type'),
            room_no=dict_val.get('room_no'),
            room_type_code=dict_val.get('room_type_code'),
            charged_entity_id=dict_val.get('charged_entity_id'),
            rate_plan_reference_id=dict_val.get('rate_plan_reference_id'),
            rate_plan_name=dict_val.get('rate_plan_name'),
            rate_plan_code=dict_val.get('rate_plan_code'),
            room_stay_id=dict_val.get('room_stay_id'),
        )


class ExpenseChargeItemDetails(ChargeItemDetails):
    __slots__ = (
        'room_stay_id',
        'room_type_code',
        'occupancy',
        'room_type',
        'room_no',
        'extra_attributes',
        'is_pos_charge',
        'interface_id',
        'pos_order_id',
        'pos_order_number',
        'pos_bill_id',
        'pos_bill_number',
        'seller_name',
        'pos_item_quantity',
        'is_transferred_to_other_booking',
        'is_touche_pos_charge',
        'revenue_center',
        'serving_time',
        'workstation_id',
        'waiter_id',
    )

    def __init__(
        self,
        room_stay_id=None,
        room_no=None,
        room_type=None,
        room_type_code=None,
        occupancy=None,
        extra_attributes=None,
        is_pos_charge=False,
        interface_id=None,
        pos_order_id=None,
        pos_order_number=None,
        pos_bill_id=None,
        pos_bill_number=None,
        seller_name=None,
        pos_item_quantity=None,
        is_transferred_to_other_booking=False,
        is_touche_pos_charge=False,
        revenue_center=None,
        serving_time=None,
        workstation_id=None,
        waiter_id=None,
    ):
        """

        :param str room_no:
        :param str room_type:
        :param str room_type_code:
        :param int occupancy:
        :param dict extra_attributes:
        """
        self.room_stay_id = room_stay_id
        self.occupancy = occupancy
        self.room_type = room_type
        self.room_type_code = room_type_code
        self.room_no = room_no
        self.extra_attributes = extra_attributes
        self.is_pos_charge = is_pos_charge
        self.interface_id = interface_id
        self.pos_order_id = pos_order_id
        self.pos_order_number = pos_order_number
        self.pos_bill_id = pos_bill_id
        self.pos_bill_number = pos_bill_number
        self.seller_name = seller_name
        self.pos_item_quantity = pos_item_quantity
        self.is_transferred_to_other_booking = is_transferred_to_other_booking
        self.is_touche_pos_charge = is_touche_pos_charge
        self.revenue_center = revenue_center
        self.serving_time = serving_time
        self.waiter_id = waiter_id
        self.workstation_id = workstation_id

    def dict(self):
        ret_val = dict(
            occupancy=self.occupancy,
            room_type=self.room_type,
            room_type_code=self.room_type_code,
            room_no=self.room_no,
            room_stay_id=self.room_stay_id,
            is_pos_charge=self.is_pos_charge,
            is_transferred_to_other_booking=self.is_transferred_to_other_booking,
        )

        if self.is_pos_charge:
            ret_val.update(
                dict(
                    pos_order_id=self.pos_order_id,
                    pos_order_number=self.pos_order_number,
                    pos_bill_id=self.pos_bill_id,
                    pos_bill_number=self.pos_bill_number,
                    seller_name=self.seller_name,
                    pos_item_quantity=self.pos_item_quantity,
                )
            )

        if self.is_touche_pos_charge:
            ret_val.update(
                dict(
                    interface_id=self.interface_id,
                    pos_bill_id=self.pos_bill_id,
                    revenue_center=self.revenue_center,
                    waiter_id=self.waiter_id,
                    serving_time=self.serving_time,
                    workstation_id=self.workstation_id,
                    is_touche_pos_charge=self.is_touche_pos_charge,
                )
            )
        if self.extra_attributes:
            ret_val.update(self.extra_attributes)
        return ret_val


class ChargeItem(object):
    __slots__ = ('item_id', 'name', 'sku_category_id', 'details', 'hsn_sac_code')

    def __init__(
        self, name, sku_category_id, details=None, hsn_code=None, item_id=None
    ):
        """

        :param str name:
        :param str sku_category_id:
        :param ChargeItemDetails details:
        """
        # TODO validate dict
        if not sku_category_id or not name:
            raise ValidationException(
                description='Item name or sku_category_id cannot be none'
            )

        if not details or isinstance(details, dict):
            details = ChargeItemDetails(details)

        self.item_id = item_id
        self.name = name
        self.sku_category_id = sku_category_id
        self.details = details.dict()
        self.hsn_sac_code = hsn_code

    @property
    def item_code(self):
        if not isinstance(self.hsn_sac_code, ItemCode):
            return ItemCode(ItemCodeTypes.HSN, self.hsn_sac_code)

        return self.hsn_sac_code

    @item_code.setter
    def item_code(self, value):
        self.hsn_sac_code = value

    def update_name(self, name):
        self.name = name

    def update_details(self, details):
        if not details or isinstance(details, dict):
            details = ChargeItemDetails(details)
        self.details = details.dict()

    def is_pos_charge(self):
        if self.details:
            return self.details.get('is_pos_charge', False)

    def is_transferred_to_other_booking(self):
        if self.details:
            return self.details.get('is_transferred_to_other_booking', False)

    def pos_order_id(self):
        if self.details:
            return self.details.get('pos_order_id', None)

    def pos_order_number(self):
        if self.details:
            return self.details.get('pos_order_number', None)

    def pos_seller_name(self):
        if self.details:
            return self.details.get('seller_name', None)


class DateWiseAvailabilityPerRoomType(object):
    __slots__ = ('room_type_id', 'date_wise_availability_change')

    def __init__(self, room_type_id, date_wise_availability_change):
        self.room_type_id = room_type_id
        self.date_wise_availability_change = date_wise_availability_change


class DNRTypeValueObject(object):
    __slots__ = ('type', 'subtype', 'comments')

    def __init__(self, type, subtype, comments):
        """

        :param DNRType type:
        :param DNRSubType subtype:
        :param str comments:
        """
        if type not in DNRType.all():
            raise ValidationException(message="Invalid DNR Type received")

        if subtype not in DNRSubType.all():
            raise ValidationException(
                message="Invalid DNR Subtype received for DNR Type: {0}".format(type)
            )

        if type is DNRType.MAINTENANCE:
            if subtype not in DNRMaintenanceSubType.all():
                raise ValidationException(
                    message="Invalid DNR Sub Type: {0} received for DNR Type: {1}".format(
                        subtype, type
                    )
                )
        elif type is DNRType.NON_MAINTENANCE:
            if subtype not in DNRNonMaintenanceSubType.all():
                raise ValidationException(
                    message="Invalid DNR Sub Type: {0} received for DNR Type: {1}".format(
                        subtype, type
                    )
                )
        elif type is DNRType.INACTIVE_ROOM:
            if subtype not in DNRInactiveRoomSubType.all():
                raise ValidationException(
                    message="Invalid DNR Sub Type: {0} received for DNR Type: {1}".format(
                        subtype, type
                    )
                )

        if subtype == DNRSubType.OTHERS and not comments:
            raise ValidationException(
                message="Comments is mandatory when DNR Subtype is Others"
            )

        self.type = type
        self.subtype = subtype
        self.comments = safe_strip(comments)

    def __str__(self):
        return "DNRType {t} ({st})".format(t=self.type, st=self.subtype)


class DateRange(object):
    def __init__(self, start_date, end_date):
        """

        :param datetime start_date:
        :param datetime end_date:
        """
        self.start_date = start_date
        self.end_date = end_date

    def as_list(self):
        return date_range(self.start_date, self.end_date, end_inclusive=True)


class BillParentInfo(object):
    def __init__(self, details):
        """

        :param dict details:
        """
        self.details = details if details else dict()

    @staticmethod
    def from_json(json):
        parent_type = json.get('parent_type')
        data = json.get('data')
        if parent_type == "BookingBillParentInfo":
            return BookingBillParentInfo.from_json(data)
        elif parent_type == "BillParentInfo":
            return BillParentInfo(details=data)

    def to_json(self):
        details = dict()
        for k, v in self.details.items():
            if isinstance(v, datetime):
                details[k] = dateutils.isoformat_datetime(v)
            else:
                details[k] = v
        return {"parent_type": "BillParentInfo", "data": details}

    def dict(self):
        return self.details


class BookingBillParentInfo(BillParentInfo):
    def __init__(
        self,
        booking_id,
        reference_number,
        creation_date,
        checkin_date=None,
        checkout_date=None,
        actual_checkin_date=None,
        actual_checkout_date=None,
    ):
        """

        :param str booking_id:
        :param str reference_number:
        :param datetime creation_date:
        :param datetime checkin_date:
        :param datetime checkout_date:
        """
        self.booking_id = booking_id
        self.reference_number = reference_number
        self.creation_date = creation_date
        self.checkin_date = checkin_date
        self.checkout_date = checkout_date
        self.actual_checkin_date = actual_checkin_date
        self.actual_checkout_date = actual_checkout_date

    def to_json(self):
        return {
            "parent_type": "BookingBillParentInfo",
            "data": {
                "booking_id": self.booking_id,
                "checkin_date": dateutils.isoformat_datetime(self.checkin_date)
                if hasattr(self, 'checkin_date') and self.checkin_date
                else None,
                "checkout_date": dateutils.isoformat_datetime(self.checkout_date)
                if hasattr(self, 'checkout_date') and self.checkout_date
                else None,
                "creation_date": dateutils.isoformat_datetime(self.creation_date)
                if hasattr(self, 'creation_date') and self.creation_date
                else None,
                "reference_number": self.reference_number,
                "actual_checkin_date": dateutils.isoformat_datetime(
                    self.actual_checkin_date
                )
                if hasattr(self, 'actual_checkin_date') and self.actual_checkin_date
                else None,
                "actual_checkout_date": dateutils.isoformat_datetime(
                    self.actual_checkout_date
                )
                if hasattr(self, 'actual_checkout_date') and self.actual_checkout_date
                else None,
            },
        }

    @staticmethod
    def from_json(json):
        creation_date = json.get('creation_date')
        checkin_date = json.get('checkin_date')
        checkout_date = json.get('checkout_date')
        actual_checkin_date = json.get('actual_checkin_date')
        actual_checkout_date = json.get('actual_checkout_date')
        return BookingBillParentInfo(
            booking_id=json.get('booking_id'),
            reference_number=json.get('reference_number'),
            creation_date=dateutils.isoformat_str_to_datetime(creation_date)
            if creation_date
            else None,
            checkin_date=dateutils.isoformat_str_to_datetime(checkin_date)
            if checkin_date
            else None,
            checkout_date=dateutils.isoformat_str_to_datetime(checkout_date)
            if checkout_date
            else None,
            actual_checkin_date=dateutils.isoformat_str_to_datetime(actual_checkin_date)
            if actual_checkin_date
            else None,
            actual_checkout_date=dateutils.isoformat_str_to_datetime(
                actual_checkout_date
            )
            if actual_checkout_date
            else None,
        )

    def dict(self):
        return dict(
            booking_id=self.booking_id,
            reference_number=self.reference_number,
            creation_date=self.creation_date,
            checkin_date=self.checkin_date,
            checkout_date=self.checkout_date,
        )


class VendorDetails(object):
    def __init__(
        self,
        hotel_id,
        name,
        state_code,
        gst_details,
        phone,
        email,
        url,
        address=None,
        legal_signature=None,
        vendor_id=None,
        is_test=False,
        fssai_number=None,
    ):
        """

        :param str hotel_id:
        :param str name:
        :param str state_code:
        :param GSTDetails gst_details:
        :param PhoneNumber phone:
        :param str email:
        :param str url:
        :param Address address:
        :param str legal_signature:
        :param str vendor_id:
        :param str fssai_number:
        """
        self.hotel_name = name
        self.hotel_id = hotel_id
        self.vendor_name = name
        self.vendor_id = vendor_id if vendor_id else hotel_id
        self.state_code = state_code
        self.gst_details = gst_details
        self.phone = phone
        self.email = safe_strip(email)
        self.url = url
        self.address = address
        self.legal_signature = legal_signature
        self.is_test = is_test
        self.fssai_number = fssai_number

    def to_json(self):
        return {
            "hotel_name": self.hotel_name if self.hotel_id else None,
            "hotel_id": self.hotel_id,
            "vendor_name": self.vendor_name,
            "vendor_id": self.vendor_id,
            "state_code": self.state_code,
            "gst_details": self.gst_details.to_json() if self.gst_details else None,
            "phone": self.phone.to_json() if self.phone else None,
            "email": self.email,
            "url": self.url,
            "address": self.address.to_json() if self.address else None,
            "legal_signature": self.legal_signature,
            "is_test": self.is_test,
            "fssai_number": self.fssai_number,
        }

    @staticmethod
    def from_json(json):
        return VendorDetails(
            hotel_id=json.get('vendor_id') or json.get('hotel_id'),
            name=json.get('vendor_name') or json.get('hotel_name'),
            state_code=json.get('state_code'),
            gst_details=GSTDetails.from_json(json.get('gst_details'))
            if json.get('gst_details')
            else None,
            phone=PhoneNumber.from_json(json.get('phone'))
            if json.get('phone')
            else None,
            email=json.get('email'),
            url=json.get('url'),
            address=Address.from_json(json.get('address'))
            if json.get('address')
            else None,
            legal_signature=json.get('legal_signature'),
            vendor_id=json.get('vendor_id'),
            is_test=json.get('is_test'),
            fssai_number=json.get('fssai_number'),
            gstin_number=json.get('gstin_number'),
        )


class InvoiceBillToInfo(object):
    def __init__(
        self,
        customer_id,
        name,
        address,
        gstin,
        phone,
        email,
        is_sez=False,
        has_lut=False,
        external_ref_id=None,
    ):
        """

        :param str customer_id:
        :param Name name:
        :param Address address:
        :param str gstin:
        :param PhoneNumber phone:
        :param str email:
        :param str external_ref_id:
        """
        self.customer_id = customer_id
        self.name = name
        self.address = address
        self.gstin_num = gstin
        self.phone = phone
        self.email = email
        self.is_sez = is_sez
        self.has_lut = has_lut
        self.external_ref_id = external_ref_id

    @staticmethod
    def create_new(bill_to_id, booking_aggregate):
        bill_to = booking_aggregate.get_customer(bill_to_id)
        booking_owner = booking_aggregate.get_booking_owner()
        is_booking_owner = bill_to.customer_id == booking_owner.customer_id

        is_sez = (
            booking_owner.gst_details.is_sez if booking_owner.gst_details else False
        )
        has_lut = (
            booking_owner.gst_details.has_lut if booking_owner.gst_details else False
        )

        if is_booking_owner:
            bill_to_name = (
                Name(bill_to.gst_details.legal_name)
                if bill_to.gst_details and bill_to.gst_details.legal_name
                else bill_to.name
            )

            bill_to_address = (
                bill_to.gst_details.address
                if bill_to.gst_details and bill_to.gst_details.address
                else bill_to.address
            )

            bill_to_gstin = (
                bill_to.gst_details.gstin_num
                if bill_to.gst_details and bill_to.gst_details.gstin_num
                else ''
            )

        else:
            bill_to_name = bill_to.name
            bill_to_address = (
                booking_owner.gst_details.address
                if booking_owner.gst_details and booking_owner.gst_details.address
                else None
            )

            bill_to_gstin = (
                booking_owner.gst_details.gstin_num
                if booking_owner.gst_details and booking_owner.gst_details.gstin_num
                else None
            )

        return InvoiceBillToInfo(
            customer_id=bill_to.customer_id,
            name=bill_to_name,
            address=bill_to_address,
            gstin=bill_to_gstin,
            phone=bill_to.phone,
            email=bill_to.email,
            is_sez=is_sez,
            has_lut=has_lut,
            external_ref_id=bill_to.external_ref_id,
        )

    @staticmethod
    def create_from_billed_entity(billed_entity, booking_aggregate):
        booking_owner = booking_aggregate.get_booking_owner()
        billed_entity_category = billed_entity.category

        if billed_entity_category == BilledEntityCategory.BOOKER_COMPANY:
            company_details = booking_aggregate.get_company_details()
            customer_id = (
                booking_owner.customer_id
                if booking_owner.company_billed_entity_id
                == company_details.billed_entity_id
                else ''
            )
            phone = (
                company_details.legal_details.phone
                if company_details.legal_details
                else None
            ) or booking_owner.phone
            email = (
                company_details.legal_details.email
                if company_details.legal_details
                else None
            ) or booking_owner.email
            bill_to_name = (
                Name(company_details.legal_details.legal_name)
                if company_details.legal_details
                and company_details.legal_details.legal_name
                else booking_owner.name
            )

            bill_to_address = (
                company_details.legal_details.address
                if company_details.legal_details
                and company_details.legal_details.address
                else None
            )

            bill_to_gstin = (
                company_details.legal_details.tin
                if company_details.legal_details and company_details.legal_details.tin
                else ''
            )
            is_sez = (
                company_details.legal_details.is_sez
                if company_details.legal_details
                else False
            )
            has_lut = (
                company_details.legal_details.has_lut
                if company_details.legal_details
                else False
            )
            external_ref_id = (
                company_details.legal_details.external_reference_id
                if company_details.legal_details
                else False
            )

        elif billed_entity_category == BilledEntityCategory.TRAVEL_AGENT:
            travel_agent_details = booking_aggregate.get_travel_agent_details()
            customer_id = (
                booking_owner.customer_id
                if booking_owner.company_billed_entity_id
                == travel_agent_details.billed_entity_id
                else ''
            )
            phone = (
                travel_agent_details.legal_details.phone
                if travel_agent_details.legal_details
                else None
            ) or booking_owner.phone
            email = (
                travel_agent_details.legal_details.email
                if travel_agent_details.legal_details
                else None
            ) or booking_owner.email
            bill_to_name = (
                Name(travel_agent_details.legal_details.legal_name)
                if travel_agent_details.legal_details
                and travel_agent_details.legal_details.legal_name
                else booking_owner.name
            )

            bill_to_address = (
                travel_agent_details.legal_details.address
                if travel_agent_details.legal_details
                and travel_agent_details.legal_details.address
                else None
            )

            bill_to_gstin = (
                travel_agent_details.legal_details.tin
                if travel_agent_details.legal_details
                and travel_agent_details.legal_details.tin
                else ''
            )
            is_sez = (
                travel_agent_details.legal_details.is_sez
                if travel_agent_details.legal_details
                else False
            )
            has_lut = (
                travel_agent_details.legal_details.has_lut
                if travel_agent_details.legal_details
                else False
            )
            external_ref_id = (
                travel_agent_details.legal_details.external_reference_id
                if travel_agent_details.legal_details
                else False
            )

        elif billed_entity_category == BilledEntityCategory.FRANCHISER:
            bill_to = booking_aggregate.get_customer_for_billed_entity(
                billed_entity.billed_entity_id
            )
            customer_id = bill_to.customer_id
            email = bill_to.email
            phone = bill_to.phone
            external_ref_id = bill_to.external_ref_id

            gst_details = bill_to.gst_details
            bill_to_name = gst_details.legal_name if gst_details else bill_to.name
            bill_to_address = gst_details.address if gst_details else bill_to.address
            bill_to_gstin = (
                bill_to.gst_details.gstin_num if bill_to.gst_details else None
            )
            is_sez = gst_details.is_sez if gst_details else False
            has_lut = gst_details.has_lut if gst_details else False

        else:
            bill_to = booking_aggregate.get_customer_for_billed_entity(
                billed_entity.billed_entity_id
            )
            bill_to_name = bill_to.name
            customer_id = bill_to.customer_id
            email = bill_to.email
            phone = bill_to.phone
            bill_to_address = bill_to.address

            bill_to_gstin = None
            gst_details = booking_owner.gst_details
            is_sez = gst_details.is_sez if gst_details else False
            has_lut = gst_details.has_lut if gst_details else False
            external_ref_id = booking_owner.external_ref_id
            if booking_aggregate.booking.seller_model == SellerType.RESELLER:
                booker = (
                    booking_aggregate.get_travel_agent_details()
                    or booking_aggregate.get_company_details()
                )
                if booker and booker.legal_details:
                    external_ref_id = booker.legal_details.external_reference_id

        return InvoiceBillToInfo(
            customer_id=customer_id,
            name=bill_to_name,
            address=bill_to_address,
            gstin=bill_to_gstin,
            phone=phone,
            email=email,
            is_sez=is_sez,
            has_lut=has_lut,
            external_ref_id=external_ref_id,
        )

    @classmethod
    def create_empty_instance(cls):
        return cls(
            customer_id=NotAssigned,
            name=NotAssigned,
            address=NotAssigned,
            gstin=NotAssigned,
            phone=NotAssigned,
            email=NotAssigned,
            external_ref_id=NotAssigned,
        )

    def __eq__(self, other):
        """Overrides the default implementation"""
        if isinstance(self, other.__class__):
            return (
                self.customer_id == other.customer_id
                and self.name == other.name
                and self.address == other.address
                and self.gstin_num == other.gstin_num
                and self.phone == other.phone
                and self.email == other.email
            )
        return False

    def to_json(self):
        return {
            "customer_id": self.customer_id,
            "name": self.name.to_json()
            if self.name and isinstance(self.name, Name)
            else self.name,
            "address": self.address.to_json() if self.address else None,
            "gstin_num": self.gstin_num,
            "phone": self.phone.to_json() if self.phone else None,
            "email": self.email,
            "is_sez": self.is_sez,
            "has_lut": self.has_lut,
            "external_ref_id": self.external_ref_id,
        }

    def get_name(self):
        if self.name and isinstance(self.name, Name):
            first_name = self.name.to_json().get('first_name')
            last_name = self.name.to_json().get('last_name')
        else:
            first_name = self.name
            last_name = ''
        return first_name, last_name

    @staticmethod
    def from_json(json):
        name = json.get('name')
        if name and isinstance(name, dict):
            name = Name.from_json(name)
        elif name and isinstance(name, str):
            name = Name(name)

        return InvoiceBillToInfo(
            customer_id=json.get('customer_id'),
            name=name,
            address=Address.from_json(json.get('address'))
            if json.get('address')
            else None,
            gstin=json.get('gstin_num'),
            phone=PhoneNumber.from_json(json.get('phone'))
            if json.get('phone')
            else None,
            email=json.get('email'),
            has_lut=json.get('has_lut'),
            is_sez=json.get('is_sez'),
            external_ref_id=json.get('external_ref_id'),
        )

    def dict(self):
        return dict(
            customer_id=self.customer_id,
            name=self.name,
            address=self.address,
            gstin=self.gstin_num,
            phone=self.phone,
            email=self.email,
        )


class BankDetails(object):
    def __init__(
        self,
        account_name,
        account_number,
        bank,
        branch,
        id,
        ifsc_code,
        branch_code,
        swift_code,
        type,
    ):
        """

        :param account_name:
        :param account_number:
        :param bank:
        :param branch:
        :param id:
        :param ifsc_code:
        :param type:
        """
        self.account_name = account_name
        self.account_number = account_number
        self.bank = bank
        self.branch = branch
        self.id = id
        self.ifsc_code = ifsc_code
        self.branch_code = branch_code
        self.swift_code = swift_code
        self.type = type

    @classmethod
    def create_empty_instance(cls):
        return cls(
            account_name=NotAssigned,
            account_number=NotAssigned,
            bank=NotAssigned,
            branch=NotAssigned,
            id=NotAssigned,
            ifsc_code=NotAssigned,
            branch_code=NotAssigned,
            swift_code=NotAssigned,
            type=NotAssigned,
        )

    def to_json(self):
        return {
            "account_name": self.account_name,
            "account_number": self.account_number,
            "bank": self.bank,
            "branch": self.branch,
            "id": self.id,
            "ifsc_code": self.ifsc_code,
            "branch_code": self.branch_code,
            "swift_code": self.swift_code,
            "type": self.type,
        }

    @classmethod
    def from_json(cls, json):
        if not json:
            return None
        return cls(
            account_name=json.get('account_name'),
            account_number=json.get('account_number'),
            bank=json.get('bank'),
            branch=json.get('branch'),
            id=json.get('id'),
            ifsc_code=json.get('ifsc_code'),
            branch_code=json.get('branch_code'),
            swift_code=json.get('swift_code'),
            type=json.get('type'),
        )


class InvoiceIssuedByInfo(object):
    def __init__(
        self, gst_details, phone, email, url, legal_signature=None, bank_details=None
    ):
        """

        :param GSTDetails gst_details:
        :param PhoneNumber phone:
        :param str email:
        :param str url:
        :param str legal_signature:
        """
        self.gst_details = gst_details
        self.phone = phone
        self.email = email
        self.url = url
        self.legal_signature = legal_signature
        self.bank_details = bank_details

    @classmethod
    def create_empty_instance(cls):
        return cls(
            gst_details=NotAssigned,
            phone=NotAssigned,
            email=NotAssigned,
            url=NotAssigned,
            legal_signature=NotAssigned,
            bank_details=NotAssigned,
        )

    def to_json(self):
        return {
            "gst_details": self.gst_details.to_json() if self.gst_details else None,
            "phone": self.phone.to_json() if self.phone else None,
            "email": self.email,
            "url": self.url,
            "legal_signature": self.legal_signature,
            "bank_details": self.bank_details.to_json() if self.bank_details else None,
        }

    @classmethod
    def from_json(cls, json):
        if not json:
            return None
        return cls(
            gst_details=GSTDetails.from_json(json.get('gst_details'))
            if json.get('gst_details')
            else None,
            phone=PhoneNumber.from_json(json.get('phone'))
            if json.get('phone')
            else None,
            email=json.get('email'),
            url=json.get('url'),
            legal_signature=json.get('legal_signature'),
            bank_details=BankDetails.from_json(json.get('bank_details'))
            if json.get('bank_details')
            else None,
        )


class InvoiceChargeToInfo(object):
    __slots__ = (
        'customer_id',
        'name',
        'is_booking_owner',
        'company_profile_id',
        'is_primary',
        'dummy',
    )

    def __init__(
        self,
        customer_id,
        name,
        company_profile_id=None,
        is_booking_owner=False,
        is_primary=False,
        dummy=None,
    ):
        """

        :param str customer_id:
        :param Name name:
        :param bool is_booking_owner:
        :param bool dummy:
        """
        self.customer_id = customer_id
        self.name = name
        self.is_booking_owner = is_booking_owner
        self.company_profile_id = company_profile_id
        self.is_primary = is_primary
        self.dummy = dummy

    def to_json(self):
        return {
            "customer_id": self.customer_id,
            "name": self.name.to_json() if self.name else None,
            "is_booking_owner": self.is_booking_owner,
            "company_profile_id": self.company_profile_id,
            "is_primary": self.is_primary,
            "dummy": self.dummy,
        }

    @staticmethod
    def from_json(json):
        return InvoiceChargeToInfo(
            customer_id=json.get('customer_id'),
            name=Name.from_json(json.get('name')) if json.get('name') else None,
            is_booking_owner=json.get('is_booking_owner'),
            company_profile_id=json.get('company_profile_id'),
            is_primary=json.get('is_primary'),
            dummy=json.get('dummy'),
        )


class PriceData(object):
    def __init__(
        self,
        pretax_amount=NotAssigned,
        posttax_amount=NotAssigned,
        applicable_date=NotAssigned,
        bill_to_type=NotAssigned,
        type=NotAssigned,
        charge_id=NotAssigned,
        charge_to=NotAssigned,
        rate_plan_reference_id=None,
        rate_plan_id=None,
        flexi_rate_plan_details=None,
        billing_instructions=NotAssigned,
        charge_components=None,
        buyer_gst_details=None,
        discounts=None,
    ):
        """

        :param Money pretax_amount:
        :param Money posttax_amount:
        :param datetime applicable_date:
        :param ths_common.constants.billing_constants.ChargeBillToTypes bill_to_type:
        :param ths_common.constants.billing_constants.ChargeTypes type:
        :param charge_id: To be used if charge needs to be updated, instead of creating new charge
        :param List[String] charge_to: To be used to update charge_split
        """
        self.pretax_amount = pretax_amount
        self.posttax_amount = posttax_amount
        self.applicable_date = applicable_date
        self.bill_to_type = bill_to_type
        self.type = type
        self.charge_id = charge_id
        self.charge_to = charge_to
        self.rate_plan_id = rate_plan_id
        self.rate_plan_reference_id = rate_plan_reference_id
        self.flexi_rate_plan_details = flexi_rate_plan_details
        self.billing_instructions = billing_instructions
        self.charge_components = charge_components
        self.buyer_gst_details = buyer_gst_details
        self.discounts = discounts

    def set_applicable_time(self, applicable_time):
        self.applicable_date = dateutils.datetime_at_given_time(
            self.applicable_date, applicable_time
        )

    def to_json(self):
        # TODO: Handle NotAssigned values
        return {
            "pretax_amount": float(self.pretax_amount) if self.pretax_amount else 0,
            "posttax_amount": float(self.posttax_amount) if self.posttax_amount else 0,
            "applicable_date": dateutils.isoformat_datetime(self.applicable_date)
            if self.applicable_date
            else None,
            "bill_to_type": self.bill_to_type.value if self.bill_to_type else None,
            "type": self.type.value if self.type else None,
            "charge_id": self.charge_id,
            "charge_to": self.charge_to,
        }


class InclusionChargeEditDto:
    def __init__(
        self, charge_id, pretax_amount=NotAssigned, posttax_amount=NotAssigned
    ):
        self.charge_id = charge_id
        self.pretax_amount = pretax_amount
        self.posttax_amount = posttax_amount


class EditRoomPriceDtoV2(object):
    def __init__(
        self,
        pretax_amount=NotAssigned,
        posttax_amount=NotAssigned,
        charge_id=NotAssigned,
        charge_to=NotAssigned,
        billing_instructions=NotAssigned,
        inclusion_charge_edits=NotAssigned,
    ):
        """

        :param Money pretax_amount:
        :param Money posttax_amount:
        :param charge_id: To be used if charge needs to be updated, instead of creating new charge
        :param List[String] charge_to: To be used to update charge_split
        :param List[BillingInstructionVO] billing_instructions
        """
        # Validate that pretax or posttax is only sent in edit room price and inclusion charge edit
        self.pretax_amount = pretax_amount
        self.posttax_amount = posttax_amount
        self.charge_id = charge_id
        self.charge_to = charge_to
        self.billing_instructions = billing_instructions
        self.inclusion_charge_edits = (
            inclusion_charge_edits if inclusion_charge_edits != NotAssigned else []
        )
        self.is_price_edited = (
            self.pretax_amount != NotAssigned or self.posttax_amount != NotAssigned
        )
        self.is_billing_instruction_edited = self.billing_instructions != NotAssigned
        self.is_consuming_guests_edited = self.charge_to != NotAssigned

    def get_inclusion_charge_edit(self, charge_id):
        if not self.inclusion_charge_edits:
            return None

        for inclusion_charge_edit in self.inclusion_charge_edits:
            if inclusion_charge_edit.charge_id == charge_id:
                return inclusion_charge_edit

    def set_base_currency(self, currency):
        if self.pretax_amount != NotAssigned:
            self.pretax_amount = Money(self.pretax_amount.amount, currency)
        if self.posttax_amount != NotAssigned:
            self.posttax_amount = Money(self.posttax_amount.amount, currency)

        for inclusion_charge_edit in self.inclusion_charge_edits:
            if inclusion_charge_edit.pretax_amount != NotAssigned:
                inclusion_charge_edit.pretax_amount = Money(
                    inclusion_charge_edit.pretax_amount.amount, currency
                )
            if inclusion_charge_edit.posttax_amount != NotAssigned:
                inclusion_charge_edit.posttax_amount = Money(
                    inclusion_charge_edit.posttax_amount.amount, currency
                )


class RoomRent(object):
    __slots__ = ('posttax_amount', 'applicable_date')

    def __init__(self, posttax_amount=None, applicable_date=None):
        """

        :param Money posttax_amount:
        :param datetime.date applicable_date:
        """
        self.posttax_amount = posttax_amount
        self.applicable_date = dateutils.to_date(applicable_date)

    def to_json(self):
        return {
            "posttax_amount": str(self.posttax_amount)
            if self.posttax_amount is not None
            else str(Money("0")),
            "applicable_date": dateutils.date_to_ymd_str(self.applicable_date)
            if self.applicable_date
            else None,
        }

    @staticmethod
    def from_json(json):
        return RoomRent(
            posttax_amount=Money(json.get('posttax_amount'))
            if json.get('posttax_amount')
            else None,
            applicable_date=dateutils.ymd_str_to_date(json.get('applicable_date')),
        )


class RoomStayConfig(object):
    __slots__ = (
        'room_type_id',
        'checkin_date',
        'checkout_date',
        'date_wise_occupancies',
    )

    def __init__(
        self, room_type_id, checkin_date, checkout_date, date_wise_occupancies=None
    ):
        """

        :param str room_type_id:
        :param datetime checkin_date:
        :param datetime checkout_date:
        :param dict date_wise_occupancies:
        """
        self.room_type_id = room_type_id
        if not checkin_date:
            raise ValueError("Checkin Date cannot be None")
        self.checkin_date = checkin_date
        if not checkout_date:
            raise ValueError("Checkout Date cannot be None")
        self.checkout_date = checkout_date

        if dateutils.to_date(self.checkout_date) <= dateutils.to_date(
            self.checkin_date
        ):
            raise ValidationException(
                message="Checkout date should be greater than checkin date"
            )

        if date_wise_occupancies:
            self.date_wise_occupancies = date_wise_occupancies

    def __eq__(self, other):
        """Overrides the default implementation"""
        if isinstance(self, other.__class__):
            return (
                self.room_type_id == other.room_type_id
                and dateutils.to_date(self.checkin_date)
                == dateutils.to_date(other.checkin_date)
                and dateutils.to_date(self.checkout_date)
                == dateutils.to_date(other.checkout_date)
            )
        return False

    def difference(self, room_stay_config):
        room_stay_configs = []
        if dateutils.to_date(self.checkin_date) > dateutils.to_date(
            room_stay_config.checkout_date
        ):
            room_stay_configs.append(
                RoomStayConfig(self.room_type_id, self.checkin_date, self.checkout_date)
            )

        elif dateutils.to_date(self.checkout_date) < dateutils.to_date(
            room_stay_config.checkin_date
        ):
            room_stay_configs.append(
                RoomStayConfig(self.room_type_id, self.checkin_date, self.checkout_date)
            )

        else:
            if dateutils.to_date(self.checkin_date) < dateutils.to_date(
                room_stay_config.checkin_date
            ):
                room_stay_configs.append(
                    RoomStayConfig(
                        self.room_type_id,
                        self.checkin_date,
                        room_stay_config.checkin_date,
                    )
                )

            if dateutils.to_date(self.checkout_date) > dateutils.to_date(
                room_stay_config.checkout_date
            ):
                room_stay_configs.append(
                    RoomStayConfig(
                        self.room_type_id,
                        room_stay_config.checkout_date,
                        self.checkout_date,
                    )
                )

        return room_stay_configs

    def override_checkin_time(self, override_checkin_time):
        self.checkin_date = dateutils.datetime_at_given_time(
            self.checkin_date, override_checkin_time
        )

    def override_checkout_time(self, override_checkout_time):
        self.checkout_date = dateutils.datetime_at_given_time(
            self.checkout_date, override_checkout_time
        )

    def with_checkin_date(self, checkin_date):
        return RoomStayConfig(
            room_type_id=self.room_type_id,
            checkin_date=checkin_date,
            checkout_date=self.checkout_date,
            date_wise_occupancies=self.date_wise_occupancies
            if hasattr(self, 'date_wise_occupancies')
            else None,
        )


class UserData(object):
    def __init__(
        self, user_type, user=None, user_auth_id=None, hotel_id=None, seller_id=None
    ):
        """

        :param str user_type:
        :param str user:
        """
        self.user_type = user_type
        self.user = user
        self.user_auth_id = user_auth_id
        self.hotel_id = hotel_id
        self.seller_id = seller_id
        # NOTE: Temporary field to read application in AccessEntityFacts, via user_data.
        # TODO: Once POS starts sending correct X-Seller-Id in headers, this check would be removed from
        #  AccessEntityFacts, and then this field should be removed
        # It is getting set in request_parsers.read_user_data_from_request_header
        self.application = None


class State(object):
    __slots__ = ('id', 'name')

    def __init__(self, state_id, name):
        self.id = state_id
        self.name = name

    def __str__(self):
        return self.name


class City(object):
    __slots__ = ('id', 'name')

    def __init__(self, city_id, name):
        self.id = city_id
        self.name = name

    def __str__(self):
        return self.name


class GuestAllocationSideEffect(object):
    def __init__(self, guest_allocation_id, is_new=False):
        self.guest_allocation_id = guest_allocation_id
        self.is_new = is_new

    def to_json(self):
        return {"guest_allocation_id": self.guest_allocation_id, "is_new": self.is_new}

    @classmethod
    def from_json(cls, guest_allocation_side_effect):
        return cls(
            guest_allocation_side_effect.get("guest_allocation_id"),
            guest_allocation_side_effect.get("is_new"),
        )


class GuestStaySideEffect(object):
    def __init__(self, guest_stay_id, current_guest_allocation=None):
        """

        :param guest_stay_id:
        :param current_guest_allocation:  GuestAllocationSideEffect
        """
        self.guest_stay_id = guest_stay_id
        self.current_guest_allocation = current_guest_allocation

    def to_json(self):
        return {
            "guest_stay_id": self.guest_stay_id,
            "current_guest_allocation": self.current_guest_allocation.to_json()
            if self.current_guest_allocation
            else None,
        }

    @classmethod
    def from_json(cls, guest_stay_side_effect):
        current_guest_allocation = (
            GuestAllocationSideEffect.from_json(
                guest_stay_side_effect.get("current_guest_allocation")
            )
            if guest_stay_side_effect.get("current_guest_allocation")
            else None
        )
        return cls(
            guest_stay_side_effect.get("guest_stay_id"),
            current_guest_allocation=current_guest_allocation,
        )


class RoomStaySideEffect(object):
    def __init__(self, room_stay_id, guest_stays=None):
        """

        :param room_stay_id:
        :param guest_stays: List[GuestStaySideEffect]
        """
        self.room_stay_id = room_stay_id
        self.guest_stays = guest_stays if guest_stays is not None else []

    def add_guest_stay(self, guest_stay):
        self.guest_stays.append(guest_stay)

    def to_json(self):
        return {
            "room_stay_id": self.room_stay_id,
            "guest_stays": [gse.to_json() for gse in self.guest_stays],
        }

    @classmethod
    def from_json(cls, room_stay_side_effect):
        room_stay_id = room_stay_side_effect.get("room_stay_id")
        guest_stays = [
            GuestStaySideEffect.from_json(guest_stay_json)
            for guest_stay_json in room_stay_side_effect.get("guest_stays", [])
        ]
        return cls(room_stay_id, guest_stays=guest_stays)


class BookingSideEffect(object):
    def __init__(self, room_stays=None):
        """

        :param room_stays: List[RoomStaySideEffect]
        """
        self.room_stays = room_stays if room_stays else []

    def add_room_stay(self, room_stay):
        self.room_stays.append(room_stay)

    def to_json(self):
        return {"room_stays": [rsse.to_json() for rsse in self.room_stays]}

    @classmethod
    def from_json(cls, booking_side_effect):
        room_stays = [
            RoomStaySideEffect.from_json(room_stay_json)
            for room_stay_json in booking_side_effect.get("room_stays", [])
        ]
        return cls(room_stays=room_stays)


class BillSideEffect(object):
    def __init__(
        self,
        cancelled_charge_ids=None,
        added_charge_ids=None,
        added_invoices=None,
        cancelled_allowances=None,
        grouped_cancelled_charges=None,
        cancelled_billed_entity_ids=None,
    ):
        self.cancelled_charge_ids = cancelled_charge_ids if cancelled_charge_ids else []
        self.added_charge_ids = added_charge_ids if added_charge_ids else []
        self.added_invoices = added_invoices if added_invoices else []
        self.cancelled_allowances = cancelled_allowances if cancelled_allowances else []
        self.grouped_cancelled_charges = (
            grouped_cancelled_charges
            if grouped_cancelled_charges
            else GroupedCancelledChargesDto()
        )
        self.cancelled_billed_entity_ids = cancelled_billed_entity_ids or []

    def to_json(self):
        return {
            "cancelled_charge_ids": self.cancelled_charge_ids,
            "added_charge_ids": self.added_charge_ids,
            "added_invoices": self.added_invoices,
            "cancelled_allowances": self.cancelled_allowances,
            "grouped_cancelled_charges": self.grouped_cancelled_charges.to_dict(),
            "cancelled_billed_entity_ids": self.cancelled_billed_entity_ids,
        }

    @classmethod
    def from_json(cls, bill_side_effect):
        grouped_cancelled_charges = GroupedCancelledChargesDto()
        if bill_side_effect.get("grouped_cancelled_charges"):
            for room_stay in bill_side_effect.get("grouped_cancelled_charges").get(
                "room_stay_charges"
            ):
                room_stay_charges_dto = RoomStayChargesDto(
                    room_stay.get('room_stay_id'), room_stay.get('charge_ids')
                )
                if room_stay_charges_dto:
                    grouped_cancelled_charges.room_stay_charges.append(
                        room_stay_charges_dto
                    )
            if bill_side_effect.get("grouped_cancelled_charges").get("expenses"):
                grouped_cancelled_charges.expenses = bill_side_effect.get(
                    "grouped_cancelled_charges"
                ).get("expenses")
        return cls(
            cancelled_charge_ids=bill_side_effect.get("cancelled_charge_ids", []),
            added_charge_ids=bill_side_effect.get("added_charge_ids", []),
            added_invoices=bill_side_effect.get("added_invoices", []),
            cancelled_allowances=bill_side_effect.get("cancelled_allowances", []),
            grouped_cancelled_charges=grouped_cancelled_charges,
            cancelled_billed_entity_ids=bill_side_effect.get(
                'cancelled_billed_entity_ids', []
            ),
        )


class BookingActionSideEffects(object):
    def __init__(self, booking=None, bill=None, inventory=None):
        """

        :param booking: BookingSideEffect
        :param bill: BillSideEffect
        :param inventory:
        """
        self.booking = booking if booking else BookingSideEffect()
        self.bill = bill if bill else BillSideEffect()
        self.inventory = inventory if inventory else dict()

    def to_json(self):
        return {
            "booking": self.booking.to_json(),
            "bill": self.bill.to_json(),
            "inventory": self.inventory,
        }

    @classmethod
    def from_json(cls, booking_action_side_effect):
        if not booking_action_side_effect:
            return cls()
        booking_side_effect = booking_action_side_effect.get("booking")
        bill_side_effect = booking_action_side_effect.get("bill")
        inventory_side_effect = booking_action_side_effect.get("inventory")
        return cls(
            BookingSideEffect.from_json(booking_side_effect)
            if booking_side_effect
            else None,
            BillSideEffect.from_json(bill_side_effect) if bill_side_effect else None,
            inventory_side_effect,
        )


class ActionReversalAlert(object):
    def __init__(self, message, payload=None):
        """
        :param str message:
        :param List payload:
        """
        self.message = message
        self.payload = payload

    def to_json(self):
        return {"message": self.message, "payload": self.payload}

    @staticmethod
    def from_json(json):
        return ActionReversalAlert(
            message=json.get('message'), payload=json.get('payload')
        )


class ActionReversalSideEffects(object):
    """
    Booking action reversal sideeffects
    """

    def __init__(self, alerts=None):
        """

        :param list alerts:
        """
        self.alerts = []
        if alerts:
            for alert in alerts:
                self.add_alert(alert)

    def add_alert(self, alert):
        if not alert:
            return
        if not isinstance(alert, ActionReversalAlert):
            raise ValidationException(description="Invalid alert object")
        self.alerts.append(alert)

    def add_alerts(self, alerts):
        for alert in alerts:
            self.add_alert(alert)

    def to_json(self):
        return {"alerts": [alert.to_json() for alert in self.alerts]}

    @classmethod
    def from_json(cls, json):
        if not json:
            return cls()
        return cls(
            alerts=[
                ActionReversalAlert.from_json(alert) for alert in json.get('alerts', [])
            ]
        )


class Occupancy(object):
    __slots__ = ('adult', 'child')

    def __init__(self, adult, child=0):
        self.adult = adult
        self.child = child

    def __eq__(self, other):
        if isinstance(self, other.__class__):
            return self.adult == other.adult and self.child == other.child
        return False

    def total(self):
        return self.adult + self.child


class EmailAttachment(object):
    def __init__(self, url, filename):
        self.url = url
        self.filename = filename

    def __str__(self):
        return "{}:{}".format(self.filename, self.url)

    def json(self):
        return dict(url=self.url, filename=self.filename)


class InvoiceEmailHotelInfo(object):
    def __init__(self, name):
        self.name = name


class InvoiceEmailBookingInfo(object):
    def __init__(self, booking_channel):
        self.channel_code = booking_channel


class InvoiceEmailData(object):
    def __init__(self, hotel_name, booking_channel):
        self.hotel = InvoiceEmailHotelInfo(hotel_name)
        self.booking = InvoiceEmailBookingInfo(booking_channel)


class ChargeComponent(object):
    __slots__ = ('name', 'posttax_amount', 'pretax_amount', 'quantity')

    def __init__(self, name, posttax_amount, pretax_amount=None, quantity=1):
        self.name = name
        self.posttax_amount = posttax_amount
        self.pretax_amount = pretax_amount
        self.quantity = quantity

    def to_json(self):
        return {
            "name": self.name,
            "posttax_amount": str(self.posttax_amount),
            "pretax_amount": str(self.pretax_amount) if self.pretax_amount else None,
            "quantity": self.quantity,
        }

    @staticmethod
    def from_json(json):
        return ChargeComponent(
            name=json.get('name'),
            posttax_amount=Money(json.get('posttax_amount')),
            pretax_amount=Money(json.get('pretax_amount'))
            if json.get('pretax_amount')
            else None,
            quantity=json.get('quantity'),
        )


class EmploymentDetails(object):
    __slots__ = ('is_destination_employed', 'company_name')

    def __init__(self, is_destination_employed=NotAssigned, company_name=NotAssigned):
        self.is_destination_employed = is_destination_employed
        self.company_name = company_name

    def to_json(self):
        return {
            "is_destination_employed": self.is_destination_employed,
            "company_name": self.company_name,
        }

    @staticmethod
    def from_json(json):
        return EmploymentDetails(
            is_destination_employed=json.get('is_destination_employed'),
            company_name=json.get('company_name'),
        )

    def __eq__(self, other):
        return (
            isinstance(other, EmploymentDetails)
            and self.is_destination_employed == other.is_destination_employed
            and self.company_name == other.company_name
        )


class VisaDetails(object):
    __slots__ = (
        'registration_number',
        'destination_arrival_date',
        'destination_stay_duration',
        'issued_date',
        'issued_place',
    )

    def __init__(
        self,
        registration_number,
        destination_arrival_date=NotAssigned,
        destination_stay_duration=NotAssigned,
        issued_date=NotAssigned,
        issued_place=NotAssigned,
    ):
        self.registration_number = registration_number
        self.destination_arrival_date = destination_arrival_date
        self.destination_stay_duration = destination_stay_duration
        self.issued_date = issued_date
        self.issued_place = issued_place

    def to_json(self):
        return {
            "registration_number": self.registration_number,
            "destination_arrival_date": str(self.destination_arrival_date)
            if self.destination_arrival_date
            else None,
            "destination_stay_duration": self.destination_stay_duration,
            'issued_date': str(self.issued_date) if self.issued_date else None,
            'issued_place': self.issued_place,
        }

    @staticmethod
    def from_json(json):
        return VisaDetails(
            registration_number=json.get('registration_number'),
            destination_arrival_date=dateutils.isoformat_str_to_datetime(
                json.get('destination_arrival_date')
            )
            if json.get('destination_arrival_date')
            else None,
            destination_stay_duration=json.get('destination_stay_duration'),
            issued_date=dateutils.isoformat_str_to_datetime(json.get('issued_date'))
            if json.get('issued_date')
            else None,
            issued_place=json.get('issued_place'),
        )

    def __eq__(self, other):
        return (
            isinstance(other, VisaDetails)
            and self.registration_number == other.registration_number
            and self.destination_arrival_date == other.destination_arrival_date
            and self.destination_stay_duration == other.destination_stay_duration
            and self.issued_date == other.issued_date
            and self.issued_place == other.issued_place
        )


class TravelDetails(object):
    __slots__ = (
        'arrival_from',
        'next_destination',
        'visit_purpose',
        'destination_stay_duration',
        'visa',
    )

    def __init__(
        self,
        visa=NotAssigned,
        arrival_from=NotAssigned,
        next_destination=NotAssigned,
        destination_stay_duration=NotAssigned,
        visit_purpose=NotAssigned,
    ):
        self.arrival_from = arrival_from
        self.next_destination = next_destination
        self.visit_purpose = visit_purpose
        self.destination_stay_duration = destination_stay_duration
        self.visa = visa

    def to_json(self):
        return {
            "arrival_from": self.arrival_from,
            "next_destination": self.next_destination,
            "visit_purpose": self.visit_purpose,
            "destination_stay_duration": self.destination_stay_duration,
            "visa": VisaDetails.to_json(self.visa) if self.visa else None,
        }

    @staticmethod
    def from_json(json):
        return TravelDetails(
            arrival_from=json.get('arrival_from'),
            next_destination=json.get('next_destination'),
            visit_purpose=json.get('visit_purpose'),
            destination_stay_duration=json.get('destination_stay_duration'),
            visa=VisaDetails.from_json(json.get('visa')) if json.get('visa') else None,
        )

    def __eq__(self, other):
        return (
            isinstance(other, TravelDetails)
            and self.arrival_from == other.arrival_from
            and self.next_destination == other.next_destination
            and self.visit_purpose == other.visit_purpose
            and self.destination_stay_duration == other.destination_stay_duration
            and self.visa == other.visa
        )


class CashCounterAmount:
    def __init__(self, amounts):
        self._amounts = amounts if amounts else []
        for amount in self._amounts:
            if not isinstance(amount, Money):
                raise ValidationException(
                    description="CashCounterAmounts::Invalid amount value: {} for cash "
                    "counter".format(amount)
                )
        self.currency_wise_amount_dict = {
            amount.currency: amount for amount in self.amounts
        }

    def __str__(self):
        return ','.join([str(amount) for amount in self.amounts])

    def to_json(self):
        return [str(amount) for amount in self.amounts]

    @property
    def amounts(self):
        currency_wise_amount = dict()
        for amount in self._amounts:
            if not currency_wise_amount.get(amount.currency):
                currency_wise_amount[amount.currency] = amount
            else:
                currency_wise_amount[amount.currency] += amount

        return list(currency_wise_amount.values())


class GuestPreference:
    def __init__(
        self,
        room_preferences,
        fnb_preferences,
        housekeeping_preferences,
        transfers_preferences,
        spa_preferences,
        newspaper_preferences,
        others_preferences,
    ):
        self.room_preferences = room_preferences
        self.fnb_preferences = fnb_preferences
        self.housekeeping_preferences = housekeeping_preferences
        self.transfers_preferences = transfers_preferences
        self.spa_preferences = spa_preferences
        self.newspaper_preferences = newspaper_preferences
        self.others_preferences = others_preferences

    def __eq__(self, other):
        return (
            isinstance(other, GuestPreference)
            and self.room_preferences == other.room_preferences
            and self.fnb_preferences == other.fnb_preferences
            and self.housekeeping_preferences == other.housekeeping_preferences
            and self.transfers_preferences == other.transfers_preferences
            and self.spa_preferences == other.spa_preferences
            and self.newspaper_preferences == other.newspaper_preferences
            and self.others_preferences == other.others_preferences
        )

    def to_json(self):
        return {
            "room_preferences": self.room_preferences,
            "fnb_preferences": self.fnb_preferences,
            "housekeeping_preferences": self.housekeeping_preferences,
            "transfers_preferences": self.transfers_preferences,
            "spa_preferences": self.spa_preferences,
            "newspaper_preferences": self.newspaper_preferences,
            "others_preferences": self.others_preferences,
        }

    @staticmethod
    def from_json(json):
        if type(json) is not dict:
            return None

        return GuestPreference(
            room_preferences=json.get('room_preferences'),
            fnb_preferences=json.get('fnb_preferences'),
            housekeeping_preferences=json.get('housekeeping_preferences'),
            transfers_preferences=json.get('transfers_preferences'),
            spa_preferences=json.get('spa_preferences'),
            newspaper_preferences=json.get('newspaper_preferences'),
            others_preferences=json.get('others_preferences'),
        )


class GuestMetadata:
    def __init__(
        self,
        privacy_options=None,
        primary_language_id=None,
        mfname_code=None,
        profile_certification_details=None,
    ):
        self.privacy_options = privacy_options
        self.primary_language_id = primary_language_id
        self.mfname_code = mfname_code
        self.profile_certification_details = profile_certification_details

    def __eq__(self, other):
        return (
            isinstance(other, GuestMetadata)
            and self.privacy_options == other.privacy_options
            and self.primary_language_id == other.primary_language_id
            and self.mfname_code == other.mfname_code
            and self.profile_certification_details
            == other.profile_certification_details
        )

    def to_json(self):
        return dict(
            privacy_options=self.privacy_options,
            primary_language_id=self.primary_language_id,
            mfname_code=self.mfname_code,
            profile_certification_details=self.profile_certification_details,
        )

    @staticmethod
    def from_json(json):
        return GuestMetadata(
            privacy_options=json.get("privacy_options"),
            primary_language_id=json.get("primary_language_id"),
            mfname_code=json.get("mfname_code"),
            profile_certification_details=json.get("profile_certification_details"),
        )


class CompanyMetadata(GuestMetadata):
    def __init__(
        self,
        privacy_options=None,
        primary_language_id=None,
        mfname_code=None,
        profile_certification_details=None,
        nationality=None,
    ):
        super().__init__(
            privacy_options,
            primary_language_id,
            mfname_code,
            profile_certification_details,
        )
        self.nationality = nationality

    def __eq__(self, other):
        return (
            isinstance(other, CompanyMetadata)
            and self.privacy_options == other.privacy_options
            and self.primary_language_id == other.primary_language_id
            and self.mfname_code == other.mfname_code
            and self.profile_certification_details
            == other.profile_certification_details
            and self.nationality == other.nationality
        )

    def to_json(self):
        return dict(
            privacy_options=self.privacy_options,
            primary_language_id=self.primary_language_id,
            mfname_code=self.mfname_code,
            profile_certification_details=self.profile_certification_details,
            nationality=self.nationality,
        )

    @staticmethod
    def from_json(json):
        return CompanyMetadata(
            privacy_options=json.get("privacy_options"),
            primary_language_id=json.get("primary_language_id"),
            mfname_code=json.get("mfname_code"),
            profile_certification_details=json.get("profile_certification_details"),
            nationality=json.get("nationality"),
        )


class RoomRatePlan:
    def __init__(self, stay_date, rate_plan_id):
        self.stay_date = stay_date
        self.rate_plan_id = rate_plan_id

    def to_json(self):
        return dict(
            stay_date=dateutils.date_to_ymd_str(self.stay_date),
            rate_plan_id=self.rate_plan_id,
        )

    @classmethod
    def from_json(cls, json):
        return RoomRatePlan(
            stay_date=dateutils.ymd_str_to_date(json.get('stay_date')),
            rate_plan_id=str(json.get('rate_plan_id')),
        )

    @staticmethod
    def create_empty_instance():
        room_rate_plan = RoomRatePlan(stay_date=None, rate_plan_id=None)
        return room_rate_plan


class LoyaltyProgramDetails:
    def __init__(
        self,
        program_name=None,
        program_id=None,
        membership_number=None,
        membership_level=None,
        current_points_balance=None,
        external_url=None,
        program_start_date=None,
        program_end_date=None,
    ):
        self.program_name = program_name
        self.program_id = program_id
        self.membership_number = membership_number
        self.membership_level = membership_level
        self.current_points_balance = current_points_balance
        self.external_url = external_url
        self.program_start_date = program_start_date
        self.program_end_date = program_end_date

    def __eq__(self, other):
        return (
            isinstance(other, LoyaltyProgramDetails)
            and self.program_name == other.program_name
            and self.program_id == other.program_id
            and self.membership_number == other.membership_number
            and self.membership_level == other.membership_level
            and self.current_points_balance == other.current_points_balance
            and self.external_url == other.external_url
            and self.program_start_date == other.program_start_date
            and self.program_end_date == other.program_end_date
        )

    def to_json(self):
        return {
            "program_name": self.program_name,
            "program_id": self.program_id,
            "membership_number": self.membership_number,
            "membership_level": self.membership_level,
            "current_points_balance": self.current_points_balance,
            "external_url": self.external_url,
            "program_start_date": str(self.program_start_date)
            if self.program_start_date
            else None,
            "program_end_date": str(self.program_end_date)
            if self.program_end_date
            else None,
        }

    @staticmethod
    def from_json(json):
        return LoyaltyProgramDetails(
            program_name=json.get('program_name'),
            program_id=json.get('program_id'),
            membership_number=json.get('membership_number'),
            membership_level=json.get('membership_level'),
            current_points_balance=json.get('current_points_balance'),
            external_url=json.get('external_url'),
            program_start_date=dateutils.ymd_str_to_date(json.get('program_start_date'))
            if json.get('program_start_date') is not None
            else None,
            program_end_date=dateutils.ymd_str_to_date(json.get('program_end_date'))
            if json.get('program_end_date') is not None
            else None,
        )

    @staticmethod
    def create_empty_instance():
        return LoyaltyProgramDetails(
            program_name=None,
            program_id=None,
            membership_number=None,
            membership_level=None,
            current_points_balance=None,
            external_url=None,
            program_start_date=None,
            program_end_date=None,
        )


class VIPDetails:
    def __init__(self, status, details):
        self.status = status
        self.details = details

    def __eq__(self, other):
        return (
            isinstance(other, VIPDetails)
            and self.status == other.status
            and self.details == other.details
        )

    def to_json(self):
        return {"status": self.status, "details": self.details}

    @staticmethod
    def from_json(json):
        return VIPDetails(status=json.get('status'), details=json.get('details'))


class PassportDetails:
    def __init__(
        self,
        name_on_passport,
        passport_number,
        issued_by_country,
        issued_at_place,
        issue_date,
        expiry_date,
        attachment_url,
    ):
        self.name_on_passport = name_on_passport
        self.passport_number = passport_number
        self.issued_by_country = issued_by_country
        self.issued_at_place = issued_at_place
        self.issue_date = issue_date
        self.expiry_date = expiry_date
        self.attachment_url = attachment_url

    def __eq__(self, other):
        return (
            isinstance(other, PassportDetails)
            and self.name_on_passport == other.name_on_passport
            and self.passport_number == other.passport_number
            and self.issued_by_country == other.issued_by_country
            and self.issued_at_place == other.issued_at_place
            and self.issue_date == other.issue_date
            and self.expiry_date == other.expiry_date
            and self.attachment_url == other.attachment_url
        )

    def to_json(self):
        return {
            "name_on_passport": self.name_on_passport,
            "passport_number": self.passport_number,
            "issued_by_country": self.issued_by_country,
            "issued_at_place": self.issued_at_place,
            "issue_date": str(self.issue_date) if self.issue_date else None,
            "expiry_date": str(self.expiry_date) if self.expiry_date else None,
            "attachment_url": self.attachment_url,
        }

    @staticmethod
    def from_json(json):
        return PassportDetails(
            name_on_passport=json.get('name_on_passport'),
            passport_number=json.get('passport_number'),
            issued_by_country=json.get('issued_by_country'),
            issued_at_place=json.get('issued_at_place'),
            issue_date=dateutils.isoformat_str_to_datetime(json.get('issue_date'))
            if json.get('issue_date')
            else None,
            expiry_date=dateutils.isoformat_str_to_datetime(json.get('expiry_date'))
            if json.get('expiry_date')
            else None,
            attachment_url=json.get('attachment_url'),
        )


class CustomerVisaDetails:
    def __init__(
        self,
        visa_type,
        visa_number,
        issued_by_country,
        issued_at_place,
        issue_date,
        expiry_date,
        is_employed_in_issuing_country,
        attachment_url,
    ):
        self.visa_type = visa_type
        self.visa_number = visa_number
        self.issued_by_country = issued_by_country
        self.issued_at_place = issued_at_place
        self.issue_date = issue_date
        self.expiry_date = expiry_date
        self.is_employed_in_issuing_country = is_employed_in_issuing_country
        self.attachment_url = attachment_url

    def __eq__(self, other):
        return (
            isinstance(other, CustomerVisaDetails)
            and self.visa_type == other.visa_type
            and self.visa_number == other.visa_number
            and self.issued_by_country == other.issued_by_country
            and self.issue_date == other.issue_date
            and self.issued_at_place == other.issued_at_place
            and self.expiry_date == other.expiry_date
            and self.is_employed_in_issuing_country
            == other.is_employed_in_issuing_country
            and self.attachment_url == other.attachment_url
        )

    def to_json(self):
        return {
            "visa_type": self.visa_type,
            "visa_number": self.visa_number,
            "issued_by_country": self.issued_by_country,
            "issued_at_place": self.issued_at_place,
            "issue_date": str(self.issue_date) if self.issue_date else None,
            "expiry_date": str(self.expiry_date) if self.expiry_date else None,
            "is_employed_in_issuing_country": self.is_employed_in_issuing_country,
            "attachment_url": self.attachment_url,
        }

    @staticmethod
    def from_json(json):
        return CustomerVisaDetails(
            visa_type=json.get('visa_type'),
            visa_number=json.get('visa_number'),
            issued_by_country=json.get('issued_by_country'),
            issued_at_place=json.get('issued_at_place'),
            issue_date=dateutils.isoformat_str_to_datetime(json.get('issue_date'))
            if json.get('issue_date')
            else None,
            expiry_date=dateutils.isoformat_str_to_datetime(json.get('expiry_date'))
            if json.get('expiry_date')
            else None,
            is_employed_in_issuing_country=json.get('is_employed_in_issuing_country'),
            attachment_url=json.get('attachment_url'),
        )


class TravelBaseDetails:
    def __init__(
        self, datetime, mode, flight_or_train_number, flight_or_train_datetime, transfer
    ):
        self.datetime = datetime
        self.mode = mode
        self.flight_or_train_number = flight_or_train_number
        self.flight_or_train_datetime = flight_or_train_datetime
        self.transfer = transfer

    def __eq__(self, other):
        return (
            isinstance(other, TravelBaseDetails)
            and self.datetime == other.datetime
            and self.mode == other.mode
            and self.flight_or_train_number == other.flight_or_train_number
            and self.flight_or_train_datetime == other.flight_or_train_datetime
            and self.transfer == other.transfer
        )

    def to_json(self):
        return {
            "datetime": str(self.datetime) if self.datetime else None,
            "mode": self.mode,
            "flight_or_train_number": self.flight_or_train_number,
            "flight_or_train_datetime": str(self.flight_or_train_datetime)
            if self.flight_or_train_datetime
            else None,
            "transfer": self.transfer,
        }

    @staticmethod
    def from_json(json):
        return TravelBaseDetails(
            datetime=dateutils.isoformat_str_to_datetime(str(json.get('datetime')))
            if json.get('datetime')
            else None,
            mode=json.get('mode'),
            flight_or_train_number=json.get('flight_or_train_number'),
            flight_or_train_datetime=dateutils.isoformat_str_to_datetime(
                str(json.get('flight_or_train_datetime'))
            )
            if json.get('flight_or_train_datetime')
            else None,
            transfer=json.get('transfer'),
        )


class AccountDetails(object):
    """
    Account Details
    """

    __slots__ = ('account_id', 'version')

    def __init__(self, account_id=None, version=None):
        """
        param account_id
        param version
        """
        self.account_id = account_id
        self.version = version

    def to_json(self):
        return {'account_id': self.account_id, 'version': self.version}

    @staticmethod
    def from_json(json):
        if not json:
            return None
        return AccountDetails(
            account_id=json.get('account_id'), version=json.get('version')
        )

    def __str__(self):
        return "Account ID : {} | version: {}".format(self.account_id, self.version)


class LegalDetails(object):
    """
    Legal Details
    """

    __slots__ = (
        'legal_name',
        'email',
        'phone',
        'tin',
        'address',
        'is_sez',
        'has_lut',
        'client_internal_code',
        'external_reference_id',
    )

    def __init__(
        self,
        address,
        legal_name,
        email=NotAssigned,
        phone=NotAssigned,
        tin=NotAssigned,
        client_internal_code=NotAssigned,
        external_reference_id=NotAssigned,
        is_sez=False,
        has_lut=False,
    ):
        """

        :param address:
        :param legal_name:
        :param email:
        :param phone:
        :param tin:
        :param client_internal_code:
        :param external_reference_id:
        :param is_sez:
        :param has_lut:
        """
        self.legal_name = safe_strip(legal_name)
        self.email = email
        self.phone = phone
        self.tin = safe_strip(tin)
        self.address = address
        self.is_sez = is_sez
        self.has_lut = has_lut
        self.client_internal_code = client_internal_code
        self.external_reference_id = external_reference_id

    def replace(self, obj):
        self.legal_name = obj.legal_name
        self.email = obj.email
        self.phone = obj.phone
        self.tin = obj.tin
        self.address.replace(obj.address)
        self.is_sez = obj.is_sez
        self.has_lut = obj.has_lut
        self.client_internal_code = obj.client_internal_code
        self.external_reference_id = obj.external_reference_id

    def to_json(self):
        return {
            "legal_name": self.legal_name,
            "email": self.email,
            "phone": self.phone.to_json() if self.phone else None,
            "tin": self.tin,
            "address": self.address.to_json() if self.address else None,
            "is_sez": self.is_sez,
            "has_lut": self.has_lut,
            "client_internal_code": self.client_internal_code,
            "external_reference_id": self.external_reference_id,
        }

    @staticmethod
    def from_json(json):
        return LegalDetails(
            legal_name=json.get('legal_name'),
            email=json.get('email'),
            phone=PhoneNumber.from_json(json['phone']) if json.get('phone') else None,
            tin=json.get('tin'),
            address=Address.from_json(json.get('address'))
            if json.get('address')
            else None,
            is_sez=json.get('is_sez', False),
            has_lut=json.get('has_lut', False),
            client_internal_code=json.get('client_internal_code'),
            external_reference_id=json.get('external_reference_id'),
        )

    def __str__(self):
        return "TIN: {0} | Address: {1} | Legal Name: {2}".format(
            self.tin, str(self.address), self.legal_name
        )

    @staticmethod
    def create_empty_instance():
        legal_details = LegalDetails(
            legal_name=NotAssigned, tin=NotAssigned, address=NotAssigned
        )
        return legal_details

    def __eq__(self, other):
        if isinstance(other, LegalDetails):
            return (
                self.legal_name == other.legal_name
                and self.email == other.email
                and self.phone == other.phone
                and self.tin == other.tin
                and self.address == other.address
                and self.is_sez == other.is_sez
                and self.has_lut == other.has_lut
                and self.client_internal_code == other.client_internal_code
                and self.external_reference_id == other.external_reference_id
            )

    def has_same_tax_determiners(self, other):
        if isinstance(other, LegalDetails):
            return self.is_sez == other.is_sez and self.has_lut == other.has_lut
        if other is None:
            return not (self.is_sez or self.has_lut)
        return False


class TaxDeterminersChangeTracker(abc.ABC):
    def __init__(self):
        self.is_tax_determiners_changed = False

    def check_for_tax_determiner_change(self, other):
        current_legal_details = self.legal_details
        if isinstance(other, CompanyDetails):
            legal_details = other.legal_details
            if legal_details == current_legal_details:
                return
            source, target = (
                (legal_details, current_legal_details)
                if legal_details
                else (current_legal_details, legal_details)
            )
            self.is_tax_determiners_changed = not (
                source.has_same_tax_determiners(target)
            )
        elif other is None and current_legal_details:
            self.is_tax_determiners_changed = (
                current_legal_details.is_sez or current_legal_details.has_lut
            )


class CompanyDetails(TaxDeterminersChangeTracker):
    __slots__ = ('legal_details', 'billed_entity_id', 'metadata')

    def __init__(
        self, legal_details=NotAssigned, billed_entity_id=None, metadata=NotAssigned
    ):
        super().__init__()
        self.legal_details = legal_details
        self.billed_entity_id = billed_entity_id
        self.metadata = metadata

    def to_json(self):
        return dict(
            legal_details=self.legal_details.to_json() if self.legal_details else None,
            billed_entity_id=self.billed_entity_id if self.billed_entity_id else None,
            metadata=self.metadata.to_json() if self.metadata else None,
        )

    @staticmethod
    def from_json(json):
        return CompanyDetails(
            legal_details=LegalDetails.from_json(json["legal_details"])
            if json.get("legal_details")
            else None,
            billed_entity_id=int(json["billed_entity_id"])
            if json.get("billed_entity_id")
            else None,
            metadata=CompanyMetadata.from_json(json["metadata"])
            if json.get("metadata")
            else None,
        )

    def __eq__(self, other):
        if isinstance(other, CompanyDetails):
            return (
                self.legal_details == other.legal_details
                and self.metadata == other.metadata
            )


class SegmentValue:
    def __init__(self, name=None, code=None):
        self.name = name
        self.code = code

    def to_json(self):
        return dict(
            name=self.name,
            code=self.code,
        )

    @staticmethod
    def from_json(json):
        return SegmentValue(name=json['name'], code=json['code'])


class Segment:
    def __init__(self, name=None, value=None, group_name=None):
        self.name = name
        self.value = value
        self.group_name = group_name

    def to_json(self):
        return dict(
            name=self.name,
            value=self.value.to_json() if self.value else None,
            group_name=self.group_name,
        )

    @staticmethod
    def from_json(json):
        return Segment(
            name=json['name'],
            value=SegmentValue.from_json(json['value']) if json.get('value') else None,
            group_name=json['group_name'],
        )


class RoomStayChargesDto(object):
    def __init__(self, room_stay_id, charge_ids):
        self.room_stay_id = room_stay_id
        self.charge_ids = charge_ids or []

    def to_dict(self):
        return dict(room_stay_id=self.room_stay_id, charge_ids=self.charge_ids)


class GroupedCancelledChargesDto(object):
    def __init__(self, room_stay_charges: [RoomStayChargesDto] = None, expenses=None):
        self.room_stay_charges = room_stay_charges or []
        self.expenses = expenses or []

    def to_dict(self):
        return dict(
            room_stay_charges=[rsc.to_dict() for rsc in self.room_stay_charges],
            expenses=self.expenses,
        )


class CommissionTax:
    def __init__(self, tax=None, tcs=None, tds=None, rcm=None):
        self.tax = tax
        self.tcs = tcs
        self.tds = tds
        self.rcm = rcm

    def __eq__(self, other):
        if isinstance(other, CommissionTax):
            return (
                self.tax == other.tax
                and self.tcs == other.tcs
                and self.tds == other.tds
                and self.rcm == other.rcm
            )
        return False

    def __bool__(self):
        return self is not None and any([self.tax, self.tcs, self.tds, self.rcm])

    def to_json(self):
        return dict(
            tax=float(self.tax) if self.tax else None,
            tcs=float(self.tcs) if self.tcs else None,
            tds=float(self.tds) if self.tds else None,
            rcm=float(self.rcm) if self.rcm else None,
        )

    @staticmethod
    def from_json(json):
        return CommissionTax(
            tax=json.get('tax'),
            tcs=json.get('tcs'),
            tds=json.get('tds'),
            rcm=json.get('rcm'),
        )


class TACommissionDetails:
    def __init__(
        self,
        commission_type=None,
        commission_value=None,
        post_commission_amount=None,
        commission_tax=None,
        recalculate_commission_on_booking_modification=True,
    ):
        self.commission_type = (
            commission_type if commission_type else TACommissionTypes.PERCENT
        )
        self.commission_value = commission_value
        self.post_commission_amount = post_commission_amount
        self.commission_tax = commission_tax
        self.recalculate_commission_on_booking_modification = (
            recalculate_commission_on_booking_modification
        )

    def to_json(self):
        return dict(
            commission_value=float(self.commission_value)
            if self.commission_value
            else 0.0,
            commission_type=self.commission_type.value
            if self.commission_type
            else None,
            post_commission_amount=self.post_commission_amount,
            commission_tax=self.commission_tax.to_json()
            if self.commission_tax
            else None,
            recalculate_commission_on_booking_modification=self.recalculate_commission_on_booking_modification
            if self.recalculate_commission_on_booking_modification
            else True,
        )

    def __eq__(self, other):
        if isinstance(other, TACommissionDetails):
            return (
                self.commission_type == other.commission_type
                and self.commission_value == other.commission_value
                and self.commission_tax == other.commission_tax
            )
        return False

    @staticmethod
    def from_json(json):
        commission_tax = json.get('commission_tax')
        return TACommissionDetails(
            commission_value=json.get('commission_value'),
            commission_type=TACommissionTypes(json['commission_type'])
            if json.get('commission_type')
            else None,
            post_commission_amount=json.get('post_commission_amount'),
            commission_tax=CommissionTax.from_json(commission_tax)
            if commission_tax
            else None,
            recalculate_commission_on_booking_modification=json.get(
                'recalculate_commission_on_booking_modification',
                True,
            ),
        )

    def has_commission_rule(self):
        return self.commission_value is not None

    def has_tax_rule(self):
        return bool(self.commission_tax)


class TADetails(CompanyDetails):
    __slots__ = (
        'legal_details',
        'billed_entity_id',
        'ta_commission_details',
        'metadata',
    )

    def __init__(
        self,
        legal_details=NotAssigned,
        billed_entity_id=None,
        ta_commission_details=NotAssigned,
        metadata=NotAssigned,
    ):
        super().__init__(legal_details, billed_entity_id, metadata)
        self.ta_commission_details = ta_commission_details

    def to_json(self):
        return dict(
            legal_details=self.legal_details.to_json() if self.legal_details else None,
            ta_commission_details=self.ta_commission_details.to_json()
            if self.ta_commission_details
            else None,
            billed_entity_id=self.billed_entity_id if self.billed_entity_id else None,
            metadata=self.metadata.to_json() if self.metadata else None,
        )

    @staticmethod
    def from_json(json):
        return TADetails(
            legal_details=LegalDetails.from_json(json['legal_details'])
            if json.get('legal_details')
            else NotAssigned,
            ta_commission_details=TACommissionDetails.from_json(
                json['ta_commission_details']
            )
            if json.get('ta_commission_details')
            else NotAssigned,
            billed_entity_id=int(json["billed_entity_id"])
            if json.get("billed_entity_id")
            else None,
            metadata=CompanyMetadata.from_json(json["metadata"])
            if json.get("metadata")
            else None,
        )

    def __eq__(self, other):
        if isinstance(other, TADetails):
            return (
                self.legal_details == other.legal_details
                and self.ta_commission_details == other.ta_commission_details
                and self.metadata == other.metadata
            )
        return False

    def has_commission_rule(self):
        return (
            self.ta_commission_details
            and self.ta_commission_details.has_commission_rule()
        )

    def has_super_hero_profile_tagged(self):
        return self.legal_details and self.legal_details.external_reference_id


class PayoutDetails:
    def __init__(self, pg_payout_id=None, short_url=None, expire_by=None, status=None):
        self.short_url = short_url
        self.pg_payout_id = pg_payout_id
        self.expire_by = expire_by
        self.status = status

    def to_json(self):
        return dict(
            pg_payout_id=self.pg_payout_id,
            short_url=self.short_url,
            expire_by=self.expire_by,
            status=self.status,
        )

    @staticmethod
    def from_json(json):
        return PayoutDetails(
            short_url=json['short_url'],
            pg_payout_id=json['pg_payout_id'],
            expire_by=json.get('expire_by'),
            status=json.get('status'),
        )


class PayoutContactDetails:
    def __init__(self, email=None, phone=None):
        self.email = email
        self.phone = phone

    def to_json(self):
        return dict(
            email=self.email,
            phone=self.phone.to_json() if self.phone else None,
        )

    @staticmethod
    def from_json(json):
        return PayoutContactDetails(
            email=json['email'],
            phone=json.get('phone') if json.get('phone') else None,
        )


class AttachmentDetails:
    def __init__(
        self, attachment_group=None, file_type=None, display_name=None, url=None
    ):
        self.attachment_group = attachment_group
        self.file_type = file_type
        self.display_name = display_name
        self.url = url

    def to_json(self):
        return dict(
            attachment_group=self.attachment_group,
            file_type=self.file_type,
            display_name=self.display_name,
            url=self.url,
        )

    @staticmethod
    def from_json(json_data):
        return AttachmentDetails(
            attachment_group=json_data.get('attachment_group'),
            file_type=json_data.get('file_type'),
            display_name=json_data.get('display_name'),
            url=json_data.get('url'),
        )


class BillingInstructions:
    def __init__(
        self,
        default_billed_entity_category=None,
        default_payment_instruction=None,
        default_billed_entity_category_for_extras=None,
        default_payment_instruction_for_extras=None,
        update_existing_charges=False,
    ):
        self.default_billed_entity_category = default_billed_entity_category
        self.default_payment_instruction = default_payment_instruction
        self.default_billed_entity_category_for_extras = (
            default_billed_entity_category_for_extras
        )
        self.default_payment_instruction_for_extras = (
            default_payment_instruction_for_extras
        )
        self.update_existing_charges = update_existing_charges


class RatePlanCommissionDetails:
    def __init__(
        self,
        commission_percent=NotAssigned,
        commission_type=NotAssigned,
    ):
        self.commission_percent = commission_percent
        self.commission_type = commission_type


class GuaranteeInformation:
    def __init__(self, guarantee_type=None, guarantee_details=None):
        self.guarantee_type = guarantee_type
        self.guarantee_details = guarantee_details

    def __eq__(self, other):
        if isinstance(other, GuaranteeInformation):
            return (
                self.guarantee_type == other.guarantee_type
                and self.guarantee_details == other.guarantee_details
            )
        return False

    def to_json(self):
        return dict(
            guarantee_type=self.guarantee_type.value,
            guarantee_details=self.guarantee_details,
        )

    @staticmethod
    def from_json(json_data):
        return GuaranteeInformation(
            guarantee_type=GuaranteeTypes(json_data.get('guarantee_type')),
            guarantee_details=json_data.get('guarantee_details'),
        )


class ECIServiceDetails:
    def __init__(self, inventory_block_id=None, hours=None):
        self.inventory_block_id = inventory_block_id
        self.hours = hours

    def to_dict(self):
        return dict(
            inventory_block_id=self.inventory_block_id,
            hours=self.hours,
        )

    @staticmethod
    def from_dict(json):
        return ECIServiceDetails(
            inventory_block_id=json.get('inventory_block_id'),
            hours=json.get('hours'),
        )


class LCOServiceDetails:
    def __init__(self, inventory_block_id=None, hours=None):
        self.inventory_block_id = inventory_block_id
        self.hours = hours

    def to_dict(self):
        return dict(
            inventory_block_id=self.inventory_block_id,
            hours=self.hours,
        )

    @staticmethod
    def from_dict(json):
        return LCOServiceDetails(
            inventory_block_id=json.get('inventory_block_id'),
            hours=json.get('hours'),
        )


class ExpenseServiceContext:
    _service_class_registry = {
        ServiceTypes.EARLY_CHECKIN: ECIServiceDetails,
        ServiceTypes.LATE_CHECKOUT: LCOServiceDetails,
    }

    def __init__(self, service_type, service_details):
        self.service_type = service_type
        self.service_details = service_details

    @staticmethod
    def create_empty_instance():
        return ExpenseServiceContext(service_type=None, service_details=None)

    def to_dict(self):
        return dict(
            service_type=self.service_type.value,
            service_details=self.service_details.to_dict()
            if self.service_details and not isinstance(self.service_details, dict)
            else None,
        )

    @classmethod
    def from_dict(cls, data):
        service_type = ServiceTypes(data.get('service_type'))
        service_details = data.get('service_details')

        service_detail_cls = cls._service_class_registry.get(service_type)

        if service_detail_cls:
            if service_details:
                service_details = service_detail_cls.from_dict(service_details)
            else:
                service_details = service_detail_cls()

        return cls(service_type=service_type, service_details=service_details)


class Discount:
    def __init__(
        self,
        discount_detail_reference_id=None,
        discount_value=None,
        applicable_date=None,
    ):
        self.discount_detail_reference_id = discount_detail_reference_id
        self.discount_value = discount_value
        self.applicable_date = applicable_date

    def to_json(self):
        return dict(
            discount_detail_reference_id=self.discount_detail_reference_id,
            discount_value=str(self.discount_value),
            applicable_date=self.applicable_date,
        )

    @staticmethod
    def from_json(json_data):
        return Discount(
            discount_detail_reference_id=json_data.get('discount_detail_reference_id'),
            discount_value=Money(json_data.get('discount_value')),
            applicable_date=json_data.get('applicable_date'),
        )


class DiscountDetail:
    def __init__(
        self,
        code=None,
        description=None,
        implementation=None,
        name=None,
        reference_id=None,
        total_discount_value=None,
        type=None,
        debit_mode=None,
        debit_percentage=None,
        reward_transaction_id=None,
        reward_source_name=None,
    ):
        self.code = code
        self.description = description
        self.implementation = implementation
        self.name = name
        self.reference_id = reference_id
        self.total_discount_value = total_discount_value
        self.type = type
        self.debit_mode = debit_mode
        self.debit_percentage = debit_percentage
        self.reward_transaction_id = reward_transaction_id
        self.reward_source_name = reward_source_name

    def to_json(self):
        return dict(
            code=self.code,
            description=self.description,
            implementation=self.implementation,
            name=self.name,
            reference_id=self.reference_id,
            total_discount_value=str(self.total_discount_value),
            type=self.type,
            debit_mode=self.debit_mode,
            debit_percentage=self.debit_percentage,
            reward_transaction_id=self.reward_transaction_id,
            reward_source_name=self.reward_source_name,
        )

    @staticmethod
    def from_json(json_data):
        return DiscountDetail(
            code=json_data.get('code'),
            description=json_data.get('description'),
            implementation=json_data.get('implementation'),
            name=json_data.get('name'),
            reference_id=json_data.get('reference_id'),
            total_discount_value=Money(json_data.get('total_discount_value')),
            type=json_data.get('type'),
            debit_mode=json_data.get('debit_mode'),
            debit_percentage=json_data.get('debit_percentage'),
            reward_transaction_id=json_data.get('reward_transaction_id'),
            reward_source_name=json_data.get('reward_source_name'),
        )


class GuardrailDetails:
    def __init__(
        self,
        stay_date,
        min_price,
        max_price,
        global_max_price,
        global_min_price,
        child_count,
        adult_count,
        applicable_guardrails_ids,
    ):
        self.stay_date = stay_date
        self.min_price = min_price
        self.max_price = max_price
        self.child_count = child_count
        self.adult_count = adult_count
        self.global_max_price = global_max_price
        self.global_min_price = global_min_price
        self.applicable_guardrails_ids = applicable_guardrails_ids

    @staticmethod
    def from_json(data):
        return GuardrailDetails(
            stay_date=data.get("stay_date"),
            min_price=data.get("min_price"),
            max_price=data.get("max_price"),
            global_max_price=data.get("global_max_price"),
            global_min_price=data.get("global_min_price"),
            child_count=data.get("child_count"),
            adult_count=data.get("adult_count"),
            applicable_guardrails_ids=data.get("applicable_guardrails_ids"),
        )

    def to_json(self):
        return dict(
            stay_date=self.stay_date,
            min_price=self.min_price,
            max_price=self.max_price,
            global_max_price=self.global_max_price,
            global_min_price=self.global_min_price,
            child_count=self.child_count,
            adult_count=self.adult_count,
            applicable_guardrails_ids=self.applicable_guardrails_ids,
        )


class Guardrail:
    def __init__(self, room_stay_id, details):
        self.room_stay_id = room_stay_id
        self.details = details

    @staticmethod
    def from_json(data):
        return Guardrail(
            room_stay_id=data.get("room_stay_id"),
            details=[
                GuardrailDetails.from_json(guardrail_detail)
                for guardrail_detail in data.get("details")
            ]
            if data.get("details")
            else None,
        )

    def to_json(self):
        return dict(
            room_stay_id=self.room_stay_id,
            details=[guardrail_detail.to_json() for guardrail_detail in self.details]
            if self.details
            else None,
        )
