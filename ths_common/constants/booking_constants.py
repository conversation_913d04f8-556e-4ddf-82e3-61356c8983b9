# pylint: disable=not-an-iterable
# coding=utf-8
"""
Booking Constants
"""

from treebo_commons.utils import dateutils

from ths_common.constants.base_enum import BaseEnum


class Salutation(BaseEnum):
    """
    Salutations
    """

    MR = 'Mr.'
    MRS = 'Mrs.'
    MISS = 'Miss'
    MR_MISS = 'Mr.Mrs.'
    FAM = 'FAM'
    SIRS = 'Sirs'
    CH = 'Ch.'
    DR = 'Dr.'
    MS = 'Ms'
    REVEREND = 'Reverend'
    SIR = 'Sir'
    LADY = 'Lady'
    PROF = 'Prof'

    # old value
    MISS_OLD = 'Miss.'
    MS_OLD = 'Ms.'
    MX_OLD = 'Mx.'
    SIR_OLD = 'Sir.'


class Genders(BaseEnum):
    """
    Gender enum
    """

    MALE = 'male'
    FEMALE = 'female'
    OTHER = 'other'


class BookingTypes(BaseEnum):
    """
    Enum for booking types
    """

    ROOM = 'room'
    CONFERENCE = 'conference'

    @staticmethod
    def key():
        return 'booking_type'

    @staticmethod
    def option_label():
        return 'Booking Type'

    @classmethod
    def get_enums(cls, user_type):
        return [enum for enum in cls]


class RoomStayType(BaseEnum):
    """
    Enum for room stays
    """

    NIGHT = 'night'
    HOUR = 'hour'


class OverflowActions(BaseEnum):
    """
    Enum for the possible overflow actions
    """

    MARK = "mark"
    UNMARK = "unmark"


class BookingApplications(BaseEnum):
    TREEBO_PMS = 'treebo-pms'
    PRIMUS = 'primus'
    SU = 'su'
    EMMA = 'emma'


class BookingActions(BaseEnum):
    """
    Enum for the booking actions possible
    """

    CONFIRM = "confirm"
    CHECKIN = "checkin"
    CHECKOUT = "checkout"
    CANCEL = "cancel"
    NOSHOW = 'noshow'
    RESERVE = "reserve"
    INVOICE_REGENERATION = 'invoice_regeneration'
    UNDO_CHECKIN = "undo_checkin"
    UNDO_CHECKOUT = "undo_checkout"
    UNDO_CANCEL = "undo_cancel"
    UNDO_NOSHOW = "undo_noshow"
    UNDO_CONFIRM = "undo_confirm"


class ActionEntities(BaseEnum):
    """
    Entities on which actions can be done
    """

    BOOKING = "booking"
    ROOM_STAY = "room_stay"
    GUEST_STAY = "guest_stay"


class ActionStatus(BaseEnum):
    """
    The status of the actions performed on a booking
    """

    CREATED = "created"
    INPROCESS = "inprocess"
    SUCCESS = "success"
    FAILED = "failed"
    REVERSED = "reversed"


class ReversalAllowed(BaseEnum):
    ALLOWED = "allowed"
    DISALLOWED = "disallowed"
    IRREVERSIBLE = "irreversible"


class BookingChannels(BaseEnum):
    """
    Enum for Booking sources
    """

    DIRECT = 'direct'
    ASSISTED_SALES = 'assisted-sales'
    OTA = 'ota'
    B2B = 'b2b'
    TA = 'ta'
    TREEBO_INTERNAL = 'treebo-internal'
    HOTEL = 'hotel'


class PayType(object):
    """
    Enum for Pay types
    """

    PREPAID = 'prepaid'
    PAY_AT_HOTEL = 'pay_at_hotel'
    BTC = 'btc'


class IDProofType(object):
    """
    ID Proof types Enum
    """

    PASSPORT = 'passport'
    AADHAR = 'aadhar'
    VOTER_ID = 'voter_id'
    DRIVING_LICENSE = 'driving_license'

    @staticmethod
    def key():
        return 'id_proof_type'

    @staticmethod
    def option_label():
        return 'Id Proof Type'

    @classmethod
    def get_enums(cls, user_type):
        return [enum for enum in cls]


class ProfileTypes(BaseEnum):
    """
    Enum for Profile types
    """

    INDIVIDUAL = 'individual'
    CORPORATE = 'corporate'
    SME = 'sme'


class BookingStatus(BaseEnum):
    """
    Enum of booking status
    """

    RESERVED = 'reserved', "Reserved"
    TEMPORARY = 'temporary', "Temporary"
    CONFIRMED = 'confirmed', "Confirmed"
    CHECKED_IN = 'checked_in', "Checked In"
    CHECKED_OUT = 'checked_out', "Checked Out"
    NOSHOW = 'noshow', "No Show"
    CANCELLED = 'cancelled', "Cancelled"
    PART_CHECKIN = 'part_checked_in', "Part Checked In"
    PART_CHECKOUT = 'part_checked_out', "Part Checked Out"

    @staticmethod
    def valid_booking_status():
        return [
            x.value
            for x in [
                BookingStatus.RESERVED,
                BookingStatus.TEMPORARY,
                BookingStatus.CONFIRMED,
                BookingStatus.CHECKED_OUT,
                BookingStatus.NOSHOW,
                BookingStatus.CANCELLED,
                BookingStatus.PART_CHECKIN,
                BookingStatus.PART_CHECKOUT,
                BookingStatus.CHECKED_IN,
            ]
        ]

    @staticmethod
    def key():
        return 'booking_status'

    @staticmethod
    def option_label():
        return 'Booking Status'

    @classmethod
    def night_auditable_status(cls):
        return [
            x.value
            for x in [
                BookingStatus.CHECKED_IN,
                BookingStatus.PART_CHECKIN,
                BookingStatus.PART_CHECKOUT,
            ]
        ]

    @classmethod
    def searchable_status(cls):
        return [
            x.value
            for x in [
                BookingStatus.TEMPORARY,
                BookingStatus.CHECKED_IN,
                BookingStatus.CHECKED_OUT,
                BookingStatus.NOSHOW,
                BookingStatus.CANCELLED,
            ]
        ]

    @classmethod
    def allowed_new_booking_status(cls):
        return [
            x.value
            for x in [
                BookingStatus.RESERVED,
                BookingStatus.CONFIRMED,
                BookingStatus.TEMPORARY,
            ]
        ]

    @classmethod
    def valid_room_stay_status(cls):
        return [
            x.value
            for x in [
                BookingStatus.RESERVED,
                BookingStatus.CHECKED_IN,
                BookingStatus.CHECKED_OUT,
                BookingStatus.NOSHOW,
                BookingStatus.CANCELLED,
                BookingStatus.PART_CHECKIN,
                BookingStatus.PART_CHECKOUT,
            ]
        ]

    @classmethod
    def valid_guest_stay_status(cls):
        return [
            x.value
            for x in [
                BookingStatus.RESERVED,
                BookingStatus.CHECKED_IN,
                BookingStatus.CHECKED_OUT,
                BookingStatus.NOSHOW,
                BookingStatus.CANCELLED,
            ]
        ]

    @classmethod
    def get_enums(cls, user_type):
        return [enum for enum in cls if enum != BookingStatus.RESERVED]


class BookingSyncStatus(BaseEnum):
    """
    Enum of booking status
    """

    UNPROCESSED = 'unprocessed'
    SUCCESS = 'success'
    FAIL = 'fail'


class GuestStatus(BaseEnum):
    """
    Enum of Guest status
    """

    ACTIVE = 'active'
    DELETED = 'deleted'


class AddonStatus(BaseEnum):
    CREATED = 'created'
    DELETED = 'deleted'


class AgeGroup(BaseEnum):
    """
    Age group of people
    """

    INFANT = 'infant'
    CHILD = 'child'
    ADULT = 'adult'


class ExpenseTypes(BaseEnum):
    """
    Set of allowed expenses
    """

    FOOD = 'food'
    CAB_SERVICE = 'cab_service'
    EARLY_CHECKOUT = 'early_checkout'


class ExpenseStatus(BaseEnum):
    """
    Set of expense status
    """

    CREATED = "created"
    CONSUMED = "consumed"
    CANCELLED = "cancelled"
    DELETED = "deleted"


class ExpenseAddedBy(BaseEnum):
    TREEBO = "treebo"
    HOTEL = "hotel"
    POS = "pos"


class BookingSubResources(object):
    ROOM_STAY = "booking.room_stay"


class BookingSearchParameter(BaseEnum):
    GUEST_NAME = "guest_name", "Guest Name"
    GUEST_PHONE = "guest_phone", "Guest Phone Number"
    GUEST_EMAIL = "guest_email", "Guest Email"
    REFERENCE_NUMBER = "reference_number", "Booking ID"
    STATUS = "status", "Status"
    BOOKING_OWNER_REFERENCE_ID = (
        "booking_owner_reference_id",
        "Booking Owner Reference ID",
    )

    @staticmethod
    def key():
        return 'booking_search_parameter'

    @staticmethod
    def option_label():
        return 'Booking Search Parameter'

    @classmethod
    def get_enums(cls, user_type):
        return [enum for enum in cls]


class CancelReason(BaseEnum):
    NOT_RESPONDING = "not_responding", "Guest Not Responding"
    PLAN_CHANGED = "plan_changed", "Guest Plan Changed"
    FOUND_BETTER_DEAL = "found_better_deal", "Guest Found Better Deal"
    CREATED_BY_MISTAKE = "created_by_mistake", "Created By Mistake"
    BOOKING_MOVED = "booking_moved", "Booking Moved"
    OTHER = "other", "Other"

    @staticmethod
    def key():
        return 'cancel_reason'

    @staticmethod
    def option_label():
        return 'Cancel Reason'

    @classmethod
    def get_enums(cls, user_type):
        return [enum for enum in cls]


class InvoiceGroupStatus(BaseEnum):
    PREVIEW = 'preview'
    GENERATED = 'generated'
    REGENERATED = 'regenerated'
    CANCELLED = 'cancelled'
    LOCKED = 'locked'
    SENT_FOR_GST_FILING = 'sent_for_gst_filing'


class AddonRelativeDate(BaseEnum):
    CHECKIN = 'checkin', 'Checkin'
    CHECKIN_PLUS_ONE = 'checkin_plus_one', 'Checkin Plus One'
    CHECKOUT = 'checkout', 'Checkout'
    CHECKOUT_MINUS_ONE = 'checkout_minus_one', 'Checkout Minus One'

    @staticmethod
    def option_label():
        return 'Addon Relative Date Options'

    @classmethod
    def get_enums(cls, user_type):
        return [enum for enum in cls]

    @staticmethod
    def key():
        return 'addon_relative_date'

    def get_absolute_date(self, checkin_date, checkout_date):
        if self == AddonRelativeDate.CHECKIN:
            return checkin_date

        if self == AddonRelativeDate.CHECKIN_PLUS_ONE:
            return dateutils.add(checkin_date, days=1)

        if self == AddonRelativeDate.CHECKOUT:
            return checkout_date

        if self == AddonRelativeDate.CHECKOUT_MINUS_ONE:
            return dateutils.subtract(checkout_date, days=1)

        return None


class AttachmentGroup(BaseEnum):
    ID_PROOF = 'id_proof', 'ID Proof'
    BOOKING_REQUEST = 'booking_request', 'Booking Request'
    BOOKING_RELOCATION_REQUEST = (
        'booking_relocation_request',
        'Booking Relocation Request',
    )
    REFUND_REQUEST = "refund_request", "Refund Request"
    CREDIT_SHELL_REFUND_REQUEST = (
        "credit_shell_refund_request",
        "Credit Shell Refund Request",
    )
    REISSUE_INVOICE_REQUEST = "reissue_invoice_request", "Reissue Invoice Request"
    BOOKING_CANCELLATION_REQUEST = (
        "booking_cancellation_request",
        "Booking Cancellation Request",
    )


class AttachmentFileType(object):
    PNG = 'png'
    JPG = 'jpg'
    JPEG = 'jpeg'
    PDF = 'pdf'


class ERegCardStatus(BaseEnum):
    INCOMPLETE = 'incomplete'
    COMPLETE = 'complete'
    LOCKED = 'locked'


class AttachmentStatus(BaseEnum):
    PENDING_VERIFICATION = 'pending_verification'
    VERIFIED = 'verified'
    REJECTED = 'rejected'


class PreferencesNameEnum(BaseEnum):
    NEWSPAPER_NAME = 'newspaper_name'


class GuestMetaDataNameEnum(BaseEnum):
    ACDC_PROFILE_LINK = 'acdc_profile_link'


class CancellationChargeType(BaseEnum):
    PERCENT_BOOKING_VALUE = 'percent_of_booking_value'
    PERCENT_ROOM_NIGHT_CHARGE = 'percent_of_room_night_charge'


class UnitOfChildCharge(BaseEnum):
    FIXED_VALUE = 'fixed_value'
    PERCENTAGE_OF_ADULT_SINGLE_OCCUPANCY_PRICE = (
        'percent_of_adult_single_occupancy_price'
    )


class BookingSubChannels(BaseEnum):
    """
    Enum for Booking sources sub channel
    """

    B2B_BULK = 'bulk'
    TA_BULK = 'ta-bulk'
    LOCAL_TA = 'local-ta'
    B2B_TA = 'b2b-ta'
    TA_COMMISSIONABLE = 'ta-commissionable'
    TMC = 'tmc'


class RatePlanCodes(BaseEnum):
    """
    Enum for Rate Plan Codes
    """

    TRB_DEFAULT = 'TRB-DEFAULT'
    TRB_DEFAULT_NRP = 'TRB-DEFAULT-NRP'
    TRB_DEFAULT_D_5 = 'TRB-DEFAULT-D-5'
    TRB_DEFAULT_BULK = 'TRB-DEFAULT-BULK'


class RatePlanTags(BaseEnum):
    """
    Enum for Rate Plan Codes
    """

    REFUNDABLE = 'Refundable'
    NON_REFUNDABLE = 'Non Refundable'


class NatureOfSupply(BaseEnum):
    """
    Enum for Booking types
    """

    B2B = 'b2b'
    B2C = 'b2c'


class TACommissionStatus(BaseEnum):
    """
    Enum for lifecycle of ta commission
    """

    CREATED = 'created'
    CANCELLED = 'cancelled'
    LOCKED = 'locked'
    NULLIFIED_BY_REISSUE = 'nullified_by_reissue'


class TACommissionTypes(BaseEnum):
    FIXED = 'fixed'
    PERCENT = 'percent'


class ServiceTypes(BaseEnum):
    EARLY_CHECKIN = 'early_checkin'
    LATE_CHECKOUT = 'late_checkout'


class CancellationPolicy(BaseEnum):
    CANCELLATION_FEE_AS_PER_RATE_PLAN = 'cancellation_fee_as_per_rate_plan'
    COMPLETE_CANCELLATION_FEE_WAVIER = 'complete_cancellation_fee_waiver'
    RETAIN_COMPLETE_PAYMENT = 'retain_complete_payment'
    RETAIN_COMPLETE_BOOKING_AMOUNT = 'retain_complete_booking_amount'
    CUSTOM_CANCELLATION_FEE = 'custom_cancellation_fee'


class GuaranteeTypes(BaseEnum):
    OTHERS = "others"
    CREDIT_CARD = "cc"
    TRAVEL_AGENT = "travel_agent"
    CORPORATE = "corporate"
    HOTEL_SALES_PROMISE = "hotel_sales_promise"
    TREEBO_SALES_PROMISE = "treebo_sales_promise"
    PAYMENT_GUARANTEE = "payment_guarantee"


class DiscountType(BaseEnum):
    TCP = "TCP"
